package com.mchz.nyx.meta.instance.store;


import com.mchz.nyx.meta.entity.DbMetaObject;
import com.mchz.nyx.meta.support.SchemaDiffDetail;
import com.mchz.nyx.meta.support.TableInsertDetail;
import com.mchz.nyx.meta.support.TableUpdateDetail;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
public interface NyxEventStore {

    /**
     * 查询（含逻辑删除）
     *
     * @param sourceId 数据源id
     * @param schema   schema
     * @return 结果
     */
    DbMetaObject getSchemaObj(long sourceId, String schema);

    /**
     * 查询（含逻辑删除）
     *
     * @param sourceId 数据源id
     * @return 结果
     */
    List<DbMetaObject> listSchemaObj(long sourceId);

    /**
     * 查询（含逻辑删除）
     *
     * @param sourceId 数据源id
     * @param schemaId schemaId
     * @param type     1:表 2:视图 3:表与视图
     * @param tables   表列表
     * @return 结果
     */
    List<DbMetaObject> listTableOrViewObj(long sourceId, long schemaId, int type, List<String> tables);

    /**
     * 查询（含逻辑删除）
     *
     * @param sourceId 数据源id
     * @param pid      父主键
     * @return 结果
     */
    List<DbMetaObject> listObjByPid(long sourceId, long pid);

    /**
     * 获取注释
     */
    Map<Long, String> getComment(List<Long> tIds, List<Long> cIds);


    void insertSchema(long sourceId, SchemaDiffDetail detail);

    /**
     * 表数据插入
     */
    CompletableFuture<Boolean> insertTableDetail(long sourceId, TableInsertDetail detail);

    /**
     * 更新表格
     */
    CompletableFuture<Boolean> updateTableDetail(long sourceId, TableUpdateDetail detail);

    default void shutdown() {
    }
}
