package com.mchz.nyx.meta.instance.crawl.mcdatasource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.mchz.datasource.cli.DatasourceMetabaseCli;
import com.mchz.mcdatasource.core.DataBaseType;
import com.mchz.nyx.meta.exception.CrawlerException;
import com.mchz.nyx.meta.instance.crawl.AbstractSchemaEventCrawl;
import com.mchz.nyx.meta.model.meta.CatalogSchema;
import com.mchz.nyx.meta.support.DataSourceConfig;
import com.mchz.nyx.meta.support.MetaCrawlConfig;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Properties;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
public class McDatasourceSchemaCrawl extends AbstractSchemaEventCrawl {
    private final DataSourceConfig source;
    private final int type;

    public McDatasourceSchemaCrawl(MetaCrawlConfig config) {
        super(config);
        this.source = config.getSource();
        int tmp;
        if (DataBaseType.specialPluginIdLikeMssql(source.getType()) || DataBaseType.SYBASE.pluginId.equals(source.getType())) {
            tmp = 1;
        } else if (DataBaseType.specialPluginIdLikePgSQL(source.getType()) || DataBaseType.specialPluginIdLikeKingbase(source.getType())) {
            tmp = 2;
        } else {
            tmp = 0;
        }
        this.type = tmp;
    }

    @Override
    protected void execute() {
        try {
            DatasourceMetabaseCli metadataCli = new DatasourceMetabaseCli(source.getType(), source.getHost(), source.getDb(), source.getPort(), source.getUser(), source.getPass(), true, ObjUtil.defaultIfNull(source.getAdvanced(), () -> new Properties()));
            List<String> list = metadataCli.fetchSchemas();
            if (CollUtil.isEmpty(list)) {
                return;
            }
            List<CatalogSchema> schemas;
            switch (type) {
                case 1:
                    schemas = list.stream().map(McDatasourceSchemaCrawl::splitCatalogAndSchema).collect(Collectors.toList());
                    break;
                case 2:
                    schemas = list.stream().map(v -> CatalogSchema.of(v, source.getDb(), v)).collect(Collectors.toList());
                    break;
                case 0:
                default:
                    schemas = list.stream().map(CatalogSchema::of).collect(Collectors.toList());
                    break;
            }

            schema(schemas);
        } catch (Exception e) {
            throw new CrawlerException("元数据采集schema信息失败", e);
        }
    }

    private static CatalogSchema splitCatalogAndSchema(String catalogSchema) {
        if (catalogSchema.startsWith("[")) {
            List<String> list = StrUtil.split(catalogSchema, "].", 2, false, false);
            return CatalogSchema.of(catalogSchema, list.get(0).substring(1), list.get(1));
        }
        List<String> list = StrUtil.split(catalogSchema, ".", 2, false, false);
        return CatalogSchema.of(catalogSchema, list.get(0), list.get(1));
    }
}
