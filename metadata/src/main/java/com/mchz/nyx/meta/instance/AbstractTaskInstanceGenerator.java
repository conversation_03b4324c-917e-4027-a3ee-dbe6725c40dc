package com.mchz.nyx.meta.instance;

import com.mchz.mcdatasource.core.DataBaseType;
import com.mchz.nyx.meta.NyxInstance;
import com.mchz.nyx.meta.NyxInstanceGenerator;
import com.mchz.nyx.meta.common.enums.MetaObjType;
import com.mchz.nyx.meta.instance.crawl.*;
import com.mchz.nyx.meta.instance.crawl.mcdatasource.*;
import com.mchz.nyx.meta.instance.sink.DefaultNyxEventSink;
import com.mchz.nyx.meta.instance.sink.NyxEventSink;
import com.mchz.nyx.meta.instance.store.NyxEventStore;
import com.mchz.nyx.meta.support.MetaCrawlConfig;

import java.util.ArrayList;
import java.util.EnumSet;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
public abstract class AbstractTaskInstanceGenerator<T extends MetaCrawlConfig> implements NyxInstanceGenerator<T> {

    @Override
    public NyxInstance generate(T config) {
        NyxEventStore store = buildEventStore(config);
        NyxEventSink sink = buildEventSink(config, store);
        NyxEventCrawl crawl = buildEventCrawl(config, sink);
        return new DefaultNyxInstance(config.getSourceId(), crawl, sink, store);
    }

    protected NyxEventCrawl buildEventCrawl(T config, NyxEventSink sink) {
        int meta;
        if (null != config.getCrawlType()) {
            meta = config.getCrawlType();
        } else if (null != config.getCrawl()) {
            int i = 0;
            for (MetaObjType metaObjType : EnumSet.copyOf(config.getCrawl())) {
                i += metaObjType.getValue();
            }
            meta = i;
        } else {
            meta = 255;
        }
        DsMetaAdapter adapter = buildMetaAdapter(config);
        if (null != adapter) {
            MetaAgentCrawl crawl = new MetaAgentCrawl(config, adapter, meta);
            crawl.setSourceId(config.getSourceId());
            crawl.setEventSink(sink);
            return crawl;
        }
        List<AbstractEventCrawl> res = new ArrayList<>(4);
        defaultCrawl(config, meta, res);

        res.forEach(v -> {
            v.setSourceId(config.getSourceId());
            v.setEventSink(sink);
        });
        if (1 == res.size()) {
            return res.get(0);
        }
        return new GroupEventCrawl(res);
    }

    protected NyxEventSink buildEventSink(T config, NyxEventStore store) {
        DefaultNyxEventSink sink = new DefaultNyxEventSink();
        sink.setSourceId(config.getSourceId());
        sink.setEventStore(store);
        sink.setCount(config.count());
        return sink;
    }

    protected DsMetaAdapter buildMetaAdapter(T config) {
        return null;
    }

    protected abstract NyxEventStore buildEventStore(T config);

    protected void defaultCrawl(T config, int meta, List<AbstractEventCrawl> res) {
        DataBaseType type = DataBaseType.findByPluginId(config.getSource().getType());
        if (MetaObjType.SCHEMA.contain(meta)) {
            res.add(new McDatasourceSchemaCrawl(config));
        }

        if (MetaObjType.TABLE.contain(meta)) {
            if (supportAllTablesAndViews(type)) {
                res.add(new McDatasourceTableName1Crawl(config, MetaObjType.VIEW.contain(meta)));
            } else {
                res.add(new McDatasourceTableName2Crawl(config, MetaObjType.VIEW.contain(meta) && supportView(type)));
            }
        }
        if (MetaObjType.COLUMN.contain(meta)) {
            if (supportStream(type)) {
                res.add(new McDatasourceStreamCrawl(config, config.count(), config.foreign()));
                if (MetaObjType.VIEW.contain(meta)) {
                    res.add(new McDatasourceViewCrawl(config));
                }
            } else {
                res.add(new McDatasourceObjectCrawl(config, MetaObjType.VIEW.contain(meta)));
            }
        }
    }

    private boolean needCatalog(DataBaseType databaseType) {
        switch (databaseType) {
            case PGSQL:
            case PGSQL_14:
            case RDS_PGSQL:
            case GREENPLUM:
            case GREENPLUM_OFFICIAL:
            case ADB_PGSQL:
            case POLAR_PGSQL:
            case MSSQL:
            case MSSQLJTDS:
            case MSSQLNATIVE:
                return true;
            default:
                return false;
        }
    }

    private boolean supportStream(DataBaseType databaseType) {
        switch (databaseType) {
            case ORACLE:
            case MYSQL:
            case MYSQL_5:
            case MSSQL:
            case DB2:
            case PGSQL:
            case HIVE:
            case HIVE_FHD653:
                return true;
            default:
                return false;
        }
    }

    private boolean supportAllTablesAndViews(DataBaseType type) {
        switch (type) {
            case ORACLE:
            case MYSQL:
            case MYSQL_5:
            case RDS_MYSQL:
            case MSSQL:
            case DB2:
            case PGSQL:
            case GBASE8A:
            case GBASE8S87:
            case DM:
            case TERADATA:
                return true;
            default:
                return false;
        }
    }

    private boolean supportView(DataBaseType type) {
        switch (type) {
            case ORACLE:
            case MSSQL:
            case KINGBASE86:
            case OCEANBASE:
            case OCEANBASE_ORACLE:
            case MYSQL:
            case MYSQL_5:
            case RDS_MYSQL:
            case GBASE8A:
            case GBASE8S87:
            case DM:
            case TERADATA:
                return true;
            default:
                return false;
        }
    }
}
