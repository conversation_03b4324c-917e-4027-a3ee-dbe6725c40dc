package com.mchz.nyx.meta.instance.crawl;

import com.mchz.nyx.meta.common.enums.MetaObjType;
import com.mchz.nyx.meta.entity.DbMetaObject;
import com.mchz.nyx.meta.model.meta.CatalogSchema;
import com.mchz.nyx.meta.model.meta.WrapTable;

import java.util.List;
import java.util.function.Function;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/18
 */
public interface NyxCrawlAgent {
    void schema(List<CatalogSchema> schemas);

    void schema(List<CatalogSchema> schemas, List<String> delSchema);

    void stageStart(CatalogSchema catalogSchema, List<String> tableNames, MetaObjType first, MetaObjType... include);

    void tableName(List<DbMetaObject> tables);

    void consume(WrapTable table);

    void stageFinish(CatalogSchema catalogSchema);

    void crawlTableName(CatalogSchema catalogSchema, List<String> tables, MetaObjType type);

    <T> void parseColumn(List<T> columns, WrapTable table, Function<T, DbMetaObject> objectFun, MetaColumnFun<T> columnFun);
}
