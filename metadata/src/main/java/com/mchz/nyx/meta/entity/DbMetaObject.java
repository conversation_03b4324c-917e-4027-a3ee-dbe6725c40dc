package com.mchz.nyx.meta.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2022/1/10 14:06
 */
@Data
@TableName("dbmeta_object")
public class DbMetaObject {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long oid;

    /**
     * 数据源主键
     */
    private Long sourceId;

    /**
     * 名称
     */
    private String name;

    /**
     * 类型
     */
    private String type;

    /**
     * 描述
     */
    private String description;

    /**
     * 删除标记
     */
    private Boolean deleted;

    /**
     * 创建时间
     */
    private Date createdAt;

    /**
     * 更新时间
     */
    private Date updatedAt;

    /**
     * 修订版本
     */
    private Integer revision;

    /**
     * 内容哈希
     */
    private String hashValue;

    /**
     * 父主键
     */
    private Long pid;
}
