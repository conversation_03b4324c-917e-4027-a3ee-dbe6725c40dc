package com.mchz.nyx.meta.instance.crawl.mcdatasource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.mchz.mcdatasource.model.db.DatasourceDatabaseMeta;
import com.mchz.mcdatasource.model.dbmeta.ForeignKeyInfo;
import com.mchz.mcdatasource.model.dbmeta.IndexMetaData;
import com.mchz.mcdatasource.model.dbmeta.StreamForeignKeyInfo;
import com.mchz.mcdatasource.model.dbmeta.StreamIndexMetaData;
import com.mchz.nyx.common.enums.DataTypeGroup;
import com.mchz.nyx.meta.common.constants.MetadataConst;
import com.mchz.nyx.meta.common.enums.MetaObjType;
import com.mchz.nyx.meta.entity.*;
import com.mchz.nyx.meta.instance.crawl.AbstractMetaEventCrawl;
import com.mchz.nyx.meta.model.meta.Entry;
import com.mchz.nyx.meta.model.meta.WrapObj;
import com.mchz.nyx.meta.model.meta.WrapTable;
import com.mchz.nyx.meta.support.DataSourceConfig;
import com.mchz.nyx.meta.support.MetaCrawlConfig;
import lombok.extern.slf4j.Slf4j;
import org.pentaho.di.core.database.DatabaseMeta;

import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractMcDatasourceCrawl extends AbstractMetaEventCrawl {

    public AbstractMcDatasourceCrawl(MetaCrawlConfig config) {
        super(config);
    }

    protected static DatabaseMeta buildDatabaseMeta(DataSourceConfig source) {
        return new DatasourceDatabaseMeta(source.getType(), source.getHost(), source.getDb(), source.getPort(), source.getUser(), source.getPass(), ObjUtil.defaultIfNull(source.getAdvanced(), () -> new Properties())).getDatabaseMeta();
    }

    protected static DatabaseMeta buildDatabaseMeta(DataSourceConfig source, Properties advanced) {
        return new DatasourceDatabaseMeta(source.getType(), source.getHost(), source.getDb(), source.getPort(), source.getUser(), source.getPass(), advanced).getDatabaseMeta();
    }

    protected void parseIndex(List<StreamIndexMetaData> indexInfos, WrapTable table) {
        if (CollUtil.isEmpty(indexInfos)) {
            return;
        }
        DbMetaObject obj = table.getObj();
        List<StreamIndexMetaData> tmp = deduplication(indexInfos, v -> Arrays.asList(v.getName(), v.getType()), v -> log.warn("【元数据】索引异常{}[{}:{}] SQL:{}", obj.getName(), obj.getType(), v.getName(), v.getSql()));
        WrapObj<DbMetaIndex> wIdx = new WrapObj<>(obj, tmp.size());
        List<WrapObj<DbMetaIndexColumn>> icList = new ArrayList<>(tmp.size());
        table.setIndex(wIdx);
        table.setIndexColumn(icList);

        Map<String, Entry<DbMetaColumn>> columnMap = table.getColumnMap();
        for (StreamIndexMetaData info : tmp) {
            DbMetaIndex metaIndex = buildIndex(info);
            if (null == metaIndex) {
                continue;
            }
            DbMetaObject oIdx = buildIndexObject(info, metaIndex.getType());
            wIdx.add(oIdx, metaIndex);

            List<IndexMetaData> list = info.getIndexMetaDatas();
            if (CollUtil.isEmpty(list)) {
                continue;
            }
            WrapObj<DbMetaIndexColumn> wIdxCol = new WrapObj<>(oIdx, list.size());
            int i = 1;
            for (IndexMetaData index : list) {
                Entry<DbMetaColumn> entry = columnMap.get(index.getColumnName());
                //字段表达式
                if (null == entry) {
                    i++;
                    continue;
                }
                wIdxCol.add(entry.getObj(), buildIndexColumn(i++));
                entry.getValue().setIsIndex(Boolean.TRUE);
            }
            icList.add(wIdxCol);
        }
    }

    protected void parseForeignKey(List<StreamForeignKeyInfo> foreignInfos, WrapTable table) {
        if (CollUtil.isEmpty(foreignInfos)) {
            return;
        }
        DbMetaObject obj = table.getObj();
        List<StreamForeignKeyInfo> tmp = deduplication(foreignInfos, StreamForeignKeyInfo::getName, v -> log.warn("【元数据】外键异常{}[{}] SQL:{}", obj.getName(), v.getName(), v.getSql()));
        WrapObj<DbMetaForeignKey> wFk = new WrapObj<>(obj, tmp.size());
        List<WrapObj<DbMetaForeignKeyInfo>> fiList = new ArrayList<>(tmp.size());
        table.setFk(wFk);
        table.setFkInfo(fiList);

        Map<String, Entry<DbMetaColumn>> map = table.getColumnMap();
        for (StreamForeignKeyInfo info : tmp) {
            DbMetaObject oFk = buildFkObject(info);
            DbMetaForeignKey fk = buildFk(info);
            wFk.add(oFk, fk);

            List<ForeignKeyInfo> list = info.getForeignKeyInfoList();
            if (CollUtil.isEmpty(list)) {
                continue;
            }
            WrapObj<DbMetaForeignKeyInfo> wFkCol = new WrapObj<>(oFk, list.size());
            int i = 1;
            for (ForeignKeyInfo foreignKeyInfo : list) {
                Entry<DbMetaColumn> entry = map.get(foreignKeyInfo.getForeignColumnName());
                Objects.requireNonNull(entry);
                wFkCol.add(entry.getObj(), buildFkInfo(foreignKeyInfo, i++));
            }
            fiList.add(wFkCol);
        }
    }

    protected DbMetaObject buildIndexObject(StreamIndexMetaData idx, String type) {
        DbMetaObject obj = new DbMetaObject();
        obj.setSourceId(sourceId);
        obj.setName(idx.getName());
        obj.setType(type);
        return obj;
    }

    protected DbMetaIndex buildIndex(StreamIndexMetaData data) {
        DbMetaIndex meta = new DbMetaIndex();
        MetaObjType type = MetaObjType.getType(data.getType());
        if (MetaObjType.OTHER.equals(type)) {
            return null;
        }
        meta.setType(type.name());
        switch (type) {
            case PRIMARY_KEY:
                meta.setIsPk(true);
                meta.setIsUnique(true);
                break;
            case INDEX:
                meta.setIsPk(false);
                meta.setIsUnique(false);
                break;
            case UNIQUE_INDEX:
                meta.setIsPk(false);
                meta.setIsUnique(true);
                break;
            default:
        }
        return meta;
    }

    protected DbMetaIndexColumn buildIndexColumn(int index) {
        DbMetaIndexColumn meta = new DbMetaIndexColumn();
        meta.setPosition(index);
        return meta;
    }

    protected DbMetaObject buildFkObject(StreamForeignKeyInfo idx) {
        DbMetaObject obj = new DbMetaObject();
        obj.setSourceId(sourceId);
        obj.setName(idx.getName());
        obj.setType(MetaObjType.FOREIGN_KEY.name());
        return obj;
    }

    protected DbMetaForeignKey buildFk(StreamForeignKeyInfo info) {
        return new DbMetaForeignKey();
    }

    protected DbMetaForeignKeyInfo buildFkInfo(ForeignKeyInfo fk, int index) {
        DbMetaForeignKeyInfo info = new DbMetaForeignKeyInfo();
        info.setRefSchema(fk.getPrimaryOwner());
        info.setRefTable(fk.getPrimaryTableName());
        info.setRefColumn(fk.getPrimaryColumnName());
        info.setPosition(index);
        return info;
    }

    protected void predictGroup(String dataType, DbMetaColumn type) {
        final DataTypeGroup typeGroup;
        switch (dataType.toUpperCase(Locale.ROOT)) {
            case "ARRAY":
            case "DISTINCT":
            case "JAVA_OBJECT":
            case "STRUCT":
            case MetadataConst.UNKNOWN_DATE_TYPE:
                typeGroup = DataTypeGroup.OBJECT;
                break;
            case "BINARY":
            case "LONGVARBINARY":
            case "VARBINARY":
            case "LONG RAW":
                typeGroup = DataTypeGroup.BINARY;
                break;
            case "BIT":
            case "BOOLEAN":
                typeGroup = DataTypeGroup.BIT;
                break;
            case "ROWID":
                typeGroup = DataTypeGroup.ID;
                break;
            case "INT":
            case "BIGINT":
            case "INTEGER":
            case "SMALLINT":
            case "TINYINT":
                typeGroup = DataTypeGroup.INTEGER;
                break;
            case "BLOB":
            case "CLOB":
            case "NCLOB":
            case "JSONB":
                typeGroup = DataTypeGroup.LARGE_OBJECT;
                break;
            case "DECIMAL":
            case "DOUBLE":
            case "FLOAT":
            case "REAL":
                typeGroup = DataTypeGroup.REAL;
                break;
            case "REF":
            case "REF_CURSOR":
                typeGroup = DataTypeGroup.REFERENCE;
                break;
            case "DATE":
            case "TIME":
            case "TIMESTAMP":
            case "TIMESTAMP_WITH_TIMEZONE":
            case "TIME_WITH_TIMEZONE":
            case "TIMESTAMP WITHOUT TIME ZONE":
            case "TIMESTAMP WITH TIME ZONE":
                typeGroup = DataTypeGroup.TEMPORAL;
                break;
            case "DATALINK":
                typeGroup = DataTypeGroup.URL;
                break;
            case "SQLXML":
                typeGroup = DataTypeGroup.XML;
                break;
            case "NUMBER":
            case "NUMERIC":
                if (null == type.getScale() || 0 == type.getScale()) {
                    typeGroup = DataTypeGroup.INTEGER;
                } else {
                    typeGroup = DataTypeGroup.REAL;
                }
                break;
            case "OTHER":
                typeGroup = DataTypeGroup.UNKNOWN;
                break;
            default:
                typeGroup = DataTypeGroup.CHARACTER;
        }
        type.setTypeGroup(typeGroup.name());
    }

    protected Long convertRows(long rows) {
        if (-1 == rows) {
            return null;
        }
        return rows;
    }

    protected Long convertLength(String length) {
        final String max = "MAX";
        if (!max.equals(length)) {
            try {
                return Long.valueOf(length);
            } catch (NumberFormatException ignore) {
                return null;
            }
        } else {
            return null;
        }
    }

    private <T, K> List<T> deduplication(List<T> list, Function<T, K> getKey, Consumer<T> withLog) {
        Set<K> set = new HashSet<>(list.size());
        return list.stream().filter(v -> {
            if (set.add(getKey.apply(v))) {
                return true;
            }
            withLog.accept(v);
            return false;
        }).collect(Collectors.toList());
    }
}
