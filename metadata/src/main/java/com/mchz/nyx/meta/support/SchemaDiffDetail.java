package com.mchz.nyx.meta.support;

import com.mchz.nyx.meta.entity.DbMetaObject;
import com.mchz.nyx.meta.entity.DbMetaSchema;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
public interface SchemaDiffDetail {
    List<DbMetaObject> getInsertObj();

    List<DbMetaSchema> getInsert();

    List<DbMetaObject> getUpdateObj();

    List<DbMetaSchema> getUpdate();

    List<Long> getRestoreObjIds();

    List<Long> getDeleteObjIds();
}
