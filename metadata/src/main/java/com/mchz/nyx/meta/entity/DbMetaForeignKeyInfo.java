package com.mchz.nyx.meta.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mchz.nyx.meta.support.ObjectInfo;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2022/1/11 19:31
 */
@Data
@TableName("dbmeta_foreign_key_info")
public class DbMetaForeignKeyInfo implements ObjectInfo {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 数据源ID
     */
    private Long sourceId;

    /**
     * 表主键
     */
    private Long fkId;

    /**
     * 字段主键
     */
    private Long columnId;

    /**
     * 关联模式
     */
    private String refSchema;

    /**
     * 关联表
     */
    private String refTable;

    /**
     * 关联字段
     */
    private String refColumn;

    /**
     * 位置
     */
    private Integer position;

    @Override
    public void setOid(Long id) {
        setColumnId(id);
    }

    @Override
    public void setParent(DbMetaObject obj) {
        setFkId(obj.getOid());
    }
}
