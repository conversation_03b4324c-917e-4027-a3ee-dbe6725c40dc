package com.mchz.nyx.meta.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @see com.mchz.mcdatasource.model.constant.DbObjectType
 * @see com.mchz.mcdatasource.model.core.StreamDataType
 */
@Getter
@AllArgsConstructor
public enum MetaObjType {
    /**
     * 元数据 类型
     */
    SCHEMA(1),
    TABLE(2),
    COLUMN(4),
    INDEX(8),
    UNIQUE_INDEX(0),
    PRIMARY_KEY(0),
    FOREIGN_KEY(0),
    VIEW(128),
    OTHER(0);

    private final int value;


    public static MetaObjType getType(String value) {
        for (MetaObjType type : values()) {
            if (type.name().equals(value)) {
                return type;
            }
        }
        return OTHER;
    }

    public boolean contain(int v) {
        return (v & value) > 0;
    }
}
