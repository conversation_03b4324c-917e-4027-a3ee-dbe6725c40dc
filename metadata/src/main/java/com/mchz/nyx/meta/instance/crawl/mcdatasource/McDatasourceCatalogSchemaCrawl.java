package com.mchz.nyx.meta.instance.crawl.mcdatasource;

import com.mchz.mcdatasource.core.DataType;
import com.mchz.mcdatasource.core.engine.dbobject.DbObjectEngine;
import com.mchz.mcdatasource.model.dbmeta.DbTableMetaData;
import com.mchz.mcdatasource.model.request.ObjectSqlRequest;
import com.mchz.nyx.meta.exception.CrawlerException;
import com.mchz.nyx.meta.instance.crawl.AbstractSchemaEventCrawl;
import com.mchz.nyx.meta.model.meta.CatalogSchema;
import com.mchz.nyx.meta.support.DataSourceConfig;
import com.mchz.nyx.meta.support.MetaCrawlConfig;
import lombok.extern.slf4j.Slf4j;
import org.pentaho.di.core.database.DatabaseMeta;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
public class McDatasourceCatalogSchemaCrawl extends AbstractSchemaEventCrawl {
    private final DataSourceConfig source;

    public McDatasourceCatalogSchemaCrawl(MetaCrawlConfig config) {
        super(config);
        this.source = config.getSource();
    }

    @Override
    protected void execute() {
        DatabaseMeta databaseMeta = AbstractMcDatasourceCrawl.buildDatabaseMeta(source);
        ObjectSqlRequest request = new ObjectSqlRequest();
        request.setDataTypes(Collections.singletonList(DataType.ALL_SCHEMA));
        try {
            DbTableMetaData res = DbObjectEngine.getInstance().getDbMetaDataByJdbc(databaseMeta, 0, request);
            if (null != res.getSchemas()) {
                schema(res.getSchemas().stream().map(CatalogSchema::of).collect(Collectors.toList()));
            } else if (null != res.getCatalogSchemaMap()) {
                List<CatalogSchema> list = res.getCatalogSchemaMap().entrySet().stream().flatMap(v -> v.getValue().stream().map(s -> CatalogSchema.of(v.getKey(), s))).collect(Collectors.toList());
                schema(list);
            }
        } catch (Exception e) {
            throw new CrawlerException("元数据采集schema信息异常", e);
        }
    }
}
