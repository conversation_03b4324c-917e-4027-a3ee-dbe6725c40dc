package com.mchz.nyx.meta.model.meta;

import com.mchz.nyx.meta.entity.DbMetaObject;
import com.mchz.nyx.meta.entity.DbMetaSchema;
import com.mchz.nyx.meta.support.SchemaDiffDetail;
import lombok.Data;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
@Data
public class SchemaDiff implements SchemaDiffDetail {
    private WrapObj<DbMetaSchema> schema;
    private List<DbMetaObject> updateObj;
    private List<DbMetaSchema> update;
    private List<Long> restoreObjIds;
    private List<Long> deleteObjIds;

    @Override
    public List<DbMetaObject> getInsertObj() {
        return schema.getObjStream().collect(Collectors.toList());
    }

    @Override
    public List<DbMetaSchema> getInsert() {
        return schema.getStream().collect(Collectors.toList());
    }
}
