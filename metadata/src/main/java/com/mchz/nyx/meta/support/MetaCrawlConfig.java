package com.mchz.nyx.meta.support;

import com.mchz.nyx.meta.common.enums.MetaObjType;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/2
 */
public interface MetaCrawlConfig {
    DataSourceConfig getSource();

    List<SchemaTables> getSchemaTables();

    default String getLastSchema() {
        return null;
    }

    List<MetaObjType> getCrawl();

    default CharsetConvert getCharsetConvert(){
        return null;
    }

    default boolean order() {
        return false;
    }

    default boolean count() {
        return false;
    }

    default boolean foreign() {
        return false;
    }

    default long getSourceId() {
        return getSource().getSourceId();
    }

    default Integer getCrawlType() {
        return null;
    }
}
