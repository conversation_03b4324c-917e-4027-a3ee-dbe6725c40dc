package com.mchz.nyx.meta.instance.sink;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.lmax.disruptor.BlockingWaitStrategy;
import com.lmax.disruptor.EventHandler;
import com.lmax.disruptor.InsufficientCapacityException;
import com.lmax.disruptor.RingBuffer;
import com.lmax.disruptor.dsl.Disruptor;
import com.lmax.disruptor.dsl.ProducerType;
import com.mchz.nyx.common.exception.NyxException;
import com.mchz.nyx.common.util.WaitUtil;
import com.mchz.nyx.meta.common.constants.MetadataConst;
import com.mchz.nyx.meta.common.enums.EventType;
import com.mchz.nyx.meta.common.enums.MetaObjType;
import com.mchz.nyx.meta.entity.DbMetaColumn;
import com.mchz.nyx.meta.entity.DbMetaObject;
import com.mchz.nyx.meta.entity.DbMetaSchema;
import com.mchz.nyx.meta.entity.DbMetaTable;
import com.mchz.nyx.meta.exception.CrawlerException;
import com.mchz.nyx.meta.instance.store.NyxEventStore;
import com.mchz.nyx.meta.model.event.NyxEvent;
import com.mchz.nyx.meta.model.meta.*;
import lombok.Data;
import lombok.Setter;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;


public abstract class AbstractNyxEventSink implements NyxEventSink, EventHandler<NyxEvent> {
    private static final int BATCH_SIZE = 50;
    /**
     * 声明RingBuffer大小，必须为 2 的 n 次方
     */
    private static final int RING_BUFFER_SIZE = 1024;

    @Setter
    private long sourceId;
    @Setter
    private NyxEventStore eventStore;
    @Setter
    private boolean count;

    private boolean insertTag = true;
    private CompletableFuture<?> future;

    private Map<String, SchemaContext> cache;

    private SchemaContext now;

    private List<WrapTable> tables;

    private AtomicLong eventsPublishBlockingTime;

    private Disruptor<NyxEvent> disruptor;
    private RingBuffer<NyxEvent> ringBuffer;
    private NyxException throwable;

    @Override
    public void start() {
        if (null != ringBuffer) {
            throw new CrawlerException(this.getClass().getName() + " has start , don't repeat start");
        }
        eventsPublishBlockingTime = new AtomicLong();

        future = CompletableFuture.completedFuture(null);
        cache = new HashMap<>(1);

        tables = new ArrayList<>();

        ThreadFactory threadFactory = ThreadUtil.newNamedThreadFactory(String.format("meta-%s-", StrUtil.subSufByLength(String.valueOf(sourceId), 3)), null, true, (t, e) -> throwable = ExceptionUtil.wrap(e, NyxException.class));
        disruptor = new Disruptor<>(NyxEvent::new, RING_BUFFER_SIZE, threadFactory, ProducerType.SINGLE, new BlockingWaitStrategy());
        disruptor.handleEventsWith(this);
        // 启动 Disruptor
        ringBuffer = disruptor.start();
    }

    @Override
    public void shutdown() {
        disruptor.shutdown();
        future.join();
        if (null != throwable) {
            throw throwable;
        }
    }

    @Override
    public void sink(Consumer<NyxEvent> func) {
        publish(func);
    }

    @Override
    public void onEvent(NyxEvent event, long sequence, boolean endOfBatch) {
        if (!EventType.TABLE.equals(event.getType())) {
            batchDealTable();
        }
        switch (event.getType()) {
            case SCHEMA:
                dealSchema(event.getSchemas(), event.getStrList());
                break;
            case START:
                load(event.getSchema(), event.getValue(), event.getStrList());
                break;
            case TABLE_NAME:
                UpdateTable update = getUpdate(event.getTables().size());
                dealTableName(update, event.getTables());
                update(update);
                break;
            case TABLE:
                tables.add(event.getTable());
                if (tables.size() >= BATCH_SIZE) {
                    endOfBatch = true;
                }
                break;
            case FINISH:
                finish(event.getSchema());
                break;
            default:
        }
        event.clear();
        if (endOfBatch) {
            batchDealTable();
        }
    }

    private UpdateTable getUpdate(int size) {
        if (null == now) {
            throw new NyxException("not running,start it first");
        }
        return new UpdateTable(now.getSchema(), size);
    }

    private void update(UpdateTable update) {
        CompletableFuture<Boolean> temp;
        if (insertTag) {
            temp = eventStore.insertTableDetail(sourceId, update.getInsert());
        } else {
            List<Long> tIds = update.getUpdateTable().stream().map(DbMetaTable::getOid).collect(Collectors.toList());
            List<Long> cIds = update.getUpdateColumn().stream().map(DbMetaColumn::getOid).collect(Collectors.toList());
            Map<Long, String> commentMap = eventStore.getComment(tIds, cIds);
            if (CollUtil.isNotEmpty(commentMap)) {
                for (DbMetaObject obj : update.getUpdateObj()) {
                    if (null == obj.getDescription()) {
                        continue;
                    }
                    if (obj.getDescription().equals(commentMap.get(obj.getOid()))) {
                        obj.setDescription(null);
                    }
                }
            }
            temp = eventStore.updateTableDetail(sourceId, update);
        }
        if (null == temp) {
            return;
        }
        future = CompletableFuture.allOf(future, temp);
    }

    private void dealSchema(List<CatalogSchema> schemaList, List<String> delSchema) {
        List<DbMetaObject> list = eventStore.listSchemaObj(sourceId);
        Map<String, DbMetaObject> map = list.stream().collect(Collectors.toMap(DbMetaObject::getName, Function.identity()));
        WrapObj<DbMetaSchema> wrapObj = new WrapObj<>(null);
        List<DbMetaObject> updateObj = new ArrayList<>();
        List<DbMetaSchema> update = new ArrayList<>();
        List<Long> restoreObjIds = new ArrayList<>();
        boolean del = null == delSchema;
        if (CollUtil.isNotEmpty(schemaList)) {
            for (CatalogSchema catalogSchema : schemaList) {
                DbMetaObject old = del ? map.remove(catalogSchema.getName()) : map.get(catalogSchema.getName());
                DbMetaSchema schema = buildSchema(catalogSchema);
                String hashValue = getSchemaHashValue(schema);
                if (null == old) {
                    wrapObj.add(buildSchemaObject(catalogSchema.getName(), hashValue), schema);
                    continue;
                }
                if (old.getDeleted()) {
                    restoreObjIds.add(old.getOid());
                }
                if (null != hashValue && !hashValue.equals(old.getHashValue())) {
                    DbMetaObject obj = new DbMetaObject();
                    obj.setOid(old.getOid());
                    obj.setRevision(ObjUtil.defaultIfNull(old.getRevision(), 1) + 1);
                    obj.setHashValue(hashValue);
                    updateObj.add(obj);
                    schema.setOid(old.getOid());
                    update.add(schema);
                }
            }
        }
        List<Long> delIds;
        if (del) {
            delIds = map.values().stream().map(DbMetaObject::getOid).collect(Collectors.toList());
        } else {
            delIds = delSchema.stream().map(map::get).filter(v -> null != v && !v.getDeleted()).map(DbMetaObject::getOid).collect(Collectors.toList());
        }
        SchemaDiff res = new SchemaDiff();
        res.setSchema(wrapObj);
        res.setUpdateObj(updateObj);
        res.setUpdate(update);
        res.setRestoreObjIds(restoreObjIds);
        res.setDeleteObjIds(delIds);
        eventStore.insertSchema(sourceId, res);
    }

    private void dealTableName(UpdateTable update, List<DbMetaObject> tables) {
        WrapObj<DbMetaTable> insert = update.getInsert().getTableWrapObj();
        if (insertTag) {
            tables.forEach(obj -> insert.add(obj, buildTable(obj)));
            return;
        }
        for (DbMetaObject obj : tables) {
            DbMetaObject lTable = lastObj(obj);
            if (null == lTable) {
                insert.add(obj, buildTable(obj));
            } else if (lTable.getDeleted()) {
                update.getRestoreObjIds().add(lTable.getOid());
            }
        }
    }

    private void batchDealTable() {
        if (tables.isEmpty()) {
            return;
        }
        UpdateTable update = getUpdate(tables.size());
        dealTable(update, tables);
        update(update);
        tables.clear();
    }

    private void dealTable(UpdateTable update, List<WrapTable> tables) {
        InsertTable insert = update.getInsert();
        if (insertTag) {
            tables.forEach(v -> {
                updateHashValue(v);
                insert.addTable(v);
            });
            return;
        }
        for (WrapTable table : tables) {
            updateHashValue(table);
            DbMetaObject lTable = lastObj(table.getObj());
            if (null == lTable) {
                insert.addTable(table);
                continue;
            }
            if (Objects.equals(table.getObj().getHashValue(), lTable.getHashValue())) {
                if (lTable.getDeleted()) {
                    update.restoreTable(lTable);
                }
                if (count && null != table.getTable().getRows()) {
                    update.addUpdateTableOther(lTable, table.getTable());
                }
                continue;
            }
            diffTable(update, lTable, table, eventStore.listObjByPid(sourceId, lTable.getOid()));
        }
    }

    private void load(CatalogSchema catalogSchema, int value, List<String> tables) {
        if (null != now && now.getSchema().getName().equals(catalogSchema.getName())) {
            if (value != now.getType()) {
                throw new NyxException(String.format("load %s(%d) error,exist %d", catalogSchema.getName(), value, now.getType()));
            }
            return;
        }
        SchemaContext tmp = cache.get(catalogSchema.getName());
        if (null != tmp) {
            if (value != tmp.getType()) {
                throw new NyxException(String.format("load %s(%d) error,exist %d", catalogSchema.getName(), value, tmp.getType()));
            }
            now = tmp;
            return;
        }
        DbMetaObject schema = eventStore.getSchemaObj(sourceId, catalogSchema.getName());
        if (null == schema) {
            throw new NyxException(String.format("Can't start %s: not loaded", catalogSchema.getName()));
        }
        boolean t = MetaObjType.TABLE.contain(value);
        boolean v = MetaObjType.VIEW.contain(value);
        Map<String, DbMetaObject> tableMap;
        Map<String, DbMetaObject> viewMap;

        if (t && v) {
            Map<String, Map<String, DbMetaObject>> map = getStream(sourceId, schema.getOid(), 3, tables).collect(Collectors.groupingBy(DbMetaObject::getType, Collectors.toMap(DbMetaObject::getName, Function.identity(), this::merge)));
            tableMap = map.getOrDefault(MetaObjType.TABLE.name(), Collections.emptyMap());
            viewMap = map.getOrDefault(MetaObjType.VIEW.name(), Collections.emptyMap());
        } else if (t) {
            tableMap = getStream(sourceId, schema.getOid(), 1, tables).collect(Collectors.toMap(DbMetaObject::getName, Function.identity(), this::merge));
            viewMap = Collections.emptyMap();
        } else if (v) {
            tableMap = Collections.emptyMap();
            viewMap = getStream(sourceId, schema.getOid(), 2, tables).collect(Collectors.toMap(DbMetaObject::getName, Function.identity(), this::merge));
        } else {
            throw new NyxException(String.format("Can't start %s: unspecified type", catalogSchema.getName()));
        }
        insertTag = tableMap.isEmpty() && viewMap.isEmpty();
        now = new SchemaContext(schema, value, tableMap, viewMap);
        cache.put(schema.getName(), now);
    }

    private DbMetaObject merge(DbMetaObject o1, DbMetaObject o2) {
        return o1;
    }

    private Stream<DbMetaObject> getStream(long sourceId, long schemaId, int type, List<String> tables) {
        if (CollUtil.isEmpty(tables)) {
            List<DbMetaObject> list = eventStore.listTableOrViewObj(sourceId, schemaId, type, null);
            return list.stream();
        }
        List<DbMetaObject> list;
        if (tables.size() > MetadataConst.SCOPE_TABLE_SIZE) {
            list = eventStore.listTableOrViewObj(sourceId, schemaId, type, null);
            Set<String> set = new HashSet<>(tables);
            return list.stream().filter(o -> set.contains(o.getName()));
        } else {
            list = eventStore.listTableOrViewObj(sourceId, schemaId, type, tables);
            return list.stream();
        }
    }

    private void finish(CatalogSchema catalogSchema) {
        SchemaContext tmp = cache.remove(catalogSchema.getName());
        if (null == tmp) {
            throw new NyxException(String.format("Can't finish %s: unload", catalogSchema.getName()));
        }
        List<Long> ids = Stream.concat(tmp.getTableMap().values().stream(), tmp.getViewMap().values().stream()).filter(v -> !v.getDeleted()).map(DbMetaObject::getOid).collect(Collectors.toList());
        if (!ids.isEmpty()) {
            UpdateTable update = getUpdate(0);
            update.deleteTableIds(ids);
            future = CompletableFuture.allOf(future, eventStore.updateTableDetail(sourceId, update));
        }
        if (tmp == now) {
            now = null;
        }
    }

    protected abstract String getSchemaHashValue(DbMetaSchema schema);

    protected abstract void updateHashValue(WrapTable table);

    protected abstract void diffTable(UpdateTable update, DbMetaObject lTable, WrapTable table, List<DbMetaObject> allFromTable);

    private DbMetaObject lastObj(DbMetaObject obj) {
        if (MetaObjType.VIEW.name().equals(obj.getType())) {
            return now.getViewMap().remove(obj.getName());
        }
        return now.getTableMap().remove(obj.getName());
    }

    private DbMetaObject buildSchemaObject(String schema, String hashValue) {
        DbMetaObject obj = new DbMetaObject();
        obj.setSourceId(sourceId);
        obj.setName(schema);
        obj.setType(MetaObjType.SCHEMA.name());
        obj.setHashValue(hashValue);
        obj.setPid(0L);
        return obj;
    }

    private DbMetaSchema buildSchema(CatalogSchema schema) {
        DbMetaSchema meta = new DbMetaSchema();
        meta.setCatalog(schema.getCatalog());
        meta.setSchema(schema.getSchema());
        return meta;
    }

    private DbMetaTable buildTable(DbMetaObject table) {
        DbMetaTable meta = new DbMetaTable();
        meta.setType(table.getType());
        meta.setComment(table.getDescription());
        meta.setColumnNum(0);
        return meta;
    }

    private void publish(Consumer<NyxEvent> func) {
        boolean interupted;
        long blockingStart = 0L;
        int fullTimes = 0;
        do {
            try {
                long next = ringBuffer.tryNext();
                NyxEvent data = ringBuffer.get(next);
                func.accept(data);
                ringBuffer.publish(next);
                if (fullTimes > 0) {
                    eventsPublishBlockingTime.addAndGet(System.nanoTime() - blockingStart);
                }
                break;
            } catch (InsufficientCapacityException e) {
                if (fullTimes == 0) {
                    blockingStart = System.nanoTime();
                }
                WaitUtil.applyWait(++fullTimes);
                if (null != throwable) {
                    throw throwable;
                }
                interupted = Thread.interrupted();
                if ((fullTimes & 1023) == 0) {
                    long nextStart = System.nanoTime();
                    eventsPublishBlockingTime.addAndGet(nextStart - blockingStart);
                    blockingStart = nextStart;
                }
            }
        } while (!interupted);
    }

    @Data
    private static class SchemaContext {
        private final DbMetaObject schema;
        private final int type;
        private final Map<String, DbMetaObject> tableMap;
        private final Map<String, DbMetaObject> viewMap;
    }
}
