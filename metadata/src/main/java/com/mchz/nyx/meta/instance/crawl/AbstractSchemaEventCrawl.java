package com.mchz.nyx.meta.instance.crawl;

import com.mchz.nyx.meta.model.meta.CatalogSchema;
import com.mchz.nyx.meta.support.MetaCrawlConfig;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/2
 */
public abstract class AbstractSchemaEventCrawl extends AbstractMetaEventCrawl {
    private final String lastSchema;

    public AbstractSchemaEventCrawl(MetaCrawlConfig config) {
        super(config);
        this.lastSchema = config.getLastSchema();
    }

    protected void schema(List<CatalogSchema> schemas) {
        schema(schemas, lastSchema);
    }
}
