package com.mchz.nyx.meta.instance.crawl.mcdatasource;

import cn.hutool.core.util.StrUtil;
import com.mchz.datasource.cli.DatasourceMetabaseCli;
import com.mchz.mcdatasource.api.model.CustomMutableSchema;
import com.mchz.mcdatasource.core.DatasourceConstant;
import com.mchz.mcdatasource.model.constant.DbObjectType;
import com.mchz.nyx.meta.common.constants.MetadataConst;
import com.mchz.nyx.meta.common.enums.MetaObjType;
import com.mchz.nyx.meta.entity.DbMetaColumn;
import com.mchz.nyx.meta.entity.DbMetaObject;
import com.mchz.nyx.meta.entity.DbMetaTable;
import com.mchz.nyx.meta.exception.CrawlerException;
import com.mchz.nyx.meta.instance.crawl.NyxEventCrawl;
import com.mchz.nyx.meta.model.meta.WrapTable;
import com.mchz.nyx.meta.support.DataSourceConfig;
import com.mchz.nyx.meta.support.MetaCrawlConfig;
import com.mchz.nyx.meta.support.SchemaTables;
import org.apache.metamodel.schema.Column;
import org.apache.metamodel.schema.ColumnType;
import org.apache.metamodel.schema.Table;

import java.util.List;
import java.util.Properties;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
public class McDatasourceViewCrawl extends AbstractMcDatasourceCrawl implements NyxEventCrawl {

    private final DataSourceConfig source;
    private final List<SchemaTables> schemaTables;

    public McDatasourceViewCrawl(MetaCrawlConfig task) {
        super(task);
        this.source = task.getSource();
        this.schemaTables = task.getSchemaTables();
    }

    @Override
    public void execute() {
        Properties advanced = new Properties();
        advanced.putAll(source.getAdvanced());
        advanced.setProperty(DatasourceConstant.ONLY_NEED_COLUMNS, "true");
        advanced.setProperty(DatasourceConstant.SUPPORT_GET_TABLE_COMMENTS, "true");
        // VIEW start
        advanced.setProperty(DatasourceConstant.SUPPORT_BACK_VIEW_OBJECT, "true");
        advanced.setProperty("defaultRowPrefetch", "1000");
        // VIEW end
        for (SchemaTables st : schemaTables) {
            try {
                advanced.setProperty(DatasourceConstant.KEY_DB_LIST, st.getSchema().getCatalogSchema());
                if (null != st.getTables()) {
                    advanced.setProperty(DatasourceConstant.KEY_DB_AND_TABLE_LIST, String.join(StrUtil.COMMA, st.getTables()));
                }
                DatasourceMetabaseCli metadataCli = new DatasourceMetabaseCli(source.getType(), source.getHost(), source.getDb(), source.getPort(), source.getUser(), source.getPass(), true, advanced);
                List<CustomMutableSchema> customMutableSchemas = metadataCli.fetchColumns(DbObjectType.VIEW);
                CustomMutableSchema one = customMutableSchemas.get(0);
                if (0 != one.getTableCount()) {
                    stageStart(st.getSchema(), st.getTables(), MetaObjType.VIEW);
                    one.getTables().forEach(this::consumeView);
                    stageFinish(st.getSchema());
                }
            } catch (Exception e) {
                throw new CrawlerException(st.getSchema().getName(), e);
            }
        }
    }

    private void consumeView(Table view) {
        DbMetaObject oView = buildViewObject(view);
        DbMetaTable mView = buildView(view);
        WrapTable wView = new WrapTable(oView, mView);
        parseColumn(view.getColumns(), wView, this::buildColumnObject, this::buildColumn);
        consume(wView);
    }

    private DbMetaObject buildViewObject(Table view) {
        DbMetaObject obj = new DbMetaObject();
        obj.setName(view.getName());
        obj.setType(MetaObjType.VIEW.name());
        return obj;
    }

    private DbMetaObject buildColumnObject(Column column) {
        DbMetaObject obj = new DbMetaObject();
        obj.setName(column.getName());
        obj.setType(MetaObjType.COLUMN.name());
        return obj;
    }

    private DbMetaTable buildView(Table view) {
        DbMetaTable meta = new DbMetaTable();
        meta.setType(MetaObjType.VIEW.name());
        meta.setComment(view.getRemarks());
        return meta;
    }

    private DbMetaColumn buildColumn(Column column, int index) {
        DbMetaColumn meta = new DbMetaColumn();
        meta.setComment(column.getRemarks());
        meta.setDataType(StrUtil.nullToDefault(column.getNativeType(), MetadataConst.UNKNOWN_DATE_TYPE));
        //给出分组
        predictGroup(ColumnType.OTHER.equals(column.getType()) ? meta.getDataType() : column.getType().getName(), meta);
        meta.setPosition(index);
        return meta;
    }

}
