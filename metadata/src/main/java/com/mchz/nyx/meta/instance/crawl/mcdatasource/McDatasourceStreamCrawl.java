package com.mchz.nyx.meta.instance.crawl.mcdatasource;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import com.mchz.mcdatasource.core.DatasourceConstant;
import com.mchz.mcdatasource.core.callback.MetaCallBackInterface;
import com.mchz.mcdatasource.core.engine.dbobject.DbObjectEngine;
import com.mchz.mcdatasource.model.MetaColumn;
import com.mchz.mcdatasource.model.MetaDataStreamRequest;
import com.mchz.mcdatasource.model.MetaSchema;
import com.mchz.mcdatasource.model.MetaTable;
import com.mchz.mcdatasource.model.core.StreamDataType;
import com.mchz.nyx.meta.common.constants.MetadataConst;
import com.mchz.nyx.meta.common.enums.MetaObjType;
import com.mchz.nyx.meta.entity.DbMetaColumn;
import com.mchz.nyx.meta.entity.DbMetaObject;
import com.mchz.nyx.meta.entity.DbMetaTable;
import com.mchz.nyx.meta.exception.CrawlerException;
import com.mchz.nyx.meta.instance.crawl.NyxEventCrawl;
import com.mchz.nyx.meta.model.meta.WrapTable;
import com.mchz.nyx.meta.support.DataSourceConfig;
import com.mchz.nyx.meta.support.MetaCrawlConfig;
import com.mchz.nyx.meta.support.SchemaTables;
import lombok.extern.slf4j.Slf4j;
import org.pentaho.di.core.database.DatabaseMeta;

import java.util.List;
import java.util.Properties;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
public class McDatasourceStreamCrawl extends AbstractMcDatasourceCrawl implements NyxEventCrawl, MetaCallBackInterface<MetaSchema> {
    private final DataSourceConfig source;
    private final List<SchemaTables> schemaTables;
    private final List<StreamDataType> dataTypes;
    private final boolean foreign;

    public McDatasourceStreamCrawl(MetaCrawlConfig task, boolean count, boolean foreign) {
        super(task);
        this.source = task.getSource();
        this.schemaTables = task.getSchemaTables();
        this.foreign = foreign;
        List<StreamDataType> values = ListUtil.toList(StreamDataType.TABLE_COLUMN, StreamDataType.COLUMN_COMMENT, StreamDataType.INDEX, StreamDataType.PRIMARY_KEY, StreamDataType.UNIQUE_INDEX,
            StreamDataType.TABLE_COMMENTS, StreamDataType.VIRTUAL_COLUMN);
        if (foreign) {
            values.add(StreamDataType.FOREIGN_KEY);
        }
        if (count) {
            values.add(StreamDataType.TABLE_COUNT);
        }
        this.dataTypes = values;
    }

    @Override
    public void execute() {
        Properties advanced = new Properties();
        advanced.putAll(source.getAdvanced());
        advanced.setProperty(DatasourceConstant.ONLY_NEED_COLUMNS, "true");
        advanced.setProperty(DatasourceConstant.SUPPORT_GET_TABLE_COMMENTS, "true");
        DatabaseMeta databaseMeta = buildDatabaseMeta(source, advanced);
        MetaDataStreamRequest request = new MetaDataStreamRequest();
        request.setDataTypes(dataTypes);
        request.setSourceId(Long.hashCode(sourceId));
        for (SchemaTables st : schemaTables) {
            log.info("【元数据】正在处理 [{}]", st.getSchema().getName());
            request.setSchema(st.getSchema().getCatalogSchema());
            request.setTables(st.getTables());
            stageStart(st.getSchema(), st.getTables(), MetaObjType.TABLE);
            DbObjectEngine.getInstance().getMetaDataByStream(databaseMeta, request, this);
            stageFinish(st.getSchema());
        }
    }

    @Override
    public void process(MetaSchema metaSchema) {
        metaSchema.getTables().forEach(this::consumeTable);
    }

    @Override
    public void processError(Exception e) {
        throw ExceptionUtil.wrap(e, CrawlerException.class);
    }

    @Override
    public void processFinish() {

    }

    private void consumeTable(MetaTable table) {
        DbMetaObject oTable = buildTableObject(table);
        DbMetaTable mTable = buildTable(table);
        WrapTable wTable = new WrapTable(oTable, mTable);

        parseColumn(table.getColumns(), wTable);
        parseIndex(table.getIndexInfos(), wTable);
        if (foreign) {
            parseForeignKey(table.getForeignInfos(), wTable);
        }

        consume(wTable);
    }

    private void parseColumn(List<MetaColumn> columns, WrapTable table) {
        parseColumn(columns, table, this::buildColumnObject, this::buildColumn);
    }

    private DbMetaObject buildTableObject(MetaTable table) {
        DbMetaObject obj = new DbMetaObject();
        obj.setName(table.getName());
        obj.setType(MetaObjType.TABLE.name());
        return obj;
    }

    private DbMetaTable buildTable(MetaTable table) {
        DbMetaTable meta = new DbMetaTable();
        meta.setType(MetaObjType.TABLE.name());
        meta.setComment(table.getRemarks());
        meta.setPartitionColumn(table.getPartitionColumnName());
        meta.setSpace(table.getSpace());
        meta.setRows(convertRows(table.getCount()));
        return meta;
    }

    private DbMetaObject buildColumnObject(MetaColumn column) {
        DbMetaObject obj = new DbMetaObject();
        obj.setName(column.getName());
        obj.setType(MetaObjType.COLUMN.name());
        return obj;
    }

    private DbMetaColumn buildColumn(MetaColumn column, int index) {
        DbMetaColumn meta = new DbMetaColumn();
        meta.setComment(column.getRemarks());
        if (null != column.getColumnLength()) {
            meta.setLength(convertLength(column.getColumnLength()));
        }
        try {
            meta.setScale(null == column.getScale() ? null : Integer.valueOf(column.getScale()));
        } catch (NumberFormatException ignore) {
            log.debug("【统一数据源】scale :{}", column.getScale());
        }
        try {
            meta.setPrecision(null == meta.getLength() ? null : Math.toIntExact(meta.getLength()));
        } catch (ArithmeticException ignore) {
            log.debug("【统一数据源】precision :{}", meta.getLength());
        }
        meta.setNullable(column.getNullable());
        meta.setIsPk(column.isPrimaryKeyEnable());
        meta.setIsUnique(meta.getIsPk() ? Boolean.TRUE : column.isUniqueKeyEnable());
        meta.setIsIndex(Boolean.TRUE.equals(meta.getIsUnique()));
        if (foreign) {
            meta.setIsFk(column.isForeignKeyEnable());
        }
        meta.setPosition(index);
        meta.setDataType(StrUtil.nullToDefault(column.getDataType(), MetadataConst.UNKNOWN_DATE_TYPE));
        //给出分组
        predictGroup(meta.getDataType(), meta);
        return meta;
    }
}
