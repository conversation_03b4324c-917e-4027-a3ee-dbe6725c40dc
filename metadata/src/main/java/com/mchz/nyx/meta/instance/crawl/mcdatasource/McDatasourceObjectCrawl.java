package com.mchz.nyx.meta.instance.crawl.mcdatasource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.mchz.mcdatasource.core.DataType;
import com.mchz.mcdatasource.core.engine.dbobject.DbObjectEngine;
import com.mchz.mcdatasource.model.dbmeta.*;
import com.mchz.mcdatasource.model.request.ObjectSqlRequest;
import com.mchz.nyx.common.exception.NyxException;
import com.mchz.nyx.common.util.CommUtil;
import com.mchz.nyx.meta.common.constants.MetadataConst;
import com.mchz.nyx.meta.common.enums.MetaObjType;
import com.mchz.nyx.meta.entity.DbMetaColumn;
import com.mchz.nyx.meta.entity.DbMetaObject;
import com.mchz.nyx.meta.entity.DbMetaTable;
import com.mchz.nyx.meta.model.meta.CatalogSchema;
import com.mchz.nyx.meta.model.meta.WrapTable;
import com.mchz.nyx.meta.support.DataSourceConfig;
import com.mchz.nyx.meta.support.MetaCrawlConfig;
import com.mchz.nyx.meta.support.SchemaTables;
import lombok.extern.slf4j.Slf4j;
import org.pentaho.di.core.database.DatabaseMeta;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
public class McDatasourceObjectCrawl extends AbstractMcDatasourceCrawl {
    private final DataSourceConfig source;
    private final List<DataType> dataTypes;
    private final List<SchemaTables> schemaTables;

    public McDatasourceObjectCrawl(MetaCrawlConfig task, boolean view) {
        super(task);
        this.source = task.getSource();
        this.dataTypes = view ? MetadataConst.CRAWLER_WITH_VIEW_TYPES : MetadataConst.CRAWLER_TABLE_TYPES;
        this.schemaTables = task.getSchemaTables();
    }

    @Override
    public void execute() {
        DatabaseMeta databaseMeta = buildDatabaseMeta(source);
        ObjectSqlRequest request = new ObjectSqlRequest();
        request.setDataTypes(dataTypes);
        for (SchemaTables st : schemaTables) {
            CatalogSchema schema = st.getSchema();
            log.info("【元数据】正在处理 [{}]", st.getSchema().getName());
            request.setSchemaName(schema.getName());
            request.setTables(st.getTables());
            try {
                ColumnAndCreateTableSql res = DbObjectEngine.getInstance().getObjectAndSql(databaseMeta, 0, request);
                if (CollUtil.isEmpty(res.getTableObjectSqlMetaDataList())) {
                    continue;
                }
                stageStart(schema, st.getTables(), MetaObjType.TABLE, MetaObjType.VIEW);
                processObjectEngine(res);
                stageFinish(schema);
            } catch (Exception e) {
                throw new NyxException(e);
            }
        }
    }

    private void processObjectEngine(ColumnAndCreateTableSql param) {
        Map<String, List<ColumnMataData>> columnMap = getNameListMap(param.getColumnMataDataList(), ColumnMataData::getTableName);
        param.setColumnMataDataList(null);
        Map<String, List<ColumnMataData>> viewColumnMap = getNameListMap(param.getViewColumnMataDataList(), ColumnMataData::getTableName);
        param.setViewColumnMataDataList(null);
        Map<String, List<IndexMetaData>> indexInfoMap = getNameListMap(param.getIndexAll(), IndexMetaData::getTableName);
        Map<String, List<ForeignKeyInfo>> fkInfoMap = getNameListMap(param.getForeignKeyInfoList(), ForeignKeyInfo::getForeignTableName);
        List<TableObjectSqlMetaData> list = param.getTableObjectSqlMetaDataList();

        Map<String, List<TableObjectSqlMetaData>> indexMap = CommUtil.groupByPid(list, TableObjectSqlMetaData::getTableName, v -> {
            if (MetaObjType.TABLE.name().equals(v.getObjectType()) || MetaObjType.VIEW.name().equals(v.getObjectType())) {
                v.setSql(null);
                return false;
            }
            return true;
        });

        for (TableObjectSqlMetaData data : list) {
            if (MetaObjType.TABLE.name().equals(data.getObjectType())) {
                consume(parseTable(data, columnMap.get(data.getName()), indexMap.get(data.getName()), indexInfoMap.get(data.getName()), fkInfoMap.get(data.getName())));
            } else if (MetaObjType.VIEW.name().equals(data.getObjectType())) {
                consume(parseView(data, viewColumnMap.get(data.getName())));
            }
        }
    }

    private WrapTable parseTable(TableObjectSqlMetaData table, List<ColumnMataData> columns, List<TableObjectSqlMetaData> indexList, List<IndexMetaData> indexInfos, List<ForeignKeyInfo> fkInfos) {
        DbMetaObject oTable = buildTableObject(table);
        DbMetaTable mTable = buildTable(table);
        WrapTable wTable = new WrapTable(oTable, mTable);

        parseColumn(columns, wTable);

        if (CollUtil.isNotEmpty(indexList)) {
            List<StreamIndexMetaData> idx = new ArrayList<>(indexList.size());
            List<StreamForeignKeyInfo> fk = new ArrayList<>();
            Map<String, List<IndexMetaData>> idxMap = getNameListMap(indexInfos, IndexMetaData::getIndexName);
            Map<String, List<ForeignKeyInfo>> fkMap = getNameListMap(fkInfos, ForeignKeyInfo::getConstraintName);
            for (TableObjectSqlMetaData data : indexList) {
                if (MetaObjType.FOREIGN_KEY.name().equals(data.getObjectType())) {
                    List<ForeignKeyInfo> list = fkMap.get(data.getName());
                    StreamForeignKeyInfo info = new StreamForeignKeyInfo();
                    info.setName(data.getName());
                    info.setSql(data.getSql());
                    info.setForeignKeyInfoList(list);
                    fk.add(info);
                } else {
                    List<IndexMetaData> list = idxMap.get(data.getName());
                    StreamIndexMetaData info = new StreamIndexMetaData();
                    info.setName(data.getName());
                    info.setType(data.getObjectType());
                    info.setSql(data.getSql());
                    info.setIndexMetaDatas(list);
                    idx.add(info);
                }
            }
            parseIndex(idx, wTable);
            parseForeignKey(fk, wTable);
        }
        return wTable;
    }

    private WrapTable parseView(TableObjectSqlMetaData view, List<ColumnMataData> columns) {
        DbMetaObject oView = buildTableObject(view);
        oView.setType(MetaObjType.VIEW.name());
        DbMetaTable mView = buildView(view);
        WrapTable wView = new WrapTable(oView, mView);

        parseColumn(columns, wView);
        return wView;
    }

    private void parseColumn(List<ColumnMataData> columns, WrapTable table) {
        parseColumn(columns, table, this::buildColumnObject, this::buildColumn);
    }

    private DbMetaObject buildTableObject(TableObjectSqlMetaData table) {
        DbMetaObject obj = new DbMetaObject();
        obj.setName(table.getName());
        obj.setType(MetaObjType.TABLE.name());
        return obj;
    }

    private DbMetaObject buildColumnObject(ColumnMataData column) {
        DbMetaObject obj = new DbMetaObject();
        obj.setName(column.getColumnName());
        obj.setType(MetaObjType.COLUMN.name());
        return obj;
    }

    private DbMetaTable buildTable(TableObjectSqlMetaData table) {
        DbMetaTable meta = new DbMetaTable();
        meta.setType(MetaObjType.TABLE.name());
        meta.setComment(table.getTableComments());
        meta.setPartitionColumn(table.getPartitionColumnName());
        meta.setSpace(table.getTableSpace());
        meta.setRows(convertRows(table.getCount()));
        return meta;
    }

    private DbMetaTable buildView(TableObjectSqlMetaData view) {
        DbMetaTable meta = new DbMetaTable();
        meta.setType(MetaObjType.VIEW.name());
        meta.setComment(view.getTableComments());
        return meta;
    }

    private DbMetaColumn buildColumn(ColumnMataData column, int index) {
        DbMetaColumn meta = new DbMetaColumn();
        meta.setComment(column.getComments());
        meta.setDataType(StrUtil.nullToDefault(column.getDataType(), MetadataConst.UNKNOWN_DATE_TYPE));

        if (null != column.getColumnLength()) {
            meta.setLength(convertLength(column.getColumnLength()));
        }
        try {
            meta.setPrecision(null == meta.getLength() ? null : Math.toIntExact(meta.getLength()));
        } catch (ArithmeticException ignore) {
            log.debug("【统一数据源】precision :{}", meta.getLength());
        }
        try {
            meta.setScale(null == column.getScale() ? null : Integer.valueOf(column.getScale()));
        } catch (NumberFormatException ignore) {
            log.debug("【统一数据源】scale :{}", column.getScale());
        }
        meta.setNullable(column.isNull());
        meta.setPosition(index);
        meta.setIsPk(column.isPrimaryKeyEnable());
        meta.setIsUnique(meta.getIsPk() ? Boolean.TRUE : column.isUniqueKeyEnable());
        meta.setIsIndex(Boolean.TRUE.equals(meta.getIsUnique()));
        meta.setIsFk(column.isForeignKeyEnable());
        //给出分组
        predictGroup(meta.getDataType(), meta);
        return meta;
    }

    private <T> Map<String, List<T>> getNameListMap(List<T> list, Function<T, String> nameFun) {
        return CommUtil.groupByPid(list, nameFun);
    }
}
