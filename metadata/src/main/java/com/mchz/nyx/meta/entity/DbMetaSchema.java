package com.mchz.nyx.meta.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.mchz.nyx.meta.support.ObjectInfo;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2022/1/10 14:06
 */
@Data
@TableName("dbmeta_schema")
public class DbMetaSchema implements ObjectInfo {
    /**
     * 主键
     */
    @TableId(type = IdType.INPUT)
    private Long oid;

    /**
     * 数据源ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long sourceId;

    private String catalog;

    private String schema;

    /**
     * 字符集、编码
     */
    private String charset;

    /**
     * 排序规则
     */
    private String collation;

    @Override
    public void setParent(DbMetaObject obj) {
    }
}
