package com.mchz.nyx.meta.model.meta;

import com.mchz.nyx.meta.entity.DbMetaObject;
import com.mchz.nyx.meta.support.ObjectInfo;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Stream;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
@Getter
public class WrapObj<T extends ObjectInfo> {
    private final DbMetaObject parent;
    private final List<Entry<T>> list;

    public WrapObj(DbMetaObject parent, int size) {
        this.parent = parent;
        this.list = new ArrayList<>(size);
    }

    public WrapObj(DbMetaObject parent) {
        this.parent = parent;
        this.list = new ArrayList<>();
    }

    public Entry<T> add(DbMetaObject obj, T info) {
        Entry<T> entry = new Entry<>(obj, info);
        list.add(entry);
        return entry;
    }

    public void add(Entry<T> entry) {
        list.add(entry);
    }

    public void addAll(WrapObj<T> wrapObj) {
        list.addAll(wrapObj.getList());
    }

    public int size(){
        return list.size();
    }

    public boolean isEmpty(){
        return list.isEmpty();
    }

    public Stream<T> getStream() {
        if (list.isEmpty()) {
            return Stream.empty();
        }
        if (null == parent) {
            return list.stream().map(v -> {
                T value = v.getValue();
                value.setOid(v.getObj().getOid());
                value.setSourceId(v.getObj().getSourceId());
                return value;
            });
        }
        return list.stream().map(v -> {
            T value = v.getValue();
            value.setOid(v.getObj().getOid());
            value.setSourceId(v.getObj().getSourceId());
            value.setParent(parent);
            return value;
        });
    }

    public Stream<DbMetaObject> getObjStream() {
        if (null == parent) {
            return list.stream().map(Entry::getObj);
        }
        return list.stream().map(v -> {
            v.getObj().setPid(parent.getOid());
            return v.getObj();
        });
    }
}
