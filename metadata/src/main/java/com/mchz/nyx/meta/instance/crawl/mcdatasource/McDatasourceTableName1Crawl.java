package com.mchz.nyx.meta.instance.crawl.mcdatasource;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.mchz.datasource.cli.DatasourceMetabaseCli;
import com.mchz.mcdatasource.core.DatasourceConstant;
import com.mchz.mcdatasource.model.SchemaName;
import com.mchz.mcdatasource.model.constant.DbObjectType;
import com.mchz.nyx.meta.common.enums.MetaObjType;
import com.mchz.nyx.meta.exception.CrawlerException;
import com.mchz.nyx.meta.instance.crawl.AbstractMetaEventCrawl;
import com.mchz.nyx.meta.model.meta.CatalogSchema;
import com.mchz.nyx.meta.support.DataSourceConfig;
import com.mchz.nyx.meta.support.MetaCrawlConfig;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Properties;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
public class McDatasourceTableName1Crawl extends AbstractMetaEventCrawl {
    private final DataSourceConfig source;
    private final boolean view;
    private final String dbList;

    public McDatasourceTableName1Crawl(MetaCrawlConfig task, boolean view) {
        super(task);
        this.source = task.getSource();
        this.view = view;
        if (CollUtil.isNotEmpty(task.getSchemaTables())) {
            this.dbList = task.getSchemaTables().stream().map(v -> v.getSchema().getCatalogSchema()).collect(Collectors.joining(StrUtil.COMMA));
        } else {
            this.dbList = null;
        }
    }

    @Override
    protected void execute() {
        Properties advanced = new Properties();
        advanced.putAll(source.getAdvanced());
        if (null != dbList) {
            advanced.setProperty(DatasourceConstant.KEY_DB_LIST, dbList);
        }
        try {
            DatasourceMetabaseCli metadataCli = new DatasourceMetabaseCli(source.getType(), source.getHost(), source.getDb(), source.getPort(), source.getUser(), source.getPass(), true, advanced);
            metadataCli.getAllTablesAndViews(DbObjectType.TABLE).forEach((schemaName, tableNames) -> crawlTableName(schemaName, tableNames, MetaObjType.TABLE));
            if (view) {
                metadataCli.getAllTablesAndViews(DbObjectType.VIEW).forEach((schemaName, tableNames) -> crawlTableName(schemaName, tableNames, MetaObjType.VIEW));
            }
        } catch (Exception e) {
            throw new CrawlerException("元数据采集表格信息异常", e);
        }
    }

    private void crawlTableName(SchemaName schemaName, List<String> tableNames, MetaObjType type) {
        CatalogSchema catalogSchema = CatalogSchema.of(schemaName.getCatalog(), schemaName.getSchema());
        crawlTableName(catalogSchema, tableNames, type);
    }
}
