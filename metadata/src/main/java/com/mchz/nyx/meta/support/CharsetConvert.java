package com.mchz.nyx.meta.support;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import com.mchz.nyx.common.exception.NyxException;
import lombok.AllArgsConstructor;

import java.io.UnsupportedEncodingException;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/14
 */
@AllArgsConstructor
public class CharsetConvert {
    private final String client;
    private final String server;

    public String encode(String s) {
        if (StrUtil.isEmpty(s)) {
            return s;
        }
        try {
            return new String(s.getBytes(this.client), this.server);
        } catch (UnsupportedEncodingException e) {
            throw new NyxException(ExceptionUtil.getMessage(e), e);
        }

    }

    public String decode(String s) {
        if (StrUtil.isEmpty(s)) {
            return s;
        }
        try {
            return new String(s.getBytes(this.server), this.client);
        } catch (UnsupportedEncodingException e) {
            throw new NyxException(ExceptionUtil.getMessage(e), e);
        }
    }

    public Object decode(Object s) {
        if (s instanceof String) {
            return decode((String) s);
        }
        return s;
    }
}
