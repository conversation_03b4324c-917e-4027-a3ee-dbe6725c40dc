package com.mchz.nyx.meta.support;

import com.mchz.nyx.meta.model.meta.CatalogSchema;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class SchemaTables {
    private final CatalogSchema schema;
    private final List<String> tables;
    private final List<String> excludeTables;

    public static SchemaTables of(CatalogSchema schema) {
        return new SchemaTables(schema, null, null);
    }

    public static SchemaTables of(CatalogSchema schema, List<String> table) {
        return new SchemaTables(schema, table, null);
    }

    public static SchemaTables ofExclude(CatalogSchema schema, List<String> table) {
        return new SchemaTables(schema, null, table);
    }
}
