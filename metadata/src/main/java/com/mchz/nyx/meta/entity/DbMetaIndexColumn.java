package com.mchz.nyx.meta.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mchz.nyx.meta.support.ObjectInfo;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2022/1/10 14:06
 */
@Data
@TableName("dbmeta_index_column")
public class DbMetaIndexColumn implements ObjectInfo {
    /**
     * 自增id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 数据源ID
     */
    private Long sourceId;

    /**
     * 索引主键
     */
    private Long indexId;

    /**
     * 列主键
     */
    private Long columnId;

    /**
     * 位置
     */
    private Integer position;

    @Override
    public void setOid(Long id) {
        setColumnId(id);
    }

    @Override
    public void setParent(DbMetaObject obj) {
        setIndexId(obj.getOid());
    }
}
