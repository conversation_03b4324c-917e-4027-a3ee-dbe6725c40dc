package com.mchz.nyx.meta.model.meta;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class SchemaTableNames {
    private final CatalogSchema schema;
    private final boolean view;
    private final List<String> tables;

    public static SchemaTableNames of(CatalogSchema schema, List<String> table) {
        return new SchemaTableNames(schema, false, table);
    }

    public static SchemaTableNames ofView(CatalogSchema schema, List<String> table) {
        return new SchemaTableNames(schema, true, table);
    }
}
