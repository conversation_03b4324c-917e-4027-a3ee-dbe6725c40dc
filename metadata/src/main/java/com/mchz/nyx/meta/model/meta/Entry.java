package com.mchz.nyx.meta.model.meta;

import com.mchz.nyx.meta.entity.DbMetaObject;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor(access = AccessLevel.PACKAGE)
public class Entry<T> {
    private final DbMetaObject obj;
    private final T value;

    @Override
    public String toString() {
        return obj.getName() + " " + value;
    }
}
