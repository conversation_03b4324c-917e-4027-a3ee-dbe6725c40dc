package com.mchz.nyx.meta.model.meta;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class SchemaTable {
    private final CatalogSchema schema;
    private final WrapTable table;

    public static SchemaTable of(CatalogSchema schema, WrapTable table) {
        return new SchemaTable(schema,  table);
    }
}
