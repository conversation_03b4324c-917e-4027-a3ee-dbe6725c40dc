package com.mchz.nyx.meta.instance.crawl.mcdatasource;

import com.mchz.datasource.cli.DatasourceMetabaseCli;
import com.mchz.mcdatasource.core.DatasourceConstant;
import com.mchz.nyx.meta.common.enums.MetaObjType;
import com.mchz.nyx.meta.exception.CrawlerException;
import com.mchz.nyx.meta.instance.crawl.AbstractMetaEventCrawl;
import com.mchz.nyx.meta.model.meta.CatalogSchema;
import com.mchz.nyx.meta.support.DataSourceConfig;
import com.mchz.nyx.meta.support.MetaCrawlConfig;
import com.mchz.nyx.meta.support.SchemaTables;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Properties;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
public class McDatasourceTableName2Crawl extends AbstractMetaEventCrawl {
    private final DataSourceConfig source;
    private final boolean view;
    private final List<SchemaTables> schemaTables;

    public McDatasourceTableName2Crawl(MetaCrawlConfig task, boolean view) {
        super(task);
        this.source = task.getSource();
        this.view = view;
        this.schemaTables = task.getSchemaTables();
    }

    @Override
    protected void execute() {
        Properties advanced = new Properties();
        advanced.putAll(source.getAdvanced());
        try {
            advanced.setProperty(DatasourceConstant.TABLE_NOT_JOIN_SCHEMA, "true");
            if (view) {
                advanced.setProperty(DatasourceConstant.SUPPORT_BACK_VIEW_OBJECT, "true");
            }
            for (SchemaTables schema : schemaTables) {
                CatalogSchema catalogSchema = schema.getSchema();
                advanced.setProperty(DatasourceConstant.KEY_DB_LIST, catalogSchema.getCatalogSchema());
                DatasourceMetabaseCli metadataCli = new DatasourceMetabaseCli(source.getType(), source.getHost(), source.getDb(), source.getPort(), source.getUser(), source.getPass(), true, advanced);
                List<String> list = metadataCli.fetchTables();
                crawlTableName(catalogSchema, list, MetaObjType.TABLE);
                if (view) {
                    list = metadataCli.fetchViews();
                    crawlTableName(catalogSchema, list, MetaObjType.VIEW);
                }
            }
        } catch (Exception e) {
            throw new CrawlerException("元数据采集表格信息异常", e);
        }
    }
}
