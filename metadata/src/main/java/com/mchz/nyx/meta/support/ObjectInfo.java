package com.mchz.nyx.meta.support;

import com.mchz.nyx.meta.entity.DbMetaObject;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2022/1/12 10:23
 */
public interface ObjectInfo {

    /**
     * oid
     *
     * @param id 对象主键
     */
    void setOid(Long id);

    /**
     * sourceId
     *
     * @param id 数据源id
     */
    void setSourceId(Long id);

    /**
     * pid
     *
     * @param obj 父类主键
     */
    void setParent(DbMetaObject obj);
}
