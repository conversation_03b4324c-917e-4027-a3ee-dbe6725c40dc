package com.mchz.nyx.meta.instance.crawl;

import java.util.Arrays;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
public class GroupEventCrawl implements NyxEventCrawl {
    private final List<? extends NyxEventCrawl> crawls;

    public GroupEventCrawl(List<? extends NyxEventCrawl> crawls) {
        this.crawls = crawls;
    }

    public GroupEventCrawl(NyxEventCrawl... crawls) {
        this.crawls = Arrays.asList(crawls);
    }

    @Override
    public boolean crawl() {
        boolean flag = false;
        for (NyxEventCrawl crawl : crawls) {
            flag |= crawl.crawl();
        }
        return flag;
    }
}
