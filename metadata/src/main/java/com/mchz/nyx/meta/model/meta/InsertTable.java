package com.mchz.nyx.meta.model.meta;

import cn.hutool.core.collection.CollUtil;
import com.mchz.nyx.meta.entity.*;
import com.mchz.nyx.meta.support.TableInsertDetail;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
@Getter
public class InsertTable implements TableInsertDetail {
    private final WrapObj<DbMetaTable> tableWrapObj;
    private final List<WrapObj<DbMetaColumn>> columnList;
    private final List<WrapObj<DbMetaIndex>> indexList;
    private final List<WrapObj<DbMetaIndexColumn>> icList;
    private final List<WrapObj<DbMetaForeignKey>> fkList;
    private final List<WrapObj<DbMetaForeignKeyInfo>> fkInfoList;

    public InsertTable(DbMetaObject schema, int tableSize) {
        this.tableWrapObj = new WrapObj<>(schema);
        this.columnList = new ArrayList<>(tableSize);
        this.indexList = new ArrayList<>(tableSize);
        this.icList = new ArrayList<>();
        this.fkList = new ArrayList<>(tableSize);
        this.fkInfoList = new ArrayList<>();
    }

    public void addTable(WrapTable table) {
        tableWrapObj.add(table.getObj(), table.getTable());

        if (null != table.getColumn()) {
            columnList.add(table.getColumn());
        }
        if (null != table.getIndex()) {
            indexList.add(table.getIndex());
        }
        if (null != table.getFk()) {
            fkList.add(table.getFk());
        }
        if (CollUtil.isNotEmpty(table.getIndexColumn())) {
            icList.addAll(table.getIndexColumn());
        }
        if (CollUtil.isNotEmpty(table.getFkInfo())) {
            fkInfoList.addAll(table.getFkInfo());
        }
    }

    public boolean isEmpty() {
        return tableWrapObj.getList().isEmpty()
            && columnList.isEmpty()
            && indexList.isEmpty()
            && fkList.isEmpty();
    }

    @Override
    public List<DbMetaObject> getTableObjects() {
        return tableWrapObj.getObjStream().collect(Collectors.toList());
    }

    @Override
    public List<DbMetaObject> getObjects() {
        return Stream.concat(Stream.concat(columnList.stream().flatMap(WrapObj::getObjStream),
                indexList.stream().flatMap(WrapObj::getObjStream)),
            fkList.stream().flatMap(WrapObj::getObjStream)).collect(Collectors.toList());
    }

    @Override
    public List<DbMetaTable> getTables() {
        return tableWrapObj.getStream().collect(Collectors.toList());
    }

    @Override
    public List<DbMetaColumn> getColumns() {
        return columnList.stream().flatMap(WrapObj::getStream).collect(Collectors.toList());
    }

    @Override
    public List<DbMetaIndex> getIndex() {
        return indexList.stream().flatMap(WrapObj::getStream).collect(Collectors.toList());
    }

    @Override
    public List<DbMetaIndexColumn> getIndexColumns() {
        return icList.stream().flatMap(WrapObj::getStream).collect(Collectors.toList());
    }

    @Override
    public List<DbMetaForeignKey> getForeignKeys() {
        return fkList.stream().flatMap(WrapObj::getStream).collect(Collectors.toList());
    }

    @Override
    public List<DbMetaForeignKeyInfo> getForeignKeyInfos() {
        return fkInfoList.stream().flatMap(WrapObj::getStream).collect(Collectors.toList());
    }
}
