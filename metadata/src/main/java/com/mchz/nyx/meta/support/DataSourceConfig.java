package com.mchz.nyx.meta.support;

import java.io.Serializable;
import java.util.Properties;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/10/27 14:58
 */
public interface DataSourceConfig extends Serializable {
    Long getSourceId();

    String getType();

    String getHost();

    String getPort();

    String getDb();

    String getUser();

    String getPass();

    Properties getAdvanced();
}
