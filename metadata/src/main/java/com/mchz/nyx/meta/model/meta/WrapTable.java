package com.mchz.nyx.meta.model.meta;

import com.mchz.nyx.meta.common.enums.MetaObjType;
import com.mchz.nyx.meta.entity.*;
import lombok.Data;

import java.util.List;
import java.util.Map;


/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
@Data
public class WrapTable {
    private final DbMetaObject obj;
    private final DbMetaTable table;
    private WrapObj<DbMetaColumn> column;
    private Map<String, Entry<DbMetaColumn>> columnMap;
    private WrapObj<DbMetaIndex> index;
    private List<WrapObj<DbMetaIndexColumn>> indexColumn;
    private WrapObj<DbMetaForeignKey> fk;
    private List<WrapObj<DbMetaForeignKeyInfo>> fkInfo;

    public WrapTable(DbMetaObject obj, DbMetaTable table) {
        this.obj = obj;
        this.table = table;
    }

    public boolean isView() {
        return MetaObjType.VIEW.name().equals(obj.getType());
    }
}
