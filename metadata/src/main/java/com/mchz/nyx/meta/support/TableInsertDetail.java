package com.mchz.nyx.meta.support;


import com.mchz.nyx.meta.entity.*;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
public interface TableInsertDetail {
    /**
     * 表格
     *
     * @return list
     */
    List<DbMetaObject> getTableObjects();

    /**
     * 非表格
     *
     * @return list
     */
    List<DbMetaObject> getObjects();

    /**
     * 表格详情
     *
     * @return list
     */
    List<DbMetaTable> getTables();

    /**
     * 字段详情
     *
     * @return list
     */
    List<DbMetaColumn> getColumns();

    /**
     * 索引详情
     *
     * @return list
     */
    List<DbMetaIndex> getIndex();

    /**
     * 索引关联详情
     *
     * @return list
     */
    List<DbMetaIndexColumn> getIndexColumns();

    /**
     * 外键详情
     *
     * @return list
     */
    List<DbMetaForeignKey> getForeignKeys();

    /**
     * 外键关联详情
     *
     * @return list
     */
    List<DbMetaForeignKeyInfo> getForeignKeyInfos();
}
