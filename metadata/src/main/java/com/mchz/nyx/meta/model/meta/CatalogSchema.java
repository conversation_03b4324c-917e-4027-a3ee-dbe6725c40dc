package com.mchz.nyx.meta.model.meta;

import cn.hutool.core.util.StrUtil;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class CatalogSchema {
    private static final String DOT = StrUtil.DOT;

    private final String name;
    private final String catalogSchema;
    private final String catalog;
    private final String schema;

    public static CatalogSchema of(String catalogSchema) {
        return new CatalogSchema(catalogSchema, catalogSchema, null, null);
    }

    public static CatalogSchema of(String catalogSchema, String catalog, String schema) {
        return new CatalogSchema(catalogSchema, catalogSchema, catalog, schema);
    }

    public static CatalogSchema of(String catalog, String schema) {
        if (null != catalog && null != schema) {
            return new CatalogSchema(wrap(catalog) + DOT + wrap(schema), catalog + DOT + schema, catalog, schema);
        } else if (null != schema) {
            return ofSchema(schema);
        } else if (null != catalog) {
            return ofCatalog(catalog);
        }
        return of(null);
    }

    public static CatalogSchema ofCatalog(String catalog) {
        return new CatalogSchema(catalog, catalog, catalog, null);
    }

    public static CatalogSchema ofSchema(String schema) {
        return new CatalogSchema(schema, schema, null, schema);
    }

    private static String wrap(String value) {
        if (!value.contains(DOT)) {
            return value;
        }
        final char s = '\'';
        StringBuilder sb = new StringBuilder(value.length() + 2);
        sb.append(s);
        for (int i = 0; i < value.length(); i++) {
            char c = value.charAt(i);
            sb.append(c);
            if (s == c) {
                sb.append(c);
            }
        }
        sb.append(s);
        return sb.toString();
    }
}
