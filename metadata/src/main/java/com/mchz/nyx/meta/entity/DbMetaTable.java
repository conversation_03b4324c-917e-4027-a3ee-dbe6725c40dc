package com.mchz.nyx.meta.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.mchz.nyx.meta.support.ObjectInfo;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2022/1/10 14:06
 */
@Data
@TableName("dbmeta_table")
public class DbMetaTable implements ObjectInfo {
    /**
     * 主键
     */
    @TableId(type = IdType.INPUT)
    private Long oid;

    /**
     * 数据源ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long sourceId;

    /**
     * schema
     */
    @TableField(fill = FieldFill.INSERT)
    private Long schemaId;

    /**
     * 类型
     */
    @TableField(fill = FieldFill.INSERT)
    private String type;

    /**
     * 列数量
     */
    private Integer columnNum;

    /**
     * 分区字段
     */
    private String partitionColumn;

    /**
     * 表空间
     */
    private String space;

    /**
     * 行数
     */
    private Long rows;

    /**
     * 注释
     */
    private String comment;

    @Override
    public void setParent(DbMetaObject obj) {
        setSchemaId(obj.getOid());
    }
}
