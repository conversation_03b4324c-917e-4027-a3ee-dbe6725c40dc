package com.mchz.nyx.meta.instance.sink;

import cn.hutool.core.collection.CollUtil;
import com.mchz.nyx.meta.common.enums.MetaObjType;
import com.mchz.nyx.meta.entity.DbMetaObject;
import com.mchz.nyx.meta.model.event.NyxEvent;
import com.mchz.nyx.meta.model.meta.CatalogSchema;
import com.mchz.nyx.meta.model.meta.WrapTable;

import java.util.List;
import java.util.function.Consumer;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
public interface NyxEventSink {

    void start();

    void sink(Consumer<NyxEvent> func);

    void shutdown();

    default void schema(List<CatalogSchema> schemas, List<String> delSchema) {
        if (CollUtil.isEmpty(schemas) && null != delSchema && delSchema.isEmpty()) {
            return;
        }
        sink(v -> v.schema(schemas, delSchema));
    }

    default void stageStart(CatalogSchema catalogSchema, List<String> tableNames, MetaObjType first, MetaObjType... include) {
        sink(v -> v.schemaStart(catalogSchema, tableNames, first, include));
    }

    default void tableName(List<DbMetaObject> tables) {
        if (tables.isEmpty()) {
            return;
        }
        sink(v -> v.tableName(tables));
    }

    default void consume(WrapTable table) {
        sink(v -> v.table(table));
    }

    default void stageFinish(CatalogSchema catalogSchema) {
        sink(v -> v.schemaFinish(catalogSchema));
    }
}
