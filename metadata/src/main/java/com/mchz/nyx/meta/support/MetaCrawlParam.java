package com.mchz.nyx.meta.support;

import com.mchz.nyx.meta.common.enums.MetaObjType;
import com.mchz.nyx.meta.model.meta.CatalogSchema;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.NonNull;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * 源数据发现数据源配置
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/1/4 11:29
 */
@Data
@NoArgsConstructor
public class MetaCrawlParam implements MetaCrawlConfig {
    private DataSourceConfig source;

    private List<SchemaTables> schemaTables;

    private List<MetaObjType> crawl;
    private Integer crawlType;

    public MetaCrawlParam(@NonNull DataSourceConfig config) {
        this.source = config;
    }

    public long getSourceId() {
        return source.getSourceId();
    }

    public void setSchemas(List<String> schemas) {
        this.schemaTables = schemas.stream().map(v -> SchemaTables.of(CatalogSchema.of(v))).collect(Collectors.toList());
    }

    public void setSchemas(Set<String> schemas) {
        this.schemaTables = schemas.stream().map(v -> SchemaTables.of(CatalogSchema.of(v))).collect(Collectors.toList());
    }
}
