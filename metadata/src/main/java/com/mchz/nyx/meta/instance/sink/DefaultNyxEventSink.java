package com.mchz.nyx.meta.instance.sink;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.crypto.digest.MD5;
import com.mchz.nyx.meta.common.enums.MetaObjType;
import com.mchz.nyx.meta.entity.*;
import com.mchz.nyx.meta.model.meta.Entry;
import com.mchz.nyx.meta.model.meta.UpdateTable;
import com.mchz.nyx.meta.model.meta.WrapObj;
import com.mchz.nyx.meta.model.meta.WrapTable;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
public class DefaultNyxEventSink extends AbstractNyxEventSink {
    private final MD5 digester;

    public DefaultNyxEventSink() {
        this.digester = MD5.create();
    }

    @Override
    protected void diffTable(UpdateTable update, DbMetaObject lTable, WrapTable table, List<DbMetaObject> allFromTable) {
        Map<MetaObjType, List<DbMetaObject>> map;
        if (CollUtil.isEmpty(allFromTable)) {
            map = Collections.emptyMap();
        } else {
            map = allFromTable.stream().collect(Collectors.groupingBy(v -> MetaObjType.getType(v.getType()), () -> new EnumMap<>(MetaObjType.class), Collectors.toList()));
        }
        update.addUpdateTable(lTable, table.getObj(), table.getTable());
        diffColumn(update, table.getColumn(), map.get(MetaObjType.COLUMN));
        if (table.isView()) {
            return;
        }
        diffIndex(update, table.getIndex(), table.getIndexColumn(), map.get(MetaObjType.INDEX), map.get(MetaObjType.UNIQUE_INDEX), map.get(MetaObjType.PRIMARY_KEY));
        diffFk(update, table.getFk(), table.getFkInfo(), map.get(MetaObjType.FOREIGN_KEY));
    }

    private void diffColumn(UpdateTable res, WrapObj<DbMetaColumn> wCol, List<DbMetaObject> columns) {
        if (null == wCol) {
            return;
        }
        Map<String, DbMetaObject> lCols = getNameObjMap(columns);
        WrapObj<DbMetaColumn> insert = new WrapObj<>(wCol.getParent());
        for (Entry<DbMetaColumn> entry : wCol.getList()) {
            DbMetaObject obj = entry.getObj();
            DbMetaObject lObj = lCols.remove(obj.getName());
            if (null == lObj) {
                insert.add(entry);
                continue;
            }
            res.addUpdateColumn(lObj, obj, entry.getValue());
        }
        res.insertColumn(insert);
        res.deleteObj(lCols.values());
    }

    private void diffIndex(UpdateTable res, WrapObj<DbMetaIndex> wIdx, List<WrapObj<DbMetaIndexColumn>> indexColumn, List<DbMetaObject> idxList, List<DbMetaObject> udxList, List<DbMetaObject> pkList) {
        if (null == wIdx) {
            return;
        }
        if (wIdx.isEmpty()) {
            res.deleteObj(idxList);
            res.deleteObj(udxList);
            res.deleteObj(pkList);
            return;
        }
        Map<String, DbMetaObject> lIdx = getNameObjMap(idxList);
        Map<String, DbMetaObject> lUdx = getNameObjMap(udxList);
        Map<String, DbMetaObject> lPk = getNameObjMap(pkList);
        Map<DbMetaObject, WrapObj<DbMetaIndexColumn>> icMap = indexColumn.stream().collect(Collectors.toMap(WrapObj::getParent, Function.identity()));
        WrapObj<DbMetaIndex> insert = new WrapObj<>(wIdx.getParent());
        List<WrapObj<DbMetaIndexColumn>> insertIndexColumn = new ArrayList<>(indexColumn.size());
        for (Entry<DbMetaIndex> entry : wIdx.getList()) {
            DbMetaObject obj = entry.getObj();
            WrapObj<DbMetaIndexColumn> wIndexColumn = icMap.get(obj);
            DbMetaObject lObj;
            switch (MetaObjType.getType(obj.getType())) {
                case INDEX:
                    lObj = lIdx.remove(obj.getName());
                    break;
                case UNIQUE_INDEX:
                    lObj = lUdx.remove(obj.getName());
                    break;
                case PRIMARY_KEY:
                    lObj = lPk.remove(obj.getName());
                    break;
                default:
                    continue;
            }
            if (null == lObj) {
                insert.add(entry);
            } else if (res.addUpdateIndex(lObj, obj, entry.getValue())) {
                res.deleteIndexColumn(lObj.getOid());
            } else {
                continue;
            }
            if (null != wIndexColumn) {
                insertIndexColumn.add(wIndexColumn);
            }
        }
        res.insertIndex(insert);
        res.insertIndexColumn(insertIndexColumn);
        res.deleteObj(lIdx.values());
        res.deleteObj(lUdx.values());
        res.deleteObj(lPk.values());
    }

    private void diffFk(UpdateTable res, WrapObj<DbMetaForeignKey> wFk, List<WrapObj<DbMetaForeignKeyInfo>> fkInfo, List<DbMetaObject> fkList) {
        if (null == wFk) {
            return;
        }
        if (wFk.isEmpty()) {
            res.deleteObj(fkList);
            return;
        }
        Map<String, DbMetaObject> lIndex = getNameObjMap(fkList);
        Map<DbMetaObject, WrapObj<DbMetaForeignKeyInfo>> icMap = fkInfo.stream().collect(Collectors.toMap(WrapObj::getParent, Function.identity()));
        WrapObj<DbMetaForeignKey> insert = new WrapObj<>(wFk.getParent());
        List<WrapObj<DbMetaForeignKeyInfo>> insertIndexColumn = new ArrayList<>(fkInfo.size());
        for (Entry<DbMetaForeignKey> entry : wFk.getList()) {
            DbMetaObject obj = entry.getObj();
            WrapObj<DbMetaForeignKeyInfo> wIndexColumn = icMap.remove(obj);
            DbMetaObject lObj = lIndex.remove(obj.getName());
            if (null == lObj) {
                insertIndexColumn.add(wIndexColumn);
                insert.add(entry);
                continue;
            }
            if (res.addUpdateFk(lObj, obj, entry.getValue())) {
                res.deleteFkInfo(lObj.getOid());
                insertIndexColumn.add(wIndexColumn);
            }
        }
        res.insertFk(insert);
        res.insertFkInfo(insertIndexColumn);
        res.deleteObj(lIndex.values());
    }

    private Map<String, DbMetaObject> getNameObjMap(List<DbMetaObject> list) {
        if (CollUtil.isEmpty(list)) {
            return new HashMap<>(0);
        }
        return list.stream().collect(Collectors.toMap(DbMetaObject::getName, Function.identity()));
    }

    // hash value #############################################################################################################################

    @Override
    protected String getSchemaHashValue(DbMetaSchema schema) {
        List<String> list = Arrays.asList(schema.getCatalog(), schema.getSchema(), schema.getCharset(), schema.getCollation());
        if (list.stream().allMatch(Objects::isNull)) {
            return "";
        }
        return digester.digestHex(list.toString());
    }

    @Override
    protected void updateHashValue(WrapTable wrapTable) {
        List<String> hashList = new ArrayList<>(4);
        if (null != wrapTable.getColumn()) {
            String column = wrapTable.getColumn().getList().stream().map(this::updateColumnHashValue).collect(Collectors.joining());
            hashList.add(digester.digestHex(column));
        }
        if (CollUtil.isNotEmpty(wrapTable.getIndexColumn())) {
            wrapTable.getIndexColumn().forEach(this::updateIndexColumnHashValue);
        }
        if (null != wrapTable.getIndex()) {
            String index = wrapTable.getIndex().getList().stream().map(this::updateIndexHashValue).collect(Collectors.joining());
            hashList.add(digester.digestHex(index));
        }
        if (CollUtil.isNotEmpty(wrapTable.getFkInfo())) {
            wrapTable.getFkInfo().forEach(this::updateFkInfoHashValue);
        }
        if (null != wrapTable.getFk() && !wrapTable.getFk().isEmpty()) {
            String fk = wrapTable.getFk().getList().stream().map(this::updateFkHashValue).collect(Collectors.joining());
            hashList.add(digester.digestHex(fk));
        }
        DbMetaObject obj = wrapTable.getObj();
        DbMetaTable table = wrapTable.getTable();
        String tableValue = Arrays.asList(obj.getName(), table.getType(), table.getColumnNum(), table.getPartitionColumn(), table.getSpace(), table.getComment()).toString();
        String tableHashValue = digester.digestHex(tableValue);
        hashList.add(tableHashValue);
        obj.setHashValue(digester.digestHex16(hashList.toString()) + DigestUtil.md5HexTo16(tableHashValue));
    }

    private String updateColumnHashValue(Entry<DbMetaColumn> entry) {
        DbMetaObject obj = entry.getObj();
        DbMetaColumn column = entry.getValue();
        String value = Arrays.asList(obj.getName(), column.getDataType(), column.getTypeGroup(), column.getLength(), column.getPrecision(), column.getScale(), column.getPosition(),
            column.getIsAutoIncremented(), column.getIsGenerated(), column.getIsPk(), column.getIsUnique(), column.getIsIndex(), column.getIsFk(), column.getComment()).toString();
        String hash = digester.digestHex(value);
        obj.setHashValue(hash);
        return hash;
    }

    private String updateIndexHashValue(Entry<DbMetaIndex> entry) {
        DbMetaObject obj = entry.getObj();
        DbMetaIndex index = entry.getValue();
        String value = Arrays.asList(obj.getName(), index.getType(), index.getIsPk(), index.getIsUnique(), index.getMethod(), obj.getDescription(), obj.getHashValue()).toString();
        String hash = digester.digestHex(value);
        obj.setHashValue(hash);
        return hash;
    }

    private void updateIndexColumnHashValue(WrapObj<DbMetaIndexColumn> obj) {
        String columns = obj.getList().stream().map(v -> v.getValue().getPosition() + " " + v.getObj().getName()).collect(Collectors.toList()).toString();
        obj.getParent().setHashValue(digester.digestHex(columns));
    }

    private String updateFkHashValue(Entry<DbMetaForeignKey> entry) {
        DbMetaObject obj = entry.getObj();
        String value = Arrays.asList(obj.getName(), obj.getDescription(), obj.getHashValue()).toString();
        String hash = digester.digestHex(value);
        obj.setHashValue(hash);
        return hash;
    }

    private void updateFkInfoHashValue(WrapObj<DbMetaForeignKeyInfo> obj) {
        String columns = obj.getList().stream().flatMap(v -> {
            DbMetaObject column = v.getObj();
            DbMetaForeignKeyInfo value = v.getValue();
            return Stream.of(column.getName(), value.getRefSchema(), value.getRefTable(), value.getRefColumn(), value.getPosition());
        }).collect(Collectors.toList()).toString();
        obj.getParent().setHashValue(digester.digestHex(columns));
    }

}
