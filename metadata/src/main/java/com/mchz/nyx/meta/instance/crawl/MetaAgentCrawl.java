package com.mchz.nyx.meta.instance.crawl;

import com.mchz.nyx.meta.common.enums.MetaObjType;
import com.mchz.nyx.meta.entity.DbMetaObject;
import com.mchz.nyx.meta.model.meta.CatalogSchema;
import com.mchz.nyx.meta.model.meta.WrapTable;
import com.mchz.nyx.meta.support.DataSourceConfig;
import com.mchz.nyx.meta.support.MetaCrawlConfig;
import com.mchz.nyx.meta.support.SchemaTables;
import lombok.AllArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.function.Function;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
@Setter
@Slf4j
public class MetaAgentCrawl extends AbstractMetaEventCrawl {
    private final DsMetaAdapter adapter;

    private final int type;
    private final DataSourceConfig source;
    private final List<SchemaTables> schemaTables;
    private final String lastSchema;

    public MetaAgentCrawl(MetaCrawlConfig config, DsMetaAdapter adapter, int type) {
        super(config);
        this.adapter = adapter;
        this.type = type;
        this.source = config.getSource();
        this.schemaTables = config.getSchemaTables();
        this.lastSchema = config.getLastSchema();
    }

    @Override
    protected void execute() {
        adapter.execute(new Inner(this), source, type, schemaTables);
    }

    @AllArgsConstructor
    private static class Inner implements NyxCrawlAgent {
        private final MetaAgentCrawl base;

        @Override
        public void schema(List<CatalogSchema> schemas) {
            base.schema(schemas, base.lastSchema);
        }

        @Override
        public void schema(List<CatalogSchema> schemas, List<String> delSchema) {
            base.eventSink.schema(schemas, delSchema);
        }

        @Override
        public void stageStart(CatalogSchema catalogSchema, List<String> tableNames, MetaObjType first, MetaObjType... include) {
            base.stageStart(catalogSchema, tableNames, first, include);
        }

        @Override
        public void tableName(List<DbMetaObject> tables) {
            base.tableName(tables);
        }

        @Override
        public void consume(WrapTable table) {
            base.eventSink.consume(table);
        }

        @Override
        public void stageFinish(CatalogSchema catalogSchema) {
            base.stageFinish(catalogSchema);
        }

        @Override
        public void crawlTableName(CatalogSchema catalogSchema, List<String> tables, MetaObjType type) {
            base.crawlTableName(catalogSchema, tables, type);
        }

        @Override
        public <T> void parseColumn(List<T> columns, WrapTable table, Function<T, DbMetaObject> objectFun, MetaColumnFun<T> columnFun) {
            base.parseColumn(columns, table, objectFun, columnFun);
        }
    }
}
