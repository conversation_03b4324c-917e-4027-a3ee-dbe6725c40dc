package com.mchz.nyx.meta.instance.crawl;

import com.mchz.nyx.meta.common.enums.MetaObjType;
import com.mchz.nyx.meta.entity.DbMetaObject;
import com.mchz.nyx.meta.instance.sink.NyxEventSink;
import com.mchz.nyx.meta.model.meta.CatalogSchema;
import com.mchz.nyx.meta.model.meta.WrapTable;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
@Setter
@Slf4j
public abstract class AbstractEventCrawl implements NyxEventCrawl {
    protected long sourceId;
    protected NyxEventSink eventSink;

    @Override
    public synchronized boolean crawl() {
        execute();
        return true;
    }

    protected abstract void execute();

    protected void stageStart(CatalogSchema catalogSchema, List<String> tableNames, MetaObjType first, MetaObjType... include) {
        eventSink.stageStart(catalogSchema, tableNames, first, include);
    }

    protected void tableName(List<DbMetaObject> tables) {
        eventSink.tableName(tables);
    }

    protected void consume(WrapTable table) {
        eventSink.consume(table);
    }

    protected void stageFinish(CatalogSchema catalogSchema) {
        eventSink.stageFinish(catalogSchema);
    }
}
