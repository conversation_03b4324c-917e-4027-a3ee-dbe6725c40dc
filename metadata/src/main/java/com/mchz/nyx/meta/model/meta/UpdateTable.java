package com.mchz.nyx.meta.model.meta;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.mchz.nyx.meta.entity.*;
import com.mchz.nyx.meta.support.ObjectInfo;
import com.mchz.nyx.meta.support.TableUpdateDetail;
import lombok.Getter;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
@Getter
public class UpdateTable implements TableUpdateDetail {
    private final InsertTable insert;

    /**
     * 后置更新obj
     */
    private final List<DbMetaObject> updateObj;
    private final List<DbMetaTable> updateTable;
    private final List<DbMetaTable> updateTableOther;
    private final List<DbMetaColumn> updateColumn;
    private final List<DbMetaIndex> updateIndex;
    private final List<DbMetaForeignKey> updateFk;

    private final List<Long> deleteObjIds;
    private final List<Long> deleteTableIds;
    private final List<Long> restoreObjIds;
    private final List<Long> restoreTableIds;
    /**
     * 前置于插入
     */
    private final List<Long> deleteIndexColumnIds;
    private final List<Long> deleteFkInfoIds;


    public UpdateTable(DbMetaObject schema, int tableSize) {
        this(new InsertTable(schema, tableSize));
    }

    public UpdateTable(InsertTable insert) {
        this.insert = insert;
        this.updateObj = new LinkedList<>();
        this.updateTable = new ArrayList<>();
        this.updateTableOther = new ArrayList<>();
        this.updateColumn = new LinkedList<>();
        this.updateIndex = new ArrayList<>();
        this.updateFk = new ArrayList<>();

        this.deleteObjIds = new ArrayList<>();
        this.deleteTableIds = new ArrayList<>();
        this.restoreObjIds = new ArrayList<>();
        this.restoreTableIds = new ArrayList<>();
        this.deleteIndexColumnIds = new ArrayList<>();
        this.deleteFkInfoIds = new ArrayList<>();
    }

    public void insertColumn(WrapObj<DbMetaColumn> columns) {
        if (columns.getList().isEmpty()) {
            return;
        }
        insert.getColumnList().add(columns);
    }

    public void insertIndex(WrapObj<DbMetaIndex> index) {
        if (index.getList().isEmpty()) {
            return;
        }
        insert.getIndexList().add(index);
    }

    public void insertIndexColumn(List<WrapObj<DbMetaIndexColumn>> indexColumn) {
        if (CollUtil.isEmpty(indexColumn)) {
            return;
        }
        insert.getIcList().addAll(indexColumn);
    }

    public void insertFk(WrapObj<DbMetaForeignKey> fk) {
        if (fk.getList().isEmpty()) {
            return;
        }
        insert.getFkList().add(fk);
    }

    public void insertFkInfo(List<WrapObj<DbMetaForeignKeyInfo>> fkInfo) {
        if (CollUtil.isEmpty(fkInfo)) {
            return;
        }
        insert.getFkInfoList().addAll(fkInfo);
    }

    public void addUpdateTable(DbMetaObject lastObj, DbMetaObject obj, DbMetaTable info) {
        merge(lastObj, obj, info);
        updateTable.add(info);
    }

    public void addUpdateTableOther(DbMetaObject lastObj, DbMetaTable info) {
        DbMetaTable temp = new DbMetaTable();
        temp.setOid(lastObj.getOid());
        temp.setRows(info.getRows());
        updateTableOther.add(temp);
    }

    public void addUpdateColumn(DbMetaObject lastObj, DbMetaObject obj, DbMetaColumn info) {
        if (merge(lastObj, obj, info)) {
            updateColumn.add(info);
        }
    }

    public boolean addUpdateIndex(DbMetaObject lastObj, DbMetaObject obj, DbMetaIndex info) {
        if (merge(lastObj, obj, info)) {
            updateIndex.add(info);
            return true;
        }
        return false;
    }

    public boolean addUpdateFk(DbMetaObject lastObj, DbMetaObject obj, DbMetaForeignKey info) {
        // info 无可更新属性
        return merge(lastObj, obj, info);
    }

    public void restoreTable(DbMetaObject tableObj) {
        restoreTableIds.add(tableObj.getOid());
    }

    public void deleteObj(Collection<DbMetaObject> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        deleteObjIds.addAll(list.stream().filter(v -> !v.getDeleted()).map(DbMetaObject::getOid).collect(Collectors.toList()));
    }

    public void deleteIndexColumn(Long indexId) {
        deleteIndexColumnIds.add(indexId);
    }

    public void deleteFkInfo(Long fkId) {
        deleteFkInfoIds.add(fkId);
    }

    public void deleteTableIds(List<Long> ids) {
        deleteTableIds.addAll(ids);
    }

    private <T extends ObjectInfo> boolean merge(DbMetaObject lObj, DbMetaObject obj, T info) {
        obj.setOid(lObj.getOid());
        if (lObj.getDeleted()) {
            restoreObjIds.add(lObj.getOid());
        }
        if (Objects.equals(lObj.getHashValue(), obj.getHashValue())) {
            return false;
        }
        obj.setRevision(ObjUtil.defaultIfNull(lObj.getRevision(), 1) + 1);
        info.setOid(lObj.getOid());
        updateObj.add(obj);
        return true;
    }

    @Override
    public List<DbMetaObject> getTableObjects() {
        return insert.getTableObjects();
    }

    @Override
    public List<DbMetaObject> getObjects() {
        return insert.getObjects();
    }

    @Override
    public List<DbMetaTable> getTables() {
        return insert.getTables();
    }

    @Override
    public List<DbMetaColumn> getColumns() {
        Long oid = insert.getTableWrapObj().getParent().getOid();
        insert.getColumnList().forEach(v -> v.getParent().setPid(oid));
        return insert.getColumns();
    }

    @Override
    public List<DbMetaIndex> getIndex() {
        return insert.getIndex();
    }

    @Override
    public List<DbMetaIndexColumn> getIndexColumns() {
        return insert.getIndexColumns();
    }

    @Override
    public List<DbMetaForeignKey> getForeignKeys() {
        return insert.getForeignKeys();
    }

    @Override
    public List<DbMetaForeignKeyInfo> getForeignKeyInfos() {
        return insert.getForeignKeyInfos();
    }
}
