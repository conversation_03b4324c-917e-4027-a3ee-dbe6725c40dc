package com.mchz.nyx.meta.exception;

import cn.hutool.core.exceptions.ExceptionUtil;
import com.mchz.nyx.common.exception.NyxException;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2023/5/26 11:02
 */
public class CrawlerException extends NyxException {
    public CrawlerException(String message) {
        super(message);
    }

    public CrawlerException(Throwable cause) {
        this("采集失败", cause);
    }

    public CrawlerException(String message, Throwable cause) {
        super(message + "," + ExceptionUtil.getSimpleMessage(cause), cause);
    }
}
