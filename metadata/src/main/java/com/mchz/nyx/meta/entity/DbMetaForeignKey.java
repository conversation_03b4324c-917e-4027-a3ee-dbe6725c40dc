package com.mchz.nyx.meta.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mchz.nyx.meta.support.ObjectInfo;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2022/1/11 19:31
 */
@Data
@TableName("dbmeta_foreign_key")
public class DbMetaForeignKey implements ObjectInfo {
    /**
     * 主键
     */
    @TableId(type = IdType.INPUT)
    private Long oid;

    /**
     * 数据源ID
     */
    private Long sourceId;

    /**
     * 表主键
     */
    private Long tableId;

    @Override
    public void setParent(DbMetaObject obj) {
        setTableId(obj.getOid());
    }
}
