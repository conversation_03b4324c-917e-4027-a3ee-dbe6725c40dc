package com.mchz.nyx.meta.instance;

import com.mchz.nyx.meta.NyxInstance;
import com.mchz.nyx.meta.instance.crawl.NyxEventCrawl;
import com.mchz.nyx.meta.instance.sink.NyxEventSink;
import com.mchz.nyx.meta.instance.store.NyxEventStore;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
@Getter
@AllArgsConstructor
public class DefaultNyxInstance implements NyxInstance {
    private long sourceId;
    private NyxEventCrawl eventCrawl;
    private NyxEventSink eventSink;
    private NyxEventStore eventStore;

    @Override
    public void run() {
        eventSink.start();
        try {
            eventCrawl.crawl();
        } finally {
            try {
                eventSink.shutdown();
            } finally {
                eventStore.shutdown();
            }
        }
    }
}
