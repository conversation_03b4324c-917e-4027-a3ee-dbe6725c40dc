package com.mchz.nyx.meta.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.mchz.nyx.meta.support.ObjectInfo;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2022/1/10 14:06
 */
@Data
@TableName("dbmeta_column")
public class DbMetaColumn implements ObjectInfo {
    /**
     * 主键
     */
    @TableId(type = IdType.INPUT)
    private Long oid;

    /**
     * 数据源ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long sourceId;
    /**
     * schema
     */
    @TableField(fill = FieldFill.INSERT)
    private Long schemaId;
    /**
     * 所属表格
     */
    @TableField(fill = FieldFill.INSERT)
    private Long tableId;

    /**
     * 原始类型
     */
    private String dataType;

    /**
     * 类型分组
     */
    private String typeGroup;

    /**
     * 长度
     */
    private Long length;

    /**
     * 精度
     */
    private Integer precision;

    /**
     * 刻度
     */
    private Integer scale;

    /**
     * 是否可以为空
     */
    private Boolean nullable;

    /**
     * 位置
     */
    private Integer position;

    /**
     * 是否自增
     */
    private Boolean isAutoIncremented;

    /**
     * 是否生成
     */
    private Boolean isGenerated;

    /**
     * 是否主键
     */
    private Boolean isPk;

    /**
     * 是否唯一键
     */
    private Boolean isUnique;

    /**
     * 是否索引
     */
    private Boolean isIndex;

    /**
     * 是否外键
     */
    private Boolean isFk;

    /**
     * 注释
     */
    private String comment;

    @Override
    public void setParent(DbMetaObject obj) {
        setTableId(obj.getOid());
        setSchemaId(obj.getPid());
    }
}
