package com.mchz.nyx.meta.model.event;

import com.mchz.nyx.meta.common.enums.EventType;
import com.mchz.nyx.meta.common.enums.MetaObjType;
import com.mchz.nyx.meta.entity.DbMetaObject;
import com.mchz.nyx.meta.model.meta.CatalogSchema;
import com.mchz.nyx.meta.model.meta.WrapTable;
import lombok.Getter;

import java.util.EnumSet;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
@Getter
public class NyxEvent {
    private EventType type;

    private List<CatalogSchema> schemas;
    private List<DbMetaObject> tables;
    private CatalogSchema schema;
    private List<String> strList;
    private int value;

    private WrapTable table;


    public void schema(List<CatalogSchema> schemas, List<String> delSchema) {
        this.type = EventType.SCHEMA;
        this.schemas = schemas;
        this.strList = delSchema;
    }

    public void schemaStart(CatalogSchema catalogSchema, List<String> tableNames, MetaObjType first, MetaObjType... include) {
        schemaStart(catalogSchema, tableNames, EnumSet.of(first, include));
    }

    public void schemaStart(CatalogSchema catalogSchema, List<String> tableNames, EnumSet<MetaObjType> include) {
        int value = 0;
        for (MetaObjType v : include) {
            value += v.getValue();
        }
        this.type = EventType.START;
        this.value = value;
        this.schema = catalogSchema;
        this.strList = tableNames;
    }

    public void schemaFinish(CatalogSchema catalogSchema) {
        this.type = EventType.FINISH;
        this.schema = catalogSchema;
    }

    public void tableName(List<DbMetaObject> tables) {
        this.type = EventType.TABLE_NAME;
        this.tables = tables;
    }

    public void table(WrapTable table) {
        this.type = EventType.TABLE;
        this.table = table;
    }

    public void clear() {
        type = null;
        schemas = null;
        tables = null;
        schema = null;
        strList = null;
        table = null;
    }
}
