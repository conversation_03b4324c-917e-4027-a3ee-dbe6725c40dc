package com.mchz.nyx.meta;

import com.mchz.nyx.meta.instance.crawl.NyxEventCrawl;
import com.mchz.nyx.meta.instance.sink.NyxEventSink;
import com.mchz.nyx.meta.instance.store.NyxEventStore;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
public interface NyxInstance  {

    long getSourceId();

    NyxEventCrawl getEventCrawl();

    NyxEventSink getEventSink();

    NyxEventStore getEventStore();

    void run();
}
