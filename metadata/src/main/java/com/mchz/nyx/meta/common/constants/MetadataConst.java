package com.mchz.nyx.meta.common.constants;

import cn.hutool.core.util.StrUtil;
import com.mchz.mcdatasource.core.DataType;

import java.util.Arrays;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2022/1/7 14:35
 */
public interface MetadataConst {
    int SCOPE_TABLE_SIZE = 100;

    List<DataType> CRAWLER_WITH_VIEW_TYPES = Arrays.asList(DataType.TABLE_COLUMN, DataType.VIEW, DataType.VIEW_COLUMN, DataType.ALL_INDEX);
    List<DataType> CRAWLER_TABLE_TYPES = Arrays.asList(DataType.TABLE_COLUMN, DataType.ALL_INDEX);

    String UNKNOWN_DATE_TYPE = StrUtil.EMPTY;
}
