package com.mchz.nyx.meta.support;

import com.mchz.nyx.meta.entity.*;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2023/3/3 11:23
 */
public interface TableUpdateDetail extends TableInsertDetail {

    List<DbMetaObject> getUpdateObj();

    List<DbMetaTable> getUpdateTable();

    List<DbMetaTable> getUpdateTableOther();

    List<DbMetaColumn> getUpdateColumn();

    List<DbMetaIndex> getUpdateIndex();

    List<DbMetaForeignKey> getUpdateFk();

    List<Long> getRestoreObjIds();

    List<Long> getRestoreTableIds();

    List<Long> getDeleteObjIds();

    List<Long> getDeleteTableIds();

    List<Long> getDeleteIndexColumnIds();

    List<Long> getDeleteFkInfoIds();
}
