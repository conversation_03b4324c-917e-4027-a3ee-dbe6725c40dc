package com.mchz.nyx.meta.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mchz.nyx.meta.support.ObjectInfo;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2022/1/10 14:06
 */
@Data
@TableName("dbmeta_index")
public class DbMetaIndex implements ObjectInfo {
    /**
     * 主键
     */
    @TableId(type = IdType.INPUT)
    private Long oid;

    /**
     * 数据源ID
     */
    private Long sourceId;

    /**
     * 所属表格
     */
    private Long tableId;

    /**
     * 类型
     */
    private String type;

    /**
     * 是否主键
     */
    private Boolean isPk;

    /**
     * 是否唯一键
     */
    private Boolean isUnique;

    /**
     * 索引方法
     */
    private String method;

    @Override
    public void setParent(DbMetaObject obj) {
        setTableId(obj.getOid());
    }
}
