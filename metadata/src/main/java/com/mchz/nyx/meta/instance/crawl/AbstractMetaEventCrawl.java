package com.mchz.nyx.meta.instance.crawl;

import cn.hutool.core.collection.CollUtil;
import com.mchz.nyx.meta.common.enums.MetaObjType;
import com.mchz.nyx.meta.entity.DbMetaColumn;
import com.mchz.nyx.meta.entity.DbMetaObject;
import com.mchz.nyx.meta.entity.DbMetaTable;
import com.mchz.nyx.meta.exception.CrawlerException;
import com.mchz.nyx.meta.model.meta.CatalogSchema;
import com.mchz.nyx.meta.model.meta.Entry;
import com.mchz.nyx.meta.model.meta.WrapObj;
import com.mchz.nyx.meta.model.meta.WrapTable;
import com.mchz.nyx.meta.support.CharsetConvert;
import com.mchz.nyx.meta.support.MetaCrawlConfig;
import com.mchz.nyx.meta.support.SchemaTables;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractMetaEventCrawl extends AbstractEventCrawl {
    protected final List<SchemaTables> schemaTables;

    protected final CharsetConvert charsetConvert;

    public AbstractMetaEventCrawl(MetaCrawlConfig config) {
        this.schemaTables = config.getSchemaTables();
        this.charsetConvert = config.getCharsetConvert();
    }

    protected void schema(List<CatalogSchema> schemas, String lastSchema) {
        if (CollUtil.isEmpty(schemas)) {
            return;
        }
        List<String> delSchema = null;
        if (null != schemaTables) {
            if (schemaTables.isEmpty()) {
                Stream<CatalogSchema> stream = schemas.stream();
                if (null != lastSchema) {
                    stream = stream.filter(v -> v.getName().compareTo(lastSchema) >= 0);
                }
                stream.sorted(Comparator.comparing(CatalogSchema::getName)).forEach(v -> schemaTables.add(SchemaTables.of(v)));
            } else if (schemas.size() != schemaTables.size()) {
                Map<String, CatalogSchema> map = schemas.stream().collect(Collectors.toMap(CatalogSchema::getName, Function.identity()));
                schemas = new ArrayList<>(schemaTables.size());
                delSchema = new ArrayList<>(2);
                Iterator<SchemaTables> iterator = schemaTables.iterator();
                while (iterator.hasNext()) {
                    SchemaTables schemaTable = iterator.next();
                    CatalogSchema schema = map.get(schemaTable.getSchema().getName());
                    if (null == schema) {
                        delSchema.add(schemaTable.getSchema().getName());
                        iterator.remove();
                    } else {
                        schemas.add(schema);
                    }
                }
            }
        }
        eventSink.schema(schemas, delSchema);
    }

    protected void crawlTableName(CatalogSchema catalogSchema, List<String> tables, MetaObjType type) {
        stageStart(catalogSchema, tables, type);
        tableName(tables.stream().map(v -> buildTableObject(v, type)).collect(Collectors.toList()));
        stageFinish(catalogSchema);
    }

    protected <T> void parseColumn(List<T> columns, WrapTable table, Function<T, DbMetaObject> objectFun, MetaColumnFun<T> columnFun) {
        if (null == columns) {
            columns = Collections.emptyList();
        }
        WrapObj<DbMetaColumn> wCol = new WrapObj<>(table.getObj(), columns.size());
        int i = 1;
        Map<String, Entry<DbMetaColumn>> columnMap = new HashMap<>(columns.size());
        for (T column : columns) {
            DbMetaObject oCol = objectFun.apply(column);
            String name = oCol.getName();
            Entry<DbMetaColumn> existing = columnMap.get(name);
            if (null == existing) {
                DbMetaColumn metaColumn = columnFun.apply(column, i);
                columnMap.put(name, wCol.add(oCol, metaColumn));
                processColumn(oCol, metaColumn);
            } else if (name.endsWith("?")) {
                log.warn("忽略列重复[{}.{}]{}:{}({}:{})", table.getObj().getName(), oCol.getName(), existing.getValue().getPosition(), i, existing.getObj().getDescription(), oCol.getDescription());
            } else {
                List<String> columnNames = columns.stream().map(v -> objectFun.apply(v).getName()).collect(Collectors.toList());
                log.warn("列重复[{}]{}({}-{}),{}", table.getObj().getName(), oCol.getName(), existing.getValue().getPosition(), i, columnNames);
                throw new CrawlerException(String.format("列重复,[%s.%s](%s:%s)", table.getObj().getName(), oCol.getName(), existing.getObj().getDescription(), oCol.getDescription()));
            }
            i++;
        }
        table.getTable().setColumnNum(columnMap.size());
        processTable(table.getObj(), table.getTable());
        table.setColumn(wCol);
        table.setColumnMap(columnMap);
    }

    private DbMetaObject buildTableObject(String name, MetaObjType type) {
        DbMetaObject obj = new DbMetaObject();
        obj.setSourceId(sourceId);
        obj.setName(name);
        obj.setType(type.name());
        return obj;
    }

    private void processTable(DbMetaObject obj, DbMetaTable table) {
        obj.setSourceId(sourceId);
        if (null == charsetConvert) {
            obj.setDescription(table.getComment());
        } else {
            String decode = charsetConvert.decode(table.getComment());
            obj.setDescription(decode);
            table.setComment(decode);
        }
    }

    private void processColumn(DbMetaObject obj, DbMetaColumn column) {
        obj.setSourceId(sourceId);
        if (null == charsetConvert) {
            obj.setDescription(column.getComment());
        } else {
            String decode = charsetConvert.decode(column.getComment());
            obj.setDescription(decode);
            column.setComment(decode);
        }
    }
}
