package com.mchz.nyx.common.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import lombok.experimental.UtilityClass;

import java.util.*;
import java.util.function.Function;
import java.util.function.Predicate;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/12/31
 */
@UtilityClass
public class CommUtil {
    public int sampling(Object[][] data, Iterator<Object[]> iterator, int k) {
        int i = 0, t;
        while (iterator.hasNext()) {
            Object[] next = iterator.next();
            if (i++ < k) {
                t = i - 1;
            } else {
                t = RandomUtil.randomInt(i);
                if (t >= k) {
                    continue;
                }
            }
            int j = 0;
            for (Object o : next) {
                data[j++][t] = o;
            }
        }
        return i;
    }

    public boolean isChineseChar(int cp) {
        return cp >= 0x4E00 && cp <= 0x9FFF || cp >= 0x3400 && cp <= 0x4DBF || cp >= 0xF900 && cp <= 0xFAFF;
    }

    public char letterUpper(char letter) {
        return (char) (letter & ~32);
    }

    public boolean isPunctuation(int ch) {
        int type = Character.getType(ch);
        return type == Character.CONNECTOR_PUNCTUATION ||
            type == Character.DASH_PUNCTUATION ||
            type == Character.START_PUNCTUATION ||
            type == Character.END_PUNCTUATION ||
            type == Character.INITIAL_QUOTE_PUNCTUATION ||
            type == Character.FINAL_QUOTE_PUNCTUATION ||
            type == Character.OTHER_PUNCTUATION;
    }

    public <K, T> Map<K, List<T>> groupByPid(List<T> list, Function<T, K> pidFun) {
        return groupByPid(list, pidFun, null);
    }

    public <K, T> Map<K, List<T>> groupByPid(List<T> list, Function<T, K> pidFun, Predicate<T> filter) {
        if (CollUtil.isEmpty(list)) {
            return new HashMap<>(0);
        }
        Map<K, List<T>> res = new HashMap<>(Math.max(16, list.size() >> 3));
        K last = null;
        List<T> temp = null;
        for (T t : list) {
            if (filter == null || filter.test(t)) {
                K key = pidFun.apply(t);
                if (!Objects.equals(key, last)) {
                    temp = res.computeIfAbsent(key, k -> new ArrayList<>(8));
                    last = key;
                }
                temp.add(t);
            }
        }
        return res;
    }
}
