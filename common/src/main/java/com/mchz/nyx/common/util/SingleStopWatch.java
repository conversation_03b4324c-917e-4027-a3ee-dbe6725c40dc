package com.mchz.nyx.common.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Getter;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/10
 */

public class SingleStopWatch implements NyxStopWatch {
    /**
     * 秒表唯一标识，用于多个秒表对象的区分
     */
    @Getter
    private final String id;

    /**
     * 开始时间
     */
    protected long startTimeNanos;
    /**
     * 总任务数
     */
    protected final AtomicLong taskCount;
    /**
     * 总运行时间
     */
    protected long totalTimeNanos;

    public SingleStopWatch(String id) {
        this.id = id;
        this.taskCount = new AtomicLong();
    }

    @Override
    public void start() {
        if (startTimeNanos > 0) {
            throw new IllegalStateException("Can't start StopWatch: it's already running");
        }
        startTimeNanos = System.nanoTime();
    }

    @Override
    public void stop() {
        if (startTimeNanos <= 0) {
            throw new IllegalStateException("Can't stop StopWatch: it's not running");
        }
        totalTimeNanos = System.nanoTime() - startTimeNanos;
    }

    @Override
    public long[] startChild(String childName) {
        if (startTimeNanos <= 0) {
            startTimeNanos = System.nanoTime();
        }
        taskCount.incrementAndGet();
        return null;
    }

    @Override
    public void stopChild(long[] key) {
    }

    @Override
    public String prettyPrint(TimeUnit timeUnit) {
        if (0 == totalTimeNanos && startTimeNanos > 0) {
            stop();
        }
        return StrUtil.format("【计时器】{} [{}]: running time = {}({})", id, taskCount.get(), DateUtil.formatBetween(TimeUnit.NANOSECONDS.toMillis(totalTimeNanos)), totalTimeNanos);
    }
}
