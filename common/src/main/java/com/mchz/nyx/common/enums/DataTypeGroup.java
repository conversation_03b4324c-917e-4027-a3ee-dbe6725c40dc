package com.mchz.nyx.common.enums;

import java.sql.SQLType;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <p>
 * 数据类型
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/10/29 14:14
 */
public enum DataTypeGroup {
    /**
     * 未知
     */
    UNKNOWN,
    BINARY,
    BIT,
    CHARACTER,
    ID,
    INTEGER,
    REAL,
    REFERENCE,
    TEMPORAL,
    URL,
    XML,
    LARGE_OBJECT,
    OBJECT;

    public static DataTypeGroup parseTypeGroup(Integer key) {
        if (null == key) {
            return null;
        }
        return DataTypeGroup.valueOf(key);
    }

    public static DataTypeGroup valueOf(final SQLType type) {
        return valueOf(type.getVendorTypeNumber());
    }

    public static DataTypeGroup valueOf(final int type) {

        final DataTypeGroup typeGroup;
        switch (type) {
            case java.sql.Types.ARRAY:
            case java.sql.Types.DISTINCT:
            case java.sql.Types.JAVA_OBJECT:
            case java.sql.Types.OTHER:
            case java.sql.Types.STRUCT:
                typeGroup = DataTypeGroup.OBJECT;
                break;
            case java.sql.Types.BINARY:
            case java.sql.Types.LONGVARBINARY:
            case java.sql.Types.VARBINARY:
                typeGroup = DataTypeGroup.BINARY;
                break;
            case java.sql.Types.BIT:
            case java.sql.Types.BOOLEAN:
                typeGroup = DataTypeGroup.BIT;
                break;
            case java.sql.Types.CHAR:
            case java.sql.Types.LONGNVARCHAR:
            case java.sql.Types.LONGVARCHAR:
            case java.sql.Types.NCHAR:
            case java.sql.Types.NVARCHAR:
            case java.sql.Types.VARCHAR:
                typeGroup = DataTypeGroup.CHARACTER;
                break;
            case java.sql.Types.ROWID:
                typeGroup = DataTypeGroup.ID;
                break;
            case java.sql.Types.BIGINT:
            case java.sql.Types.INTEGER:
            case java.sql.Types.SMALLINT:
            case java.sql.Types.TINYINT:
                typeGroup = DataTypeGroup.INTEGER;
                break;
            case java.sql.Types.BLOB:
            case java.sql.Types.CLOB:
            case java.sql.Types.NCLOB:
                typeGroup = DataTypeGroup.LARGE_OBJECT;
                break;
            case java.sql.Types.DECIMAL:
            case java.sql.Types.DOUBLE:
            case java.sql.Types.FLOAT:
            case java.sql.Types.NUMERIC:
            case java.sql.Types.REAL:
                typeGroup = DataTypeGroup.REAL;
                break;
            case java.sql.Types.REF:
            case java.sql.Types.REF_CURSOR:
                typeGroup = DataTypeGroup.REFERENCE;
                break;
            case java.sql.Types.DATE:
            case java.sql.Types.TIME:
            case java.sql.Types.TIMESTAMP:
            case java.sql.Types.TIMESTAMP_WITH_TIMEZONE:
            case java.sql.Types.TIME_WITH_TIMEZONE:
                typeGroup = DataTypeGroup.TEMPORAL;
                break;
            case java.sql.Types.DATALINK:
                typeGroup = DataTypeGroup.URL;
                break;
            case java.sql.Types.SQLXML:
                typeGroup = DataTypeGroup.XML;
                break;
            default:
                typeGroup = DataTypeGroup.UNKNOWN;
                break;
        }
        return typeGroup;
    }

    private static final Map<String, DataTypeGroup> CACHE_MAP = new ConcurrentHashMap<>();

    static {
        for (DataTypeGroup type : values()) {
            CACHE_MAP.put(type.name(), type);
        }
    }

    public static DataTypeGroup getType(String name) {
        return CACHE_MAP.getOrDefault(name, UNKNOWN);
    }

    public static boolean isSimple(DataTypeGroup typeGroup) {
        switch (typeGroup) {
            case OBJECT:
            case LARGE_OBJECT:
            case BINARY:
            case XML:
            case UNKNOWN:
                return false;
            default:
                return true;
        }
    }
}
