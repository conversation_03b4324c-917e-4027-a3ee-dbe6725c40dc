package com.mchz.nyx.common.exception;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
public class NyxException extends RuntimeException {
    public NyxException() {
        super();
    }

    public NyxException(String message) {
        super(message);
    }

    public NyxException(String message, Throwable cause) {
        super(message, cause);
    }

    public NyxException(Throwable cause) {
        super(cause);
    }

    @Override
    public synchronized Throwable fillInStackTrace() {
        return this;
    }
}
