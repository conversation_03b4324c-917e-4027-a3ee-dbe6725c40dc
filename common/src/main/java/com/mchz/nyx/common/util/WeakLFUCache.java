package com.mchz.nyx.common.util;

import cn.hutool.cache.CacheListener;
import cn.hutool.cache.impl.CacheObj;
import cn.hutool.cache.impl.LFUCache;
import cn.hutool.core.lang.mutable.Mutable;
import cn.hutool.core.map.WeakConcurrentMap;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/9/29
 */
public class WeakLFUCache<K, V> extends LFUCache<K, V> {
    private static final long serialVersionUID = 1L;

    /**
     * 构造
     *
     * @param timeout 超时时常，单位毫秒，-1或0表示无限制
     */
    public WeakLFUCache(int capacity, long timeout) {
        super(capacity, timeout);
        cacheMap = new WeakConcurrentMap<>();
    }

    @Override
    public WeakLFUCache<K, V> setListener(CacheListener<K, V> listener) {
        super.setListener(listener);

        final WeakConcurrentMap<Mutable<K>, CacheObj<K, V>> map = (WeakConcurrentMap<Mutable<K>, CacheObj<K, V>>) this.cacheMap;
        map.setPurgeListener((key, value) -> listener.onRemove(null, value.getValue()));

        return this;
    }
}
