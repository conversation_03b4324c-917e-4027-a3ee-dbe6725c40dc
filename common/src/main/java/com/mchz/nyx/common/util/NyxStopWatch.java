package com.mchz.nyx.common.util;

import cn.hutool.core.util.StrUtil;
import org.slf4j.Logger;

import java.util.concurrent.TimeUnit;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/10
 */
public interface NyxStopWatch {

    String getId();

    void start();

    void stop();

    long[] startChild(String childName);

    void stopChild(long[] key);

    default String prettyPrint() {
        return prettyPrint(null);
    }

    String prettyPrint(TimeUnit timeUnit);

    static NyxStopWatch of(Logger log, String id) {
        if (log.isDebugEnabled()) {
            return new ChildStopWatch(id);
        }
        return new SingleStopWatch(id);
    }

    static NyxStopWatch of(Logger log) {
        return of(log, StrUtil.EMPTY);
    }
}
