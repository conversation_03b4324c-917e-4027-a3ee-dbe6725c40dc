package com.mchz.nyx.common.util;

import lombok.experimental.UtilityClass;

import java.util.concurrent.locks.LockSupport;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/10/9
 */
@UtilityClass
public class WaitUtil {
    private final int MAX_FULL_TIMES = 20;

    public void applyWait(int fullTimes) {
        if (fullTimes <= 3) {
            Thread.yield();
        } else {
            int newFullTimes = Math.min(fullTimes, MAX_FULL_TIMES);
            LockSupport.parkNanos(100 * 1000L * newFullTimes);
        }
    }
}
