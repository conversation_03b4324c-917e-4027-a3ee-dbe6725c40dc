package com.mchz.nyx.common.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.text.NumberFormat;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/10
 */
public class ChildStopWatch extends SingleStopWatch {

    private final Map<long[], TaskInfo> taskMap;
    private final Map<String, SummaryInfo> summaryMap;


    // ------------------------------------------------------------------------------------------- Constructor start

    /**
     * 构造，不启动任何任务
     *
     * @param id 用于标识秒表的唯一ID
     */
    ChildStopWatch(String id) {
        super(id);
        this.summaryMap = new ConcurrentHashMap<>();
        this.taskMap = new ConcurrentHashMap<>();
    }

    // ------------------------------------------------------------------------------------------- Constructor end

    @Override
    public long[] startChild(String childName) {
        long now = System.nanoTime();
        if (startTimeNanos <= 0) {
            startTimeNanos = now;
        }
        long[] key = {taskCount.incrementAndGet(), Thread.currentThread().getId()};
        taskMap.put(key, new TaskInfo(childName, now));
        return key;
    }

    @Override
    public void stopChild(long[] key) {
        long end = System.nanoTime();
        TaskInfo info = taskMap.remove(key);
        if (null == info) {
            throw new IllegalStateException("Can't stop StopWatch: it's not running");
        }
        summaryMap.computeIfAbsent(info.getTaskName(), k -> new SummaryInfo()).record(end - info.getStartNanos());
    }

    @Override
    public String prettyPrint(TimeUnit timeUnit) {
        TimeUnit unit;
        if (null == timeUnit) {
            unit = TimeUnit.NANOSECONDS;
        } else {
            unit = timeUnit;
        }

        final StringBuilder sb = new StringBuilder(super.prettyPrint(timeUnit));
        String lineSeparator = FileUtil.getLineSeparator();
        sb.append(lineSeparator);
        sb.append("---------------------------------------------------------").append(lineSeparator);
        sb.append("  %   Task name        detail           ").append(DateUtil.getShotName(unit)).append(lineSeparator);
        sb.append("---------------------------------------------------------").append(lineSeparator);
        final NumberFormat nf = NumberFormat.getNumberInstance();
        nf.setMinimumIntegerDigits(9);
        nf.setGroupingUsed(false);

        final NumberFormat pf = NumberFormat.getPercentInstance();
        pf.setMinimumIntegerDigits(2);
        pf.setGroupingUsed(false);
        String placeholder = "                 ";
        summaryMap.entrySet().stream().sorted(Map.Entry.comparingByValue()).forEach(v -> {
            long timeNanos = v.getValue().getTimeNanos();
            sb.append(pf.format((double) timeNanos / totalTimeNanos)).append("   ")
                .append(v.getKey()).append(placeholder, Math.min(v.getKey().length(), placeholder.length() - 2), placeholder.length())
                .append(DateUtil.formatBetween(TimeUnit.NANOSECONDS.toMillis(timeNanos))).append("(").append(v.getValue().getCount()).append(")").append("  ")
                .append(nf.format(unit.convert(timeNanos, TimeUnit.NANOSECONDS))).append(lineSeparator);
        });
        return sb.toString();
    }

    @Getter
    @AllArgsConstructor
    public static final class TaskInfo {
        private final String taskName;
        private final long startNanos;
    }

    public static final class SummaryInfo implements Comparable<SummaryInfo> {
        private final AtomicLong timeNanos;
        private final AtomicInteger count;

        public SummaryInfo() {
            this.timeNanos = new AtomicLong();
            this.count = new AtomicInteger();
        }

        public long getTimeNanos() {
            return timeNanos.get();
        }

        public int getCount() {
            return count.get();
        }

        public void record(long lastTime) {
            timeNanos.addAndGet(lastTime);
            count.incrementAndGet();
        }

        @Override
        public int compareTo(SummaryInfo o) {
            if (timeNanos.get() == o.timeNanos.get()) {
                return o.count.get() - count.get();
            }
            return Long.compare(o.timeNanos.get(), timeNanos.get());
        }
    }
}

