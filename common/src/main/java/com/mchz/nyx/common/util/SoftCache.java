package com.mchz.nyx.common.util;

import cn.hutool.cache.CacheListener;
import cn.hutool.cache.impl.CacheObj;
import cn.hutool.cache.impl.TimedCache;
import cn.hutool.core.lang.Opt;
import cn.hutool.core.lang.mutable.Mutable;
import cn.hutool.core.map.ReferenceConcurrentMap;
import cn.hutool.core.map.SafeConcurrentHashMap;
import cn.hutool.core.util.ReferenceUtil;

import java.lang.ref.Reference;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/9/29
 */
public class SoftCache<K, V> extends TimedCache<K, V> {
    private static final long serialVersionUID = 1L;

    /**
     * 构造
     *
     * @param timeout 超时时常，单位毫秒，-1或0表示无限制
     */
    public SoftCache(long timeout) {
        super(timeout, new ReferenceConcurrentMap<>(new SafeConcurrentHashMap<>(), ReferenceUtil.ReferenceType.SOFT));
    }

    @Override
    public SoftCache<K, V> setListener(CacheListener<K, V> listener) {
        super.setListener(listener);

        final ReferenceConcurrentMap<Mutable<K>, CacheObj<K, V>> map = (ReferenceConcurrentMap<Mutable<K>, CacheObj<K, V>>) this.cacheMap;
        map.setPurgeListener((key, value) -> listener.onRemove(Opt.ofNullable(key).map(Reference::get).map(Mutable::get).get(), value.getValue()));

        return this;
    }
}
