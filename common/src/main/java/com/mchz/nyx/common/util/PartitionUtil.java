package com.mchz.nyx.common.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.collection.PartitionIter;
import lombok.experimental.UtilityClass;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Function;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
@UtilityClass
public class PartitionUtil {
    public final int DEFAULT_PART_SIZE = 500;

    public <E> List<List<E>> part(List<E> collection) {
        return part(collection, DEFAULT_PART_SIZE);
    }

    public <E> void part(List<E> collection, Consumer<List<E>> consumer) {
        part(collection, DEFAULT_PART_SIZE).forEach(consumer);
    }

    public <E> List<List<E>> part(List<E> collection, int size) {
        return ListUtil.partition(collection, size);
    }

    public <E> void part(Iterable<E> collection, Consumer<List<E>> consumer) {
        part(collection, DEFAULT_PART_SIZE).forEach(consumer);
    }

    public <E> Iterable<List<E>> part(Iterable<E> iterable) {
        return part(iterable, DEFAULT_PART_SIZE);
    }

    public <E> Iterable<List<E>> part(Iterable<E> iterable, int size) {
        if (CollUtil.isEmpty(iterable)) {
            return Collections.emptyList();
        }
        return new PartitionIter<>(iterable.iterator(), size);
    }

    public <E, R> List<R> reduce(List<E> collection, Function<List<E>, List<R>> function) {
        return reduce(collection, DEFAULT_PART_SIZE, function);
    }

    public <E, R> List<R> reduce(List<E> collection, int size, Function<List<E>, List<R>> function) {
        if (CollUtil.isEmpty(collection)) {
            return new ArrayList<>(0);
        }
        List<R> result = new ArrayList<>(collection.size());
        ListUtil.partition(collection, size).forEach(v -> {
            List<R> apply = function.apply(v);
            result.addAll(apply);
        });
        return result;
    }

    public <E, R> List<R> fragment(List<E> collection, BiConsumer<List<E>, List<R>> function) {
        return fragment(collection, DEFAULT_PART_SIZE, function);
    }

    public <E, R> List<R> fragment(List<E> collection, int size, BiConsumer<List<E>, List<R>> function) {
        if (CollUtil.isEmpty(collection)) {
            return new ArrayList<>(0);
        }
        List<R> result = new ArrayList<>(collection.size());
        ListUtil.partition(collection, size).forEach(v -> function.accept(v, result));
        return result;
    }
}
