package com.mchz.nyx.common.util;

import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReferenceArray;

/**
 * <p>
 * 单生产者队列
 * </p>
 *
 * <AUTHOR>
 * @since 2025/1/6
 */
public class SingleProducerQueue<T> {
    private final AtomicReferenceArray<T> buffer;
    private final int capacity;
    private final AtomicInteger head;
    private volatile int tail;
    private final int mask;

    public SingleProducerQueue(int capacity) {
        if ((capacity & (capacity - 1)) != 0) {
            throw new IllegalArgumentException("Capacity must be a power of 2");
        }

        this.buffer = new AtomicReferenceArray<>(capacity);
        this.capacity = capacity;
        this.head = new AtomicInteger(0);
        this.tail = 0;
        this.mask = capacity - 1;
    }

    public boolean push(T item) {
        int nextTail = (tail + 1) & mask;

        if (nextTail == head.get()) {
            return false;
        }

        buffer.set(tail, item);
        tail = nextTail;

        return true;
    }

    public T poll() {
        int currentHead = head.get();

        if (currentHead == tail) {
            return null;
        }

        T item = buffer.get(currentHead);
        int nextHead = (currentHead + 1) & mask;

        if (head.compareAndSet(currentHead, nextHead)) {
            buffer.set(currentHead, null);
            return item;
        }

        return null;
    }

    public T peek() {
        int currentHead = head.get();

        if (currentHead == tail) {
            return null;
        }

        return buffer.get(currentHead);
    }

    public boolean isEmpty() {
        return head.get() == tail;
    }

    public boolean isFull() {
        int nextTail = (tail + 1) & mask;
        return nextTail == head.get();
    }

    public int size() {
        int h = head.get();
        int t = tail;
        if (t >= h) {
            return t - h;
        } else {
            return capacity - (h - t);
        }
    }
}
