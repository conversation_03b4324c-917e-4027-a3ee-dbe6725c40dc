package com.mchz.nyx.common.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.db.Db;
import cn.hutool.db.dialect.Dialect;
import cn.hutool.db.dialect.DriverUtil;
import cn.hutool.db.dialect.impl.*;
import cn.hutool.db.sql.Wrapper;
import lombok.experimental.UtilityClass;

import javax.sql.DataSource;

import java.util.function.Supplier;

import static cn.hutool.db.dialect.DriverNamePool.*;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/10
 */
@UtilityClass
public class DbDialectFactory {

    public Db use(DataSource ds) {
        return Db.use(ds, newDialect(ds));
    }

    public Wrapper getWrapper(String driverName) {
        return newDialect(driverName).getWrapper();
    }

    public Dialect newDialect(DataSource ds) {
        return newDialect(DriverUtil.identifyDriver(ds));
    }

    public Dialect newDialect(String driverName) {
        if (StrUtil.isNotBlank(driverName)) {
            if (DRIVER_MYSQL.equalsIgnoreCase(driverName) || DRIVER_MYSQL_V6.equalsIgnoreCase(driverName)) {
                return new MysqlDialect();
            } else if (DRIVER_ORACLE.equalsIgnoreCase(driverName) || DRIVER_ORACLE_OLD.equalsIgnoreCase(driverName)) {
                return buildDialect(OracleDialect::new);
            } else if (DRIVER_SQLLITE3.equalsIgnoreCase(driverName)) {
                return new Sqlite3Dialect();
            } else if (DRIVER_POSTGRESQL.equalsIgnoreCase(driverName)) {
                return new PostgresqlDialect();
            } else if (DRIVER_H2.equalsIgnoreCase(driverName)) {
                return new H2Dialect();
            } else if (DRIVER_SQLSERVER.equalsIgnoreCase(driverName)) {
                return new SqlServer2012Dialect();
            } else if (DRIVER_PHOENIX.equalsIgnoreCase(driverName)) {
                return new PhoenixDialect();
            } else if (DRIVER_DM7.equalsIgnoreCase(driverName)) {
                return buildDialect(DmDialect::new);
            }
        }
        // 无法识别可支持的数据库类型默认使用ANSI方言，可兼容大部分SQL语句
        return buildDialect(AnsiSqlDialect::new);
    }

    private Dialect buildDialect(Supplier<Dialect> dialectSupplier) {
        Dialect dialect = dialectSupplier.get();
        dialect.getWrapper().setPreWrapQuote('"');
        dialect.getWrapper().setSufWrapQuote('"');
        return dialect;
    }
}
