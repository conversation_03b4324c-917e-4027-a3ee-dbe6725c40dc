<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.mchz.nyx</groupId>
    <artifactId>metis</artifactId>
    <version>1.3.0</version>
  </parent>

  <artifactId>metis-engine</artifactId>

  <dependencies>
    <dependency>
      <groupId>com.mchz.base.tools</groupId>
      <artifactId>launcher</artifactId>
    </dependency>
    <dependency>
      <groupId>com.mchz.nyx</groupId>
      <artifactId>metis-core-engine</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.mchz.nyx</groupId>
      <artifactId>common</artifactId>
    </dependency>
    <!-- 数据源驱动 start-->
    <dependency>
      <groupId>org.postgresql</groupId>
      <artifactId>postgresql</artifactId>
    </dependency>
    <dependency>
      <groupId>com.dameng</groupId>
      <artifactId>DmJdbcDriver18</artifactId>
      <version>8.1.2.141</version>
    </dependency>
    <!-- 数据源驱动 end-->
    <dependency>
      <groupId>com.github.mengweijin</groupId>
      <artifactId>db-migration</artifactId>
      <version>2.0.8</version>
    </dependency>
    <dependency>
      <groupId>org.flywaydb</groupId>
      <artifactId>flyway-core</artifactId>
      <version>${flyway.version}</version>
    </dependency>
    <dependency>
      <groupId>org.flywaydb</groupId>
      <artifactId>flyway-mysql</artifactId>
      <version>${flyway.version}</version>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-dependency-plugin</artifactId>
        <executions>
          <execution>
            <id>copy-dependencies</id>
            <phase>package</phase>
            <goals>
              <goal>copy-dependencies</goal>
            </goals>
            <configuration>
              <outputDirectory>${project.build.directory}/lib</outputDirectory>
              <overWriteReleases>true</overWriteReleases>
              <overWriteSnapshots>true</overWriteSnapshots>
              <overWriteIfNewer>true</overWriteIfNewer>
              <excludeArtifactIds>lombok,metis-core</excludeArtifactIds>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <version>3.2.0</version>
        <configuration>
          <archive>
            <manifest>
              <addClasspath>true</addClasspath>
              <classpathPrefix>lib/</classpathPrefix>
              <useUniqueVersions>false</useUniqueVersions>
            </manifest>
          </archive>
          <outputDirectory>${project.build.directory}</outputDirectory>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
