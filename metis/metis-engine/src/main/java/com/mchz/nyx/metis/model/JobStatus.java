package com.mchz.nyx.metis.model;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/6/23
 */
@Getter
@AllArgsConstructor
public enum JobStatus {
    PENDING(0, "待处理"),
    RUNNING(1, "执行中"),
    SUCCESS(2, "已完成"),
    FAILED(3, "失败"),
    CANCELED(4, "已取消"),
    TIMEOUT(5, "超时"),
    RETRYING(6, "重试中"),
    PAUSED(7, "已暂停");

    private final int code;
    private final String description;
}
