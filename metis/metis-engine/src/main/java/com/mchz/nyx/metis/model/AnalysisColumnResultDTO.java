package com.mchz.nyx.metis.model;

import com.mchz.base.metis.AnalysisColumnResult;
import lombok.Data;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/8/13
 */
@Data
public class AnalysisColumnResultDTO implements AnalysisColumnResult {
    private String dsUid;
    private String catalog;
    private String schema;
    private String tableName;
    private String columnName;

    private String tableComment;
    private String columnComment;

    private String columnType;

    private Long bizId;

    private String bizName;
    private Integer sensitive;
    private Integer level;
    private String levelName;
    private List<String> types;
}
