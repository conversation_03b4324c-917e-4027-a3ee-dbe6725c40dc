package com.mchz.nyx.metis;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.Db;
import cn.hutool.db.dialect.Dialect;
import cn.hutool.db.dialect.DialectFactory;
import cn.hutool.db.dialect.DialectName;
import cn.hutool.db.ds.pooled.DbConfig;
import cn.hutool.db.ds.pooled.PooledDataSource;
import cn.hutool.system.SystemUtil;
import com.mchz.base.metis.*;
import com.mchz.base.metis.common.MetisConst;
import com.mchz.base.metis.model.JdbcConfig;
import com.mchz.base.tools.launcher.Launcher;
import com.mchz.nyx.common.exception.NyxException;
import com.mchz.nyx.common.util.DbDialectFactory;
import com.mchz.nyx.metis.service.AnalysisService;
import com.mchz.nyx.metis.service.ClassificationApiService;
import com.mchz.nyx.metis.service.DefaultJobManager;
import com.mchz.nyx.metis.service.JobManagerService;
import com.mchz.nyx.metis.util.Func;
import org.flywaydb.core.Flyway;
import org.flywaydb.core.api.configuration.FluentConfiguration;

import javax.sql.DataSource;
import java.io.BufferedReader;
import java.io.IOException;
import java.lang.reflect.Method;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;
import java.util.function.Supplier;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/8/16
 */
public class MetisApiLoader implements CustomApiLoader {
    private static final String ENGINE_CLASS = "com.mchz.nyx.pipeline.embed.MetisEngineProxy";
    private static final String APP_HOME = SystemUtil.get(MetisConst.APP_HOME);

    private volatile DataSource ds;
    private volatile MetisEngine engine;

    private JdbcConfig sourceConfig;

    @Override
    public void setSourceConfig(JdbcConfig config) {
        sourceConfig = config;
    }

    @Override
    public JobManager getJobManager() {
        return apply(() -> {
            MetisEngine engine = getMetisEngine();
            Db db = DbDialectFactory.use(getDatasource(engine));
            return new DefaultJobManager(engine, new AnalysisService(db, buildClassificationApi(engine)), new JobManagerService(db));
        });
    }

    @Override
    public ClassificationApi getClassificationApi() {
        return apply(() -> buildClassificationApi(getMetisEngine()));
    }

    private ClassificationApiService buildClassificationApi(MetisEngine engine) {
        Db db = DbDialectFactory.use(engine.getContentDatasource());
        return new ClassificationApiService(db);
    }

    private <T> T apply(Supplier<T> supplier) {
        ClassLoader loader = Thread.currentThread().getContextClassLoader();
        try {
            Thread.currentThread().setContextClassLoader(MetisApiLoader.class.getClassLoader());
            return supplier.get();
        } finally {
            Thread.currentThread().setContextClassLoader(loader);
        }
    }

    private synchronized DataSource getDatasource(MetisEngine engine) {
        if (null == ds) {
            if (null == engine) {
                ds = initDataSource();
            } else {
                DataSource tmp = engine.getDatasource();
                withFlyway(tmp, null, engine.getSchema());
                ds = tmp;
            }
        }
        return ds;
    }

    private synchronized MetisEngine getMetisEngine() {
        if (null == engine) {
            String appPath = APP_HOME + AthenaConst.ENGINE_PATH;
            if (!FileUtil.isFile(appPath)) {
                throw new NyxException("app load error :" + appPath);
            }
            ClassLoader loader = Thread.currentThread().getContextClassLoader();
            // ,"spring.jmx.default-domain=com.mchz.nyx"
            List<String> args = new ArrayList<>(6);
            args.add("--spring.main.web-application-type=none");
            args.add("--spring.config.location=classpath:/");
            if (null != sourceConfig) {
                args.add("--sourcedata.jdbc.url=" + sourceConfig.getUrl());
                args.add("--sourcedata.jdbc.password=" + sourceConfig.getPassword());
                args.add("--sourcedata.jdbc.username=" + sourceConfig.getUsername());
                args.add("--sourcedata.jdbc.driver-class-name=" + ObjUtil.defaultIfNull(sourceConfig.getDriverClassName(), () -> DialectFactory.identifyDriver(sourceConfig.getUrl())));
                sourceConfig = null;
            }
            try {
                Launcher.load(appPath, "jar", args.toArray(new String[0]), loader, MetisEngine.class.getPackage().getName(), "dm.jdbc.", "org.postgresql.");
                ClassLoader cl = Thread.currentThread().getContextClassLoader();
                Class<?> clazz = cl.loadClass(ENGINE_CLASS);
                Method method = ReflectUtil.getMethod(clazz, "engine");
                engine = ReflectUtil.invokeStatic(method);
            } catch (Exception e) {
                throw new NyxException(e);
            } finally {
                Thread.currentThread().setContextClassLoader(loader);
            }
        }
        return engine;
    }

    private DataSource initDataSource() {
        DbConfig config;
        String schema;
        if (null != sourceConfig) {
            config = new DbConfig();
            config.setUrl(sourceConfig.getUrl());
            config.setUser(sourceConfig.getUsername());
            config.setPass(sourceConfig.getPassword());
            config.setDriver(sourceConfig.getDriverClassName());
            schema = null;
        } else {
            Properties properties = new Properties();
            try (BufferedReader reader = FileUtil.getReader(MetisApiLoader.APP_HOME + "/config/sourcedata.properties", StandardCharsets.UTF_8)) {
                properties.load(reader);
            } catch (IOException e) {
                throw new NyxException("加载sourcedata.properties文件失败," + e.getMessage());
            }
            config = new DbConfig();
            config.setUrl(properties.getProperty("sourcedata.jdbc.url"));
            config.setUser(properties.getProperty("sourcedata.jdbc.username"));
            config.setPass(properties.getProperty("sourcedata.jdbc.password"));
            config.setDriver(properties.getProperty("sourcedata.jdbc.driver-class-name"));
            schema = properties.getProperty("mybatis-plus.global-config.db-config.schema");
        }
        config.setInitialSize(1);
        config.setMaxActive(10);
        DataSource tmp = new PooledDataSource(config);
        withFlyway(tmp, DbDialectFactory.newDialect(config.getDriver()), schema);
        return tmp;
    }

    private void withFlyway(DataSource dataSource, Dialect dialect, String schema) {
        if (null == dialect) {
            dialect = DialectFactory.getDialect(dataSource);
        }
        String location;
        FluentConfiguration configure = Flyway.configure(MetisApiLoader.class.getClassLoader());
        if (DialectName.MYSQL.name().equals(dialect.dialectName())) {
            location = "metis/migration/mysql";
        } else if (DialectName.POSTGRESQL.name().equals(dialect.dialectName())) {
            location = "metis/migration/postgresql";
        } else if (DialectName.DM.name().equals(dialect.dialectName())) {
            location = "metis/migration/dm";
            configure.baselineOnMigrate(true);
            configure.baselineVersion("1.2.2");
        } else {
            return;
        }
        configure.locations(location);
        if (StrUtil.isNotEmpty(schema)) {
            configure.schemas(schema);
            Func.setSchema(schema);
        }
        Flyway flyway = configure.dataSource(dataSource).load();
        flyway.migrate();
    }
}
