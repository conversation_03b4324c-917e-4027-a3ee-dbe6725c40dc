package com.mchz.nyx.metis.entity;

import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
@Data
public class DbmetaColumn {
    /**
     * 主键
     */
    private Long oid;

    /**
     * 数据源ID
     */
    private Long sourceId;
    /**
     * schema
     */
    private Long schemaId;
    /**
     * 所属表格
     */
    private Long tableId;
    /**
     * 注释
     */
    private String comment;
    /**
     * 原始类型
     */
    private String dataType;

    /**
     * 类型分组
     */
    private String typeGroup;

    /**
     * 长度
     */
    private Long length;

    /**
     * 精度
     */
    private Integer precision;

    /**
     * 刻度
     */
    private Integer scale;

    /**
     * 是否可以为空
     */
    private Boolean nullable;

    /**
     * 位置
     */
    private Integer position;

    /**
     * 是否自增
     */
    private Boolean isAutoIncremented;

    /**
     * 是否生成
     */
    private Boolean isGenerated;

    /**
     * 是否主键
     */
    private Boolean isPk;

    /**
     * 是否唯一键
     */
    private Boolean isUnique;

    /**
     * 是否索引
     */
    private Boolean isIndex;

    /**
     * 是否外键
     */
    private Boolean isFk;
}
