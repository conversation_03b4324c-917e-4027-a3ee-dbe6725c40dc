package com.mchz.nyx.metis.service;

import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import com.mchz.base.metis.Options;
import com.mchz.base.metis.StandardService;
import com.mchz.nyx.metis.entity.AnalysisColumn;
import com.mchz.nyx.metis.entity.ClassifyData;
import com.mchz.nyx.metis.model.*;
import com.mchz.nyx.metis.util.Func;
import lombok.AllArgsConstructor;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/8/15
 */
@AllArgsConstructor
public class AnalysisService {
    private final Db db;
    private final ClassificationApiService apiService;

    public AnalysisResultsDTO fetchResult(String dsUid, Long planId, Long jobId, boolean custom, String tenantId, Options options, StandardService standardService) {
        Map<Long, ClassifyData> map = custom ? null : apiService.listClassifyData().stream().collect(Collectors.toMap(ClassifyData::getArtifactId, Function.identity()));
        AnalysisColumn analysisColumn = new AnalysisColumn();
        AnalysisResultsDTO analysisResults = new AnalysisResultsDTO();
        if (null != planId) {
            analysisColumn.setPlanId(planId);
        } else if (null != jobId) {
            analysisColumn.setJobId(jobId);
        } else {
            return analysisResults;
        }
        if (options.isRecognized()) {
            analysisColumn.setStatus(3);
        }
        Entity q = Entity.create(Func.getTableName(AnalysisColumn.class)).parseBean(analysisColumn, true, true);
        AbstractDataLoadHandler handler;
        boolean withComment = options.isWithComment();
        if (custom) {
            if (options.isWithClassify()) {
                handler = new DataLoadAbundantHandler(db, q, dsUid, withComment, tenantId, standardService);
            } else {
                handler = new DataLoadSuccinctHandler(db, q, dsUid, withComment);
            }
        } else {
            handler = new DataLoadStdHandler(db, q, dsUid, withComment, map);
        }
        FunList<AnalysisColumnResultDTO> analysisColumns = new FunList<>(handler);
        analysisResults.setColumns(analysisColumns);
        return analysisResults;
    }
}
