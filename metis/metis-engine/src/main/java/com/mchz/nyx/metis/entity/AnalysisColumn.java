package com.mchz.nyx.metis.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 抽样数据分析
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-27
 */
@Data
public class AnalysisColumn implements Serializable {
    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 配置id
     */
    private Long planId;

    /**
     * 执行作业id
     */
    private Long jobId;

    /**
     * 数据源id
     */
    private Long sourceId;

    /**
     * schema元数据id
     */
    private Long schemaId;

    /**
     * table元数据id
     */
    private Long tableId;

    /**
     * column元数据id
     */
    private Long columnId;

    /**
     * schema
     */
    private String schemaName;

    /**
     * 表名
     */
    private String tableName;

    /**
     * 列名
     */
    private String columnName;

    /**
     * 业务类型，字典表中业务类型id
     */
    private Long bizId;

    /**
     * @link com.mchz.nyx.pipeline.entity.AnalysisColumn
     */
    private Integer status;
}
