package com.mchz.nyx.metis.model;

import lombok.AllArgsConstructor;
import lombok.NonNull;

import java.util.Iterator;
import java.util.function.Function;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/8/20
 */
@AllArgsConstructor
public class FunList<T> implements Iterable<T> {
    private final Function<Long, PageData<T>> fun;

    @NonNull
    @Override
    public Iterator<T> iterator() {
        return new FunListIterator();
    }

    private class FunListIterator implements Iterator<T> {
        private Long lastId;
        private Iterator<T> data;
        private boolean end;

        @Override
        public boolean hasNext() {
            if (end) {
                return false;
            }
            if (null == data || !data.hasNext()) {
                PageData<T> res = fun.apply(lastId);
                if (null == res) {
                    end = true;
                    return false;
                }
                data = res.iterator();
                lastId = res.getLastId();
            }
            return true;
        }

        @Override
        public T next() {
            if (hasNext()) {
                return data.next();
            }
            return null;
        }
    }
}
