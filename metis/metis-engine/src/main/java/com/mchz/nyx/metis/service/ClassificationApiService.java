package com.mchz.nyx.metis.service;

import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import com.mchz.base.metis.ClassificationApi;
import com.mchz.base.metis.model.StdInfo;
import com.mchz.nyx.metis.entity.Artifact;
import com.mchz.nyx.metis.entity.ClassifyData;
import com.mchz.nyx.metis.model.BizTermDTO;
import com.mchz.nyx.metis.model.BizTermRepoDTO;
import com.mchz.nyx.metis.util.Func;
import lombok.AllArgsConstructor;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/8/13
 */
@AllArgsConstructor
public class ClassificationApiService implements ClassificationApi {
    private final Db db;

    @Override
    public List<StdInfo> getClassificationStandards() {
        return Collections.singletonList(new StdInfo(1L, "个人信息"));
    }

    @Override
    public BizTermRepoDTO getAllBizTerms() {
        List<BizTermDTO> list = listArtifact().stream().map(v -> new BizTermDTO(v.getName(), v.getDescription())).collect(Collectors.toList());
        BizTermRepoDTO bizTermRepo = new BizTermRepoDTO();
        bizTermRepo.setList(list);
        return bizTermRepo;
    }

    public List<Artifact> listArtifact() {
        Entity q = Entity.create(Func.getTableName2(Artifact.class)).set(Func.nameUlc(Artifact::getArtifactType), "data_class");
        try {
            return db.findAll(q, Artifact.class);
        } catch (SQLException e) {
            return new ArrayList<>(0);
        }
    }

    public List<ClassifyData> listClassifyData() {
        Entity q = Entity.create(Func.getTableName2(ClassifyData.class));
        try {
            return db.findAll(q, ClassifyData.class);
        } catch (SQLException e) {
            return new ArrayList<>(0);
        }
    }
}
