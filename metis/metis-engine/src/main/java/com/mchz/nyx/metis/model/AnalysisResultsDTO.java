package com.mchz.nyx.metis.model;

import com.mchz.base.metis.AnalysisColumnResult;
import com.mchz.base.metis.AnalysisResults;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/8/13
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AnalysisResultsDTO implements AnalysisResults {
    private Iterable<? extends AnalysisColumnResult> columns;
}
