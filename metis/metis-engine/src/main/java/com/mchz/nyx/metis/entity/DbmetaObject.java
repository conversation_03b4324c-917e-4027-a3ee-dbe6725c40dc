package com.mchz.nyx.metis.entity;

import lombok.Data;

import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date 2022/1/10 14:06
 */
@Data
public class DbmetaObject {
    /**
     * 主键
     */
    private Long oid;

    /**
     * 数据源主键
     */
    private Long sourceId;

    /**
     * 名称
     */
    private String name;

    /**
     * 类型
     */
    private String type;

    /**
     * 描述
     */
    private String description;

    /**
     * 删除标记
     */
    private Boolean deleted;

    /**
     * 父主键
     */
    private Long pid;
}
