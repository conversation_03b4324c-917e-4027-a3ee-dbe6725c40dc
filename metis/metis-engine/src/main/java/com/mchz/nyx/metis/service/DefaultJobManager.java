package com.mchz.nyx.metis.service;


import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import com.mchz.base.metis.*;
import com.mchz.base.metis.exception.MetisApiException;
import com.mchz.base.metis.exception.MetisJobException;
import com.mchz.base.metis.model.TrafficParam;
import com.mchz.base.metis.model.TrafficResult;
import com.mchz.base.metis.util.concurrent.FailureCallback;
import com.mchz.base.metis.util.concurrent.SuccessCallback;
import com.mchz.nyx.common.exception.NyxException;
import com.mchz.nyx.metis.entity.SysJob;
import com.mchz.nyx.metis.entity.SysPlan;
import com.mchz.nyx.metis.model.AnalysisResultsDTO;
import com.mchz.nyx.metis.model.JobStatus;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.function.BiConsumer;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/8/13
 */

@Slf4j
public class DefaultJobManager implements JobManager, BiConsumer<Long, FailureInfo> {
    private final MetisEngine engine;
    private final AnalysisService analysisService;
    private final JobManagerService jobManagerService;

    private boolean customStd;
    private Executor callbackExecutor;
    private SuccessCallback<String> successCallback;
    private FailureCallback<String> failureCallback;

    private StandardService standardService;

    public DefaultJobManager(MetisEngine engine, AnalysisService analysisService, JobManagerService jobManagerService) {
        this.engine = engine;
        this.analysisService = analysisService;
        this.jobManagerService = jobManagerService;
        this.customStd = false;
        engine.register(this);
    }

    @Override
    public void accept(Long jobId, FailureInfo info) {
        processCallback(jobId, info);
        processPendingJobs();
    }

    @Override
    public void setCallbackDefaultExecutor(@NonNull Executor executor) {
        callbackExecutor = executor;
    }

    @Override
    public void register(SuccessCallback<String> successCallback, FailureCallback<String> failureCallback) {
        boolean first = null == this.failureCallback;
        this.successCallback = successCallback;
        this.failureCallback = null == failureCallback ? (result, ex) -> {} : failureCallback;
        if (first) {
            for (SysJob job : jobManagerService.getRunningJobs()) {
                try {
                    processCallback(job.getId(), new FailureInfo("任务未完成", null));
                    jobManagerService.updateJobStatus(job.getId(), JobStatus.FAILED);
                } catch (Exception e) {
                    log.error("{}({})任务状态更新失败", job.getId(), job.getDsUid());
                }
            }
        }

    }

    @Override
    public void register(StandardService standardService, RangeService rangeService) {
        this.customStd = true;
        this.standardService = standardService;
        engine.register(standardService);
        engine.register(rangeService);
    }

    @Override
    public void unregister() {
        successCallback = null;
        failureCallback = (result, ex) -> {};
    }

    @Override
    public String submit(DatasourceMeta source, JobConfig config) {
        if (null == source) {
            throw new NyxException("source not null");
        }
        if (null == config) {
            throw new NyxException("config not null");
        }
        if (null != config.getTenantId() && config.getTenantId().contains(StrUtil.SPACE)) {
            throw new NyxException("tenantId no Spaces");
        }
        if (null == source.getDsUid()) {
            throw new NyxException("dsUid cannot be null");
        }
        if (source.getDsUid().length() > 100) {
            throw new NyxException("dsUid length limit 100");
        }
        if (source.getDsUid().contains(StrUtil.SPACE)) {
            throw new NyxException("dsUid no Spaces");
        }
        if (null == source.getPluginId()) {
            throw new NyxException("pluginId cannot be null");
        }
        if (null != config.getExtInfo() && config.getExtInfo().length() > 200) {
            throw new NyxException("WorkflowId length limit 200");
        }
        if (null == config.getStdId()) {
            config.setStdId(1L);
        }
        Long sourceId = jobManagerService.bindSourceId(config.getTenantId(), source.getDsUid(), source);
        Long planId = jobManagerService.bindPlanId(config, sourceId, customStd);
        Long jobId = jobManagerService.generatedJobId(planId, source.getDsUid(), config.getExtInfo());
        try {
            engine.submit(sourceId, source, planId, jobId, config);
            jobManagerService.updateJobStatus(jobId, JobStatus.RUNNING);
        } catch (Exception e) {
            if (checkError(e)) {
                log.warn("{} 任务未启动,{}", source.getDsUid(), e.getMessage());
                processPendingJobs();
            } else {
                throw new MetisApiException(e.getMessage());
            }
        }
        return jobManagerService.getUUid(jobId);
    }

    @Override
    public void interrupt(String id) {
        engine.interrupt(jobManagerService.getJobId(id));
    }

    @Override
    public AnalysisResults fetchResult(String id, Options options) {
        SysJob job = jobManagerService.getJob(id);
        if (null == job) {
            return new AnalysisResultsDTO(Collections.emptyList());
        }
        SysPlan config = jobManagerService.getPlanConfig(job.getPlanId(), false);
        boolean custom = Boolean.TRUE.equals(config.getCustom());
        if (null == options) {
            options = custom ? Options.succinct() : Options.abundant();
        }
        return analysisService.fetchResult(job.getDsUid(), job.getPlanId(), null, custom, config.getTenantId(), options, standardService);
    }

    @Override
    public String getExtInfo(String id) {
        SysJob job = jobManagerService.getJob(id);
        return null != job ? job.getExtInfo() : null;
    }

    @Override
    public String getTenant(String id) {
        return jobManagerService.getTenant(id);
    }

    @Override
    public List<TrafficResult> trafficClassify(List<TrafficParam> req, JobConfig config) {
        return engine.trafficClassify(req, config);
    }

    @Override
    public void releaseTrafficClassify(JobConfig config) {
        engine.release(config);
    }

    private void processCallback(Long jobId, FailureInfo info) {
        String id = jobManagerService.getUUid(jobId);
        if (null == info) {
            jobManagerService.updateJobStatus(jobId, JobStatus.SUCCESS);
            if (null == successCallback) {
                return;
            }
            if (null == id) {
                throw new NyxException("job finish with " + jobId);
            }
            if (null == callbackExecutor) {
                CompletableFuture.runAsync(() -> successCallback.onSuccess(id));
            } else {
                CompletableFuture.runAsync(() -> successCallback.onSuccess(id), callbackExecutor);
            }
        } else {
            jobManagerService.updateJobStatus(jobId, JobStatus.FAILED);
            if (null == failureCallback) {
                return;
            }
            MetisJobException error = new MetisJobException(info.getMsg(), info.getRemark());
            if (null == callbackExecutor) {
                CompletableFuture.runAsync(() -> failureCallback.onFailure(id, error));
            } else {
                CompletableFuture.runAsync(() -> failureCallback.onFailure(id, error), callbackExecutor);
            }
        }
    }

    private void processPendingJobs() {
        for (SysJob job : jobManagerService.getPendingJobs()) {
            SysPlan plan;
            JobConfig jobConfig;
            DataSourceParam dataSource;
            try {
                plan = jobManagerService.getPlanConfig(job.getPlanId(), true);
                jobConfig = jobManagerService.analysisJobConfig(plan);
                dataSource = jobManagerService.getDataSource(plan);
            } catch (Exception e) {
                log.error("Fatal error processing job {}. Stopping batch processing.", job.getId(), e);
                break;
            }
            try {
                engine.submit(plan.getSourceId(), dataSource, job.getPlanId(), job.getId(), jobConfig);
            } catch (NyxException e) {
                if (checkError(e)) {
                    log.warn("任务未启动J{}(P{}-S{}),{}", job.getId(), job.getPlanId(), plan.getSourceId(), e.getMessage());
                    break;
                } else {
                    processCallback(job.getId(), new FailureInfo(e.getMessage(), null));
                }
            }
        }
    }

    private boolean checkError(Exception e) {
        if ("ApiException".equals(e.getClass().getSimpleName())) {
            try {
                int code = (int) ReflectUtil.getFieldValue(e, "code");
                return code > 20000 && code < 20010;
            } catch (Exception ignore) {
                return false;
            }
        }
        return false;
    }
}
