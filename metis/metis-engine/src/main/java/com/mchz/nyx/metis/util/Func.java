package com.mchz.nyx.metis.util;

import cn.hutool.cache.Cache;
import cn.hutool.cache.CacheUtil;
import cn.hutool.core.lang.func.Func1;
import cn.hutool.core.lang.func.LambdaUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.symmetric.AES;
import cn.hutool.json.JSONUtil;
import com.mchz.base.metis.DataSourceParam;
import com.mchz.base.metis.DatasourceMeta;
import lombok.experimental.UtilityClass;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Locale;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/8/16
 */
@UtilityClass
public class Func {
    private final Cache<Class<?>, String> TABLE_NAME_CACHE = CacheUtil.newWeakCache(43200000);
    private final AES AES = SecureUtil.aes("6247FBA1101D367A".getBytes(StandardCharsets.UTF_8));
    private String SCHEMA = " ";

    public void setSchema(String schema) {
        SCHEMA = " " + schema + ".";
        TABLE_NAME_CACHE.clear();
    }

    public String getTableName(Class<?> clazz) {
        return TABLE_NAME_CACHE.get(clazz, () -> SCHEMA + StrUtil.toUnderlineCase(clazz.getSimpleName()));
    }

    public String getTableName2(Class<?> clazz) {
        return TABLE_NAME_CACHE.get(clazz, () -> StrUtil.toUnderlineCase(clazz.getSimpleName())).toUpperCase(Locale.ROOT);
    }

    public <T> String name(Func1<T, ?> fun) {
        return LambdaUtil.getFieldName(fun);
    }

    @SafeVarargs
    public <T> String[] names(Func1<T, ?>... functions) {
        return Arrays.stream(functions).map(LambdaUtil::getFieldName).toArray(String[]::new);
    }

    public <T> String nameUlc(Func1<T, ?> fun) {
        String name = LambdaUtil.getFieldName(fun);
        return StrUtil.toUnderlineCase(name);
    }

    @SafeVarargs
    public <T> String[] namesUlc(Func1<T, ?>... functions) {
        return Arrays.stream(functions).map(Func::nameUlc).toArray(String[]::new);
    }

    public String encode(int version, DatasourceMeta source) {
        if (1 != version) {
            throw new IllegalArgumentException("version");
        }
        String jsonStr = JSONUtil.toJsonStr(new DataSourceParam(source));
        return AES.encryptBase64(jsonStr);
    }

    public DataSourceParam decode(int version, String jsonStr) {
        if (1 != version) {
            throw new IllegalArgumentException("version");
        }
        return JSONUtil.toBean(AES.decryptStr(jsonStr), DataSourceParam.class);
    }

}
