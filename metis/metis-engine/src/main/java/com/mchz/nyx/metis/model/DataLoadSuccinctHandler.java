package com.mchz.nyx.metis.model;

import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import com.mchz.nyx.metis.entity.AnalysisColumn;

import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/2/20
 */
public class DataLoadSuccinctHandler extends AbstractDataLoadHandler {
    private final boolean withComment;

    public DataLoadSuccinctHandler(Db db, Entity query, String dsUid, boolean withComment) {
        super(db, query, dsUid);
        this.withComment = withComment;
    }

    @Override
    protected List<AnalysisColumnResultDTO> buildResult(String dsUid, List<AnalysisColumn> list) throws SQLException {
        List<Long> columnIds = new ArrayList<>(list.size());
        List<Long> ids = new ArrayList<>(list.size());
        list.forEach(v -> {
            columnIds.add(v.getColumnId());
            ids.add(v.getColumnId());
            ids.add(v.getTableId());
        });
        Map<Long, String> typeMap = getColumnTypeMap(columnIds);
        Map<Long, String> commentMap = withComment ? getObjComment(ids) : null;
        return list.stream().map(v -> buildColumnResult(v, typeMap, commentMap)).collect(Collectors.toList());
    }
}
