package com.mchz.nyx.metis.model;

import cn.hutool.cache.Cache;
import cn.hutool.cache.CacheUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import cn.hutool.db.Page;
import cn.hutool.db.handler.BeanListHandler;
import cn.hutool.db.sql.Order;
import com.mchz.base.metis.AthenaConst;
import com.mchz.nyx.common.exception.NyxException;
import com.mchz.nyx.metis.entity.AnalysisColumn;
import com.mchz.nyx.metis.entity.DbmetaColumn;
import com.mchz.nyx.metis.entity.DbmetaObject;
import com.mchz.nyx.metis.entity.DbmetaSchema;
import com.mchz.nyx.metis.util.Func;

import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/2/20
 */
public abstract class AbstractDataLoadHandler implements Function<Long, PageData<AnalysisColumnResultDTO>> {
    private final String id = Func.nameUlc(AnalysisColumn::getId);
    private final String oid = Func.nameUlc(DbmetaSchema::getOid);
    final Db db;

    private final Entity query;
    private final String dsUid;

    private final Cache<Long, DbmetaSchema> cache;
    private final String st;
    private final String[] fields;

    public AbstractDataLoadHandler(Db db, Entity query, String dsUid) {
        this.db = db;
        this.query = query;
        this.dsUid = dsUid;
        this.cache = CacheUtil.newFIFOCache(100, AthenaConst.CACHE_TIMEOUT);
        this.st = Func.getTableName(DbmetaSchema.class);
        this.fields = new String[]{Func.nameUlc(DbmetaSchema::getCatalog), Func.nameUlc(DbmetaSchema::getSchema)};
    }

    @Override
    public PageData<AnalysisColumnResultDTO> apply(Long lastId) {
        if (null != lastId) {
            query.set(id, "> " + lastId);
        }
        try {
            List<AnalysisColumn> list = db.page(query, new Page(0, 5000, new Order(id)), BeanListHandler.create(AnalysisColumn.class));
            if (CollUtil.isEmpty(list)) {
                return null;
            }
            return new PageData<>(CollUtil.getLast(list).getId(), buildResult(dsUid, list));
        } catch (SQLException e) {
            throw new NyxException(e);
        }
    }

    protected abstract List<AnalysisColumnResultDTO> buildResult(String dsUid, List<AnalysisColumn> columns) throws SQLException;

    protected Map<Long, String> getColumnTypeMap(List<Long> columnIds) throws SQLException {
        Map<Long, String> typeMap = new HashMap<>(columnIds.size());
        db.find(Entity.create(Func.getTableName(DbmetaColumn.class)).set(Func.nameUlc(DbmetaColumn::getOid), columnIds), rs -> {
            while (rs.next()) {
                typeMap.put(rs.getLong(1), rs.getString(2));
            }
            return null;
        }, Func.namesUlc(DbmetaColumn::getOid, DbmetaColumn::getDataType));
        return typeMap;
    }

    protected Map<Long, String> getObjComment(List<Long> ids) throws SQLException {
        Map<Long, String> commentMap = new HashMap<>(ids.size());
        db.find(Entity.create(Func.getTableName(DbmetaObject.class)).set(Func.nameUlc(DbmetaObject::getOid), ids), rs -> {
            while (rs.next()) {
                commentMap.put(rs.getLong(1), rs.getString(2));
            }
            return null;
        }, Func.namesUlc(DbmetaObject::getOid, DbmetaObject::getDescription));
        return commentMap;
    }

    protected AnalysisColumnResultDTO buildColumnResult(AnalysisColumn column, Map<Long, String> typeMap, Map<Long, String> commentMap) {
        DbmetaSchema schema = getSchema(column.getSchemaId(), column.getSchemaName());
        AnalysisColumnResultDTO res = new AnalysisColumnResultDTO();
        res.setDsUid(dsUid);
        res.setCatalog(schema.getCatalog());
        res.setSchema(schema.getSchema());
        res.setTableName(column.getTableName());
        res.setColumnName(column.getColumnName());
        res.setBizId(column.getBizId());
        res.setColumnType(typeMap.get(column.getColumnId()));
        if (commentMap != null) {
            res.setTableComment(commentMap.get(column.getTableId()));
            res.setColumnComment(commentMap.get(column.getColumnId()));
        }
        return res;
    }

    private DbmetaSchema getSchema(Long schemaId, String name) {
        return cache.get(schemaId, () -> {
            Entity entity = db.get(Entity.create(st).addFieldNames(fields).set(oid, schemaId));
            DbmetaSchema schema;
            if (null == entity) {
                schema = new DbmetaSchema();
            } else {
                schema = entity.toBean(DbmetaSchema.class);
            }
            if (null == schema.getCatalog() && null == schema.getSchema()) {
                schema.setSchema(name);
            }
            return schema;
        });
    }
}
