package com.mchz.nyx.metis.model;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import com.mchz.nyx.metis.entity.AnalysisColumn;
import com.mchz.nyx.metis.entity.ClassifyData;

import java.util.*;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/2/20
 */
public class DataLoadStdHandler extends DataLoadSuccinctHandler {
    private final Map<Long, ClassifyData> map;

    public DataLoadStdHandler(Db db, Entity query, String dsUid, boolean withComment, Map<Long, ClassifyData> map) {
        super(db, query, dsUid, withComment);
        this.map = map;
    }

    @Override
    protected AnalysisColumnResultDTO buildColumnResult(AnalysisColumn column, Map<Long, String> typeMap, Map<Long, String> commentMap) {
        AnalysisColumnResultDTO res = super.buildColumnResult(column, typeMap, commentMap);
        res.setBizId(null);
        fillOther(column.getBizId(), res);
        return res;
    }

    private void fillOther(Long bizId, AnalysisColumnResultDTO res) {
        if (null == bizId) {
            return;
        }
        ClassifyData data = map.get(bizId);
        if (null == data) {
            return;
        }
        res.setBizName(data.getName());
        int level = ObjUtil.defaultIfNull(data.getLevel(), 2);
        res.setLevel(level);
        res.setLevelName(getLevelName(level));
        res.setSensitive(getSensitive(level));
        res.setTypes(StrUtil.split(data.getBelongType(), StrUtil.DASHED));
    }

    private String getLevelName(int level) {
        switch (level) {
            case 1:
                return "低敏感级";
            case 2:
                return "一般敏感级";
            case 3:
                return "较高敏感级";
            case 4:
                return "高敏感级";
            default:
                return StrUtil.EMPTY;
        }
    }

    private Integer getSensitive(int level) {
        return level > 1 ? 1 : 0;
    }
}
