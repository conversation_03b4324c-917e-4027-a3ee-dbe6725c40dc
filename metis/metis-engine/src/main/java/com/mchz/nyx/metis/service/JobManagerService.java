package com.mchz.nyx.metis.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import cn.hutool.db.handler.<PERSON>andler;
import cn.hutool.db.handler.BeanListHandler;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.mchz.base.metis.DataSourceParam;
import com.mchz.base.metis.DatasourceMeta;
import com.mchz.base.metis.JobConfig;
import com.mchz.nyx.common.exception.NyxException;
import com.mchz.nyx.metis.entity.SysJob;
import com.mchz.nyx.metis.entity.SysPlan;
import com.mchz.nyx.metis.entity.SysSourceTask;
import com.mchz.nyx.metis.model.JobStatus;
import com.mchz.nyx.metis.util.Func;
import lombok.AllArgsConstructor;

import java.sql.SQLException;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/8/15
 */
@AllArgsConstructor
public class JobManagerService {
    private final Db db;

    public Long bindSourceId(String tenantId, String dsUid, DatasourceMeta source) {
        int version = 1;
        SysSourceTask sourceTask = new SysSourceTask();
        sourceTask.setVersion(version);
        sourceTask.setDetail(Func.encode(version, source));

        String tableName = Func.getTableName(SysSourceTask.class);
        String idField = Func.nameUlc(SysSourceTask::getId);
        String extIdField = Func.nameUlc(SysSourceTask::getExtId);
        String extId = StrUtil.isEmpty(tenantId) ? dsUid : tenantId + StrUtil.SPACE + dsUid;

        Entity where = Entity.create(tableName).addFieldNames(idField).set(extIdField, extId);
        try {
            Entity entity = db.get(where);
            if (null == entity) {
                sourceTask.setExtId(extId);
                return db.insertForGeneratedKey(Entity.create(tableName).parseBean(sourceTask, true, true));
            } else {
                Long id = entity.getLong(idField);
                Entity updateEntity = Entity.create(tableName).parseBean(sourceTask, true, true);
                db.update(updateEntity, Entity.create().set(idField, id));
                return id;
            }
        } catch (SQLException e) {
            throw new NyxException("Failed to bind source id for extId: " + extId, e);
        }
    }

    public Long bindPlanId(JobConfig config, Long sourceId, boolean customStd) {
        String tableName = Func.getTableName(SysPlan.class);
        String key = Func.nameUlc(SysPlan::getId);
        String hash = getHash(config, customStd);
        Entity where = Entity.create(tableName).addFieldNames(key).set(Func.nameUlc(SysPlan::getSourceId), sourceId).set(Func.nameUlc(SysPlan::getHash), hash);
        try {
            Entity entity = db.get(where);
            if (null == entity) {
                SysPlan b = new SysPlan();
                b.setTenantId(StrUtil.nullToEmpty(config.getTenantId()));
                b.setSourceId(sourceId);
                b.setCustom(customStd);
                b.setHash(hash);
                b.setCreatedAt(new Date());
                JSONObject obj = JSONUtil.parseObj(config);
                obj.remove(Func.name(JobConfig::getTenantId));
                obj.remove(Func.name(JobConfig::getExtInfo));
                b.setConfigDetail(JSONUtil.toJsonStr(obj));
                return db.insertForGeneratedKey(Entity.create(tableName).parseBean(b, true, true));
            } else {
                return entity.getLong(key);
            }
        } catch (SQLException e) {
            throw new NyxException(e);
        }
    }

    public Long generatedJobId(Long planId, String dsUid, String extInfo) {
        SysJob job = new SysJob();
        job.setPlanId(planId);
        job.setDsUid(dsUid);
        job.setExtInfo(extInfo);
        job.setCreatedAt(new Date());
        job.setStatus(JobStatus.PENDING.getCode());
        try {
            return db.insertForGeneratedKey(Entity.create(Func.getTableName(SysJob.class)).parseBean(job, true, true));
        } catch (SQLException e) {
            throw new NyxException(e);
        }
    }

    public void updateJobStatus(Long jobId, JobStatus status) {
        Entity entity = Entity.create(Func.getTableName(SysJob.class))
            .set(Func.nameUlc(SysJob::getStatus), status.getCode());
        if (JobStatus.RUNNING.equals(status)) {
            entity.set(Func.nameUlc(SysJob::getStartAt), new Date());
        }
        try {
            db.update(entity, Entity.create().set(Func.nameUlc(SysJob::getId), jobId));
        } catch (SQLException e) {
            throw new NyxException(e);
        }
    }

    public List<SysJob> getPendingJobs() {
        try {
            Entity query = Entity.create(Func.getTableName(SysJob.class))
                .addFieldNames(Func.namesUlc(SysJob::getId, SysJob::getDsUid, SysJob::getPlanId))
                .set(Func.nameUlc(SysJob::getStatus), JobStatus.PENDING.getCode());
            return db.page(query, 0, 5, BeanListHandler.create(SysJob.class));
        } catch (SQLException e) {
            throw new NyxException("Failed to get pending jobs", e);
        }
    }

    public List<SysJob> getRunningJobs() {
        try {
            Entity query = Entity.create(Func.getTableName(SysJob.class))
                .addFieldNames(Func.namesUlc(SysJob::getId, SysJob::getDsUid))
                .set(Func.nameUlc(SysJob::getStatus), JobStatus.RUNNING.getCode());
            return db.find(query, SysJob.class);
        } catch (SQLException e) {
            throw new NyxException("Failed to get running jobs", e);
        }
    }

    public String getUUid(Long jobId) {
        return "1-" + jobId.toString();
    }

    public SysPlan getPlanConfig(Long planId, boolean needDetail) {
        try {
            Entity q = Entity.create(Func.getTableName(SysPlan.class)).addFieldNames(Func.namesUlc(SysPlan::getTenantId, SysPlan::getCustom)).set(Func.nameUlc(SysPlan::getId), planId);
            if (needDetail) {
                q.addFieldNames(Func.namesUlc(SysPlan::getSourceId, SysPlan::getConfigDetail));
            }
            return db.find(q.getFieldNames(), q, BeanHandler.create(SysPlan.class));
        } catch (SQLException e) {
            throw new NyxException(e);
        }
    }

    public SysJob getJob(String id) {
        Long jobId = getJobId(id);
        try {
            Entity entity = db.get(Func.getTableName(SysJob.class), Func.nameUlc(SysJob::getId), jobId);
            return null != entity ? entity.toBean(SysJob.class) : null;
        } catch (SQLException e) {
            throw new NyxException("Failed to get job by id: " + id, e);
        }
    }

    public String getTenant(String id) {
        Long jobId = getJobId(id);
        try {
            String planIdField = Func.nameUlc(SysJob::getPlanId);
            Entity jobQuery = Entity.create(Func.getTableName(SysJob.class)).addFieldNames(planIdField).set(Func.nameUlc(SysJob::getId), jobId);
            Entity jobEntity = db.get(jobQuery);
            Long planId;
            if (null == jobEntity || null == (planId = jobEntity.getLong(planIdField))) {
                return null;
            }
            String tenantIdField = Func.nameUlc(SysPlan::getTenantId);
            Entity planQuery = Entity.create(Func.getTableName(SysPlan.class)).addFieldNames(tenantIdField).set(Func.nameUlc(SysPlan::getId), planId);
            Entity planEntity = db.get(planQuery);
            return planEntity != null ? planEntity.getStr(tenantIdField) : null;
        } catch (SQLException e) {
            throw new NyxException("Failed to get tenant for jobId: " + jobId, e);
        }
    }

    public Long getJobId(String id) {
        try {
            return Long.valueOf(id.substring(2));
        } catch (Exception e) {
            throw new IllegalArgumentException("id");
        }
    }

    public JobConfig analysisJobConfig(SysPlan plan) {
        JobConfig config = JSONUtil.toBean(plan.getConfigDetail(), JobConfig.class);
        config.setTenantId(plan.getTenantId());
        return config;
    }

    public DataSourceParam getDataSource(SysPlan plan) {
        try {
            Entity q = Entity.create(Func.getTableName(SysSourceTask.class)).addFieldNames(Func.namesUlc(SysSourceTask::getVersion, SysSourceTask::getDetail)).set(Func.nameUlc(SysSourceTask::getId), plan.getSourceId());
            SysSourceTask sourceTask = db.find(q.getFieldNames(), q, BeanHandler.create(SysSourceTask.class));
            return Func.decode(sourceTask.getVersion(), sourceTask.getDetail());
        } catch (SQLException e) {
            throw new NyxException("Failed to get source task for planId: " + plan.getId(), e);
        }
    }

    private String getHash(JobConfig config, boolean customStd) {
        StringBuilder sb = new StringBuilder();
        sb.append(config.getStdId());
        if (customStd) {
            sb.append("'");
        }
        if (null != config.getTenantId()) {
            sb.append(config.getTenantId());
            sb.append(StrUtil.SPACE);
        }
        if (null != config.getId()) {
            sb.append(config.getId());
        } else if (null != config.getSchemas()) {
            config.getSchemas().sort(Comparator.naturalOrder());
            sb.append(config.getSchemas().toString());
        }
        return MD5.create().digestHex(sb.toString());
    }
}
