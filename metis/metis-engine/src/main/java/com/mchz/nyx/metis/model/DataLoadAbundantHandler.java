package com.mchz.nyx.metis.model;

import cn.hutool.cache.Cache;
import cn.hutool.cache.CacheUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.db.Db;
import cn.hutool.db.Entity;
import com.mchz.base.metis.StandardService;
import com.mchz.base.metis.model.ClassifyTypeDTO;
import com.mchz.nyx.metis.entity.AnalysisColumn;

import java.sql.SQLException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/2/20
 */
public class DataLoadAbundantHandler extends AbstractDataLoadHandler {
    private final boolean withComment;

    private final String tenantId;
    private final Cache<Long, ClassifyTypeDTO> map;
    private final StandardService standardService;

    public DataLoadAbundantHandler(Db db, Entity query, String dsUid, boolean withComment, String tenantId, StandardService standardService) {
        super(db, query, dsUid);
        this.withComment = withComment;
        this.tenantId = tenantId;
        this.standardService = standardService;
        this.map = CacheUtil.newLFUCache(100, 120000);
    }

    @Override
    protected List<AnalysisColumnResultDTO> buildResult(String dsUid, List<AnalysisColumn> list) throws SQLException {
        List<Long> columnIds = new ArrayList<>(list.size());
        List<Long> ids = new ArrayList<>(list.size());
        Set<Long> bizIds = new HashSet<>(list.size());
        Map<Long, ClassifyTypeDTO> classifyMap = new HashMap<>();
        list.forEach(v -> {
            columnIds.add(v.getColumnId());
            ids.add(v.getColumnId());
            ids.add(v.getTableId());
            ClassifyTypeDTO temp = map.get(v.getBizId());
            if (null == temp) {
                bizIds.add(v.getBizId());
            } else {
                classifyMap.put(v.getBizId(), temp);
            }
        });
        Map<Long, String> typeMap = getColumnTypeMap(columnIds);
        Map<Long, String> commentMap = withComment ? getObjComment(ids) : null;
        if (CollUtil.isNotEmpty(bizIds)) {
            List<ClassifyTypeDTO> loaded = standardService.loadClassifyType(bizIds, tenantId);
            if (CollUtil.isNotEmpty(loaded)) {
                loaded.forEach(v -> classifyMap.put(v.getId(), v));
            }
        }
        return list.stream().map(v -> {
            AnalysisColumnResultDTO res =  buildColumnResult(v, typeMap, commentMap);
            fillOther(v, res, classifyMap);
            return res;
        }).collect(Collectors.toList());
    }

    private void fillOther(AnalysisColumn column, AnalysisColumnResultDTO res, Map<Long, ClassifyTypeDTO> classifyMap) {
        if (null == column.getBizId()) {
            return;
        }
        ClassifyTypeDTO dto = classifyMap.get(column.getBizId());
        if (null == dto) {
            return;
        }
        res.setBizName(dto.getBizName());
        res.setLevel(dto.getLevel());
        res.setLevelName(dto.getLevelName());
        res.setSensitive(dto.getSensitive());
        res.setTypes(dto.getTypes());
    }
}
