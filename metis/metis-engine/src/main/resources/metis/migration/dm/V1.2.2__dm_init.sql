CREATE TABLE analysis_column (
  "id" bigint NOT NULL AUTO_INCREMENT,
  "plan_id" bigint NOT NULL,
  "job_id" bigint NOT NULL,
  "source_id" int NOT NULL,
  "schema_id" bigint NOT NULL,
  "table_id" bigint NOT NULL,
  "column_id" bigint NOT NULL,
  "schema_name" varchar(255) NOT NULL,
  "table_name" varchar(255) NOT NULL,
  "column_name" varchar(255) NOT NULL,
  "biz_id" bigint DEFAULT '0',
  "classify_id" bigint DEFAULT '0',
  "level" int DEFAULT NULL,
  "flag" tinyint NOT NULL,
  "status" int NOT NULL,
  "repeat_type" tinyint NOT NULL,
  "repeat_column_id" bigint DEFAULT NULL,
  "hit_rate" decimal(20, 2) DEFAULT NULL,
  "remark" varchar(50) DEFAULT NULL,
  PRIMARY KEY ("id")
);
CREATE UNIQUE INDEX "analysis_column_udx_cid" ON analysis_column ("plan_id", "column_id");
CREATE INDEX "analysis_column_idx_column" ON analysis_column ("plan_id", "table_id", "column_name");
CREATE INDEX "analysis_column_idx_repeat_cid" ON analysis_column ("plan_id", "repeat_column_id", "status");
CREATE INDEX "analysis_column_idx_column_id" ON analysis_column ("job_id", "table_id", "column_id");


CREATE TABLE analysis_table (
  "id" bigint NOT NULL AUTO_INCREMENT,
  "plan_id" bigint NOT NULL,
  "job_id" bigint NOT NULL,
  "source_id" int NOT NULL,
  "schema_id" bigint NOT NULL,
  "table_id" bigint NOT NULL,
  "schema_name" varchar(255) NOT NULL,
  "table_name" varchar(255) NOT NULL,
  "version" int NOT NULL,
  "repeat_type" tinyint NOT NULL,
  "repeat_table_id" bigint DEFAULT NULL,
  "repeat_rate" decimal(5, 2) DEFAULT NULL,
  "status" tinyint NOT NULL,
  "classify_id" varchar(2000) DEFAULT NULL,
  "level" int DEFAULT NULL,
  "remark" varchar(50) DEFAULT NULL,
  PRIMARY KEY ("id")
);
CREATE UNIQUE INDEX "analysis_table_udx_table_id" ON analysis_table ("plan_id", "table_id");
CREATE INDEX "analysis_table_idx_repeat_id" ON analysis_table ("plan_id", "repeat_table_id");


CREATE TABLE dbmeta_column (
  "oid" bigint NOT NULL,
  "source_id" bigint NOT NULL,
  "schema_id" bigint NOT NULL,
  "table_id" bigint NOT NULL,
  "data_type" varchar(1000) NOT NULL,
  "type_group" varchar(50) DEFAULT NULL,
  "length" bigint DEFAULT NULL,
  "precision" int DEFAULT NULL,
  "scale" int DEFAULT NULL,
  "nullable" tinyint DEFAULT NULL,
  "position" int NOT NULL,
  "is_auto_incremented" tinyint DEFAULT NULL,
  "is_generated" tinyint DEFAULT NULL,
  "is_pk" tinyint DEFAULT NULL,
  "is_unique" tinyint DEFAULT NULL,
  "is_index" tinyint DEFAULT NULL,
  "is_fk" tinyint DEFAULT NULL,
  "comment" TEXT,
  PRIMARY KEY ("oid")
);
CREATE INDEX "dbmeta_column_idx_table_id" ON dbmeta_column ("table_id");
CREATE INDEX "dbmeta_column_idx_source_id" ON dbmeta_column ("source_id");


CREATE TABLE dbmeta_foreign_key (
  "oid" bigint NOT NULL,
  "source_id" bigint NOT NULL,
  "table_id" bigint NOT NULL,
  PRIMARY KEY ("oid")
);
CREATE INDEX "dbmeta_foreign_key_idx_source_id" ON dbmeta_foreign_key ("source_id");


CREATE TABLE dbmeta_foreign_key_info (
  "id" bigint NOT NULL AUTO_INCREMENT,
  "source_id" bigint NOT NULL,
  "fk_id" bigint NOT NULL,
  "column_id" bigint NOT NULL,
  "ref_schema" varchar(255) DEFAULT NULL,
  "ref_table" varchar(255) DEFAULT NULL,
  "ref_column" varchar(255) DEFAULT NULL,
  "position" int NOT NULL,
  PRIMARY KEY ("id")
);
CREATE INDEX "dbmeta_foreign_key_info_idx_fk_id" ON dbmeta_foreign_key_info ("fk_id");
CREATE INDEX "dbmeta_foreign_key_info_idx_source_id" ON dbmeta_foreign_key_info ("source_id");


CREATE TABLE dbmeta_index (
  "oid" bigint NOT NULL,
  "source_id" bigint NOT NULL,
  "table_id" bigint NOT NULL,
  "type" varchar(50) NOT NULL,
  "is_pk" tinyint DEFAULT NULL,
  "is_unique" tinyint DEFAULT NULL,
  "method" varchar(50) DEFAULT NULL,
  PRIMARY KEY ("oid")
);
CREATE INDEX "dbmeta_index_idx_table_id" ON dbmeta_index ("table_id");
CREATE INDEX "dbmeta_index_idx_source_id" ON dbmeta_index ("source_id");


CREATE TABLE dbmeta_index_column (
  "id" bigint NOT NULL AUTO_INCREMENT,
  "source_id" bigint NOT NULL,
  "index_id" bigint NOT NULL,
  "column_id" bigint NOT NULL,
  "position" int NOT NULL,
  PRIMARY KEY ("id")
);
CREATE INDEX "dbmeta_index_column_idx_index_id" ON dbmeta_index_column ("index_id");
CREATE INDEX "dbmeta_index_column_idx_source_id" ON dbmeta_index_column ("source_id");


CREATE TABLE dbmeta_object (
  "oid" bigint NOT NULL AUTO_INCREMENT,
  "source_id" bigint NOT NULL,
  "name" varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_bin NOT NULL,
  "type" varchar(50) NOT NULL,
  "description" TEXT,
  "tags" TEXT,
  "deleted" tinyint NOT NULL,
  "created_at" datetime NOT NULL,
  "updated_at" datetime NOT NULL,
  "revision" int NOT NULL,
  "hash_value" varchar(100) DEFAULT NULL,
  "pid" bigint NOT NULL,
  PRIMARY KEY ("oid")
);
CREATE INDEX "dbmeta_object_idx_object_tree" ON dbmeta_object ("source_id", "pid", "type", "name");


CREATE TABLE dbmeta_schema (
  "oid" bigint NOT NULL,
  "source_id" bigint NOT NULL,
  "catalog" varchar(255) DEFAULT NULL,
  "schema" varchar(255) DEFAULT NULL,
  "charset" varchar(20) DEFAULT NULL,
  "collation" varchar(50) DEFAULT NULL,
  PRIMARY KEY ("oid")
);
CREATE INDEX "dbmeta_schema_idx_source_id" ON dbmeta_schema ("source_id");

CREATE TABLE dbmeta_table (
  "oid" bigint NOT NULL,
  "source_id" bigint NOT NULL,
  "schema_id" bigint NOT NULL,
  "type" varchar(50) NOT NULL,
  "column_num" int DEFAULT NULL,
  "partition_column" varchar(255) DEFAULT NULL,
  "space" varchar(50) DEFAULT NULL,
  "rows" bigint DEFAULT NULL,
  "comment" TEXT,
  PRIMARY KEY ("oid")
);
CREATE INDEX "dbmeta_table_idx_schema_id" ON dbmeta_table ("schema_id");
CREATE INDEX "dbmeta_table_idx_source_id" ON dbmeta_table ("source_id");

CREATE TABLE sys_job (
  "id" bigint NOT NULL AUTO_INCREMENT,
  "plan_id" bigint NOT NULL,
  "ds_uid" varchar(255) NOT NULL,
  "ext_info" varchar(255) DEFAULT NULL,
  "created_at" datetime NOT NULL,
  PRIMARY KEY ("id")
);

CREATE TABLE sys_plan (
  "id" bigint NOT NULL AUTO_INCREMENT,
  "tenant_id" varchar(10) NOT NULL DEFAULT '',
  "source_id" bigint NOT NULL,
  "custom" tinyint DEFAULT NULL,
  "hash" varchar(255) NOT NULL,
  "created_at" datetime NOT NULL,
  "config_detail" VARCHAR(32767) DEFAULT NULL,
  PRIMARY KEY ("id")
);
CREATE INDEX "sys_plan_udx_source_id_hash" ON sys_plan ("source_id", "hash");


CREATE TABLE sys_source_task (
  "id" bigint NOT NULL AUTO_INCREMENT,
  "ext_id" varchar(255) DEFAULT NULL,
  PRIMARY KEY ("id")
);
