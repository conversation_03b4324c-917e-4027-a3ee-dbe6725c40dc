CREATE TABLE IF NOT EXISTS analysis_column (
  "id"               serial8,
  "plan_id"          int8         NOT NULL,
  "job_id"           int8         NOT NULL,
  "source_id"        int4         NOT NULL,
  "schema_id"        int8         NOT NULL,
  "table_id"         int8         NOT NULL,
  "column_id"        int8         NOT NULL,
  "schema_name"      varchar(255) NOT NULL,
  "table_name"       varchar(255) NOT NULL,
  "column_name"      varchar(255) NOT NULL,
  "biz_id"           int8,
  "classify_id"      int8,
  "level"            int4,
  "flag"             int2         NOT NULL,
  "status"           int4         NOT NULL,
  "repeat_type"      int2         NOT NULL,
  "repeat_column_id" int8,
  "is_empty"         bool         NOT NULL,
  "hit_rate"         numeric(20, 2),
  "remark"           varchar(50),
  PRIMARY KEY ("id"),
  UNIQUE ("plan_id", "column_id")
);
CREATE INDEX idx_column ON analysis_column ("plan_id", "table_id", "column_name");
CREATE INDEX idx_repeat_cid ON analysis_column ("plan_id", "repeat_column_id", "status");

CREATE TABLE IF NOT EXISTS analysis_table (
  "id"              serial8,
  "plan_id"         int8         NOT NULL,
  "job_id"          int8         NOT NULL,
  "source_id"       int4         NOT NULL,
  "schema_id"       int8         NOT NULL,
  "table_id"        int8         NOT NULL,
  "schema_name"     varchar(255) NOT NULL,
  "table_name"      varchar(255) NOT NULL,
  "version"         int4         NOT NULL,
  "repeat_type"     int2         NOT NULL,
  "repeat_table_id" int8,
  "repeat_rate"     numeric(5, 2),
  "status"          int2         NOT NULL,
  "remark"          varchar(50),
  PRIMARY KEY ("id"),
  UNIQUE ("plan_id", "table_id")
);
CREATE INDEX idx_repeat_id ON analysis_table ("plan_id", "repeat_table_id");

CREATE TABLE IF NOT EXISTS dbmeta_object (
  "oid"         serial8,
  "source_id"   int8         NOT NULL,
  "name"        varchar(255) NOT NULL,
  "type"        varchar(50)  NOT NULL,
  "description" text,
  "tags"        text,
  "deleted"     bool         NOT NULL,
  "created_at"  timestamp(6) NOT NULL,
  "updated_at"  timestamp(6) NOT NULL,
  "revision"    int4         NOT NULL,
  "hash_value"  varchar(100),
  "pid"         int8         NOT NULL,
  PRIMARY KEY ("oid"),
  UNIQUE ("source_id", "pid", "type", "name")
);
CREATE INDEX idx_dbmeta_object_source_id ON dbmeta_object ("source_id");

CREATE TABLE IF NOT EXISTS dbmeta_schema (
  "oid"       int8 NOT NULL,
  "source_id" int8 NOT NULL,
  "catalog"   varchar(255),
  "schema"    varchar(255),
  "charset"   varchar(20),
  "collation" varchar(50),
  PRIMARY KEY ("oid")
);
CREATE INDEX idx_dbmeta_schema_source_id ON dbmeta_schema ("source_id");

CREATE TABLE IF NOT EXISTS dbmeta_table (
  "oid"              int8        NOT NULL,
  "source_id"        int8        NOT NULL,
  "schema_id"        int8        NOT NULL,
  "type"             varchar(50) NOT NULL,
  "column_num"       int4,
  "partition_column" varchar(255),
  "space"            varchar(50),
  "rows"             int8,
  "comment"          text,
  PRIMARY KEY ("oid")
);
CREATE INDEX idx_dbmeta_table_source_id ON dbmeta_table ("source_id");

CREATE TABLE IF NOT EXISTS dbmeta_column (
  "oid"                 int8          NOT NULL,
  "source_id"           int8          NOT NULL,
  "schema_id"           int8          NOT NULL,
  "table_id"            int8          NOT NULL,
  "data_type"           varchar(1000) NOT NULL,
  "type_group"          varchar(50),
  "length"              int8,
  "precision"           int4,
  "scale"               int4,
  "nullable"            bool,
  "position"            int4          NOT NULL,
  "is_auto_incremented" bool,
  "is_generated"        bool,
  "is_pk"               bool,
  "is_unique"           bool,
  "is_index"            bool,
  "is_fk"               bool,
  "comment"             text,
  PRIMARY KEY ("oid")
);
CREATE INDEX idx_dbmeta_column_source_id ON dbmeta_column ("source_id");
CREATE INDEX idx_dbmeta_column_table_id ON dbmeta_column ("table_id");

CREATE TABLE IF NOT EXISTS dbmeta_index (
  "oid"       int8        NOT NULL,
  "source_id" int8        NOT NULL,
  "table_id"  int8        NOT NULL,
  "type"      varchar(50) NOT NULL,
  "is_pk"     bool,
  "is_unique" bool,
  "method"    varchar(50),
  PRIMARY KEY ("oid")
);
CREATE INDEX idx_dbmeta_index_source_id ON dbmeta_index ("source_id");

CREATE TABLE IF NOT EXISTS dbmeta_index_column (
  "id"        serial8,
  "source_id" int8 NOT NULL,
  "index_id"  int8 NOT NULL,
  "column_id" int8 NOT NULL,
  "position"  int4 NOT NULL,
  PRIMARY KEY ("id")
);
CREATE INDEX idx_dbmeta_index_column_source_id ON dbmeta_index_column ("source_id");

CREATE TABLE IF NOT EXISTS dbmeta_foreign_key (
  "oid"       int8 NOT NULL,
  "source_id" int8 NOT NULL,
  "table_id"  int8 NOT NULL,
  PRIMARY KEY ("oid")
);
CREATE INDEX idx_dbmeta_foreign_key_source_id ON dbmeta_foreign_key ("source_id");

CREATE TABLE IF NOT EXISTS dbmeta_foreign_key_info (
  "id"         serial8,
  "source_id"  int8 NOT NULL,
  "fk_id"      int8 NOT NULL,
  "column_id"  int8 NOT NULL,
  "ref_schema" varchar(255),
  "ref_table"  varchar(255),
  "ref_column" varchar(255),
  "position"   int4 NOT NULL,
  PRIMARY KEY ("id")
);
CREATE INDEX idx_dbmeta_foreign_key_info_source_id ON dbmeta_foreign_key_info ("source_id");

CREATE TABLE IF NOT EXISTS sys_source_task (
  "id"     serial8,
  "ext_id" varchar(255),
  PRIMARY KEY ("id")
);

