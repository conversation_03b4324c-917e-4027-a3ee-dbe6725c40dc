CREATE INDEX idx_column_id ON analysis_column ("job_id", "table_id", "column_id");

CREATE TABLE sys_plan (
  "id"         serial8,
  "source_id"  int8         NOT NULL,
  "hash"       varchar(255) NOT NULL,
  "created_at" timestamp(6) NOT NULL,
  PRIMARY KEY ("id"),
  UNIQUE ("source_id", "hash")
);

CREATE TABLE sys_job (
  "id"         serial8,
  "plan_id"    int8         NOT NULL,
  "ds_uid"     varchar(255) NOT NULL,
  "ext_info"   varchar(255) DEFAULT NULL,
  "created_at" timestamp(6) NOT NULL,
  PRIMARY KEY ("id")
);

INSERT INTO sys_plan ("source_id", "hash", "created_at") SELECT "id", '', now() FROM sys_source_task;
INSERT INTO sys_job ("plan_id", "ds_uid", "created_at") SELECT "id", "ext_id", now() FROM sys_source_task;
