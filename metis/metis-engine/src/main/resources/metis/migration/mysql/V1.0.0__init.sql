CREATE TABLE IF NOT EXISTS analysis_column (
  `id`               bigint       NOT NULL AUTO_INCREMENT COMMENT '主键',
  `plan_id`          bigint       NOT NULL COMMENT '配置id',
  `job_id`           bigint       NOT NULL COMMENT '执行作业id',
  `source_id`        int          NOT NULL COMMENT '数据源id',
  `schema_id`        bigint       NOT NULL COMMENT 'schema元数据id',
  `table_id`         bigint       NOT NULL COMMENT 'table元数据id',
  `column_id`        bigint       NOT NULL COMMENT 'column元数据id',
  `schema_name`      varchar(255) NOT NULL COMMENT 'schema',
  `table_name`       varchar(255) NOT NULL COMMENT '表名',
  `column_name`      varchar(255) NOT NULL COMMENT '列名',
  `biz_id`           bigint                DEFAULT '0' COMMENT '业务类型，字典表中业务类型id',
  `classify_id`      bigint                DEFAULT '0' COMMENT '分类type id',
  `level`            int                   DEFAULT NULL COMMENT '分级级别',
  `flag`             tinyint(8)   NOT NULL COMMENT '1-8',
  `status`           int          NOT NULL COMMENT '状态1用户确认2自动确认3自动发现4没有发现5',
  `repeat_type`      tinyint(4)   NOT NULL,
  `repeat_column_id` bigint                DEFAULT NULL,
  `is_empty`         tinyint(1)   NOT NULL DEFAULT '0' COMMENT '是否为空字段: 0->不是, 1->是',
  `hit_rate`         decimal(20, 2)        DEFAULT NULL COMMENT '命中率',
  `remark`           varchar(50)           DEFAULT NULL COMMENT '备用',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `udx_cid` (`plan_id`, `column_id`) USING BTREE,
  KEY `idx_column` (`plan_id`, `table_id`, `column_name`) USING BTREE,
  KEY `idx_repeat_cid` (`plan_id`, `repeat_column_id`, `status`) USING BTREE
) ENGINE = InnoDB;

CREATE TABLE IF NOT EXISTS analysis_table (
  `id`              bigint       NOT NULL AUTO_INCREMENT,
  `plan_id`         bigint       NOT NULL COMMENT '配置id',
  `job_id`          bigint       NOT NULL COMMENT '执行作业id',
  `source_id`       int          NOT NULL COMMENT '数据源id',
  `schema_id`       bigint       NOT NULL COMMENT '元数据id',
  `table_id`        bigint       NOT NULL COMMENT '元数据id',
  `schema_name`     varchar(255) NOT NULL COMMENT 'schema',
  `table_name`      varchar(255) NOT NULL COMMENT '表名',
  `version`         int          NOT NULL,
  `repeat_type`     tinyint(4)   NOT NULL,
  `repeat_table_id` bigint        DEFAULT NULL,
  `repeat_rate`     decimal(5, 2) DEFAULT NULL COMMENT '重复率',
  `status`          tinyint(8)   NOT NULL COMMENT '状态',
  `remark`          varchar(50)   DEFAULT NULL COMMENT '备用',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `udx_table_id` (`plan_id`, `table_id`) USING BTREE,
  KEY `idx_repeat_id` (`plan_id`, `repeat_table_id`) USING BTREE
) ENGINE = InnoDB;

SET @sql = 'CREATE TABLE IF NOT EXISTS dbmeta_object
(
  `oid`         bigint                       NOT NULL AUTO_INCREMENT COMMENT ''主键'',
  `source_id`   bigint                       NOT NULL COMMENT ''数据源主键'',
  `name`        varchar(255) COLLATE utf8mb4_0900_bin NOT NULL COMMENT ''名称'' ,
  `type`        varchar(50)                      NOT NULL COMMENT ''类型'',
  `description` text COMMENT ''描述'',
  `tags`        text COMMENT ''标记'',
  `deleted`     tinyint(1)                       NOT NULL COMMENT ''是否删除'',
  `created_at`  datetime                         NOT NULL COMMENT ''创建时间'',
  `updated_at`  datetime                         NOT NULL COMMENT ''更新时间'',
  `revision`    int                          NOT NULL COMMENT ''修订版本'',
  `hash_value`  varchar(100) DEFAULT NULL COMMENT ''内容hash'',
  `pid`         bigint                       NOT NULL COMMENT ''父主键(oid)'',
  PRIMARY KEY (`oid`) USING BTREE,
  UNIQUE KEY `udx_object_tree` (`source_id`, `pid`, `type`, `name`) USING BTREE
) ENGINE = InnoDB COMMENT =''对象属性'';';
SELECT 'CREATE TABLE IF NOT EXISTS dbmeta_object
(
  `oid`         bigint                       NOT NULL AUTO_INCREMENT COMMENT ''主键'',
  `source_id`   bigint                       NOT NULL COMMENT ''数据源主键'',
  `name`        varchar(255) COLLATE utf8mb4_bin NOT NULL COMMENT ''名称'',
  `type`        varchar(50)                      NOT NULL COMMENT ''类型'',
  `description` text COMMENT ''描述'',
  `tags`        text COMMENT ''标记'',
  `deleted`     tinyint(1)                       NOT NULL COMMENT ''是否删除'',
  `created_at`  datetime                         NOT NULL COMMENT ''创建时间'',
  `updated_at`  datetime                         NOT NULL COMMENT ''更新时间'',
  `revision`    int                          NOT NULL COMMENT ''修订版本'',
  `hash_value`  varchar(100) DEFAULT NULL COMMENT ''内容hash'',
  `pid`         bigint                       NOT NULL COMMENT ''父主键(oid)'',
  PRIMARY KEY (`oid`) USING BTREE,
  KEY `idx_object_tree` (`source_id`,`pid`,`type`,`name`) USING BTREE
) ENGINE = InnoDB COMMENT =''对象属性'';'
INTO @sql
FROM DUAL
WHERE EXISTS (SELECT LEFT(VERSION(), 3) = '5.7');
PREPARE stmt FROM @sql;
EXECUTE stmt;

CREATE TABLE IF NOT EXISTS dbmeta_schema (
  `oid`       bigint NOT NULL COMMENT '主键',
  `source_id` bigint NOT NULL COMMENT '数据源ID',
  `catalog`   varchar(255) DEFAULT NULL,
  `schema`    varchar(255) DEFAULT NULL,
  `charset`   varchar(20)  DEFAULT NULL COMMENT '字符集、编码',
  `collation` varchar(50)  DEFAULT NULL COMMENT '排序规则',
  PRIMARY KEY (`oid`) USING BTREE,
  KEY `idx_source_id` (`source_id`) USING BTREE
) ENGINE = InnoDB COMMENT ='数据源schema';

CREATE TABLE IF NOT EXISTS dbmeta_table (
  `oid`              bigint      NOT NULL COMMENT '主键',
  `source_id`        bigint      NOT NULL COMMENT '数据源ID',
  `schema_id`        bigint      NOT NULL COMMENT 'schema',
  `type`             varchar(50) NOT NULL COMMENT '类型',
  `column_num`       int          DEFAULT NULL COMMENT '列数量',
  `partition_column` varchar(255) DEFAULT NULL COMMENT '分区字段',
  `space`            varchar(50)  DEFAULT NULL COMMENT '表空间',
  `rows`             bigint       DEFAULT NULL COMMENT '行数',
  `comment`          text,
  PRIMARY KEY (`oid`) USING BTREE,
  KEY `idx_schema_id` (`schema_id`) USING BTREE,
  KEY `idx_source_id` (`source_id`) USING BTREE
) ENGINE = InnoDB COMMENT ='数据源表';

CREATE TABLE IF NOT EXISTS dbmeta_column (
  `oid`                 bigint        NOT NULL COMMENT '主键',
  `source_id`           bigint        NOT NULL COMMENT '数据源ID',
  `schema_id`           bigint        NOT NULL COMMENT '所属模式',
  `table_id`            bigint        NOT NULL COMMENT '所属表格',
  `data_type`           varchar(1000) NOT NULL COMMENT '原始类型',
  `type_group`          varchar(50) DEFAULT NULL COMMENT '类型分组',
  `length`              bigint      DEFAULT NULL COMMENT '长度',
  `precision`           int         DEFAULT NULL COMMENT '精度',
  `scale`               int         DEFAULT NULL COMMENT '刻度',
  `nullable`            tinyint(1)  DEFAULT NULL COMMENT '是否可以为空',
  `position`            int           NOT NULL COMMENT '位置',
  `is_auto_incremented` tinyint(1)  DEFAULT NULL COMMENT '是否自增',
  `is_generated`        tinyint(1)  DEFAULT NULL COMMENT '是否生成',
  `is_pk`               tinyint(1)  DEFAULT NULL COMMENT '是否主键',
  `is_unique`           tinyint(1)  DEFAULT NULL COMMENT '是否唯一键',
  `is_index`            tinyint(1)  DEFAULT NULL COMMENT '是否索引',
  `is_fk`               tinyint(1)  DEFAULT NULL COMMENT '是否外键',
  `comment`             text,
  PRIMARY KEY (`oid`) USING BTREE,
  KEY `idx_table_id` (`table_id`) USING BTREE,
  KEY `idx_source_id` (`source_id`) USING BTREE
) ENGINE = InnoDB COMMENT ='数据源表列';

CREATE TABLE IF NOT EXISTS dbmeta_index (
  `oid`       bigint      NOT NULL COMMENT '主键',
  `source_id` bigint      NOT NULL COMMENT '数据源ID',
  `table_id`  bigint      NOT NULL COMMENT '表',
  `type`      varchar(50) NOT NULL COMMENT '类型',
  `is_pk`     tinyint(1)  DEFAULT NULL COMMENT '是否主键',
  `is_unique` tinyint(1)  DEFAULT NULL COMMENT '是否唯一键',
  `method`    varchar(50) DEFAULT NULL COMMENT '索引方法',
  PRIMARY KEY (`oid`) USING BTREE,
  KEY `idx_table_id` (`table_id`) USING BTREE,
  KEY `idx_source_id` (`source_id`) USING BTREE
) ENGINE = InnoDB COMMENT ='数据源表索引';

CREATE TABLE IF NOT EXISTS dbmeta_index_column (
  `id`        bigint NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `source_id` bigint NOT NULL COMMENT '数据源ID',
  `index_id`  bigint NOT NULL COMMENT '索引id',
  `column_id` bigint NOT NULL COMMENT '列主键',
  `position`  int    NOT NULL COMMENT '位置',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_index_id` (`index_id`) USING BTREE,
  KEY `idx_source_id` (`source_id`) USING BTREE
) ENGINE = InnoDB COMMENT ='数据源表索引';

CREATE TABLE IF NOT EXISTS dbmeta_foreign_key (
  `oid`       bigint NOT NULL COMMENT '主键',
  `source_id` bigint NOT NULL COMMENT '数据源ID',
  `table_id`  bigint NOT NULL COMMENT '表格主键',
  PRIMARY KEY (`oid`) USING BTREE,
  KEY `idx_source_id` (`source_id`) USING BTREE
) ENGINE = InnoDB COMMENT ='数据源外键';

CREATE TABLE IF NOT EXISTS dbmeta_foreign_key_info (
  `id`         bigint NOT NULL AUTO_INCREMENT COMMENT '主键',
  `source_id`  bigint NOT NULL COMMENT '数据源ID',
  `fk_id`      bigint NOT NULL COMMENT '外键id',
  `column_id`  bigint NOT NULL COMMENT '字段主键',
  `ref_schema` varchar(255) DEFAULT NULL COMMENT '关联模式',
  `ref_table`  varchar(255) DEFAULT NULL COMMENT '关联表',
  `ref_column` varchar(255) DEFAULT NULL COMMENT '关联字段',
  `position`   int    NOT NULL COMMENT '位置',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_fk_id` (`fk_id`) USING BTREE,
  KEY `idx_source_id` (`source_id`) USING BTREE
) ENGINE = InnoDB COMMENT ='数据源外键信息';

CREATE TABLE IF NOT EXISTS sys_source_task (
  `id`     bigint NOT NULL AUTO_INCREMENT,
  `ext_id` varchar(255) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE = InnoDB;
