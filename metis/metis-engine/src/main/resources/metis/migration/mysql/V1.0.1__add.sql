ALTER TABLE analysis_column ADD INDEX `idx_column_id`(`job_id`, `table_id`, `column_id`) USING BTREE;

CREATE TABLE sys_plan (
  `id`         bigint       NOT NULL AUTO_INCREMENT,
  `source_id`  bigint       NOT NULL,
  `hash`       varchar(255) NOT NULL,
  `created_at` datetime     NOT NULL,
  PRIMARY KEY (`id`),
  KEY `udx_source_id_hash` (`source_id`, `hash`)
) ENGINE = InnoDB;

CREATE TABLE sys_job (
  `id`         bigint       NOT NULL AUTO_INCREMENT,
  `plan_id`    bigint       NOT NULL,
  `ds_uid`     varchar(255) NOT NULL,
  `ext_info`   varchar(255) DEFAULT NULL,
  `created_at` datetime     NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE = InnoDB;

INSERT INTO sys_plan (`source_id`, `hash`,`created_at`) SELECT `id`, '', now() FROM sys_source_task;
INSERT INTO sys_job (`plan_id`, `ds_uid`,`created_at`) SELECT `id`, `ext_id`, now() FROM sys_source_task;


