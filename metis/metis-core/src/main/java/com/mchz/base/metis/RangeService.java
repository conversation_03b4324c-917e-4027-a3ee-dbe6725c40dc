package com.mchz.base.metis;

import java.util.Collection;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/2/14
 */
public interface RangeService {

    /**
     * 获取字典值
     *
     * @param dictId 字典名
     * @param tenant 租户
     * @return 字典值
     */
    Set<String> getValueRange(Long dictId, String tenantId);

    /**
     * 批量获取字典值
     *
     * @param dictIds 字典名列表
     * @param tenant 租户
     * @return 字典值
     */
    Map<Long, Set<String>> listValueRange(Collection<Long> dictIds, String tenantId);
}
