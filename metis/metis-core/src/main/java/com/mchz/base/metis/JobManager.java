package com.mchz.base.metis;

import com.mchz.base.metis.model.TrafficParam;
import com.mchz.base.metis.model.TrafficResult;
import com.mchz.base.metis.util.concurrent.FailureCallback;
import com.mchz.base.metis.util.concurrent.SuccessCallback;

import java.util.List;
import java.util.concurrent.Executor;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/8/13
 */
public interface JobManager {
    /**
     * 设置默认回线程池
     */
    void setCallbackDefaultExecutor(Executor executor);

    /**
     * 注册回调函数
     */
    default void register(SuccessCallback<String> callback) {
        register(callback, null);
    }

    /**
     * 注册回调函数
     */
    void register(SuccessCallback<String> successCallback, FailureCallback<String> failureCallback);

    /**
     * 注册标准（规则/值域）服务
     */
    void register(StandardService standardService, RangeService rangeService);

    /**
     * 注销所有回调函数
     */
    void unregister();

    /**
     * 提交任务
     *
     * @return id
     */
    String submit(DatasourceMeta source, JobConfig config);

    /**
     * 中断任务
     */
    void interrupt(String id);

    /**
     * 获取结果
     */
    default AnalysisResults fetchResult(String id) {
        return fetchResult(id, Options.succinct());
    }

    /**
     * 获取结果
     */
    AnalysisResults fetchResult(String id, Options options);

    /**
     * 获取附加信息
     */
    String getExtInfo(String id);

    /**
     * 查询租户信息
     */
    String getTenant(String id);

    /**
     * 流量相关分类分级
     */
    List<TrafficResult> trafficClassify(List<TrafficParam> req, JobConfig config);

    /**
     * 释放流量相关分类分级执行引擎
     */
    void releaseTrafficClassify(JobConfig config);
}
