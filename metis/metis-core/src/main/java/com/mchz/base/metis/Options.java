package com.mchz.base.metis;


import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/8/15
 */
@Getter
@Setter
@Accessors(chain = true)
public class Options {
    private boolean withClassify;
    private boolean withComment;
    private boolean recognized;

    public static Options succinct() {
        return new Options().setWithComment(false).setWithComment(false);
    }

    public static Options abundant() {
        return new Options().withClassify().withComment();
    }

    public Options withClassify() {
        withClassify = true;
        return this;
    }

    public Options withComment() {
        withComment = true;
        return this;
    }

    public Options recognized() {
        recognized = true;
        return this;
    }
}
