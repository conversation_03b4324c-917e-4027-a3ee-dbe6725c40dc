package com.mchz.base.metis;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Properties;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/8/15
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
public class DataSourceParam implements DatasourceMeta {

    private String dsUid;
    private String pluginId;

    private String host;
    private String port;
    private String user;
    private String pass;

    private String dbName;

    /**
     * 统一数据源高级参数
     */
    private Properties properties;

    public DataSourceParam(DatasourceMeta meta) {
        this.dsUid = meta.getDsUid();
        this.pluginId = meta.getPluginId();
        this.host = meta.getHost();
        this.port = meta.getPort();
        this.user = meta.getUser();
        this.pass = meta.getPass();
        this.dbName = meta.getDbName();
        this.properties = meta.getProperties();
    }
}
