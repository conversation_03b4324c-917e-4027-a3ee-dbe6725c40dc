package com.mchz.base.metis;

import com.mchz.base.metis.model.ClassifyTypeDTO;
import com.mchz.base.metis.model.StandardDetail;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/2/14
 */
public interface StandardService {
    /**
     * 任务id
     * {
     * "rule": [
     * {
     * "id": 1,
     * "inner": "内置规则引用",
     * "regex": "",
     * "dictId": 1
     * }
     * ]
     * }
     */
    StandardDetail loadStandardDetail(Long stdId, String tenantId);

    /**
     * 任务id
     * {
     * "id":1
     * "bizName": "名称",
     * "sensitive": 1,
     * "level": 2,
     * "levelName": "敏感等级名称",
     * "types": ["个人身份信息", "证件"]
     * }
     */
    List<ClassifyTypeDTO> loadClassifyType(Long stdId, String tenantId);

    /**
     * 标准内容id列表
     * {
     * "id":1
     * "bizName": "名称",
     * "sensitive": 1,
     * "level": 2,
     * "levelName": "敏感等级名称",
     * "types": ["个人身份信息", "证件"]
     * }
     */
    List<ClassifyTypeDTO> loadClassifyType(Collection<Long> stdBizIds, String tenantId);
}
