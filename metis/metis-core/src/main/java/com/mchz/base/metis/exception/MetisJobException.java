package com.mchz.base.metis.exception;

import lombok.Getter;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/8/30
 */
@Getter
public class MetisJobException extends Exception {
    private final String detail;

    public MetisJobException(String message, String detail) {
        super(message);
        this.detail = detail;
    }

    @Override
    public synchronized Throwable fillInStackTrace() {
        return this;
    }

    public String getDetail() {
        return null == detail ? getMessage() : detail;
    }
}
