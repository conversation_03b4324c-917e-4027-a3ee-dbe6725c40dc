package com.mchz.base.metis;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/9/2
 */
public interface AnalysisColumnResult {
    /**
     * 源Uid
     */
    String getDsUid();

    /**
     * catalog
     */
    String getCatalog();

    /**
     * schema
     */
    String getSchema();

    /**
     * 表名
     */
    String getTableName();

    /**
     * 列名
     */
    String getColumnName();

    /**
     * 表注释
     */
    String getTableComment();

    /**
     * 列注释
     */
    String getColumnComment();

    /**
     * 列类型
     */
    String getColumnType();

    /**
     * 自定义业务id
     */
    Long getBizId();

    /**
     * biz名称
     */
    String getBizName();

    /**
     * 是否敏感 1:敏感 0:不敏感
     */
    Integer getSensitive();

    /**
     * 敏感等级
     */
    Integer getLevel();

    /**
     * 敏感等级名称
     */
    String getLevelName();

    /**
     * 分类
     */
    List<String> getTypes();
}
