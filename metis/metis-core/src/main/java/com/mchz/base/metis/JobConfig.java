package com.mchz.base.metis;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/8/15
 */
@Data
@Accessors(chain = true)
public class JobConfig {

    private Long id;

    private Long stdId;

    private String tenantId;

    private List<String> schemas;

    private Integer sampleLine;

    private Integer sampleRate;

    private Integer hitRate;

    /**
     * 是否开启自动规则发现
     */
    private Boolean autoRule;
    /**
     * 是否开启自动列名发现
     */
    private Boolean autoColumn;
    /**
     * 自动列名匹配基线：%
     */
    private Integer columnBaseline;
    /**
     * 是否启动自动注释配置
     */
    private Boolean autoComment;
    /**
     * 字段注释匹配基线：%
     */
    private Integer commentBaseline;

    private Boolean ignoreMeta;
    /**
     * 附加信息 长度限制200
     */
    private String extInfo;

    public static JobConfig create() {
        return new JobConfig();
    }

    public JobConfig addSchema(String... schema) {
        if (null == schemas) {
            schemas = new ArrayList<>(schema.length);
        }
        schemas.addAll(Arrays.asList(schema));
        return this;
    }
}
