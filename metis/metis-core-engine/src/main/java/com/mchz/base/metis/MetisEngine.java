package com.mchz.base.metis;

import com.mchz.base.metis.model.TrafficParam;
import com.mchz.base.metis.model.TrafficResult;

import javax.sql.DataSource;
import java.util.List;
import java.util.function.BiConsumer;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/8/14
 */
public interface MetisEngine {
    void register(BiConsumer<Long, FailureInfo> callback);

    void register(RangeService rangeService);

    void register(StandardService standardService);

    void submit(Long sourceId, DatasourceMeta source, Long planId, Long jobId, JobConfig config);

    void interrupt(Long jobId);

    void release(JobConfig config);

    List<TrafficResult> trafficClassify(List<TrafficParam> req, JobConfig config);

    DataSource getDatasource();

    DataSource getContentDatasource();

    String getSchema();
}
