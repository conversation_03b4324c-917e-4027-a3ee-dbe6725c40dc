<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.mchz.nyx</groupId>
    <artifactId>nyx</artifactId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>

  <artifactId>metis</artifactId>
  <version>1.3.0</version>
  <packaging>pom</packaging>

  <modules>
    <module>metis-core</module>
    <module>metis-core-engine</module>
    <module>metis-client</module>
    <module>metis-engine</module>
  </modules>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.mchz.cc</groupId>
        <artifactId>mc-classloader</artifactId>
        <version>1.0.0</version>
      </dependency>
      <dependency>
        <groupId>com.mchz.base.tools</groupId>
        <artifactId>launcher</artifactId>
        <version>1.0.1</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

</project>
