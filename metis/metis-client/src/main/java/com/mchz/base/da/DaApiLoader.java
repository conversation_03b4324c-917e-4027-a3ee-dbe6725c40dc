package com.mchz.base.da;


import com.mchz.base.metis.*;
import com.mchz.base.metis.common.MetisConst;
import com.mchz.base.metis.model.JdbcConfig;
import com.mchz.base.metis.model.StdInfo;
import com.mchz.base.metis.model.TrafficParam;
import com.mchz.base.metis.model.TrafficResult;
import com.mchz.base.metis.util.concurrent.FailureCallback;
import com.mchz.base.metis.util.concurrent.SuccessCallback;
import com.mchz.cc.classloader.ChildFirstClassLoader;

import java.io.File;
import java.util.List;
import java.util.concurrent.Executor;

public class DaApiLoader implements ApiLoader, JobManager, ClassificationApi {
    private static volatile DaApiLoader INSTANCE;
    /**
     * {@link com.mchz.nyx.metis.MetisApiLoader}
     */
    private static final String ENGINE_LAUNCH_CLASS_NAME = "com.mchz.nyx.metis.MetisApiLoader";

    private final CustomApiLoader apiLoader;

    private volatile JobManager jobManager;
    private volatile ClassificationApi classificationApi;

    public static DaApiLoader getInstance() {
        return getInstance(null);
    }

    public static DaApiLoader getInstance(JdbcConfig source) {
        if (null == INSTANCE) {
            synchronized (DaApiLoader.class) {
                if (null == INSTANCE) {
                    try {
                        INSTANCE = new DaApiLoader(source);
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                }
            }
        }
        return INSTANCE;
    }

    @SuppressWarnings("all")
    private DaApiLoader(JdbcConfig source) throws Exception {
        String appPath = System.getProperty(MetisConst.APP_HOME);
        if (null == appPath || appPath.isEmpty()) {
            throw new IllegalAccessException(MetisConst.APP_HOME + " is null");
        }
        File file = new File(appPath);
        if (!file.isDirectory()) {
            throw new IllegalAccessException(appPath + " not exist or not dir");
        }
        if (null != source) {
            if (null == source.getUrl()) {
                throw new IllegalArgumentException("url not empty");
            }
            if (null == source.getUsername()) {
                throw new IllegalArgumentException("username not empty");
            }
            if (null == source.getPassword()) {
                throw new IllegalArgumentException("password not empty");
            }
        }
        ChildFirstClassLoader cl = new ChildFirstClassLoader(appPath + MetisConst.API_PATH, DaApiLoader.class.getClassLoader(), "org.slf4j.");
        Class<?> apiClass = cl.loadClass(ENGINE_LAUNCH_CLASS_NAME);
        apiLoader = (CustomApiLoader) apiClass.newInstance();
        apiLoader.setSourceConfig(source);
    }

    public JobManager getJobManager() {
        if (null == jobManager) {
            synchronized (apiLoader) {
                if (null == jobManager) {
                    jobManager = apiLoader.getJobManager();
                }
            }
        }
        return jobManager;
    }

    public ClassificationApi getClassificationApi() {
        if (null == classificationApi) {
            synchronized (apiLoader) {
                if (null == classificationApi) {
                    classificationApi = apiLoader.getClassificationApi();
                }
            }
        }
        return classificationApi;
    }

    @Override
    public void setCallbackDefaultExecutor(Executor executor) {
        getJobManager().setCallbackDefaultExecutor(executor);
    }

    @Override
    public void register(SuccessCallback<String> successCallback, FailureCallback<String> failureCallback) {
        getJobManager().register(successCallback, failureCallback);
    }

    @Override
    public void register(StandardService standardService, RangeService rangeService) {
        getJobManager().register(standardService, rangeService);
    }

    @Override
    public void unregister() {
        getJobManager().unregister();
    }

    @Override
    public String submit(DatasourceMeta source, JobConfig config) {
        return getJobManager().submit(source, config);
    }

    @Override
    public void interrupt(String id) {
        getJobManager().interrupt(id);
    }

    @Override
    public AnalysisResults fetchResult(String id, Options options) {
        return getJobManager().fetchResult(id, options);
    }

    @Override
    public String getExtInfo(String id) {
        return getJobManager().getExtInfo(id);
    }

    @Override
    public String getTenant(String id) {
        return getJobManager().getTenant(id);
    }

    @Override
    public List<TrafficResult> trafficClassify(List<TrafficParam> req, JobConfig config) {
        return getJobManager().trafficClassify(req, config);
    }

    @Override
    public void releaseTrafficClassify(JobConfig config) {
        getJobManager().releaseTrafficClassify(config);
    }

    @Override
    public List<StdInfo> getClassificationStandards() {
        return getClassificationApi().getClassificationStandards();
    }

    @Override
    public BizTermRepo getAllBizTerms() {
        return getClassificationApi().getAllBizTerms();
    }
}
