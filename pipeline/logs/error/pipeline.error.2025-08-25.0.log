2025-08-25 11:48:58.987 ERROR 61276 --- [main] [] com.alibaba.druid.pool.DruidDataSource  : testWhileIdle is true, validationQuery not set
2025-08-25 11:53:42.405 ERROR 61276 --- [dark-task-1] [] c.m.n.p.job.TaskSchedulingManager       : <0.1><AXw8anMR> 【作业执行】J2(P2-S5) 执行异常

java.lang.NullPointerException: null
	at java.io.File.<init>(File.java:277) ~[na:1.8.0_301]
	at com.mchz.nyx.pipeline.service.impl.FileManagerServiceImpl.loadFile(FileManagerServiceImpl.java:56) ~[classes/:na]
	at com.mchz.nyx.pipeline.generator.meta.FileMetaAdapter.execute(FileMetaAdapter.java:110) ~[classes/:na]
	at com.mchz.nyx.meta.instance.crawl.MetaAgentCrawl.execute(MetaAgentCrawl.java:45) ~[classes/:na]
	at com.mchz.nyx.meta.instance.crawl.AbstractEventCrawl.crawl(AbstractEventCrawl.java:28) ~[classes/:na]
	at com.mchz.nyx.meta.instance.DefaultNyxInstance.run(DefaultNyxInstance.java:31) ~[classes/:na]
	at com.mchz.nyx.pipeline.job.collect.CollectMetaJob.execute(CollectMetaJob.java:73) ~[classes/:na]
	at com.mchz.nyx.pipeline.job.TaskSchedulingManager.executeTask(TaskSchedulingManager.java:169) [classes/:na]
	at com.mchz.nyx.pipeline.job.TaskSchedulingManager.lambda$submit$5(TaskSchedulingManager.java:111) [classes/:na]
	at com.alibaba.ttl.TtlRunnable.run(TtlRunnable.java:59) ~[transmittable-thread-local-2.12.4.jar:na]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) ~[na:1.8.0_301]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266) ~[na:1.8.0_301]
	at java.util.concurrent.FutureTask.run(FutureTask.java) ~[na:1.8.0_301]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_301]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_301]
	at java.lang.Thread.run(Thread.java:748) ~[na:1.8.0_301]

2025-08-25 11:54:44.289 ERROR 61276 --- [dark-task-2] [] c.m.n.p.job.TaskSchedulingManager       : <0.1><x9V0v33h> 【作业执行】J3(P2-S5) 执行异常

java.lang.NullPointerException: null
	at java.io.File.<init>(File.java:277) ~[na:1.8.0_301]
	at com.mchz.nyx.pipeline.service.impl.FileManagerServiceImpl.loadFile(FileManagerServiceImpl.java:56) ~[classes/:na]
	at com.mchz.nyx.pipeline.generator.meta.FileMetaAdapter.execute(FileMetaAdapter.java:110) ~[classes/:na]
	at com.mchz.nyx.meta.instance.crawl.MetaAgentCrawl.execute(MetaAgentCrawl.java:45) ~[classes/:na]
	at com.mchz.nyx.meta.instance.crawl.AbstractEventCrawl.crawl(AbstractEventCrawl.java:28) ~[classes/:na]
	at com.mchz.nyx.meta.instance.DefaultNyxInstance.run(DefaultNyxInstance.java:31) ~[classes/:na]
	at com.mchz.nyx.pipeline.job.collect.CollectMetaJob.execute(CollectMetaJob.java:73) ~[classes/:na]
	at com.mchz.nyx.pipeline.job.TaskSchedulingManager.executeTask(TaskSchedulingManager.java:169) [classes/:na]
	at com.mchz.nyx.pipeline.job.TaskSchedulingManager.lambda$submit$5(TaskSchedulingManager.java:111) [classes/:na]
	at com.alibaba.ttl.TtlRunnable.run(TtlRunnable.java:59) ~[transmittable-thread-local-2.12.4.jar:na]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) ~[na:1.8.0_301]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266) ~[na:1.8.0_301]
	at java.util.concurrent.FutureTask.run(FutureTask.java) ~[na:1.8.0_301]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_301]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_301]
	at java.lang.Thread.run(Thread.java:748) ~[na:1.8.0_301]

2025-08-25 11:55:27.476 ERROR 116336 --- [main] [] com.alibaba.druid.pool.DruidDataSource  : testWhileIdle is true, validationQuery not set
2025-08-25 14:25:22.922 ERROR 63940 --- [main] [] com.alibaba.druid.pool.DruidDataSource  : testWhileIdle is true, validationQuery not set
