2025-08-13 14:27:49.470 ERROR 53236 --- [main] [] com.alibaba.druid.pool.DruidDataSource  : testWhileIdle is true, validationQuery not set
2025-08-13 14:28:35.677 ERROR 53236 --- [http-nio-8888-exec-2] [] c.m.m.c.c.plugins.CommonDatabaseMeta    : <0.1><Ajq1ZRNS> 初始化  参数失败,name:Mysql,根据传入的插件路径：C:\file\mcdatasource_1681 以及版本:*******没有找到相应的插件
2025-08-13 14:28:35.693 ERROR 53236 --- [http-nio-8888-exec-2] [] c.m.n.p.e.GlobalRestExceptionHandler    : <0.1><Ajq1ZRNS> 服务异常

java.lang.NullPointerException: null
	at com.mchz.mcdatasource.core.classloder.plugins.CommonDatabaseMeta.getAttributes(CommonDatabaseMeta.java:94) ~[datasource-common-*******-release.jar:na]
	at org.pentaho.di.core.database.BaseDatabaseMeta.setDatabasePortNumberString(BaseDatabaseMeta.java:432) ~[datasource-kettle-core-*******-release.jar:na]
	at org.pentaho.di.core.database.DatabaseMeta.setDBPort(DatabaseMeta.java:811) ~[datasource-kettle-core-*******-release.jar:na]
	at org.pentaho.di.core.database.DatabaseMeta.setValues(DatabaseMeta.java:633) ~[datasource-kettle-core-*******-release.jar:na]
	at org.pentaho.di.core.database.DatabaseMeta.<init>(DatabaseMeta.java:484) ~[datasource-kettle-core-*******-release.jar:na]
	at com.mchz.mcdatasource.model.db.DatasourceDatabaseMeta.<init>(DatasourceDatabaseMeta.java:105) ~[datasource-common-*******-release.jar:na]
	at com.mchz.nyx.pipeline.service.impl.DatasourceServiceImpl.testConnection(DatasourceServiceImpl.java:48) ~[classes/:na]
	at com.mchz.nyx.pipeline.controller.DatabaseController.testConnection(DatabaseController.java:50) ~[classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) [spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555) [tomcat-embed-core-9.0.106.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623) [tomcat-embed-core-9.0.106.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) [tomcat-embed-websocket-9.0.106.jar:9.0.106]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at java.lang.Thread.run(Thread.java:748) [na:1.8.0_301]

2025-08-13 14:35:02.834 ERROR 53236 --- [http-nio-8888-exec-3] [] c.m.m.c.c.plugins.CommonDatabaseMeta    : <0.1><Io5cLOka> 初始化  参数失败,name:Mysql,根据传入的插件路径：C:\file\mcdatasource_1681 以及版本:*******没有找到相应的插件
2025-08-13 14:35:02.836 ERROR 53236 --- [http-nio-8888-exec-3] [] c.m.n.p.e.GlobalRestExceptionHandler    : <0.1><Io5cLOka> 服务异常

java.lang.NullPointerException: null
	at com.mchz.mcdatasource.core.classloder.plugins.CommonDatabaseMeta.getAttributes(CommonDatabaseMeta.java:94) ~[datasource-common-*******-release.jar:na]
	at org.pentaho.di.core.database.BaseDatabaseMeta.setDatabasePortNumberString(BaseDatabaseMeta.java:432) ~[datasource-kettle-core-*******-release.jar:na]
	at org.pentaho.di.core.database.DatabaseMeta.setDBPort(DatabaseMeta.java:811) ~[datasource-kettle-core-*******-release.jar:na]
	at org.pentaho.di.core.database.DatabaseMeta.setValues(DatabaseMeta.java:633) ~[datasource-kettle-core-*******-release.jar:na]
	at org.pentaho.di.core.database.DatabaseMeta.<init>(DatabaseMeta.java:484) ~[datasource-kettle-core-*******-release.jar:na]
	at com.mchz.mcdatasource.model.db.DatasourceDatabaseMeta.<init>(DatasourceDatabaseMeta.java:105) ~[datasource-common-*******-release.jar:na]
	at com.mchz.nyx.pipeline.service.impl.DatasourceServiceImpl.testConnection(DatasourceServiceImpl.java:48) ~[classes/:na]
	at com.mchz.nyx.pipeline.controller.DatabaseController.testConnection(DatabaseController.java:50) ~[classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) ~[spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) ~[spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) [spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555) [tomcat-embed-core-9.0.106.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623) [tomcat-embed-core-9.0.106.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) [tomcat-embed-websocket-9.0.106.jar:9.0.106]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at java.lang.Thread.run(Thread.java:748) [na:1.8.0_301]

2025-08-13 14:35:30.153 ERROR 58448 --- [main] [] com.alibaba.druid.pool.DruidDataSource  : testWhileIdle is true, validationQuery not set
2025-08-13 14:36:28.125 ERROR 58448 --- [http-nio-8888-exec-1] [] c.m.n.p.s.impl.DatasourceServiceImpl    : <0.1><TEBtMppH> 测试连接失败

com.mchz.mcdatasource.model.db.exception.DatabaseException: 
java.sql.SQLNonTransientConnectionException: Public Key Retrieval is not allowed

	at com.mchz.mcdatasource.model.db.DatasourceDatabase.connect(DatasourceDatabase.java:469) ~[datasource-common-*******-release.jar:na]
	at com.mchz.nyx.pipeline.service.impl.DatasourceServiceImpl.testConnection(DatasourceServiceImpl.java:51) ~[classes/:na]
	at com.mchz.nyx.pipeline.controller.DatabaseController.testConnection(DatabaseController.java:50) [classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:205) [spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:150) [spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:117) [spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:895) [spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:808) [spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87) [spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1072) [spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:965) [spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1006) [spring-webmvc-5.3.31.jar:5.3.31]
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:909) [spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:555) [tomcat-embed-core-9.0.106.jar:4.0.FR]
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:883) [spring-webmvc-5.3.31.jar:5.3.31]
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:623) [tomcat-embed-core-9.0.106.jar:4.0.FR]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:199) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51) [tomcat-embed-websocket-9.0.106.jar:9.0.106]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100) [spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93) [spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201) [spring-web-5.3.39.jar:5.3.39]
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:117) [spring-web-5.3.39.jar:5.3.39]
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:168) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:144) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:168) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:130) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:346) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:397) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:935) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1826) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1189) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:658) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63) [tomcat-embed-core-9.0.106.jar:9.0.106]
	at java.lang.Thread.run(Thread.java:748) [na:1.8.0_301]
Caused by: java.sql.SQLException: java.sql.SQLNonTransientConnectionException: Public Key Retrieval is not allowed
	at com.mchz.mcdatasource.core.classloder.BaseDiverProxy.connect(BaseDiverProxy.java:213) ~[datasource-common-*******-release.jar:na]
	at java.sql.DriverManager.getConnection(DriverManager.java:664) ~[na:1.8.0_301]
	at java.sql.DriverManager.getConnection(DriverManager.java:208) ~[na:1.8.0_301]
	at org.pentaho.di.core.database.Database.connectUsingClass(Database.java:525) ~[datasource-kettle-core-*******-release.jar:na]
	at org.pentaho.di.core.database.Database.normalConnect(Database.java:404) ~[datasource-kettle-core-*******-release.jar:na]
	at org.pentaho.di.core.database.Database.connect(Database.java:319) ~[datasource-kettle-core-*******-release.jar:na]
	at org.pentaho.di.core.database.Database.connect(Database.java:291) ~[datasource-kettle-core-*******-release.jar:na]
	at org.pentaho.di.core.database.Database.connect(Database.java:262) ~[datasource-kettle-core-*******-release.jar:na]
	at com.mchz.mcdatasource.model.db.DatasourceDatabase.connect(DatasourceDatabase.java:467) ~[datasource-common-*******-release.jar:na]
	... 52 common frames omitted
Caused by: java.sql.SQLNonTransientConnectionException: Public Key Retrieval is not allowed
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:110) ~[mysql-connector-java-8.0.30.jar:8.0.30]
	at com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122) ~[mysql-connector-java-8.0.30.jar:8.0.30]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828) ~[mysql-connector-java-8.0.30.jar:8.0.30]
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448) ~[mysql-connector-java-8.0.30.jar:8.0.30]
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241) ~[mysql-connector-java-8.0.30.jar:8.0.30]
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198) ~[mysql-connector-java-8.0.30.jar:8.0.30]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at com.mchz.mcdatasource.core.classloder.BaseDiverProxy$1.call(BaseDiverProxy.java:198) ~[datasource-common-*******-release.jar:na]
	at com.mchz.mcdatasource.core.classloder.BaseDiverProxy$1.call(BaseDiverProxy.java:193) ~[datasource-common-*******-release.jar:na]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266) ~[na:1.8.0_301]
	at java.util.concurrent.FutureTask.run(FutureTask.java) ~[na:1.8.0_301]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_301]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_301]
	... 1 common frames omitted
Caused by: com.mysql.cj.exceptions.UnableToConnectException: Public Key Retrieval is not allowed
	at sun.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45) ~[na:1.8.0_301]
	at java.lang.reflect.Constructor.newInstance(Constructor.java:423) ~[na:1.8.0_301]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:61) ~[mysql-connector-java-8.0.30.jar:8.0.30]
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:85) ~[mysql-connector-java-8.0.30.jar:8.0.30]
	at com.mysql.cj.protocol.a.authentication.CachingSha2PasswordPlugin.nextAuthenticationStep(CachingSha2PasswordPlugin.java:130) ~[mysql-connector-java-8.0.30.jar:8.0.30]
	at com.mysql.cj.protocol.a.authentication.CachingSha2PasswordPlugin.nextAuthenticationStep(CachingSha2PasswordPlugin.java:49) ~[mysql-connector-java-8.0.30.jar:8.0.30]
	at com.mysql.cj.protocol.a.NativeAuthenticationProvider.proceedHandshakeWithPluggableAuthentication(NativeAuthenticationProvider.java:440) ~[mysql-connector-java-8.0.30.jar:8.0.30]
	at com.mysql.cj.protocol.a.NativeAuthenticationProvider.connect(NativeAuthenticationProvider.java:210) ~[mysql-connector-java-8.0.30.jar:8.0.30]
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1369) ~[mysql-connector-java-8.0.30.jar:8.0.30]
	at com.mysql.cj.NativeSession.connect(NativeSession.java:133) ~[mysql-connector-java-8.0.30.jar:8.0.30]
	at com.mysql.cj.jdbc.ConnectionImpl.connectOneTryOnly(ConnectionImpl.java:948) ~[mysql-connector-java-8.0.30.jar:8.0.30]
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:818) ~[mysql-connector-java-8.0.30.jar:8.0.30]
	... 14 common frames omitted

2025-08-13 14:45:21.161 ERROR 100068 --- [main] [] com.alibaba.druid.pool.DruidDataSource  : testWhileIdle is true, validationQuery not set
2025-08-13 14:47:47.315 ERROR 45168 --- [main] [] com.alibaba.druid.pool.DruidDataSource  : testWhileIdle is true, validationQuery not set
2025-08-13 15:07:13.869 ERROR 85996 --- [main] [] com.alibaba.druid.pool.DruidDataSource  : testWhileIdle is true, validationQuery not set
2025-08-13 15:08:13.425 ERROR 85996 --- [dark-task-1] [] c.m.n.p.job.TaskSchedulingManager       : <0.1><4ztR7Vbn> 【作业执行】J6(P1-S5) 执行异常

org.springframework.dao.DataIntegrityViolationException: 
### Error querying database.  Cause: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的表或视图名[discovery_job]
### The error may exist in com/mchz/nyx/pipeline/mapper/DiscoveryJobMapper.java (best guess)
### The error may involve com.mchz.nyx.pipeline.mapper.DiscoveryJobMapper.selectList
### The error occurred while executing a query
### SQL: SELECT     "heartbeat"     FROM  "discovery_job"         WHERE  ("id" = ?)
### Cause: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的表或视图名[discovery_job]
; 第1 行附近出现错误:
无效的表或视图名[discovery_job]; nested exception is dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的表或视图名[discovery_job]
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:104) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.sun.proxy.$Proxy99.selectList(Unknown Source) ~[na:na]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:164) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.sun.proxy.$Proxy146.selectList(Unknown Source) ~[na:na]
	at com.baomidou.mybatisplus.core.mapper.BaseMapper.selectOne(BaseMapper.java:332) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at java.lang.invoke.MethodHandle.invokeWithArguments(MethodHandle.java:627) ~[na:1.8.0_301]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$DefaultMethodInvoker.invoke(MybatisMapperProxy.java:172) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.sun.proxy.$Proxy146.selectOne(Unknown Source) ~[na:na]
	at com.baomidou.mybatisplus.core.mapper.BaseMapper.selectOne(BaseMapper.java:321) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at java.lang.invoke.MethodHandle.invokeWithArguments(MethodHandle.java:627) ~[na:1.8.0_301]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$DefaultMethodInvoker.invoke(MybatisMapperProxy.java:182) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.sun.proxy.$Proxy146.selectOne(Unknown Source) ~[na:na]
	at com.mchz.nyx.pipeline.service.impl.JobStatusServiceImpl.jobStart(JobStatusServiceImpl.java:65) ~[classes/:na]
	at com.mchz.nyx.pipeline.job.TaskSchedulingManager.executeTask(TaskSchedulingManager.java:168) [classes/:na]
	at com.mchz.nyx.pipeline.job.TaskSchedulingManager.lambda$submit$5(TaskSchedulingManager.java:111) [classes/:na]
	at com.alibaba.ttl.TtlRunnable.run(TtlRunnable.java:59) ~[transmittable-thread-local-2.12.4.jar:na]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) ~[na:1.8.0_301]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266) ~[na:1.8.0_301]
	at java.util.concurrent.FutureTask.run(FutureTask.java) ~[na:1.8.0_301]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_301]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_301]
	at java.lang.Thread.run(Thread.java:748) ~[na:1.8.0_301]
Caused by: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的表或视图名[discovery_job]
	at dm.jdbc.driver.DBError.throwException(DBError.java:696) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.b.o.E(MSG.java:560) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.b.o.B(MSG.java:520) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.b.o.A(MSG.java:501) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.a.a(DBAccess.java:230) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.a.a(DBAccess.java:690) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.prepareSql(DmdbPreparedStatement.java:267) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.allocateHandle(DmdbPreparedStatement.java:224) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.<init>(DmdbPreparedStatement.java:126) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.<init>(DmdbPreparedStatement.java:176) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbConnection.do_prepareStatement(DmdbConnection.java:712) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbConnection.do_prepareStatement(DmdbConnection.java:698) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbConnection.prepareStatement(DmdbConnection.java:1317) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:337) ~[HikariCP-4.0.3.jar:na]
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java) ~[HikariCP-4.0.3.jar:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.logging.jdbc.ConnectionLogger.invoke(ConnectionLogger.java:53) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy207.prepareStatement(Unknown Source) ~[na:na]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.instantiateStatement(PreparedStatementHandler.java:88) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.statement.BaseStatementHandler.prepare(BaseStatementHandler.java:90) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.prepare(RoutingStatementHandler.java:60) ~[mybatis-3.5.19.jar:3.5.19]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-3.5.12.jar:3.5.12]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy230.prepare(Unknown Source) ~[na:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.mchz.sqltranslate.config.SqlTranslateInterceptor.intercept(SqlTranslateInterceptor.java:97) ~[base-mybatis-sqltranslate-spring-boot-starter-1.1.2-SNAPSHOT.jar:na]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy230.prepare(Unknown Source) ~[na:na]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:90) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:64) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110) ~[mybatis-3.5.19.jar:3.5.19]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-3.5.12.jar:3.5.12]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy229.query(Unknown Source) ~[na:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.mchz.sqltranslate.config.SqlTranslateInterceptor.intercept(SqlTranslateInterceptor.java:73) ~[base-mybatis-sqltranslate-spring-boot-starter-1.1.2-SNAPSHOT.jar:na]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy229.query(Unknown Source) ~[na:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.github.yulichang.interceptor.MPJInterceptor.intercept(MPJInterceptor.java:85) ~[mybatis-plus-join-core-1.5.4.jar:1.5.4]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy229.query(Unknown Source) ~[na:na]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142) ~[mybatis-3.5.19.jar:3.5.19]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425) ~[mybatis-spring-2.1.2.jar:2.1.2]
	... 27 common frames omitted

2025-08-13 15:08:13.469 ERROR 85996 --- [dark-task-1] [] c.m.n.p.job.TaskSchedulingManager       : <0.1><4ztR7Vbn> 【作业执行】结束异常,
### Error updating database.  Cause: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的表或视图名[discovery_job]
### The error may exist in com/mchz/nyx/pipeline/mapper/DiscoveryJobMapper.java (best guess)
### The error may involve com.mchz.nyx.pipeline.mapper.DiscoveryJobMapper.updateById
### The error occurred while executing an update
### SQL: UPDATE "discovery_job"  SET "heartbeat"=?  WHERE "id"=?
### Cause: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的表或视图名[discovery_job]
; 第1 行附近出现错误:
无效的表或视图名[discovery_job]; nested exception is dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的表或视图名[discovery_job]:org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:104)
2025-08-13 15:08:36.092 ERROR 134000 --- [main] [] com.alibaba.druid.pool.DruidDataSource  : testWhileIdle is true, validationQuery not set
2025-08-13 15:09:14.050 ERROR 148876 --- [main] [] com.alibaba.druid.pool.DruidDataSource  : testWhileIdle is true, validationQuery not set
2025-08-13 15:09:34.863 ERROR 148876 --- [dark-task-1] [] c.m.n.p.job.TaskSchedulingManager       : <0.1><y1SX8jNl> 【作业执行】J7(P1-S5) 执行异常

org.springframework.dao.DataIntegrityViolationException: 
### Error querying database.  Cause: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
### The error may exist in com/mchz/nyx/pipeline/mapper/DiscoveryJobMapper.java (best guess)
### The error may involve com.mchz.nyx.pipeline.mapper.DiscoveryJobMapper.selectList
### The error occurred while executing a query
### SQL: SELECT     "heartbeat"     FROM  discovery_job         WHERE  ("id" = ?)
### Cause: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
; 第1 行附近出现错误:
无效的列名[id]; nested exception is dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:104) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.sun.proxy.$Proxy99.selectList(Unknown Source) ~[na:na]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:164) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.sun.proxy.$Proxy146.selectList(Unknown Source) ~[na:na]
	at com.baomidou.mybatisplus.core.mapper.BaseMapper.selectOne(BaseMapper.java:332) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at java.lang.invoke.MethodHandle.invokeWithArguments(MethodHandle.java:627) ~[na:1.8.0_301]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$DefaultMethodInvoker.invoke(MybatisMapperProxy.java:172) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.sun.proxy.$Proxy146.selectOne(Unknown Source) ~[na:na]
	at com.baomidou.mybatisplus.core.mapper.BaseMapper.selectOne(BaseMapper.java:321) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at java.lang.invoke.MethodHandle.invokeWithArguments(MethodHandle.java:627) ~[na:1.8.0_301]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$DefaultMethodInvoker.invoke(MybatisMapperProxy.java:182) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.sun.proxy.$Proxy146.selectOne(Unknown Source) ~[na:na]
	at com.mchz.nyx.pipeline.service.impl.JobStatusServiceImpl.jobStart(JobStatusServiceImpl.java:65) ~[classes/:na]
	at com.mchz.nyx.pipeline.job.TaskSchedulingManager.executeTask(TaskSchedulingManager.java:168) [classes/:na]
	at com.mchz.nyx.pipeline.job.TaskSchedulingManager.lambda$submit$5(TaskSchedulingManager.java:111) [classes/:na]
	at com.alibaba.ttl.TtlRunnable.run(TtlRunnable.java:59) ~[transmittable-thread-local-2.12.4.jar:na]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) ~[na:1.8.0_301]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266) ~[na:1.8.0_301]
	at java.util.concurrent.FutureTask.run(FutureTask.java) ~[na:1.8.0_301]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_301]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_301]
	at java.lang.Thread.run(Thread.java:748) ~[na:1.8.0_301]
Caused by: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
	at dm.jdbc.driver.DBError.throwException(DBError.java:696) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.b.o.E(MSG.java:560) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.b.o.B(MSG.java:520) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.b.o.A(MSG.java:501) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.a.a(DBAccess.java:230) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.a.a(DBAccess.java:690) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.prepareSql(DmdbPreparedStatement.java:267) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.allocateHandle(DmdbPreparedStatement.java:224) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.<init>(DmdbPreparedStatement.java:126) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.<init>(DmdbPreparedStatement.java:176) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbConnection.do_prepareStatement(DmdbConnection.java:712) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbConnection.do_prepareStatement(DmdbConnection.java:698) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbConnection.prepareStatement(DmdbConnection.java:1317) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:337) ~[HikariCP-4.0.3.jar:na]
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java) ~[HikariCP-4.0.3.jar:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.logging.jdbc.ConnectionLogger.invoke(ConnectionLogger.java:53) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy207.prepareStatement(Unknown Source) ~[na:na]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.instantiateStatement(PreparedStatementHandler.java:88) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.statement.BaseStatementHandler.prepare(BaseStatementHandler.java:90) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.prepare(RoutingStatementHandler.java:60) ~[mybatis-3.5.19.jar:3.5.19]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-3.5.12.jar:3.5.12]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy230.prepare(Unknown Source) ~[na:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.mchz.sqltranslate.config.SqlTranslateInterceptor.intercept(SqlTranslateInterceptor.java:97) ~[base-mybatis-sqltranslate-spring-boot-starter-1.1.2-SNAPSHOT.jar:na]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy230.prepare(Unknown Source) ~[na:na]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:90) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:64) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110) ~[mybatis-3.5.19.jar:3.5.19]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-3.5.12.jar:3.5.12]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy229.query(Unknown Source) ~[na:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.mchz.sqltranslate.config.SqlTranslateInterceptor.intercept(SqlTranslateInterceptor.java:73) ~[base-mybatis-sqltranslate-spring-boot-starter-1.1.2-SNAPSHOT.jar:na]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy229.query(Unknown Source) ~[na:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.github.yulichang.interceptor.MPJInterceptor.intercept(MPJInterceptor.java:85) ~[mybatis-plus-join-core-1.5.4.jar:1.5.4]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy229.query(Unknown Source) ~[na:na]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142) ~[mybatis-3.5.19.jar:3.5.19]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425) ~[mybatis-spring-2.1.2.jar:2.1.2]
	... 27 common frames omitted

2025-08-13 15:09:34.923 ERROR 148876 --- [dark-task-1] [] c.m.n.p.job.TaskSchedulingManager       : <0.1><y1SX8jNl> 【作业执行】结束异常,
### Error updating database.  Cause: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
### The error may exist in com/mchz/nyx/pipeline/mapper/DiscoveryJobMapper.java (best guess)
### The error may involve com.mchz.nyx.pipeline.mapper.DiscoveryJobMapper.updateById
### The error occurred while executing an update
### SQL: UPDATE discovery_job  SET "heartbeat"=?  WHERE "id"=?
### Cause: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
; 第1 行附近出现错误:
无效的列名[id]; nested exception is dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]:org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:104)
2025-08-13 15:31:29.304 ERROR 91572 --- [main] [] o.s.boot.SpringApplication              : Application run failed

org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'apiController' defined in file [C:\projects\nyx\pipeline\target\classes\com\mchz\nyx\pipeline\controller\ApiController.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'ruleDetailServiceImpl' defined in file [C:\projects\nyx\pipeline\target\classes\com\mchz\nyx\pipeline\service\impl\RuleDetailServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'innerRuleServiceImpl' defined in file [C:\projects\nyx\pipeline\target\classes\com\mchz\nyx\pipeline\service\impl\InnerRuleServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'artifactMapper' defined in file [C:\projects\nyx\pipeline\target\classes\com\mchz\nyx\pipeline\mapper\ArtifactMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/mchz/nyx/pipeline/config/MybatisPlusConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is java.lang.IllegalArgumentException: Property 'dataSource' is required
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:801) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:224) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1372) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1222) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:955) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:929) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:591) ~[spring-context-5.3.31.jar:5.3.31]
	at org.springframework.boot.web.servlet.context.ServletWebServerApplicationContext.refresh(ServletWebServerApplicationContext.java:147) ~[spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refresh(SpringApplication.java:732) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.refreshContext(SpringApplication.java:409) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:308) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1300) [spring-boot-2.7.18.jar:2.7.18]
	at org.springframework.boot.SpringApplication.run(SpringApplication.java:1289) [spring-boot-2.7.18.jar:2.7.18]
	at com.mchz.nyx.pipeline.Application.main(Application.java:19) [classes/:na]
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'ruleDetailServiceImpl' defined in file [C:\projects\nyx\pipeline\target\classes\com\mchz\nyx\pipeline\service\impl\RuleDetailServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'innerRuleServiceImpl' defined in file [C:\projects\nyx\pipeline\target\classes\com\mchz\nyx\pipeline\service\impl\InnerRuleServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'artifactMapper' defined in file [C:\projects\nyx\pipeline\target\classes\com\mchz\nyx\pipeline\mapper\ArtifactMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/mchz/nyx/pipeline/config/MybatisPlusConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is java.lang.IllegalArgumentException: Property 'dataSource' is required
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:801) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:224) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1372) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1222) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:911) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:788) ~[spring-beans-5.3.31.jar:5.3.31]
	... 19 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'innerRuleServiceImpl' defined in file [C:\projects\nyx\pipeline\target\classes\com\mchz\nyx\pipeline\service\impl\InnerRuleServiceImpl.class]: Unsatisfied dependency expressed through constructor parameter 0; nested exception is org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'artifactMapper' defined in file [C:\projects\nyx\pipeline\target\classes\com\mchz\nyx\pipeline\mapper\ArtifactMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/mchz/nyx/pipeline/config/MybatisPlusConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is java.lang.IllegalArgumentException: Property 'dataSource' is required
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:801) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.ConstructorResolver.autowireConstructor(ConstructorResolver.java:224) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireConstructor(AbstractAutowireCapableBeanFactory.java:1372) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1222) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:911) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:788) ~[spring-beans-5.3.31.jar:5.3.31]
	... 33 common frames omitted
Caused by: org.springframework.beans.factory.UnsatisfiedDependencyException: Error creating bean with name 'artifactMapper' defined in file [C:\projects\nyx\pipeline\target\classes\com\mchz\nyx\pipeline\mapper\ArtifactMapper.class]: Unsatisfied dependency expressed through bean property 'sqlSessionFactory'; nested exception is org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/mchz/nyx/pipeline/config/MybatisPlusConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is java.lang.IllegalArgumentException: Property 'dataSource' is required
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1534) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.populateBean(AbstractAutowireCapableBeanFactory.java:1417) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:619) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.ConstructorResolver.resolveAutowiredArgument(ConstructorResolver.java:911) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.ConstructorResolver.createArgumentArray(ConstructorResolver.java:788) ~[spring-beans-5.3.31.jar:5.3.31]
	... 47 common frames omitted
Caused by: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'sqlSessionFactory' defined in class path resource [com/mchz/nyx/pipeline/config/MybatisPlusConfig.class]: Bean instantiation via factory method failed; nested exception is org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is java.lang.IllegalArgumentException: Property 'dataSource' is required
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:653) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiateUsingFactoryMethod(ConstructorResolver.java:481) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateUsingFactoryMethod(AbstractAutowireCapableBeanFactory.java:1352) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1195) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:582) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:542) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.lambda$doGetBean$0(AbstractBeanFactory.java:335) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:234) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:333) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:208) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.config.DependencyDescriptor.resolveCandidate(DependencyDescriptor.java:276) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.doResolveDependency(DefaultListableBeanFactory.java:1391) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.resolveDependency(DefaultListableBeanFactory.java:1311) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.autowireByType(AbstractAutowireCapableBeanFactory.java:1519) ~[spring-beans-5.3.31.jar:5.3.31]
	... 59 common frames omitted
Caused by: org.springframework.beans.BeanInstantiationException: Failed to instantiate [org.apache.ibatis.session.SqlSessionFactory]: Factory method 'sqlSessionFactory' threw exception; nested exception is java.lang.IllegalArgumentException: Property 'dataSource' is required
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:185) ~[spring-beans-5.3.31.jar:5.3.31]
	at org.springframework.beans.factory.support.ConstructorResolver.instantiate(ConstructorResolver.java:648) ~[spring-beans-5.3.31.jar:5.3.31]
	... 72 common frames omitted
Caused by: java.lang.IllegalArgumentException: Property 'dataSource' is required
	at org.springframework.util.Assert.notNull(Assert.java:201) ~[spring-core-5.3.41.jar:5.3.41]
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.afterPropertiesSet(MybatisSqlSessionFactoryBean.java:531) ~[mybatis-plus-spring-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean.getObject(MybatisSqlSessionFactoryBean.java:692) ~[mybatis-plus-spring-3.5.12.jar:3.5.12]
	at com.mchz.nyx.pipeline.config.MybatisPlusConfig.sqlSessionFactory(MybatisPlusConfig.java:50) ~[classes/:na]
	at com.mchz.nyx.pipeline.config.MybatisPlusConfig$$EnhancerBySpringCGLIB$$cf2e5bec.CGLIB$sqlSessionFactory$0(<generated>) ~[classes/:na]
	at com.mchz.nyx.pipeline.config.MybatisPlusConfig$$EnhancerBySpringCGLIB$$cf2e5bec$$FastClassBySpringCGLIB$$6670cfc5.invoke(<generated>) ~[classes/:na]
	at org.springframework.cglib.proxy.MethodProxy.invokeSuper(MethodProxy.java:244) ~[spring-core-5.3.41.jar:5.3.41]
	at org.springframework.context.annotation.ConfigurationClassEnhancer$BeanMethodInterceptor.intercept(ConfigurationClassEnhancer.java:331) ~[spring-context-5.3.31.jar:5.3.31]
	at com.mchz.nyx.pipeline.config.MybatisPlusConfig$$EnhancerBySpringCGLIB$$cf2e5bec.sqlSessionFactory(<generated>) ~[classes/:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:154) ~[spring-beans-5.3.31.jar:5.3.31]
	... 73 common frames omitted

2025-08-13 15:32:48.645 ERROR 132056 --- [main] [] com.alibaba.druid.pool.DruidDataSource  : testWhileIdle is true, validationQuery not set
2025-08-13 15:33:32.752 ERROR 132056 --- [dark-task-1] [] c.m.n.p.job.TaskSchedulingManager       : <0.1><gvDBjvTg> 【作业执行】J8(P1-S5) 执行异常

org.springframework.dao.DataIntegrityViolationException: 
### Error querying database.  Cause: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
### The error may exist in com/mchz/nyx/pipeline/mapper/DiscoveryJobMapper.java (best guess)
### The error may involve com.mchz.nyx.pipeline.mapper.DiscoveryJobMapper.selectList
### The error occurred while executing a query
### SQL: SELECT     "heartbeat"     FROM  discovery_job         WHERE  ("id" = ?)
### Cause: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
; 第1 行附近出现错误:
无效的列名[id]; nested exception is dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:104) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.sun.proxy.$Proxy99.selectList(Unknown Source) ~[na:na]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:164) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.sun.proxy.$Proxy146.selectList(Unknown Source) ~[na:na]
	at com.baomidou.mybatisplus.core.mapper.BaseMapper.selectOne(BaseMapper.java:332) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at java.lang.invoke.MethodHandle.invokeWithArguments(MethodHandle.java:627) ~[na:1.8.0_301]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$DefaultMethodInvoker.invoke(MybatisMapperProxy.java:172) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.sun.proxy.$Proxy146.selectOne(Unknown Source) ~[na:na]
	at com.baomidou.mybatisplus.core.mapper.BaseMapper.selectOne(BaseMapper.java:321) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at java.lang.invoke.MethodHandle.invokeWithArguments(MethodHandle.java:627) ~[na:1.8.0_301]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$DefaultMethodInvoker.invoke(MybatisMapperProxy.java:182) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.sun.proxy.$Proxy146.selectOne(Unknown Source) ~[na:na]
	at com.mchz.nyx.pipeline.service.impl.JobStatusServiceImpl.jobStart(JobStatusServiceImpl.java:65) ~[classes/:na]
	at com.mchz.nyx.pipeline.job.TaskSchedulingManager.executeTask(TaskSchedulingManager.java:168) [classes/:na]
	at com.mchz.nyx.pipeline.job.TaskSchedulingManager.lambda$submit$5(TaskSchedulingManager.java:111) [classes/:na]
	at com.alibaba.ttl.TtlRunnable.run(TtlRunnable.java:59) ~[transmittable-thread-local-2.12.4.jar:na]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) ~[na:1.8.0_301]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266) ~[na:1.8.0_301]
	at java.util.concurrent.FutureTask.run(FutureTask.java) ~[na:1.8.0_301]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_301]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_301]
	at java.lang.Thread.run(Thread.java:748) ~[na:1.8.0_301]
Caused by: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
	at dm.jdbc.driver.DBError.throwException(DBError.java:696) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.b.o.E(MSG.java:560) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.b.o.B(MSG.java:520) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.b.o.A(MSG.java:501) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.a.a(DBAccess.java:230) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.a.a(DBAccess.java:690) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.prepareSql(DmdbPreparedStatement.java:267) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.allocateHandle(DmdbPreparedStatement.java:224) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.<init>(DmdbPreparedStatement.java:126) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.<init>(DmdbPreparedStatement.java:176) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbConnection.do_prepareStatement(DmdbConnection.java:712) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbConnection.do_prepareStatement(DmdbConnection.java:698) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbConnection.prepareStatement(DmdbConnection.java:1317) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:337) ~[HikariCP-4.0.3.jar:na]
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java) ~[HikariCP-4.0.3.jar:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.logging.jdbc.ConnectionLogger.invoke(ConnectionLogger.java:53) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy207.prepareStatement(Unknown Source) ~[na:na]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.instantiateStatement(PreparedStatementHandler.java:88) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.statement.BaseStatementHandler.prepare(BaseStatementHandler.java:90) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.prepare(RoutingStatementHandler.java:60) ~[mybatis-3.5.19.jar:3.5.19]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-3.5.12.jar:3.5.12]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy230.prepare(Unknown Source) ~[na:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.mchz.sqltranslate.config.SqlTranslateInterceptor.intercept(SqlTranslateInterceptor.java:97) ~[base-mybatis-sqltranslate-spring-boot-starter-1.1.2-SNAPSHOT.jar:na]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy230.prepare(Unknown Source) ~[na:na]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:90) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:64) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110) ~[mybatis-3.5.19.jar:3.5.19]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-3.5.12.jar:3.5.12]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy229.query(Unknown Source) ~[na:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.mchz.sqltranslate.config.SqlTranslateInterceptor.intercept(SqlTranslateInterceptor.java:73) ~[base-mybatis-sqltranslate-spring-boot-starter-1.1.2-SNAPSHOT.jar:na]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy229.query(Unknown Source) ~[na:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.github.yulichang.interceptor.MPJInterceptor.intercept(MPJInterceptor.java:85) ~[mybatis-plus-join-core-1.5.4.jar:1.5.4]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy229.query(Unknown Source) ~[na:na]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142) ~[mybatis-3.5.19.jar:3.5.19]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425) ~[mybatis-spring-2.1.2.jar:2.1.2]
	... 27 common frames omitted

2025-08-13 15:33:32.795 ERROR 132056 --- [dark-task-1] [] c.m.n.p.job.TaskSchedulingManager       : <0.1><gvDBjvTg> 【作业执行】结束异常,
### Error updating database.  Cause: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
### The error may exist in com/mchz/nyx/pipeline/mapper/DiscoveryJobMapper.java (best guess)
### The error may involve com.mchz.nyx.pipeline.mapper.DiscoveryJobMapper.updateById
### The error occurred while executing an update
### SQL: UPDATE discovery_job  SET "heartbeat"=?  WHERE "id"=?
### Cause: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
; 第1 行附近出现错误:
无效的列名[id]; nested exception is dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]:org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:104)
2025-08-13 15:50:26.312 ERROR 102368 --- [main] [] com.alibaba.druid.pool.DruidDataSource  : testWhileIdle is true, validationQuery not set
2025-08-13 15:50:52.467 ERROR 25944 --- [main] [] com.alibaba.druid.pool.DruidDataSource  : testWhileIdle is true, validationQuery not set
2025-08-13 15:51:55.650 ERROR 25944 --- [dark-task-1] [] c.m.n.p.job.TaskSchedulingManager       : <0.1><y7NZHUdU> 【作业执行】J9(P1-S5) 执行异常

org.springframework.dao.DataIntegrityViolationException: 
### Error querying database.  Cause: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
### The error may exist in com/mchz/nyx/pipeline/mapper/DiscoveryJobMapper.java (best guess)
### The error may involve com.mchz.nyx.pipeline.mapper.DiscoveryJobMapper.selectList
### The error occurred while executing a query
### SQL: SELECT     "heartbeat"     FROM  discovery_job         WHERE  ("id" = ?)
### Cause: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
; 第1 行附近出现错误:
无效的列名[id]; nested exception is dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:104) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.sun.proxy.$Proxy99.selectList(Unknown Source) ~[na:na]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:164) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.sun.proxy.$Proxy146.selectList(Unknown Source) ~[na:na]
	at com.baomidou.mybatisplus.core.mapper.BaseMapper.selectOne(BaseMapper.java:332) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at java.lang.invoke.MethodHandle.invokeWithArguments(MethodHandle.java:627) ~[na:1.8.0_301]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$DefaultMethodInvoker.invoke(MybatisMapperProxy.java:172) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.sun.proxy.$Proxy146.selectOne(Unknown Source) ~[na:na]
	at com.baomidou.mybatisplus.core.mapper.BaseMapper.selectOne(BaseMapper.java:321) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at java.lang.invoke.MethodHandle.invokeWithArguments(MethodHandle.java:627) ~[na:1.8.0_301]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$DefaultMethodInvoker.invoke(MybatisMapperProxy.java:182) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.sun.proxy.$Proxy146.selectOne(Unknown Source) ~[na:na]
	at com.mchz.nyx.pipeline.service.impl.JobStatusServiceImpl.jobStart(JobStatusServiceImpl.java:65) ~[classes/:na]
	at com.mchz.nyx.pipeline.job.TaskSchedulingManager.executeTask(TaskSchedulingManager.java:168) [classes/:na]
	at com.mchz.nyx.pipeline.job.TaskSchedulingManager.lambda$submit$5(TaskSchedulingManager.java:111) [classes/:na]
	at com.alibaba.ttl.TtlRunnable.run(TtlRunnable.java:59) ~[transmittable-thread-local-2.12.4.jar:na]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) ~[na:1.8.0_301]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266) ~[na:1.8.0_301]
	at java.util.concurrent.FutureTask.run(FutureTask.java) ~[na:1.8.0_301]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_301]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_301]
	at java.lang.Thread.run(Thread.java:748) ~[na:1.8.0_301]
Caused by: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
	at dm.jdbc.driver.DBError.throwException(DBError.java:696) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.b.o.E(MSG.java:560) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.b.o.B(MSG.java:520) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.b.o.A(MSG.java:501) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.a.a(DBAccess.java:230) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.a.a(DBAccess.java:690) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.prepareSql(DmdbPreparedStatement.java:267) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.allocateHandle(DmdbPreparedStatement.java:224) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.<init>(DmdbPreparedStatement.java:126) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.<init>(DmdbPreparedStatement.java:176) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbConnection.do_prepareStatement(DmdbConnection.java:712) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbConnection.do_prepareStatement(DmdbConnection.java:698) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbConnection.prepareStatement(DmdbConnection.java:1317) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:337) ~[HikariCP-4.0.3.jar:na]
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java) ~[HikariCP-4.0.3.jar:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.logging.jdbc.ConnectionLogger.invoke(ConnectionLogger.java:53) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy207.prepareStatement(Unknown Source) ~[na:na]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.instantiateStatement(PreparedStatementHandler.java:88) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.statement.BaseStatementHandler.prepare(BaseStatementHandler.java:90) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.prepare(RoutingStatementHandler.java:60) ~[mybatis-3.5.19.jar:3.5.19]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-3.5.12.jar:3.5.12]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy230.prepare(Unknown Source) ~[na:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.mchz.sqltranslate.config.SqlTranslateInterceptor.intercept(SqlTranslateInterceptor.java:97) ~[base-mybatis-sqltranslate-spring-boot-starter-1.1.2-SNAPSHOT.jar:na]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy230.prepare(Unknown Source) ~[na:na]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:90) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:64) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110) ~[mybatis-3.5.19.jar:3.5.19]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-3.5.12.jar:3.5.12]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy229.query(Unknown Source) ~[na:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.mchz.sqltranslate.config.SqlTranslateInterceptor.intercept(SqlTranslateInterceptor.java:73) ~[base-mybatis-sqltranslate-spring-boot-starter-1.1.2-SNAPSHOT.jar:na]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy229.query(Unknown Source) ~[na:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.github.yulichang.interceptor.MPJInterceptor.intercept(MPJInterceptor.java:85) ~[mybatis-plus-join-core-1.5.4.jar:1.5.4]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy229.query(Unknown Source) ~[na:na]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142) ~[mybatis-3.5.19.jar:3.5.19]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425) ~[mybatis-spring-2.1.2.jar:2.1.2]
	... 27 common frames omitted

2025-08-13 15:51:55.693 ERROR 25944 --- [dark-task-1] [] c.m.n.p.job.TaskSchedulingManager       : <0.1><y7NZHUdU> 【作业执行】结束异常,
### Error updating database.  Cause: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
### The error may exist in com/mchz/nyx/pipeline/mapper/DiscoveryJobMapper.java (best guess)
### The error may involve com.mchz.nyx.pipeline.mapper.DiscoveryJobMapper.updateById
### The error occurred while executing an update
### SQL: UPDATE discovery_job  SET "heartbeat"=?  WHERE "id"=?
### Cause: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
; 第1 行附近出现错误:
无效的列名[id]; nested exception is dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]:org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:104)
2025-08-13 15:54:44.775 ERROR 25944 --- [dark-task-2] [] c.m.n.p.job.TaskSchedulingManager       : <0.1><Hgkn7U6q> 【作业执行】J10(P1-S5) 执行异常

org.springframework.dao.DataIntegrityViolationException: 
### Error querying database.  Cause: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
### The error may exist in com/mchz/nyx/pipeline/mapper/DiscoveryJobMapper.java (best guess)
### The error may involve com.mchz.nyx.pipeline.mapper.DiscoveryJobMapper.selectList
### The error occurred while executing a query
### SQL: SELECT     "heartbeat"     FROM  discovery_job         WHERE  ("id" = ?)
### Cause: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
; 第1 行附近出现错误:
无效的列名[id]; nested exception is dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:104) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.sun.proxy.$Proxy99.selectList(Unknown Source) ~[na:na]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:164) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.sun.proxy.$Proxy146.selectList(Unknown Source) ~[na:na]
	at com.baomidou.mybatisplus.core.mapper.BaseMapper.selectOne(BaseMapper.java:332) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at java.lang.invoke.MethodHandle.invokeWithArguments(MethodHandle.java:627) ~[na:1.8.0_301]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$DefaultMethodInvoker.invoke(MybatisMapperProxy.java:172) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.sun.proxy.$Proxy146.selectOne(Unknown Source) ~[na:na]
	at com.baomidou.mybatisplus.core.mapper.BaseMapper.selectOne(BaseMapper.java:321) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at java.lang.invoke.MethodHandle.invokeWithArguments(MethodHandle.java:627) ~[na:1.8.0_301]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$DefaultMethodInvoker.invoke(MybatisMapperProxy.java:182) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.sun.proxy.$Proxy146.selectOne(Unknown Source) ~[na:na]
	at com.mchz.nyx.pipeline.service.impl.JobStatusServiceImpl.jobStart(JobStatusServiceImpl.java:65) ~[classes/:na]
	at com.mchz.nyx.pipeline.job.TaskSchedulingManager.executeTask(TaskSchedulingManager.java:168) [classes/:na]
	at com.mchz.nyx.pipeline.job.TaskSchedulingManager.lambda$submit$5(TaskSchedulingManager.java:111) [classes/:na]
	at com.alibaba.ttl.TtlRunnable.run(TtlRunnable.java:59) ~[transmittable-thread-local-2.12.4.jar:na]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) ~[na:1.8.0_301]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266) ~[na:1.8.0_301]
	at java.util.concurrent.FutureTask.run(FutureTask.java) ~[na:1.8.0_301]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_301]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_301]
	at java.lang.Thread.run(Thread.java:748) ~[na:1.8.0_301]
Caused by: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
	at dm.jdbc.driver.DBError.throwException(DBError.java:696) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.b.o.E(MSG.java:560) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.b.o.B(MSG.java:520) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.b.o.A(MSG.java:501) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.a.a(DBAccess.java:230) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.a.a(DBAccess.java:690) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.prepareSql(DmdbPreparedStatement.java:267) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.allocateHandle(DmdbPreparedStatement.java:224) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.<init>(DmdbPreparedStatement.java:126) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.<init>(DmdbPreparedStatement.java:176) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbConnection.do_prepareStatement(DmdbConnection.java:712) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbConnection.do_prepareStatement(DmdbConnection.java:698) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbConnection.prepareStatement(DmdbConnection.java:1317) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:337) ~[HikariCP-4.0.3.jar:na]
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java) ~[HikariCP-4.0.3.jar:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.logging.jdbc.ConnectionLogger.invoke(ConnectionLogger.java:53) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy207.prepareStatement(Unknown Source) ~[na:na]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.instantiateStatement(PreparedStatementHandler.java:88) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.statement.BaseStatementHandler.prepare(BaseStatementHandler.java:90) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.prepare(RoutingStatementHandler.java:60) ~[mybatis-3.5.19.jar:3.5.19]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-3.5.12.jar:3.5.12]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy230.prepare(Unknown Source) ~[na:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.mchz.sqltranslate.config.SqlTranslateInterceptor.intercept(SqlTranslateInterceptor.java:97) ~[base-mybatis-sqltranslate-spring-boot-starter-1.1.2-SNAPSHOT.jar:na]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy230.prepare(Unknown Source) ~[na:na]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:90) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:64) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110) ~[mybatis-3.5.19.jar:3.5.19]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-3.5.12.jar:3.5.12]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy229.query(Unknown Source) ~[na:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.mchz.sqltranslate.config.SqlTranslateInterceptor.intercept(SqlTranslateInterceptor.java:73) ~[base-mybatis-sqltranslate-spring-boot-starter-1.1.2-SNAPSHOT.jar:na]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy229.query(Unknown Source) ~[na:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.github.yulichang.interceptor.MPJInterceptor.intercept(MPJInterceptor.java:85) ~[mybatis-plus-join-core-1.5.4.jar:1.5.4]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy229.query(Unknown Source) ~[na:na]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142) ~[mybatis-3.5.19.jar:3.5.19]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425) ~[mybatis-spring-2.1.2.jar:2.1.2]
	... 27 common frames omitted

2025-08-13 15:55:00.166 ERROR 25944 --- [dark-task-2] [] c.m.n.p.job.TaskSchedulingManager       : <0.1><Hgkn7U6q> 【作业执行】结束异常,
### Error updating database.  Cause: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
### The error may exist in com/mchz/nyx/pipeline/mapper/DiscoveryJobMapper.java (best guess)
### The error may involve com.mchz.nyx.pipeline.mapper.DiscoveryJobMapper.updateById
### The error occurred while executing an update
### SQL: UPDATE discovery_job  SET "heartbeat"=?  WHERE "id"=?
### Cause: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
; 第1 行附近出现错误:
无效的列名[id]; nested exception is dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]:org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:104)
2025-08-13 15:55:19.138 ERROR 145804 --- [main] [] com.alibaba.druid.pool.DruidDataSource  : testWhileIdle is true, validationQuery not set
2025-08-13 15:57:06.908 ERROR 145804 --- [dark-task-1] [] c.m.n.p.job.TaskSchedulingManager       : <0.1><vmuEbnxV> 【作业执行】J11(P1-S5) 执行异常

org.springframework.dao.DataIntegrityViolationException: 
### Error querying database.  Cause: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
### The error may exist in com/mchz/nyx/pipeline/mapper/DiscoveryJobMapper.java (best guess)
### The error may involve com.mchz.nyx.pipeline.mapper.DiscoveryJobMapper.selectList
### The error occurred while executing a query
### SQL: SELECT     "heartbeat"     FROM  discovery_job         WHERE  ("id" = ?)
### Cause: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
; 第1 行附近出现错误:
无效的列名[id]; nested exception is dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:104) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.sun.proxy.$Proxy99.selectList(Unknown Source) ~[na:na]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:164) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.sun.proxy.$Proxy146.selectList(Unknown Source) ~[na:na]
	at com.baomidou.mybatisplus.core.mapper.BaseMapper.selectOne(BaseMapper.java:332) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at java.lang.invoke.MethodHandle.invokeWithArguments(MethodHandle.java:627) ~[na:1.8.0_301]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$DefaultMethodInvoker.invoke(MybatisMapperProxy.java:172) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.sun.proxy.$Proxy146.selectOne(Unknown Source) ~[na:na]
	at com.baomidou.mybatisplus.core.mapper.BaseMapper.selectOne(BaseMapper.java:321) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at java.lang.invoke.MethodHandle.invokeWithArguments(MethodHandle.java:627) ~[na:1.8.0_301]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$DefaultMethodInvoker.invoke(MybatisMapperProxy.java:182) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.sun.proxy.$Proxy146.selectOne(Unknown Source) ~[na:na]
	at com.mchz.nyx.pipeline.service.impl.JobStatusServiceImpl.jobStart(JobStatusServiceImpl.java:65) ~[classes/:na]
	at com.mchz.nyx.pipeline.job.TaskSchedulingManager.executeTask(TaskSchedulingManager.java:168) [classes/:na]
	at com.mchz.nyx.pipeline.job.TaskSchedulingManager.lambda$submit$5(TaskSchedulingManager.java:111) [classes/:na]
	at com.alibaba.ttl.TtlRunnable.run(TtlRunnable.java:59) ~[transmittable-thread-local-2.12.4.jar:na]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) ~[na:1.8.0_301]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266) ~[na:1.8.0_301]
	at java.util.concurrent.FutureTask.run(FutureTask.java) ~[na:1.8.0_301]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_301]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_301]
	at java.lang.Thread.run(Thread.java:748) ~[na:1.8.0_301]
Caused by: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
	at dm.jdbc.driver.DBError.throwException(DBError.java:696) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.b.o.E(MSG.java:560) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.b.o.B(MSG.java:520) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.b.o.A(MSG.java:501) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.a.a(DBAccess.java:230) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.a.a(DBAccess.java:690) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.prepareSql(DmdbPreparedStatement.java:267) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.allocateHandle(DmdbPreparedStatement.java:224) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.<init>(DmdbPreparedStatement.java:126) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.<init>(DmdbPreparedStatement.java:176) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbConnection.do_prepareStatement(DmdbConnection.java:712) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbConnection.do_prepareStatement(DmdbConnection.java:698) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbConnection.prepareStatement(DmdbConnection.java:1317) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:337) ~[HikariCP-4.0.3.jar:na]
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java) ~[HikariCP-4.0.3.jar:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.logging.jdbc.ConnectionLogger.invoke(ConnectionLogger.java:53) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy207.prepareStatement(Unknown Source) ~[na:na]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.instantiateStatement(PreparedStatementHandler.java:88) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.statement.BaseStatementHandler.prepare(BaseStatementHandler.java:90) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.prepare(RoutingStatementHandler.java:60) ~[mybatis-3.5.19.jar:3.5.19]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-3.5.12.jar:3.5.12]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy230.prepare(Unknown Source) ~[na:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.mchz.sqltranslate.config.SqlTranslateInterceptor.intercept(SqlTranslateInterceptor.java:97) ~[base-mybatis-sqltranslate-spring-boot-starter-1.1.2-SNAPSHOT.jar:na]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy230.prepare(Unknown Source) ~[na:na]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:90) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:64) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110) ~[mybatis-3.5.19.jar:3.5.19]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-3.5.12.jar:3.5.12]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy229.query(Unknown Source) ~[na:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.mchz.sqltranslate.config.SqlTranslateInterceptor.intercept(SqlTranslateInterceptor.java:73) ~[base-mybatis-sqltranslate-spring-boot-starter-1.1.2-SNAPSHOT.jar:na]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy229.query(Unknown Source) ~[na:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.github.yulichang.interceptor.MPJInterceptor.intercept(MPJInterceptor.java:85) ~[mybatis-plus-join-core-1.5.4.jar:1.5.4]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy229.query(Unknown Source) ~[na:na]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142) ~[mybatis-3.5.19.jar:3.5.19]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425) ~[mybatis-spring-2.1.2.jar:2.1.2]
	... 27 common frames omitted

2025-08-13 15:57:06.957 ERROR 145804 --- [dark-task-1] [] c.m.n.p.job.TaskSchedulingManager       : <0.1><vmuEbnxV> 【作业执行】结束异常,
### Error updating database.  Cause: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
### The error may exist in com/mchz/nyx/pipeline/mapper/DiscoveryJobMapper.java (best guess)
### The error may involve com.mchz.nyx.pipeline.mapper.DiscoveryJobMapper.updateById
### The error occurred while executing an update
### SQL: UPDATE discovery_job  SET "heartbeat"=?  WHERE "id"=?
### Cause: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
; 第1 行附近出现错误:
无效的列名[id]; nested exception is dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]:org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:104)
2025-08-13 16:02:27.751 ERROR 39580 --- [main] [] com.alibaba.druid.pool.DruidDataSource  : testWhileIdle is true, validationQuery not set
2025-08-13 16:12:56.690 ERROR 39580 --- [dark-task-1] [] c.m.n.p.job.TaskSchedulingManager       : <0.1><HfOS4EUD> 【作业执行】J12(P1-S5) 执行异常

org.springframework.dao.DataIntegrityViolationException: 
### Error querying database.  Cause: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
### The error may exist in com/mchz/nyx/pipeline/mapper/DiscoveryJobMapper.java (best guess)
### The error may involve com.mchz.nyx.pipeline.mapper.DiscoveryJobMapper.selectList
### The error occurred while executing a query
### SQL: SELECT     "heartbeat"     FROM  discovery_job         WHERE  ("id" = ?)
### Cause: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
; 第1 行附近出现错误:
无效的列名[id]; nested exception is dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:104) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.sun.proxy.$Proxy99.selectList(Unknown Source) ~[na:na]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:164) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.sun.proxy.$Proxy146.selectList(Unknown Source) ~[na:na]
	at com.baomidou.mybatisplus.core.mapper.BaseMapper.selectOne(BaseMapper.java:332) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at java.lang.invoke.MethodHandle.invokeWithArguments(MethodHandle.java:627) ~[na:1.8.0_301]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$DefaultMethodInvoker.invoke(MybatisMapperProxy.java:172) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.sun.proxy.$Proxy146.selectOne(Unknown Source) ~[na:na]
	at com.baomidou.mybatisplus.core.mapper.BaseMapper.selectOne(BaseMapper.java:321) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at java.lang.invoke.MethodHandle.invokeWithArguments(MethodHandle.java:627) ~[na:1.8.0_301]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$DefaultMethodInvoker.invoke(MybatisMapperProxy.java:182) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.sun.proxy.$Proxy146.selectOne(Unknown Source) ~[na:na]
	at com.mchz.nyx.pipeline.service.impl.JobStatusServiceImpl.jobStart(JobStatusServiceImpl.java:65) ~[classes/:na]
	at com.mchz.nyx.pipeline.job.TaskSchedulingManager.executeTask(TaskSchedulingManager.java:168) [classes/:na]
	at com.mchz.nyx.pipeline.job.TaskSchedulingManager.lambda$submit$5(TaskSchedulingManager.java:111) [classes/:na]
	at com.alibaba.ttl.TtlRunnable.run(TtlRunnable.java:59) ~[transmittable-thread-local-2.12.4.jar:na]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) ~[na:1.8.0_301]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266) ~[na:1.8.0_301]
	at java.util.concurrent.FutureTask.run(FutureTask.java) ~[na:1.8.0_301]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_301]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_301]
	at java.lang.Thread.run(Thread.java:748) ~[na:1.8.0_301]
Caused by: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
	at dm.jdbc.driver.DBError.throwException(DBError.java:696) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.b.o.E(MSG.java:560) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.b.o.B(MSG.java:520) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.b.o.A(MSG.java:501) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.a.a(DBAccess.java:230) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.a.a(DBAccess.java:690) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.prepareSql(DmdbPreparedStatement.java:267) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.allocateHandle(DmdbPreparedStatement.java:224) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.<init>(DmdbPreparedStatement.java:126) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.<init>(DmdbPreparedStatement.java:176) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbConnection.do_prepareStatement(DmdbConnection.java:712) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbConnection.do_prepareStatement(DmdbConnection.java:698) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbConnection.prepareStatement(DmdbConnection.java:1317) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:337) ~[HikariCP-4.0.3.jar:na]
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java) ~[HikariCP-4.0.3.jar:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.logging.jdbc.ConnectionLogger.invoke(ConnectionLogger.java:53) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy207.prepareStatement(Unknown Source) ~[na:na]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.instantiateStatement(PreparedStatementHandler.java:88) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.statement.BaseStatementHandler.prepare(BaseStatementHandler.java:90) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.prepare(RoutingStatementHandler.java:60) ~[mybatis-3.5.19.jar:3.5.19]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-3.5.12.jar:3.5.12]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy230.prepare(Unknown Source) ~[na:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.mchz.sqltranslate.config.SqlTranslateInterceptor.intercept(SqlTranslateInterceptor.java:97) ~[base-mybatis-sqltranslate-spring-boot-starter-1.1.2-SNAPSHOT.jar:na]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy230.prepare(Unknown Source) ~[na:na]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:90) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:64) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110) ~[mybatis-3.5.19.jar:3.5.19]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-3.5.12.jar:3.5.12]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy229.query(Unknown Source) ~[na:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.mchz.sqltranslate.config.SqlTranslateInterceptor.intercept(SqlTranslateInterceptor.java:73) ~[base-mybatis-sqltranslate-spring-boot-starter-1.1.2-SNAPSHOT.jar:na]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy229.query(Unknown Source) ~[na:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.github.yulichang.interceptor.MPJInterceptor.intercept(MPJInterceptor.java:85) ~[mybatis-plus-join-core-1.5.4.jar:1.5.4]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy229.query(Unknown Source) ~[na:na]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142) ~[mybatis-3.5.19.jar:3.5.19]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425) ~[mybatis-spring-2.1.2.jar:2.1.2]
	... 27 common frames omitted

2025-08-13 16:12:56.793 ERROR 39580 --- [dark-task-1] [] c.m.n.p.job.TaskSchedulingManager       : <0.1><HfOS4EUD> 【作业执行】结束异常,
### Error updating database.  Cause: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
### The error may exist in com/mchz/nyx/pipeline/mapper/DiscoveryJobMapper.java (best guess)
### The error may involve com.mchz.nyx.pipeline.mapper.DiscoveryJobMapper.updateById
### The error occurred while executing an update
### SQL: UPDATE discovery_job  SET "heartbeat"=?  WHERE "id"=?
### Cause: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
; 第1 行附近出现错误:
无效的列名[id]; nested exception is dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]:org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:104)
2025-08-13 16:16:08.356 ERROR 40060 --- [main] [] com.alibaba.druid.pool.DruidDataSource  : testWhileIdle is true, validationQuery not set
2025-08-13 16:18:38.313 ERROR 40060 --- [dark-task-1] [] c.m.n.p.job.TaskSchedulingManager       : <0.1><XNQDFSVB> 【作业执行】J13(P1-S5) 执行异常

org.springframework.dao.DataIntegrityViolationException: 
### Error querying database.  Cause: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
### The error may exist in com/mchz/nyx/pipeline/mapper/DiscoveryJobMapper.java (best guess)
### The error may involve com.mchz.nyx.pipeline.mapper.DiscoveryJobMapper.selectList
### The error occurred while executing a query
### SQL: SELECT     "heartbeat"     FROM  discovery_job         WHERE  ("id" = ?)
### Cause: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
; 第1 行附近出现错误:
无效的列名[id]; nested exception is dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:104) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.sun.proxy.$Proxy99.selectList(Unknown Source) ~[na:na]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:164) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.sun.proxy.$Proxy146.selectList(Unknown Source) ~[na:na]
	at com.baomidou.mybatisplus.core.mapper.BaseMapper.selectOne(BaseMapper.java:332) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at java.lang.invoke.MethodHandle.invokeWithArguments(MethodHandle.java:627) ~[na:1.8.0_301]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$DefaultMethodInvoker.invoke(MybatisMapperProxy.java:172) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.sun.proxy.$Proxy146.selectOne(Unknown Source) ~[na:na]
	at com.baomidou.mybatisplus.core.mapper.BaseMapper.selectOne(BaseMapper.java:321) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at java.lang.invoke.MethodHandle.invokeWithArguments(MethodHandle.java:627) ~[na:1.8.0_301]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$DefaultMethodInvoker.invoke(MybatisMapperProxy.java:182) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.sun.proxy.$Proxy146.selectOne(Unknown Source) ~[na:na]
	at com.mchz.nyx.pipeline.service.impl.JobStatusServiceImpl.jobStart(JobStatusServiceImpl.java:65) ~[classes/:na]
	at com.mchz.nyx.pipeline.job.TaskSchedulingManager.executeTask(TaskSchedulingManager.java:168) [classes/:na]
	at com.mchz.nyx.pipeline.job.TaskSchedulingManager.lambda$submit$5(TaskSchedulingManager.java:111) [classes/:na]
	at com.alibaba.ttl.TtlRunnable.run(TtlRunnable.java:59) ~[transmittable-thread-local-2.12.4.jar:na]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) ~[na:1.8.0_301]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266) ~[na:1.8.0_301]
	at java.util.concurrent.FutureTask.run(FutureTask.java) ~[na:1.8.0_301]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_301]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_301]
	at java.lang.Thread.run(Thread.java:748) ~[na:1.8.0_301]
Caused by: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
	at dm.jdbc.driver.DBError.throwException(DBError.java:696) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.b.o.E(MSG.java:560) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.b.o.B(MSG.java:520) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.b.o.A(MSG.java:501) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.a.a(DBAccess.java:230) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.a.a(DBAccess.java:690) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.prepareSql(DmdbPreparedStatement.java:267) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.allocateHandle(DmdbPreparedStatement.java:224) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.<init>(DmdbPreparedStatement.java:126) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.<init>(DmdbPreparedStatement.java:176) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbConnection.do_prepareStatement(DmdbConnection.java:712) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbConnection.do_prepareStatement(DmdbConnection.java:698) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbConnection.prepareStatement(DmdbConnection.java:1317) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:337) ~[HikariCP-4.0.3.jar:na]
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java) ~[HikariCP-4.0.3.jar:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.logging.jdbc.ConnectionLogger.invoke(ConnectionLogger.java:53) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy207.prepareStatement(Unknown Source) ~[na:na]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.instantiateStatement(PreparedStatementHandler.java:88) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.statement.BaseStatementHandler.prepare(BaseStatementHandler.java:90) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.prepare(RoutingStatementHandler.java:60) ~[mybatis-3.5.19.jar:3.5.19]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-3.5.12.jar:3.5.12]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy230.prepare(Unknown Source) ~[na:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.mchz.sqltranslate.config.SqlTranslateInterceptor.intercept(SqlTranslateInterceptor.java:97) ~[base-mybatis-sqltranslate-spring-boot-starter-1.1.2-SNAPSHOT.jar:na]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy230.prepare(Unknown Source) ~[na:na]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:90) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:64) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110) ~[mybatis-3.5.19.jar:3.5.19]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-3.5.12.jar:3.5.12]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy229.query(Unknown Source) ~[na:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.mchz.sqltranslate.config.SqlTranslateInterceptor.intercept(SqlTranslateInterceptor.java:73) ~[base-mybatis-sqltranslate-spring-boot-starter-1.1.2-SNAPSHOT.jar:na]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy229.query(Unknown Source) ~[na:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.github.yulichang.interceptor.MPJInterceptor.intercept(MPJInterceptor.java:85) ~[mybatis-plus-join-core-1.5.4.jar:1.5.4]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy229.query(Unknown Source) ~[na:na]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142) ~[mybatis-3.5.19.jar:3.5.19]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425) ~[mybatis-spring-2.1.2.jar:2.1.2]
	... 27 common frames omitted

2025-08-13 16:18:38.356 ERROR 40060 --- [dark-task-1] [] c.m.n.p.job.TaskSchedulingManager       : <0.1><XNQDFSVB> 【作业执行】结束异常,
### Error updating database.  Cause: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
### The error may exist in com/mchz/nyx/pipeline/mapper/DiscoveryJobMapper.java (best guess)
### The error may involve com.mchz.nyx.pipeline.mapper.DiscoveryJobMapper.updateById
### The error occurred while executing an update
### SQL: UPDATE discovery_job  SET "heartbeat"=?  WHERE "id"=?
### Cause: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
; 第1 行附近出现错误:
无效的列名[id]; nested exception is dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]:org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:104)
