2025-08-18 14:42:30.188 ERROR 148456 --- [main] [] com.alibaba.druid.pool.DruidDataSource  : testWhileIdle is true, validationQuery not set
2025-08-18 14:43:55.401 ERROR 148456 --- [dark-task-1] [] c.m.n.p.job.TaskSchedulingManager       : <0.1><6tyHvh1o> 【作业执行】J40(P1-S5) 执行异常

org.springframework.dao.DataIntegrityViolationException: 
### Error querying database.  Cause: dm.jdbc.driver.DMException: 第 1 行, 第 12 列["]附近出现错误: 
标示符长度非法
### The error may exist in com/mchz/nyx/pipeline/mapper/DbMetaTableMapper.java (best guess)
### The error may involve com.mchz.nyx.pipeline.mapper.DbMetaTableMapper.selectList
### The error occurred while executing a query
### SQL: SELECT "T".""OID"", "T".""SOURCE_ID"", "T".""SCHEMA_ID"" FROM "DBMETA_TABLE" "T" WHERE ("T".""SOURCE_ID"" = ?)
### Cause: dm.jdbc.driver.DMException: 第 1 行, 第 12 列["]附近出现错误: 
标示符长度非法
; 第 1 行, 第 12 列["]附近出现错误: 
标示符长度非法; nested exception is dm.jdbc.driver.DMException: 第 1 行, 第 12 列["]附近出现错误: 
标示符长度非法
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:104) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.sun.proxy.$Proxy99.selectList(Unknown Source) ~[na:na]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:164) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.sun.proxy.$Proxy152.selectList(Unknown Source) ~[na:na]
	at com.mchz.nyx.pipeline.service.impl.MetaExtendServiceImpl.insertSampleTableFromMeta(MetaExtendServiceImpl.java:173) ~[classes/:na]
	at com.mchz.nyx.pipeline.service.impl.MetaExtendServiceImpl.fillTable(MetaExtendServiceImpl.java:153) ~[classes/:na]
	at com.mchz.nyx.pipeline.service.impl.MetaExtendServiceImpl$$FastClassBySpringCGLIB$$8bbb30d3.invoke(<generated>) ~[classes/:na]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.41.jar:5.3.41]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703) ~[spring-aop-5.3.31.jar:5.3.31]
	at com.mchz.nyx.pipeline.service.impl.MetaExtendServiceImpl$$EnhancerBySpringCGLIB$$295caf64.fillTable(<generated>) ~[classes/:na]
	at com.mchz.nyx.pipeline.job.collect.CollectMetaJob.execute(CollectMetaJob.java:81) ~[classes/:na]
	at com.mchz.nyx.pipeline.job.TaskSchedulingManager.executeTask(TaskSchedulingManager.java:169) [classes/:na]
	at com.mchz.nyx.pipeline.job.TaskSchedulingManager.lambda$submit$5(TaskSchedulingManager.java:111) [classes/:na]
	at com.alibaba.ttl.TtlRunnable.run(TtlRunnable.java:59) ~[transmittable-thread-local-2.12.4.jar:na]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) ~[na:1.8.0_301]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266) ~[na:1.8.0_301]
	at java.util.concurrent.FutureTask.run(FutureTask.java) ~[na:1.8.0_301]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_301]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_301]
	at java.lang.Thread.run(Thread.java:748) ~[na:1.8.0_301]
Caused by: dm.jdbc.driver.DMException: 第 1 行, 第 12 列["]附近出现错误: 
标示符长度非法
	at dm.jdbc.driver.DBError.throwException(DBError.java:696) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.b.o.E(MSG.java:560) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.b.o.B(MSG.java:520) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.b.o.A(MSG.java:501) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.a.a(DBAccess.java:230) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.a.a(DBAccess.java:690) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.prepareSql(DmdbPreparedStatement.java:267) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.allocateHandle(DmdbPreparedStatement.java:224) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.<init>(DmdbPreparedStatement.java:126) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.<init>(DmdbPreparedStatement.java:176) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbConnection.do_prepareStatement(DmdbConnection.java:712) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbConnection.do_prepareStatement(DmdbConnection.java:698) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbConnection.prepareStatement(DmdbConnection.java:1317) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:337) ~[HikariCP-4.0.3.jar:na]
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java) ~[HikariCP-4.0.3.jar:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.logging.jdbc.ConnectionLogger.invoke(ConnectionLogger.java:53) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy207.prepareStatement(Unknown Source) ~[na:na]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.instantiateStatement(PreparedStatementHandler.java:88) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.statement.BaseStatementHandler.prepare(BaseStatementHandler.java:90) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.prepare(RoutingStatementHandler.java:60) ~[mybatis-3.5.19.jar:3.5.19]
	at sun.reflect.GeneratedMethodAccessor26.invoke(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-3.5.12.jar:3.5.12]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy232.prepare(Unknown Source) ~[na:na]
	at sun.reflect.GeneratedMethodAccessor26.invoke(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.mchz.sqltranslate.config.SqlTranslateInterceptor.intercept(SqlTranslateInterceptor.java:105) ~[base-mybatis-sqltranslate-spring-boot-starter-1.1.2-SNAPSHOT.jar:na]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy232.prepare(Unknown Source) ~[na:na]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:90) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:64) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110) ~[mybatis-3.5.19.jar:3.5.19]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-3.5.12.jar:3.5.12]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy231.query(Unknown Source) ~[na:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.mchz.sqltranslate.config.SqlTranslateInterceptor.intercept(SqlTranslateInterceptor.java:105) ~[base-mybatis-sqltranslate-spring-boot-starter-1.1.2-SNAPSHOT.jar:na]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy231.query(Unknown Source) ~[na:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.github.yulichang.interceptor.MPJInterceptor.intercept(MPJInterceptor.java:85) ~[mybatis-plus-join-core-1.5.4.jar:1.5.4]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy231.query(Unknown Source) ~[na:na]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142) ~[mybatis-3.5.19.jar:3.5.19]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425) ~[mybatis-spring-2.1.2.jar:2.1.2]
	... 25 common frames omitted

2025-08-18 14:44:41.974 ERROR 148456 --- [http-nio-8888-exec-5] [] c.m.n.p.e.GlobalRestExceptionHandler    : <0.1><LmJmiLRW> Api异常调用(20002):1 存在未完成任务
2025-08-18 14:44:42.398 ERROR 148456 --- [dark-task-2] [] c.m.n.p.job.TaskSchedulingManager       : <0.1><8dGaWNkI> 【作业执行】J41(P1-S5) 执行异常

org.springframework.dao.DataIntegrityViolationException: 
### Error querying database.  Cause: dm.jdbc.driver.DMException: 第 1 行, 第 12 列["]附近出现错误: 
标示符长度非法
### The error may exist in com/mchz/nyx/pipeline/mapper/DbMetaTableMapper.java (best guess)
### The error may involve com.mchz.nyx.pipeline.mapper.DbMetaTableMapper.selectList
### The error occurred while executing a query
### SQL: SELECT "T".""OID"", "T".""SOURCE_ID"", "T".""SCHEMA_ID"" FROM "DBMETA_TABLE" "T" WHERE ("T".""SOURCE_ID"" = ?)
### Cause: dm.jdbc.driver.DMException: 第 1 行, 第 12 列["]附近出现错误: 
标示符长度非法
; 第 1 行, 第 12 列["]附近出现错误: 
标示符长度非法; nested exception is dm.jdbc.driver.DMException: 第 1 行, 第 12 列["]附近出现错误: 
标示符长度非法
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:104) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.sun.proxy.$Proxy99.selectList(Unknown Source) ~[na:na]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:164) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.sun.proxy.$Proxy152.selectList(Unknown Source) ~[na:na]
	at com.mchz.nyx.pipeline.service.impl.MetaExtendServiceImpl.insertSampleTableFromMeta(MetaExtendServiceImpl.java:173) ~[classes/:na]
	at com.mchz.nyx.pipeline.service.impl.MetaExtendServiceImpl.fillTable(MetaExtendServiceImpl.java:153) ~[classes/:na]
	at com.mchz.nyx.pipeline.service.impl.MetaExtendServiceImpl$$FastClassBySpringCGLIB$$8bbb30d3.invoke(<generated>) ~[classes/:na]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.41.jar:5.3.41]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703) ~[spring-aop-5.3.31.jar:5.3.31]
	at com.mchz.nyx.pipeline.service.impl.MetaExtendServiceImpl$$EnhancerBySpringCGLIB$$295caf64.fillTable(<generated>) ~[classes/:na]
	at com.mchz.nyx.pipeline.job.collect.CollectMetaJob.execute(CollectMetaJob.java:81) ~[classes/:na]
	at com.mchz.nyx.pipeline.job.TaskSchedulingManager.executeTask(TaskSchedulingManager.java:169) [classes/:na]
	at com.mchz.nyx.pipeline.job.TaskSchedulingManager.lambda$submit$5(TaskSchedulingManager.java:111) [classes/:na]
	at com.alibaba.ttl.TtlRunnable.run(TtlRunnable.java:59) ~[transmittable-thread-local-2.12.4.jar:na]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) ~[na:1.8.0_301]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266) ~[na:1.8.0_301]
	at java.util.concurrent.FutureTask.run(FutureTask.java) ~[na:1.8.0_301]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_301]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_301]
	at java.lang.Thread.run(Thread.java:748) ~[na:1.8.0_301]
Caused by: dm.jdbc.driver.DMException: 第 1 行, 第 12 列["]附近出现错误: 
标示符长度非法
	at dm.jdbc.driver.DBError.throwException(DBError.java:696) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.b.o.E(MSG.java:560) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.b.o.B(MSG.java:520) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.b.o.A(MSG.java:501) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.a.a(DBAccess.java:230) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.a.a(DBAccess.java:690) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.prepareSql(DmdbPreparedStatement.java:267) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.allocateHandle(DmdbPreparedStatement.java:224) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.<init>(DmdbPreparedStatement.java:126) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.<init>(DmdbPreparedStatement.java:176) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbConnection.do_prepareStatement(DmdbConnection.java:712) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbConnection.do_prepareStatement(DmdbConnection.java:698) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbConnection.prepareStatement(DmdbConnection.java:1317) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:337) ~[HikariCP-4.0.3.jar:na]
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java) ~[HikariCP-4.0.3.jar:na]
	at sun.reflect.GeneratedMethodAccessor77.invoke(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.logging.jdbc.ConnectionLogger.invoke(ConnectionLogger.java:53) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy207.prepareStatement(Unknown Source) ~[na:na]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.instantiateStatement(PreparedStatementHandler.java:88) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.statement.BaseStatementHandler.prepare(BaseStatementHandler.java:90) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.prepare(RoutingStatementHandler.java:60) ~[mybatis-3.5.19.jar:3.5.19]
	at sun.reflect.GeneratedMethodAccessor26.invoke(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-3.5.12.jar:3.5.12]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy232.prepare(Unknown Source) ~[na:na]
	at sun.reflect.GeneratedMethodAccessor26.invoke(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.mchz.sqltranslate.config.SqlTranslateInterceptor.intercept(SqlTranslateInterceptor.java:105) ~[base-mybatis-sqltranslate-spring-boot-starter-1.1.2-SNAPSHOT.jar:na]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy232.prepare(Unknown Source) ~[na:na]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:90) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:64) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110) ~[mybatis-3.5.19.jar:3.5.19]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-3.5.12.jar:3.5.12]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy231.query(Unknown Source) ~[na:na]
	at sun.reflect.GeneratedMethodAccessor76.invoke(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.mchz.sqltranslate.config.SqlTranslateInterceptor.intercept(SqlTranslateInterceptor.java:105) ~[base-mybatis-sqltranslate-spring-boot-starter-1.1.2-SNAPSHOT.jar:na]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy231.query(Unknown Source) ~[na:na]
	at sun.reflect.GeneratedMethodAccessor76.invoke(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.github.yulichang.interceptor.MPJInterceptor.intercept(MPJInterceptor.java:85) ~[mybatis-plus-join-core-1.5.4.jar:1.5.4]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy231.query(Unknown Source) ~[na:na]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142) ~[mybatis-3.5.19.jar:3.5.19]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425) ~[mybatis-spring-2.1.2.jar:2.1.2]
	... 25 common frames omitted

2025-08-18 14:45:12.622 ERROR 148456 --- [dark-task-3] [] c.m.n.p.job.TaskSchedulingManager       : <0.1><8ksVzs3h> 【作业执行】J42(P1-S5) 执行异常

org.springframework.dao.DataIntegrityViolationException: 
### Error querying database.  Cause: dm.jdbc.driver.DMException: 第 1 行, 第 12 列["]附近出现错误: 
标示符长度非法
### The error may exist in com/mchz/nyx/pipeline/mapper/DbMetaTableMapper.java (best guess)
### The error may involve com.mchz.nyx.pipeline.mapper.DbMetaTableMapper.selectList
### The error occurred while executing a query
### SQL: SELECT "T".""OID"", "T".""SOURCE_ID"", "T".""SCHEMA_ID"" FROM "DBMETA_TABLE" "T" WHERE ("T".""SOURCE_ID"" = ?)
### Cause: dm.jdbc.driver.DMException: 第 1 行, 第 12 列["]附近出现错误: 
标示符长度非法
; 第 1 行, 第 12 列["]附近出现错误: 
标示符长度非法; nested exception is dm.jdbc.driver.DMException: 第 1 行, 第 12 列["]附近出现错误: 
标示符长度非法
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:104) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.sun.proxy.$Proxy99.selectList(Unknown Source) ~[na:na]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:164) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.sun.proxy.$Proxy152.selectList(Unknown Source) ~[na:na]
	at com.mchz.nyx.pipeline.service.impl.MetaExtendServiceImpl.insertSampleTableFromMeta(MetaExtendServiceImpl.java:173) ~[classes/:na]
	at com.mchz.nyx.pipeline.service.impl.MetaExtendServiceImpl.fillTable(MetaExtendServiceImpl.java:153) ~[classes/:na]
	at com.mchz.nyx.pipeline.service.impl.MetaExtendServiceImpl$$FastClassBySpringCGLIB$$8bbb30d3.invoke(<generated>) ~[classes/:na]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.41.jar:5.3.41]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703) ~[spring-aop-5.3.31.jar:5.3.31]
	at com.mchz.nyx.pipeline.service.impl.MetaExtendServiceImpl$$EnhancerBySpringCGLIB$$295caf64.fillTable(<generated>) ~[classes/:na]
	at com.mchz.nyx.pipeline.job.collect.CollectMetaJob.execute(CollectMetaJob.java:81) ~[classes/:na]
	at com.mchz.nyx.pipeline.job.TaskSchedulingManager.executeTask(TaskSchedulingManager.java:169) [classes/:na]
	at com.mchz.nyx.pipeline.job.TaskSchedulingManager.lambda$submit$5(TaskSchedulingManager.java:111) [classes/:na]
	at com.alibaba.ttl.TtlRunnable.run(TtlRunnable.java:59) ~[transmittable-thread-local-2.12.4.jar:na]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) ~[na:1.8.0_301]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266) ~[na:1.8.0_301]
	at java.util.concurrent.FutureTask.run(FutureTask.java) ~[na:1.8.0_301]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_301]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_301]
	at java.lang.Thread.run(Thread.java:748) ~[na:1.8.0_301]
Caused by: dm.jdbc.driver.DMException: 第 1 行, 第 12 列["]附近出现错误: 
标示符长度非法
	at dm.jdbc.driver.DBError.throwException(DBError.java:696) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.b.o.E(MSG.java:560) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.b.o.B(MSG.java:520) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.b.o.A(MSG.java:501) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.a.a(DBAccess.java:230) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.a.a(DBAccess.java:690) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.prepareSql(DmdbPreparedStatement.java:267) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.allocateHandle(DmdbPreparedStatement.java:224) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.<init>(DmdbPreparedStatement.java:126) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.<init>(DmdbPreparedStatement.java:176) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbConnection.do_prepareStatement(DmdbConnection.java:712) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbConnection.do_prepareStatement(DmdbConnection.java:698) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbConnection.prepareStatement(DmdbConnection.java:1317) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:337) ~[HikariCP-4.0.3.jar:na]
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java) ~[HikariCP-4.0.3.jar:na]
	at sun.reflect.GeneratedMethodAccessor77.invoke(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.logging.jdbc.ConnectionLogger.invoke(ConnectionLogger.java:53) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy207.prepareStatement(Unknown Source) ~[na:na]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.instantiateStatement(PreparedStatementHandler.java:88) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.statement.BaseStatementHandler.prepare(BaseStatementHandler.java:90) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.prepare(RoutingStatementHandler.java:60) ~[mybatis-3.5.19.jar:3.5.19]
	at sun.reflect.GeneratedMethodAccessor26.invoke(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-3.5.12.jar:3.5.12]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy232.prepare(Unknown Source) ~[na:na]
	at sun.reflect.GeneratedMethodAccessor26.invoke(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.mchz.sqltranslate.config.SqlTranslateInterceptor.intercept(SqlTranslateInterceptor.java:105) ~[base-mybatis-sqltranslate-spring-boot-starter-1.1.2-SNAPSHOT.jar:na]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy232.prepare(Unknown Source) ~[na:na]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:90) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:64) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110) ~[mybatis-3.5.19.jar:3.5.19]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-3.5.12.jar:3.5.12]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy231.query(Unknown Source) ~[na:na]
	at sun.reflect.GeneratedMethodAccessor76.invoke(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.mchz.sqltranslate.config.SqlTranslateInterceptor.intercept(SqlTranslateInterceptor.java:105) ~[base-mybatis-sqltranslate-spring-boot-starter-1.1.2-SNAPSHOT.jar:na]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy231.query(Unknown Source) ~[na:na]
	at sun.reflect.GeneratedMethodAccessor76.invoke(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.github.yulichang.interceptor.MPJInterceptor.intercept(MPJInterceptor.java:85) ~[mybatis-plus-join-core-1.5.4.jar:1.5.4]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy231.query(Unknown Source) ~[na:na]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142) ~[mybatis-3.5.19.jar:3.5.19]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425) ~[mybatis-spring-2.1.2.jar:2.1.2]
	... 25 common frames omitted

2025-08-18 14:46:31.583 ERROR 35440 --- [main] [] com.alibaba.druid.pool.DruidDataSource  : testWhileIdle is true, validationQuery not set
2025-08-18 14:46:58.590 ERROR 35440 --- [dark-task-1] [] c.m.n.p.job.TaskSchedulingManager       : <0.1><uqLV3dpF> 【作业执行】J43(P1-S5) 执行异常

org.springframework.dao.DataIntegrityViolationException: 
### Error querying database.  Cause: dm.jdbc.driver.DMException: 第 1 行, 第 12 列["]附近出现错误: 
标示符长度非法
### The error may exist in com/mchz/nyx/pipeline/mapper/DbMetaTableMapper.java (best guess)
### The error may involve com.mchz.nyx.pipeline.mapper.DbMetaTableMapper.selectList
### The error occurred while executing a query
### SQL: SELECT "T".""OID"", "T".""SOURCE_ID"", "T".""SCHEMA_ID"" FROM "DBMETA_TABLE" "T" WHERE ("T".""SOURCE_ID"" = ?)
### Cause: dm.jdbc.driver.DMException: 第 1 行, 第 12 列["]附近出现错误: 
标示符长度非法
; 第 1 行, 第 12 列["]附近出现错误: 
标示符长度非法; nested exception is dm.jdbc.driver.DMException: 第 1 行, 第 12 列["]附近出现错误: 
标示符长度非法
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:104) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.sun.proxy.$Proxy99.selectList(Unknown Source) ~[na:na]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:164) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.sun.proxy.$Proxy152.selectList(Unknown Source) ~[na:na]
	at com.mchz.nyx.pipeline.service.impl.MetaExtendServiceImpl.insertSampleTableFromMeta(MetaExtendServiceImpl.java:173) ~[classes/:na]
	at com.mchz.nyx.pipeline.service.impl.MetaExtendServiceImpl.fillTable(MetaExtendServiceImpl.java:153) ~[classes/:na]
	at com.mchz.nyx.pipeline.service.impl.MetaExtendServiceImpl$$FastClassBySpringCGLIB$$8bbb30d3.invoke(<generated>) ~[classes/:na]
	at org.springframework.cglib.proxy.MethodProxy.invoke(MethodProxy.java:218) ~[spring-core-5.3.41.jar:5.3.41]
	at org.springframework.aop.framework.CglibAopProxy.invokeMethod(CglibAopProxy.java:386) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy.access$000(CglibAopProxy.java:85) ~[spring-aop-5.3.31.jar:5.3.31]
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:703) ~[spring-aop-5.3.31.jar:5.3.31]
	at com.mchz.nyx.pipeline.service.impl.MetaExtendServiceImpl$$EnhancerBySpringCGLIB$$da5adde1.fillTable(<generated>) ~[classes/:na]
	at com.mchz.nyx.pipeline.job.collect.CollectMetaJob.execute(CollectMetaJob.java:81) ~[classes/:na]
	at com.mchz.nyx.pipeline.job.TaskSchedulingManager.executeTask(TaskSchedulingManager.java:169) [classes/:na]
	at com.mchz.nyx.pipeline.job.TaskSchedulingManager.lambda$submit$5(TaskSchedulingManager.java:111) [classes/:na]
	at com.alibaba.ttl.TtlRunnable.run(TtlRunnable.java:59) ~[transmittable-thread-local-2.12.4.jar:na]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) ~[na:1.8.0_301]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266) ~[na:1.8.0_301]
	at java.util.concurrent.FutureTask.run(FutureTask.java) ~[na:1.8.0_301]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_301]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_301]
	at java.lang.Thread.run(Thread.java:748) ~[na:1.8.0_301]
Caused by: dm.jdbc.driver.DMException: 第 1 行, 第 12 列["]附近出现错误: 
标示符长度非法
	at dm.jdbc.driver.DBError.throwException(DBError.java:696) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.b.o.E(MSG.java:560) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.b.o.B(MSG.java:520) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.b.o.A(MSG.java:501) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.a.a(DBAccess.java:230) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.a.a(DBAccess.java:690) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.prepareSql(DmdbPreparedStatement.java:267) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.allocateHandle(DmdbPreparedStatement.java:224) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.<init>(DmdbPreparedStatement.java:126) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.<init>(DmdbPreparedStatement.java:176) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbConnection.do_prepareStatement(DmdbConnection.java:712) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbConnection.do_prepareStatement(DmdbConnection.java:698) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbConnection.prepareStatement(DmdbConnection.java:1317) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:337) ~[HikariCP-4.0.3.jar:na]
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java) ~[HikariCP-4.0.3.jar:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.logging.jdbc.ConnectionLogger.invoke(ConnectionLogger.java:53) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy207.prepareStatement(Unknown Source) ~[na:na]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.instantiateStatement(PreparedStatementHandler.java:88) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.statement.BaseStatementHandler.prepare(BaseStatementHandler.java:90) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.prepare(RoutingStatementHandler.java:60) ~[mybatis-3.5.19.jar:3.5.19]
	at sun.reflect.GeneratedMethodAccessor26.invoke(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-3.5.12.jar:3.5.12]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy230.prepare(Unknown Source) ~[na:na]
	at sun.reflect.GeneratedMethodAccessor26.invoke(Unknown Source) ~[na:na]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.mchz.sqltranslate.config.SqlTranslateInterceptor.intercept(SqlTranslateInterceptor.java:105) ~[base-mybatis-sqltranslate-spring-boot-starter-1.1.2-SNAPSHOT.jar:na]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy230.prepare(Unknown Source) ~[na:na]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:90) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:64) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110) ~[mybatis-3.5.19.jar:3.5.19]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-3.5.12.jar:3.5.12]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy229.query(Unknown Source) ~[na:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.mchz.sqltranslate.config.SqlTranslateInterceptor.intercept(SqlTranslateInterceptor.java:105) ~[base-mybatis-sqltranslate-spring-boot-starter-1.1.2-SNAPSHOT.jar:na]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy229.query(Unknown Source) ~[na:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.github.yulichang.interceptor.MPJInterceptor.intercept(MPJInterceptor.java:85) ~[mybatis-plus-join-core-1.5.4.jar:1.5.4]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy229.query(Unknown Source) ~[na:na]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142) ~[mybatis-3.5.19.jar:3.5.19]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425) ~[mybatis-spring-2.1.2.jar:2.1.2]
	... 25 common frames omitted

2025-08-18 14:47:19.848 ERROR 128016 --- [main] [] com.alibaba.druid.pool.DruidDataSource  : testWhileIdle is true, validationQuery not set
2025-08-18 14:47:40.652 ERROR 128016 --- [dark-task-1] [] c.m.n.p.job.TaskSchedulingManager       : <0.1><40Q6KEId> 【作业执行】J44(P1-S5) 执行异常

org.springframework.dao.DataIntegrityViolationException: 
### Error querying database.  Cause: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
### The error may exist in com/mchz/nyx/pipeline/mapper/DiscoveryJobMapper.java (best guess)
### The error may involve com.mchz.nyx.pipeline.mapper.DiscoveryJobMapper.selectList
### The error occurred while executing a query
### SQL: SELECT     "heartbeat"     FROM  discovery_job         WHERE  ("id" = ?)
### Cause: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
; 第1 行附近出现错误:
无效的列名[id]; nested exception is dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:104) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.sun.proxy.$Proxy99.selectList(Unknown Source) ~[na:na]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:164) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.sun.proxy.$Proxy146.selectList(Unknown Source) ~[na:na]
	at com.baomidou.mybatisplus.core.mapper.BaseMapper.selectOne(BaseMapper.java:332) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at java.lang.invoke.MethodHandle.invokeWithArguments(MethodHandle.java:627) ~[na:1.8.0_301]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$DefaultMethodInvoker.invoke(MybatisMapperProxy.java:172) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.sun.proxy.$Proxy146.selectOne(Unknown Source) ~[na:na]
	at com.baomidou.mybatisplus.core.mapper.BaseMapper.selectOne(BaseMapper.java:321) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at java.lang.invoke.MethodHandle.invokeWithArguments(MethodHandle.java:627) ~[na:1.8.0_301]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$DefaultMethodInvoker.invoke(MybatisMapperProxy.java:182) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.sun.proxy.$Proxy146.selectOne(Unknown Source) ~[na:na]
	at com.mchz.nyx.pipeline.service.impl.JobStatusServiceImpl.jobStart(JobStatusServiceImpl.java:65) ~[classes/:na]
	at com.mchz.nyx.pipeline.job.TaskSchedulingManager.executeTask(TaskSchedulingManager.java:168) [classes/:na]
	at com.mchz.nyx.pipeline.job.TaskSchedulingManager.lambda$submit$5(TaskSchedulingManager.java:111) [classes/:na]
	at com.alibaba.ttl.TtlRunnable.run(TtlRunnable.java:59) ~[transmittable-thread-local-2.12.4.jar:na]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) ~[na:1.8.0_301]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266) ~[na:1.8.0_301]
	at java.util.concurrent.FutureTask.run(FutureTask.java) ~[na:1.8.0_301]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_301]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_301]
	at java.lang.Thread.run(Thread.java:748) ~[na:1.8.0_301]
Caused by: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
	at dm.jdbc.driver.DBError.throwException(DBError.java:696) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.b.o.E(MSG.java:560) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.b.o.B(MSG.java:520) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.b.o.A(MSG.java:501) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.a.a(DBAccess.java:230) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.a.a(DBAccess.java:690) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.prepareSql(DmdbPreparedStatement.java:267) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.allocateHandle(DmdbPreparedStatement.java:224) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.<init>(DmdbPreparedStatement.java:126) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.<init>(DmdbPreparedStatement.java:176) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbConnection.do_prepareStatement(DmdbConnection.java:712) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbConnection.do_prepareStatement(DmdbConnection.java:698) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbConnection.prepareStatement(DmdbConnection.java:1317) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:337) ~[HikariCP-4.0.3.jar:na]
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java) ~[HikariCP-4.0.3.jar:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.logging.jdbc.ConnectionLogger.invoke(ConnectionLogger.java:53) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy207.prepareStatement(Unknown Source) ~[na:na]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.instantiateStatement(PreparedStatementHandler.java:88) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.statement.BaseStatementHandler.prepare(BaseStatementHandler.java:90) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.prepare(RoutingStatementHandler.java:60) ~[mybatis-3.5.19.jar:3.5.19]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-3.5.12.jar:3.5.12]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy230.prepare(Unknown Source) ~[na:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.mchz.sqltranslate.config.SqlTranslateInterceptor.intercept(SqlTranslateInterceptor.java:97) ~[base-mybatis-sqltranslate-spring-boot-starter-1.1.2-SNAPSHOT.jar:na]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy230.prepare(Unknown Source) ~[na:na]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:90) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:64) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110) ~[mybatis-3.5.19.jar:3.5.19]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-3.5.12.jar:3.5.12]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy229.query(Unknown Source) ~[na:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.mchz.sqltranslate.config.SqlTranslateInterceptor.intercept(SqlTranslateInterceptor.java:73) ~[base-mybatis-sqltranslate-spring-boot-starter-1.1.2-SNAPSHOT.jar:na]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy229.query(Unknown Source) ~[na:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.github.yulichang.interceptor.MPJInterceptor.intercept(MPJInterceptor.java:85) ~[mybatis-plus-join-core-1.5.4.jar:1.5.4]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy229.query(Unknown Source) ~[na:na]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142) ~[mybatis-3.5.19.jar:3.5.19]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425) ~[mybatis-spring-2.1.2.jar:2.1.2]
	... 27 common frames omitted

2025-08-18 14:47:40.690 ERROR 128016 --- [dark-task-1] [] c.m.n.p.job.TaskSchedulingManager       : <0.1><40Q6KEId> 【作业执行】结束异常,
### Error updating database.  Cause: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
### The error may exist in com/mchz/nyx/pipeline/mapper/DiscoveryJobMapper.java (best guess)
### The error may involve com.mchz.nyx.pipeline.mapper.DiscoveryJobMapper.updateById
### The error occurred while executing an update
### SQL: UPDATE discovery_job  SET "heartbeat"=?  WHERE "id"=?
### Cause: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
; 第1 行附近出现错误:
无效的列名[id]; nested exception is dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]:org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:104)
2025-08-18 14:50:22.198 ERROR 135276 --- [main] [] com.alibaba.druid.pool.DruidDataSource  : testWhileIdle is true, validationQuery not set
2025-08-18 14:50:45.214 ERROR 135276 --- [dark-task-1] [] c.m.n.p.job.TaskSchedulingManager       : <0.1><wu4raFnh> 【作业执行】J45(P1-S5) 执行异常

org.springframework.dao.DataIntegrityViolationException: 
### Error querying database.  Cause: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
### The error may exist in com/mchz/nyx/pipeline/mapper/DiscoveryJobMapper.java (best guess)
### The error may involve com.mchz.nyx.pipeline.mapper.DiscoveryJobMapper.selectList
### The error occurred while executing a query
### SQL: SELECT     "heartbeat"     FROM  discovery_job         WHERE  ("id" = ?)
### Cause: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
; 第1 行附近出现错误:
无效的列名[id]; nested exception is dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
	at org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:104) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:73) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:82) ~[spring-jdbc-5.3.31.jar:5.3.31]
	at org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.sun.proxy.$Proxy99.selectList(Unknown Source) ~[na:na]
	at org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224) ~[mybatis-spring-2.1.2.jar:2.1.2]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:164) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:156) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.sun.proxy.$Proxy146.selectList(Unknown Source) ~[na:na]
	at com.baomidou.mybatisplus.core.mapper.BaseMapper.selectOne(BaseMapper.java:332) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at java.lang.invoke.MethodHandle.invokeWithArguments(MethodHandle.java:627) ~[na:1.8.0_301]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$DefaultMethodInvoker.invoke(MybatisMapperProxy.java:172) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.sun.proxy.$Proxy146.selectOne(Unknown Source) ~[na:na]
	at com.baomidou.mybatisplus.core.mapper.BaseMapper.selectOne(BaseMapper.java:321) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at java.lang.invoke.MethodHandle.invokeWithArguments(MethodHandle.java:627) ~[na:1.8.0_301]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy$DefaultMethodInvoker.invoke(MybatisMapperProxy.java:182) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:93) ~[mybatis-plus-core-3.5.12.jar:3.5.12]
	at com.sun.proxy.$Proxy146.selectOne(Unknown Source) ~[na:na]
	at com.mchz.nyx.pipeline.service.impl.JobStatusServiceImpl.jobStart(JobStatusServiceImpl.java:65) ~[classes/:na]
	at com.mchz.nyx.pipeline.job.TaskSchedulingManager.executeTask(TaskSchedulingManager.java:168) [classes/:na]
	at com.mchz.nyx.pipeline.job.TaskSchedulingManager.lambda$submit$5(TaskSchedulingManager.java:111) [classes/:na]
	at com.alibaba.ttl.TtlRunnable.run(TtlRunnable.java:59) ~[transmittable-thread-local-2.12.4.jar:na]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) ~[na:1.8.0_301]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266) ~[na:1.8.0_301]
	at java.util.concurrent.FutureTask.run(FutureTask.java) ~[na:1.8.0_301]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_301]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_301]
	at java.lang.Thread.run(Thread.java:748) ~[na:1.8.0_301]
Caused by: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
	at dm.jdbc.driver.DBError.throwException(DBError.java:696) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.b.o.E(MSG.java:560) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.b.o.B(MSG.java:520) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.b.o.A(MSG.java:501) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.a.a(DBAccess.java:230) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.b.a.a(DBAccess.java:690) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.prepareSql(DmdbPreparedStatement.java:267) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.allocateHandle(DmdbPreparedStatement.java:224) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.<init>(DmdbPreparedStatement.java:126) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbPreparedStatement.<init>(DmdbPreparedStatement.java:176) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbConnection.do_prepareStatement(DmdbConnection.java:712) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbConnection.do_prepareStatement(DmdbConnection.java:698) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at dm.jdbc.driver.DmdbConnection.prepareStatement(DmdbConnection.java:1317) ~[DmJdbcDriver18-*********.jar:- ********* - Production]
	at com.zaxxer.hikari.pool.ProxyConnection.prepareStatement(ProxyConnection.java:337) ~[HikariCP-4.0.3.jar:na]
	at com.zaxxer.hikari.pool.HikariProxyConnection.prepareStatement(HikariProxyConnection.java) ~[HikariCP-4.0.3.jar:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.logging.jdbc.ConnectionLogger.invoke(ConnectionLogger.java:53) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy207.prepareStatement(Unknown Source) ~[na:na]
	at org.apache.ibatis.executor.statement.PreparedStatementHandler.instantiateStatement(PreparedStatementHandler.java:88) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.statement.BaseStatementHandler.prepare(BaseStatementHandler.java:90) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.statement.RoutingStatementHandler.prepare(RoutingStatementHandler.java:60) ~[mybatis-3.5.19.jar:3.5.19]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106) ~[mybatis-plus-extension-3.5.12.jar:3.5.12]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy230.prepare(Unknown Source) ~[na:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.mchz.sqltranslate.config.SqlTranslateInterceptor.intercept(SqlTranslateInterceptor.java:97) ~[base-mybatis-sqltranslate-spring-boot-starter-1.1.2-SNAPSHOT.jar:na]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy230.prepare(Unknown Source) ~[na:na]
	at org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:90) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:64) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110) ~[mybatis-3.5.19.jar:3.5.19]
	at com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81) ~[mybatis-plus-extension-3.5.12.jar:3.5.12]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy229.query(Unknown Source) ~[na:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.mchz.sqltranslate.config.SqlTranslateInterceptor.intercept(SqlTranslateInterceptor.java:73) ~[base-mybatis-sqltranslate-spring-boot-starter-1.1.2-SNAPSHOT.jar:na]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy229.query(Unknown Source) ~[na:na]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:61) ~[mybatis-3.5.19.jar:3.5.19]
	at com.github.yulichang.interceptor.MPJInterceptor.intercept(MPJInterceptor.java:85) ~[mybatis-plus-join-core-1.5.4.jar:1.5.4]
	at org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59) ~[mybatis-3.5.19.jar:3.5.19]
	at com.sun.proxy.$Proxy229.query(Unknown Source) ~[na:na]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147) ~[mybatis-3.5.19.jar:3.5.19]
	at org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142) ~[mybatis-3.5.19.jar:3.5.19]
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method) ~[na:1.8.0_301]
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62) ~[na:1.8.0_301]
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43) ~[na:1.8.0_301]
	at java.lang.reflect.Method.invoke(Method.java:498) ~[na:1.8.0_301]
	at org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425) ~[mybatis-spring-2.1.2.jar:2.1.2]
	... 27 common frames omitted

2025-08-18 14:50:45.273 ERROR 135276 --- [dark-task-1] [] c.m.n.p.job.TaskSchedulingManager       : <0.1><wu4raFnh> 【作业执行】结束异常,
### Error updating database.  Cause: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
### The error may exist in com/mchz/nyx/pipeline/mapper/DiscoveryJobMapper.java (best guess)
### The error may involve com.mchz.nyx.pipeline.mapper.DiscoveryJobMapper.updateById
### The error occurred while executing an update
### SQL: UPDATE discovery_job  SET "heartbeat"=?  WHERE "id"=?
### Cause: dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]
; 第1 行附近出现错误:
无效的列名[id]; nested exception is dm.jdbc.driver.DMException: 第1 行附近出现错误:
无效的列名[id]:org.springframework.jdbc.support.SQLStateSQLExceptionTranslator.doTranslate(SQLStateSQLExceptionTranslator.java:104)
2025-08-18 14:51:37.440 ERROR 107068 --- [main] [] com.alibaba.druid.pool.DruidDataSource  : testWhileIdle is true, validationQuery not set
