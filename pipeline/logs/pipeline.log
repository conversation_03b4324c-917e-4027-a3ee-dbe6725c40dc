2025-08-25 11:48:54.111  INFO 61276 --- [main] [] c.a.n.client.env.SearchableProperties   : properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-08-25 11:48:54.266  INFO 61276 --- [main] [] com.mchz.nyx.pipeline.Application       : Starting Application using Java 1.8.0_301 on DESKTOP-TQMNVVF with PID 61276 (C:\projects\nyx\pipeline\target\classes started by admin in C:\projects\nyx\pipeline)
2025-08-25 11:48:54.267 DEBUG 61276 --- [main] [] com.mchz.nyx.pipeline.Application       : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-08-25 11:48:54.268  INFO 61276 --- [main] [] com.mchz.nyx.pipeline.Application       : The following 1 profile is active: "standalone"
2025-08-25 11:48:56.443  INFO 61276 --- [main] [] o.s.cloud.context.scope.GenericScope    : BeanFactory id=894701e2-e985-33a1-851d-e7f0611b4c97
2025-08-25 11:48:56.638  INFO 61276 --- [main] [] ptablePropertiesBeanFactoryPostProcessor: Post-processing PropertySource instances
2025-08-25 11:48:56.639  INFO 61276 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-08-25 11:48:56.640  INFO 61276 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-08-25 11:48:56.641  INFO 61276 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-08-25 11:48:56.643  INFO 61276 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-08-25 11:48:56.647  INFO 61276 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-08-25 11:48:56.649  INFO 61276 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-08-25 11:48:56.650  INFO 61276 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-08-25 11:48:56.652  INFO 61276 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-08-25 11:48:56.653  INFO 61276 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Converting PropertySource Config resource 'file [config\sourcedata.properties]' via location './config/sourcedata.properties' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-08-25 11:48:56.653  INFO 61276 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Converting PropertySource Config resource 'class path resource [application-standalone.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-08-25 11:48:56.653  INFO 61276 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Converting PropertySource Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-08-25 11:48:56.658  INFO 61276 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Converting PropertySource TLog Default Properties [org.springframework.core.io.support.ResourcePropertySource] to EncryptableMapPropertySourceWrapper
2025-08-25 11:48:56.659  INFO 61276 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Converting PropertySource defaultProperties [org.springframework.boot.DefaultPropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-08-25 11:48:56.678  INFO 61276 --- [main] [] c.g.y.a.MybatisPlusJoinAutoConfiguration: MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
2025-08-25 11:48:57.118  INFO 61276 --- [main] [] c.u.j.filter.DefaultLazyPropertyFilter  : Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-08-25 11:48:57.126  INFO 61276 --- [main] [] c.u.j.r.DefaultLazyPropertyResolver     : Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-08-25 11:48:57.127  INFO 61276 --- [main] [] c.u.j.d.DefaultLazyPropertyDetector     : Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-08-25 11:48:57.565 DEBUG 61276 --- [main] [] c.m.n.p.c.EmbeddedTomcatConfiguration$1 : Code archive: C:\repository\org\springframework\boot\spring-boot\2.7.18\spring-boot-2.7.18.jar
2025-08-25 11:48:57.565 DEBUG 61276 --- [main] [] c.m.n.p.c.EmbeddedTomcatConfiguration$1 : Code archive: C:\repository\org\springframework\boot\spring-boot\2.7.18\spring-boot-2.7.18.jar
2025-08-25 11:48:57.566 DEBUG 61276 --- [main] [] c.m.n.p.c.EmbeddedTomcatConfiguration$1 : None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored.
2025-08-25 11:48:57.662  INFO 61276 --- [main] [] o.s.b.w.embedded.tomcat.TomcatWebServer : Tomcat initialized with port(s): 8888 (http)
2025-08-25 11:48:57.686  INFO 61276 --- [main] [] o.apache.catalina.core.StandardService  : Starting service [Tomcat]
2025-08-25 11:48:57.687  INFO 61276 --- [main] [] org.apache.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/9.0.106]
2025-08-25 11:48:57.743  WARN 61276 --- [main] [] o.a.c.webresources.DirResourceSet       : Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.8888.7495107862985142276] which is part of the web application []
2025-08-25 11:48:58.415  INFO 61276 --- [main] [] o.a.c.c.C.[Tomcat].[localhost].[/]      : Initializing Spring embedded WebApplicationContext
2025-08-25 11:48:58.415  INFO 61276 --- [main] [] w.s.c.ServletWebServerApplicationContext: Root WebApplicationContext: initialization completed in 3799 ms
2025-08-25 11:48:58.987 ERROR 61276 --- [main] [] com.alibaba.druid.pool.DruidDataSource  : testWhileIdle is true, validationQuery not set
2025-08-25 11:48:58.999  INFO 61276 --- [main] [] com.alibaba.druid.pool.DruidDataSource  : {dataSource-1,embed} inited
2025-08-25 11:48:59.000  INFO 61276 --- [main] [] c.b.d.d.DynamicRoutingDataSource        : dynamic-datasource - add a datasource named [embed] success
2025-08-25 11:48:59.000  INFO 61276 --- [main] [] c.b.d.d.DynamicRoutingDataSource        : dynamic-datasource - add a datasource named [master] success
2025-08-25 11:48:59.000  INFO 61276 --- [main] [] c.b.d.d.DynamicRoutingDataSource        : dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-08-25 11:49:02.161  INFO 61276 --- [main] [] c.m.n.p.job.TaskSchedulingManager       : DISCOVERY: DiscoveryJob
2025-08-25 11:49:02.162  INFO 61276 --- [main] [] c.m.n.p.job.TaskSchedulingManager       : ASSET_RELATION: AssetRelationJob
2025-08-25 11:49:02.162  INFO 61276 --- [main] [] c.m.n.p.job.TaskSchedulingManager       : COLLECT_META: CollectMetaJob
2025-08-25 11:49:02.750  INFO 61276 --- [main] [] m.e.s.MybatisPlusApplicationContextAware: Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@726386ed
2025-08-25 11:49:02.928  INFO 61276 --- [main] [] com.zaxxer.hikari.HikariDataSource      : dark-dbms - Starting...
2025-08-25 11:49:03.285  INFO 61276 --- [main] [] com.zaxxer.hikari.HikariDataSource      : dark-dbms - Start completed.
2025-08-25 11:49:04.243  INFO 61276 --- [main] [] o.s.b.w.embedded.tomcat.TomcatWebServer : Tomcat started on port(s): 8888 (http) with context path ''
2025-08-25 11:49:04.244  INFO 61276 --- [main] [] u.j.c.RefreshScopeRefreshedEventListener: Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-08-25 11:49:04.245  INFO 61276 --- [main] [] CachingDelegateEncryptablePropertySource: Property Source systemProperties refreshed
2025-08-25 11:49:04.245  INFO 61276 --- [main] [] CachingDelegateEncryptablePropertySource: Property Source systemEnvironment refreshed
2025-08-25 11:49:04.245  INFO 61276 --- [main] [] CachingDelegateEncryptablePropertySource: Property Source random refreshed
2025-08-25 11:49:04.245  INFO 61276 --- [main] [] CachingDelegateEncryptablePropertySource: Property Source cachedrandom refreshed
2025-08-25 11:49:04.245  INFO 61276 --- [main] [] CachingDelegateEncryptablePropertySource: Property Source springCloudClientHostInfo refreshed
2025-08-25 11:49:04.245  INFO 61276 --- [main] [] CachingDelegateEncryptablePropertySource: Property Source Config resource 'file [config\sourcedata.properties]' via location './config/sourcedata.properties' refreshed
2025-08-25 11:49:04.245  INFO 61276 --- [main] [] CachingDelegateEncryptablePropertySource: Property Source Config resource 'class path resource [application-standalone.yml]' via location 'optional:classpath:/' refreshed
2025-08-25 11:49:04.245  INFO 61276 --- [main] [] CachingDelegateEncryptablePropertySource: Property Source Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' refreshed
2025-08-25 11:49:04.245  INFO 61276 --- [main] [] CachingDelegateEncryptablePropertySource: Property Source TLog Default Properties refreshed
2025-08-25 11:49:04.245  INFO 61276 --- [main] [] CachingDelegateEncryptablePropertySource: Property Source defaultProperties refreshed
2025-08-25 11:49:04.245  INFO 61276 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-08-25 11:49:04.245  INFO 61276 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-08-25 11:49:04.246  INFO 61276 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-08-25 11:49:04.429  INFO 61276 --- [main] [] com.mchz.nyx.pipeline.Application       : Started Application in 11.184 seconds (JVM running for 25.902)
2025-08-25 11:49:04.532  INFO 61276 --- [main] [] c.m.n.p.config.McDatasourceConfig       : 设置统一数据源版本:*******;路径:C:\file\mcdatasource_1681
2025-08-25 11:53:08.873  INFO 61276 --- [http-nio-8888-exec-1] [] o.a.c.c.C.[Tomcat].[localhost].[/]      : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-25 11:53:08.876  INFO 61276 --- [http-nio-8888-exec-1] [] o.s.web.servlet.DispatcherServlet       : Initializing Servlet 'dispatcherServlet'
2025-08-25 11:53:08.919  INFO 61276 --- [http-nio-8888-exec-1] [] o.s.web.servlet.DispatcherServlet       : Completed initialization in 43 ms
2025-08-25 11:53:40.129 DEBUG 61276 --- [http-nio-8888-exec-2] [] c.m.n.p.controller.TaskController       : <0.1><AXw8anMR> {"industryId":2,"planId":2,"jobId":2,"jobType":7,"source":{"streamDataTypes":[],"id":5,"configName":"文件","configType":130,"dataBaseType":"LOCAL_FILE","host":"","port":"","username":"","dbName":"","attachment":{"headline":"true","type":"0","compress":"false"},"advancedConfig":{},"properties":{}},"sampleLine":100,"sampleRate":100,"full":false,"saveStrategy":0}
2025-08-25 11:53:40.175  INFO 61276 --- [http-nio-8888-exec-2] [] c.m.n.p.job.TaskSchedulingManager       : <0.1><AXw8anMR> 【任务管理】全库
2025-08-25 11:53:40.197  INFO 61276 --- [dark-task-1] [] c.m.n.p.job.TaskSchedulingManager       : <0.1><AXw8anMR> 【作业执行】J2(P2-S5) 文件
2025-08-25 11:53:40.199 DEBUG 61276 --- [dark-task-1] [] c.m.n.p.s.impl.TaskCallbackServiceImpl  : <0.1><AXw8anMR> 【心跳】TaskHeartbeatBeatReq(taskGroup=COLLECT_META, taskName=2, status=1)
2025-08-25 11:53:40.199  WARN 61276 --- [dark-task-1] [] c.m.nyx.pipeline.http.HttpRemoteInvoke  : <0.1><AXw8anMR> 【地址获取】默认使用localhost
2025-08-25 11:53:40.278 DEBUG 61276 --- [dark-task-1] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : <0.1><AXw8anMR> [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 11:53:41.412 DEBUG 61276 --- [dark-task-1] [] c.m.n.p.m.DiscoveryJobMapper.selectList : <0.1><AXw8anMR> ==>  Preparing: SELECT `heartbeat` FROM discovery_job WHERE (`id` = ?)
2025-08-25 11:53:41.461 DEBUG 61276 --- [dark-task-1] [] c.m.n.p.m.DiscoveryJobMapper.selectList : <0.1><AXw8anMR> ==> Parameters: 2(Long)
2025-08-25 11:53:41.496 DEBUG 61276 --- [dark-task-1] [] c.m.n.p.m.DiscoveryJobMapper.selectList : <0.1><AXw8anMR> <==      Total: 1
2025-08-25 11:53:41.501  INFO 61276 --- [dark-task-1] [] c.m.n.p.job.collect.CollectMetaJob      : <0.1><AXw8anMR> 【分类分级】采集元数据
2025-08-25 11:53:41.591 DEBUG 61276 --- [dark-task-1] [] c.m.n.p.m.F.selectList                  : <0.1><AXw8anMR> ==>  Preparing: SELECT `id`,`config_id`,`uuid`,`compress_uuid`,`name`,`format`,`moved`,`temp_path`,`path` FROM file_upload_detail WHERE (`config_id` = ?)
2025-08-25 11:53:41.591 DEBUG 61276 --- [dark-task-1] [] c.m.n.p.m.F.selectList                  : <0.1><AXw8anMR> ==> Parameters: 5(Long)
2025-08-25 11:53:41.597 DEBUG 61276 --- [dark-task-1] [] c.m.n.p.m.F.selectList                  : <0.1><AXw8anMR> <==      Total: 1
2025-08-25 11:53:41.651 DEBUG 61276 --- [meta-5-1] [] c.m.n.p.m.DbMetaObjectMapper.selectList : <0.1><AXw8anMR> ==>  Preparing: SELECT `oid`,`name`,`deleted`,`revision`,`hash_value` FROM dbmeta_object WHERE (`source_id` = ? AND `pid` = ? AND `type` = ?)
2025-08-25 11:53:41.652 DEBUG 61276 --- [meta-5-1] [] c.m.n.p.m.DbMetaObjectMapper.selectList : <0.1><AXw8anMR> ==> Parameters: 5(Long), 0(Long), SCHEMA(String)
2025-08-25 11:53:41.659 DEBUG 61276 --- [meta-5-1] [] c.m.n.p.m.DbMetaObjectMapper.selectList : <0.1><AXw8anMR> <==      Total: 0
2025-08-25 11:53:42.311 DEBUG 61276 --- [meta-5-1] [] c.m.n.p.m.DbMetaObjectMapper.insertList : <0.1><AXw8anMR> ==>  Preparing: INSERT INTO dbmeta_object (`source_id`,`name`,`type`,`description`,`deleted`,`created_at`,`updated_at`,`revision`,`hash_value`,`pid`) VALUES (?,?,?,?,?,?,?,?,?,?)
2025-08-25 11:53:42.319 DEBUG 61276 --- [meta-5-1] [] c.m.n.p.m.DbMetaObjectMapper.insertList : <0.1><AXw8anMR> ==> Parameters: 5(Long), default(String), SCHEMA(String), null, false(Boolean), 2025-08-25 11:53:42.266(Timestamp), 2025-08-25 11:53:42.266(Timestamp), 1(Integer), (String), 0(Long)
2025-08-25 11:53:42.343 DEBUG 61276 --- [meta-5-1] [] c.m.n.p.m.DbMetaObjectMapper.insertList : <0.1><AXw8anMR> <==    Updates: 1
2025-08-25 11:53:42.356 DEBUG 61276 --- [meta-5-1] [] c.m.n.p.m.DbMetaSchemaMapper.insertList : <0.1><AXw8anMR> ==>  Preparing: INSERT INTO dbmeta_schema (`oid`,`source_id`,`catalog`,`schema`,`charset`,`collation`) VALUES (?,?,?,?,?,?)
2025-08-25 11:53:42.357 DEBUG 61276 --- [meta-5-1] [] c.m.n.p.m.DbMetaSchemaMapper.insertList : <0.1><AXw8anMR> ==> Parameters: 1(Long), 5(Long), null, null, null, null
2025-08-25 11:53:42.375 DEBUG 61276 --- [meta-5-1] [] c.m.n.p.m.DbMetaSchemaMapper.insertList : <0.1><AXw8anMR> <==    Updates: 1
2025-08-25 11:53:42.405 ERROR 61276 --- [dark-task-1] [] c.m.n.p.job.TaskSchedulingManager       : <0.1><AXw8anMR> 【作业执行】J2(P2-S5) 执行异常

java.lang.NullPointerException: null
	at java.io.File.<init>(File.java:277) ~[na:1.8.0_301]
	at com.mchz.nyx.pipeline.service.impl.FileManagerServiceImpl.loadFile(FileManagerServiceImpl.java:56) ~[classes/:na]
	at com.mchz.nyx.pipeline.generator.meta.FileMetaAdapter.execute(FileMetaAdapter.java:110) ~[classes/:na]
	at com.mchz.nyx.meta.instance.crawl.MetaAgentCrawl.execute(MetaAgentCrawl.java:45) ~[classes/:na]
	at com.mchz.nyx.meta.instance.crawl.AbstractEventCrawl.crawl(AbstractEventCrawl.java:28) ~[classes/:na]
	at com.mchz.nyx.meta.instance.DefaultNyxInstance.run(DefaultNyxInstance.java:31) ~[classes/:na]
	at com.mchz.nyx.pipeline.job.collect.CollectMetaJob.execute(CollectMetaJob.java:73) ~[classes/:na]
	at com.mchz.nyx.pipeline.job.TaskSchedulingManager.executeTask(TaskSchedulingManager.java:169) [classes/:na]
	at com.mchz.nyx.pipeline.job.TaskSchedulingManager.lambda$submit$5(TaskSchedulingManager.java:111) [classes/:na]
	at com.alibaba.ttl.TtlRunnable.run(TtlRunnable.java:59) ~[transmittable-thread-local-2.12.4.jar:na]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) ~[na:1.8.0_301]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266) ~[na:1.8.0_301]
	at java.util.concurrent.FutureTask.run(FutureTask.java) ~[na:1.8.0_301]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_301]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_301]
	at java.lang.Thread.run(Thread.java:748) ~[na:1.8.0_301]

2025-08-25 11:53:42.420 DEBUG 61276 --- [dark-task-1] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : <0.1><AXw8anMR> [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 11:53:42.447 DEBUG 61276 --- [dark-task-1] [] c.m.nyx.pipeline.http.HttpRemoteInvoke  : <0.1><AXw8anMR> {"code":200,"success":true,"msg":"操作成功","tranceId":"AXw8anMR"}
2025-08-25 11:53:42.457 DEBUG 61276 --- [Thread-21] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 11:53:42.458 DEBUG 61276 --- [Thread-22] [] c.m.n.p.m.DiscoveryLogMapper.insertList : ==>  Preparing: INSERT INTO discovery_log (`job_id`,`percent`,`content`) VALUES (?,?,?)
2025-08-25 11:53:42.462 DEBUG 61276 --- [dark-task-1] [] c.m.n.p.m.DiscoveryJobMapper.updateById : <0.1><AXw8anMR> ==>  Preparing: UPDATE discovery_job SET `heartbeat`=? WHERE `id`=?
2025-08-25 11:53:42.463 DEBUG 61276 --- [dark-task-1] [] c.m.n.p.m.DiscoveryJobMapper.updateById : <0.1><AXw8anMR> ==> Parameters: {"startTime":1756094021499,"step":1,"num":0}(String), 2(Long)
2025-08-25 11:53:42.463 DEBUG 61276 --- [Thread-22] [] c.m.n.p.m.DiscoveryLogMapper.insertList : ==> Parameters: 2(Long), 0.00(BigDecimal), [{"msg":"开始采集元数据信息","status":"INFO","time":"2025-08-25 11:53:41"}](String)
2025-08-25 11:53:42.483 DEBUG 61276 --- [Thread-21] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 11:53:42.491 DEBUG 61276 --- [dark-task-1] [] c.m.n.p.m.DiscoveryJobMapper.updateById : <0.1><AXw8anMR> <==    Updates: 1
2025-08-25 11:53:42.491 DEBUG 61276 --- [dark-task-1] [] c.m.n.p.s.impl.TaskCallbackServiceImpl  : <0.1><AXw8anMR> 【心跳】TaskHeartbeatBeatReq(taskGroup=COLLECT_META, taskName=2, status=3)
2025-08-25 11:53:42.492 DEBUG 61276 --- [dark-task-1] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : <0.1><AXw8anMR> [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 11:53:42.492 DEBUG 61276 --- [Thread-21] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 11:53:42.501 DEBUG 61276 --- [Thread-21] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 11:53:42.531  INFO 61276 --- [dark-task-1] [] c.m.n.p.job.TaskSchedulingManager       : <0.1><AXw8anMR> 【计时器】J2(P2-S5) [1]: running time = 1秒13毫秒(1013767800)
---------------------------------------------------------
  %   Task name        detail           ns
---------------------------------------------------------

2025-08-25 11:53:42.541 DEBUG 61276 --- [Thread-22] [] c.m.n.p.m.DiscoveryLogMapper.insertList : <==    Updates: 1
2025-08-25 11:53:42.546 DEBUG 61276 --- [Thread-22] [] c.m.n.p.m.DiscoveryLogMapper.insertList : ==>  Preparing: INSERT INTO discovery_log (`job_id`,`percent`,`content`) VALUES (?,?,?)
2025-08-25 11:53:42.548 DEBUG 61276 --- [Thread-22] [] c.m.n.p.m.DiscoveryLogMapper.insertList : ==> Parameters: 2(Long), 0.00(BigDecimal), [{"msg":"作业执行异常,null","status":"ERROR","time":"2025-08-25 11:53:42","remark":"java.lang.NullPointerException\r\n\tat java.io.File.<init>(File.java:277)\r\n\tat com.mchz.nyx.pipeline.service.impl.FileManagerServiceImpl.loadFile(FileManagerServiceImpl.java:56)\r\n\tat com.mchz.nyx.pipeline.generator.meta.FileMetaAdapter.execute(FileMetaAdapter.java:110)\r\n\tat com.mchz.nyx.meta.instance.crawl.MetaAgentCrawl.execute(MetaAgentCrawl.java:45)\r\n\tat com.mchz.nyx.meta.instance.crawl.AbstractEventCrawl.crawl(AbstractEventCrawl.java:28)\r\n\tat com.mchz.nyx.meta.instance.DefaultNyxInstance.run(DefaultNyxInstance.java:31)\r\n\tat com.mchz.nyx.pipeline.job.collect.CollectMetaJob.execute(CollectMetaJob.java:73)\r\n\tat com.mchz.nyx.pipeline.job.TaskSchedulingManager.executeTask(TaskSchedulingManager.java:169)\r\n\tat com.mchz.nyx.pipeline.job.TaskSchedulingManager.lambda$submit$5(TaskSchedulingManager.java:111)\r\n\tat com.alibaba.ttl.TtlRunnable.run(TtlRunnable.java:59)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(F..."}](String)
2025-08-25 11:53:42.625 DEBUG 61276 --- [Thread-22] [] c.m.n.p.m.DiscoveryLogMapper.insertList : <==    Updates: 1
2025-08-25 11:54:34.452 DEBUG 61276 --- [http-nio-8888-exec-3] [] c.m.n.p.controller.TaskController       : <0.1><x9V0v33h> {"industryId":2,"planId":2,"jobId":3,"jobType":7,"source":{"streamDataTypes":[],"id":5,"configName":"文件","configType":130,"dataBaseType":"LOCAL_FILE","host":"","port":"","username":"","dbName":"","attachment":{"headline":"true","type":"0","compress":"false"},"advancedConfig":{},"properties":{}},"sampleLine":100,"sampleRate":100,"full":false,"saveStrategy":0}
2025-08-25 11:54:34.454  INFO 61276 --- [http-nio-8888-exec-3] [] c.m.n.p.job.TaskSchedulingManager       : <0.1><x9V0v33h> 【任务管理】全库
2025-08-25 11:54:34.456  INFO 61276 --- [dark-task-2] [] c.m.n.p.job.TaskSchedulingManager       : <0.1><x9V0v33h> 【作业执行】J3(P2-S5) 文件
2025-08-25 11:54:34.456 DEBUG 61276 --- [dark-task-2] [] c.m.n.p.s.impl.TaskCallbackServiceImpl  : <0.1><x9V0v33h> 【心跳】TaskHeartbeatBeatReq(taskGroup=COLLECT_META, taskName=3, status=1)
2025-08-25 11:54:34.457 DEBUG 61276 --- [dark-task-2] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : <0.1><x9V0v33h> [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 11:54:34.469 DEBUG 61276 --- [dark-task-2] [] c.m.n.p.m.DiscoveryJobMapper.selectList : <0.1><x9V0v33h> ==>  Preparing: SELECT `heartbeat` FROM discovery_job WHERE (`id` = ?)
2025-08-25 11:54:34.469 DEBUG 61276 --- [dark-task-2] [] c.m.n.p.m.DiscoveryJobMapper.selectList : <0.1><x9V0v33h> ==> Parameters: 3(Long)
2025-08-25 11:54:34.473 DEBUG 61276 --- [dark-task-2] [] c.m.n.p.m.DiscoveryJobMapper.selectList : <0.1><x9V0v33h> <==      Total: 1
2025-08-25 11:54:34.473  INFO 61276 --- [dark-task-2] [] c.m.n.p.job.collect.CollectMetaJob      : <0.1><x9V0v33h> 【分类分级】采集元数据
2025-08-25 11:54:34.476 DEBUG 61276 --- [dark-task-2] [] c.m.n.p.m.F.selectList                  : <0.1><x9V0v33h> ==>  Preparing: SELECT `id`,`config_id`,`uuid`,`compress_uuid`,`name`,`format`,`moved`,`temp_path`,`path` FROM file_upload_detail WHERE (`config_id` = ?)
2025-08-25 11:54:34.477 DEBUG 61276 --- [dark-task-2] [] c.m.n.p.m.F.selectList                  : <0.1><x9V0v33h> ==> Parameters: 5(Long)
2025-08-25 11:54:34.479 DEBUG 61276 --- [dark-task-2] [] c.m.n.p.m.F.selectList                  : <0.1><x9V0v33h> <==      Total: 1
2025-08-25 11:54:34.485 DEBUG 61276 --- [meta-5-1] [] c.m.n.p.m.DbMetaObjectMapper.selectList : <0.1><x9V0v33h> ==>  Preparing: SELECT `oid`,`name`,`deleted`,`revision`,`hash_value` FROM dbmeta_object WHERE (`source_id` = ? AND `pid` = ? AND `type` = ?)
2025-08-25 11:54:34.486 DEBUG 61276 --- [meta-5-1] [] c.m.n.p.m.DbMetaObjectMapper.selectList : <0.1><x9V0v33h> ==> Parameters: 5(Long), 0(Long), SCHEMA(String)
2025-08-25 11:54:34.489 DEBUG 61276 --- [meta-5-1] [] c.m.n.p.m.DbMetaObjectMapper.selectList : <0.1><x9V0v33h> <==      Total: 1
2025-08-25 11:54:34.527 DEBUG 61276 --- [Thread-22] [] c.m.n.p.m.DiscoveryLogMapper.insertList : ==>  Preparing: INSERT INTO discovery_log (`job_id`,`percent`,`content`) VALUES (?,?,?)
2025-08-25 11:54:34.533 DEBUG 61276 --- [Thread-22] [] c.m.n.p.m.DiscoveryLogMapper.insertList : ==> Parameters: 3(Long), 0.00(BigDecimal), [{"msg":"开始采集元数据信息","status":"INFO","time":"2025-08-25 11:54:34"}](String)
2025-08-25 11:54:44.287 DEBUG 61276 --- [Thread-22] [] c.m.n.p.m.DiscoveryLogMapper.insertList : <==    Updates: 1
2025-08-25 11:54:44.289 DEBUG 61276 --- [Thread-21] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 11:54:44.289 ERROR 61276 --- [dark-task-2] [] c.m.n.p.job.TaskSchedulingManager       : <0.1><x9V0v33h> 【作业执行】J3(P2-S5) 执行异常

java.lang.NullPointerException: null
	at java.io.File.<init>(File.java:277) ~[na:1.8.0_301]
	at com.mchz.nyx.pipeline.service.impl.FileManagerServiceImpl.loadFile(FileManagerServiceImpl.java:56) ~[classes/:na]
	at com.mchz.nyx.pipeline.generator.meta.FileMetaAdapter.execute(FileMetaAdapter.java:110) ~[classes/:na]
	at com.mchz.nyx.meta.instance.crawl.MetaAgentCrawl.execute(MetaAgentCrawl.java:45) ~[classes/:na]
	at com.mchz.nyx.meta.instance.crawl.AbstractEventCrawl.crawl(AbstractEventCrawl.java:28) ~[classes/:na]
	at com.mchz.nyx.meta.instance.DefaultNyxInstance.run(DefaultNyxInstance.java:31) ~[classes/:na]
	at com.mchz.nyx.pipeline.job.collect.CollectMetaJob.execute(CollectMetaJob.java:73) ~[classes/:na]
	at com.mchz.nyx.pipeline.job.TaskSchedulingManager.executeTask(TaskSchedulingManager.java:169) [classes/:na]
	at com.mchz.nyx.pipeline.job.TaskSchedulingManager.lambda$submit$5(TaskSchedulingManager.java:111) [classes/:na]
	at com.alibaba.ttl.TtlRunnable.run(TtlRunnable.java:59) ~[transmittable-thread-local-2.12.4.jar:na]
	at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511) ~[na:1.8.0_301]
	at java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:266) ~[na:1.8.0_301]
	at java.util.concurrent.FutureTask.run(FutureTask.java) ~[na:1.8.0_301]
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149) ~[na:1.8.0_301]
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624) ~[na:1.8.0_301]
	at java.lang.Thread.run(Thread.java:748) ~[na:1.8.0_301]

2025-08-25 11:54:44.291 DEBUG 61276 --- [dark-task-2] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : <0.1><x9V0v33h> [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 11:54:44.294 DEBUG 61276 --- [Thread-22] [] c.m.n.p.m.DiscoveryLogMapper.insertList : ==>  Preparing: INSERT INTO discovery_log (`job_id`,`percent`,`content`) VALUES (?,?,?)
2025-08-25 11:54:44.295 DEBUG 61276 --- [Thread-22] [] c.m.n.p.m.DiscoveryLogMapper.insertList : ==> Parameters: 3(Long), 0.00(BigDecimal), [{"msg":"作业执行异常,null","status":"ERROR","time":"2025-08-25 11:54:44","remark":"java.lang.NullPointerException\r\n\tat java.io.File.<init>(File.java:277)\r\n\tat com.mchz.nyx.pipeline.service.impl.FileManagerServiceImpl.loadFile(FileManagerServiceImpl.java:56)\r\n\tat com.mchz.nyx.pipeline.generator.meta.FileMetaAdapter.execute(FileMetaAdapter.java:110)\r\n\tat com.mchz.nyx.meta.instance.crawl.MetaAgentCrawl.execute(MetaAgentCrawl.java:45)\r\n\tat com.mchz.nyx.meta.instance.crawl.AbstractEventCrawl.crawl(AbstractEventCrawl.java:28)\r\n\tat com.mchz.nyx.meta.instance.DefaultNyxInstance.run(DefaultNyxInstance.java:31)\r\n\tat com.mchz.nyx.pipeline.job.collect.CollectMetaJob.execute(CollectMetaJob.java:73)\r\n\tat com.mchz.nyx.pipeline.job.TaskSchedulingManager.executeTask(TaskSchedulingManager.java:169)\r\n\tat com.mchz.nyx.pipeline.job.TaskSchedulingManager.lambda$submit$5(TaskSchedulingManager.java:111)\r\n\tat com.alibaba.ttl.TtlRunnable.run(TtlRunnable.java:59)\r\n\tat java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)\r\n\tat java.util.concurrent.FutureTask.run$$$capture(F..."}](String)
2025-08-25 11:54:44.304 DEBUG 61276 --- [Thread-21] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 11:54:44.311 DEBUG 61276 --- [Thread-21] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 11:54:44.313 DEBUG 61276 --- [dark-task-2] [] c.m.nyx.pipeline.http.HttpRemoteInvoke  : <0.1><x9V0v33h> {"code":200,"success":true,"msg":"操作成功","tranceId":"x9V0v33h"}
2025-08-25 11:54:44.319 DEBUG 61276 --- [Thread-21] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 11:54:44.319 DEBUG 61276 --- [dark-task-2] [] c.m.n.p.m.DiscoveryJobMapper.updateById : <0.1><x9V0v33h> ==>  Preparing: UPDATE discovery_job SET `heartbeat`=? WHERE `id`=?
2025-08-25 11:54:44.320 DEBUG 61276 --- [dark-task-2] [] c.m.n.p.m.DiscoveryJobMapper.updateById : <0.1><x9V0v33h> ==> Parameters: {"startTime":1756094074473,"step":1,"num":0}(String), 3(Long)
2025-08-25 11:54:44.337 DEBUG 61276 --- [Thread-22] [] c.m.n.p.m.DiscoveryLogMapper.insertList : <==    Updates: 1
2025-08-25 11:54:44.404 DEBUG 61276 --- [dark-task-2] [] c.m.n.p.m.DiscoveryJobMapper.updateById : <0.1><x9V0v33h> <==    Updates: 1
2025-08-25 11:54:44.404 DEBUG 61276 --- [dark-task-2] [] c.m.n.p.s.impl.TaskCallbackServiceImpl  : <0.1><x9V0v33h> 【心跳】TaskHeartbeatBeatReq(taskGroup=COLLECT_META, taskName=3, status=3)
2025-08-25 11:54:44.405 DEBUG 61276 --- [dark-task-2] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : <0.1><x9V0v33h> [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 11:54:44.410  INFO 61276 --- [dark-task-2] [] c.m.n.p.job.TaskSchedulingManager       : <0.1><x9V0v33h> 【计时器】J3(P2-S5) [1]: running time = 9秒937毫秒(**********)
---------------------------------------------------------
  %   Task name        detail           ns
---------------------------------------------------------

2025-08-25 11:55:14.347  INFO 61276 --- [SpringApplicationShutdownHook] [] c.m.n.p.job.TaskSchedulingManager       : Shutting down task manager executor
2025-08-25 11:55:14.416  INFO 61276 --- [SpringApplicationShutdownHook] [] c.b.d.d.DynamicRoutingDataSource        : dynamic-datasource start closing ....
2025-08-25 11:55:14.436  INFO 61276 --- [SpringApplicationShutdownHook] [] com.alibaba.druid.pool.DruidDataSource  : {dataSource-1} closing ...
2025-08-25 11:55:14.444  INFO 61276 --- [SpringApplicationShutdownHook] [] com.alibaba.druid.pool.DruidDataSource  : {dataSource-1} closed
2025-08-25 11:55:14.444  INFO 61276 --- [SpringApplicationShutdownHook] [] c.b.d.d.d.DefaultDataSourceDestroyer    : dynamic-datasource close the datasource named [embed] success,
2025-08-25 11:55:14.446  INFO 61276 --- [SpringApplicationShutdownHook] [] com.zaxxer.hikari.HikariDataSource      : dark-dbms - Shutdown initiated...
2025-08-25 11:55:14.452  INFO 61276 --- [SpringApplicationShutdownHook] [] com.zaxxer.hikari.HikariDataSource      : dark-dbms - Shutdown completed.
2025-08-25 11:55:14.554  INFO 61276 --- [SpringApplicationShutdownHook] [] c.b.d.d.d.DefaultDataSourceDestroyer    : dynamic-datasource close the datasource named [master] success,
2025-08-25 11:55:14.554  INFO 61276 --- [SpringApplicationShutdownHook] [] c.b.d.d.DynamicRoutingDataSource        : dynamic-datasource all closed success,bye
2025-08-25 11:55:22.587  INFO 116336 --- [main] [] c.a.n.client.env.SearchableProperties   : properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-08-25 11:55:22.959  INFO 116336 --- [main] [] com.mchz.nyx.pipeline.Application       : Starting Application using Java 1.8.0_301 on DESKTOP-TQMNVVF with PID 116336 (C:\projects\nyx\pipeline\target\classes started by admin in C:\projects\nyx\pipeline)
2025-08-25 11:55:22.960 DEBUG 116336 --- [main] [] com.mchz.nyx.pipeline.Application       : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-08-25 11:55:22.961  INFO 116336 --- [main] [] com.mchz.nyx.pipeline.Application       : The following 1 profile is active: "standalone"
2025-08-25 11:55:25.711  INFO 116336 --- [main] [] o.s.cloud.context.scope.GenericScope    : BeanFactory id=894701e2-e985-33a1-851d-e7f0611b4c97
2025-08-25 11:55:25.817  INFO 116336 --- [main] [] ptablePropertiesBeanFactoryPostProcessor: Post-processing PropertySource instances
2025-08-25 11:55:25.818  INFO 116336 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-08-25 11:55:25.821  INFO 116336 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-08-25 11:55:25.821  INFO 116336 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-08-25 11:55:25.822  INFO 116336 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-08-25 11:55:25.823  INFO 116336 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-08-25 11:55:25.824  INFO 116336 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-08-25 11:55:25.825  INFO 116336 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-08-25 11:55:25.825  INFO 116336 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-08-25 11:55:25.825  INFO 116336 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Converting PropertySource Config resource 'file [config\sourcedata.properties]' via location './config/sourcedata.properties' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-08-25 11:55:25.825  INFO 116336 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Converting PropertySource Config resource 'class path resource [application-standalone.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-08-25 11:55:25.825  INFO 116336 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Converting PropertySource Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-08-25 11:55:25.826  INFO 116336 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Converting PropertySource TLog Default Properties [org.springframework.core.io.support.ResourcePropertySource] to EncryptableMapPropertySourceWrapper
2025-08-25 11:55:25.826  INFO 116336 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Converting PropertySource defaultProperties [org.springframework.boot.DefaultPropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-08-25 11:55:25.835  INFO 116336 --- [main] [] c.g.y.a.MybatisPlusJoinAutoConfiguration: MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
2025-08-25 11:55:26.213  INFO 116336 --- [main] [] c.u.j.filter.DefaultLazyPropertyFilter  : Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-08-25 11:55:26.228  INFO 116336 --- [main] [] c.u.j.r.DefaultLazyPropertyResolver     : Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-08-25 11:55:26.232  INFO 116336 --- [main] [] c.u.j.d.DefaultLazyPropertyDetector     : Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-08-25 11:55:26.630 DEBUG 116336 --- [main] [] c.m.n.p.c.EmbeddedTomcatConfiguration$1 : Code archive: C:\repository\org\springframework\boot\spring-boot\2.7.18\spring-boot-2.7.18.jar
2025-08-25 11:55:26.632 DEBUG 116336 --- [main] [] c.m.n.p.c.EmbeddedTomcatConfiguration$1 : Code archive: C:\repository\org\springframework\boot\spring-boot\2.7.18\spring-boot-2.7.18.jar
2025-08-25 11:55:26.633 DEBUG 116336 --- [main] [] c.m.n.p.c.EmbeddedTomcatConfiguration$1 : None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored.
2025-08-25 11:55:26.684  INFO 116336 --- [main] [] o.s.b.w.embedded.tomcat.TomcatWebServer : Tomcat initialized with port(s): 8888 (http)
2025-08-25 11:55:26.704  INFO 116336 --- [main] [] o.apache.catalina.core.StandardService  : Starting service [Tomcat]
2025-08-25 11:55:26.704  INFO 116336 --- [main] [] org.apache.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/9.0.106]
2025-08-25 11:55:26.720  WARN 116336 --- [main] [] o.a.c.webresources.DirResourceSet       : Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.8888.94679989713999061] which is part of the web application []
2025-08-25 11:55:26.987  INFO 116336 --- [main] [] o.a.c.c.C.[Tomcat].[localhost].[/]      : Initializing Spring embedded WebApplicationContext
2025-08-25 11:55:26.987  INFO 116336 --- [main] [] w.s.c.ServletWebServerApplicationContext: Root WebApplicationContext: initialization completed in 3371 ms
2025-08-25 11:55:27.476 ERROR 116336 --- [main] [] com.alibaba.druid.pool.DruidDataSource  : testWhileIdle is true, validationQuery not set
2025-08-25 11:55:27.484  INFO 116336 --- [main] [] com.alibaba.druid.pool.DruidDataSource  : {dataSource-1,embed} inited
2025-08-25 11:55:27.485  INFO 116336 --- [main] [] c.b.d.d.DynamicRoutingDataSource        : dynamic-datasource - add a datasource named [embed] success
2025-08-25 11:55:27.485  INFO 116336 --- [main] [] c.b.d.d.DynamicRoutingDataSource        : dynamic-datasource - add a datasource named [master] success
2025-08-25 11:55:27.485  INFO 116336 --- [main] [] c.b.d.d.DynamicRoutingDataSource        : dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-08-25 11:55:30.966  INFO 116336 --- [main] [] c.m.n.p.job.TaskSchedulingManager       : DISCOVERY: DiscoveryJob
2025-08-25 11:55:30.966  INFO 116336 --- [main] [] c.m.n.p.job.TaskSchedulingManager       : ASSET_RELATION: AssetRelationJob
2025-08-25 11:55:30.966  INFO 116336 --- [main] [] c.m.n.p.job.TaskSchedulingManager       : COLLECT_META: CollectMetaJob
2025-08-25 11:55:32.027  INFO 116336 --- [main] [] m.e.s.MybatisPlusApplicationContextAware: Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@622ef26a
2025-08-25 11:55:32.423  INFO 116336 --- [main] [] com.zaxxer.hikari.HikariDataSource      : dark-dbms - Starting...
2025-08-25 11:55:32.982  INFO 116336 --- [main] [] com.zaxxer.hikari.HikariDataSource      : dark-dbms - Start completed.
2025-08-25 11:55:34.281  INFO 116336 --- [main] [] o.s.b.w.embedded.tomcat.TomcatWebServer : Tomcat started on port(s): 8888 (http) with context path ''
2025-08-25 11:55:34.284  INFO 116336 --- [main] [] u.j.c.RefreshScopeRefreshedEventListener: Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-08-25 11:55:34.284  INFO 116336 --- [main] [] CachingDelegateEncryptablePropertySource: Property Source systemProperties refreshed
2025-08-25 11:55:34.285  INFO 116336 --- [main] [] CachingDelegateEncryptablePropertySource: Property Source systemEnvironment refreshed
2025-08-25 11:55:34.285  INFO 116336 --- [main] [] CachingDelegateEncryptablePropertySource: Property Source random refreshed
2025-08-25 11:55:34.285  INFO 116336 --- [main] [] CachingDelegateEncryptablePropertySource: Property Source cachedrandom refreshed
2025-08-25 11:55:34.285  INFO 116336 --- [main] [] CachingDelegateEncryptablePropertySource: Property Source springCloudClientHostInfo refreshed
2025-08-25 11:55:34.286  INFO 116336 --- [main] [] CachingDelegateEncryptablePropertySource: Property Source Config resource 'file [config\sourcedata.properties]' via location './config/sourcedata.properties' refreshed
2025-08-25 11:55:34.286  INFO 116336 --- [main] [] CachingDelegateEncryptablePropertySource: Property Source Config resource 'class path resource [application-standalone.yml]' via location 'optional:classpath:/' refreshed
2025-08-25 11:55:34.286  INFO 116336 --- [main] [] CachingDelegateEncryptablePropertySource: Property Source Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' refreshed
2025-08-25 11:55:34.286  INFO 116336 --- [main] [] CachingDelegateEncryptablePropertySource: Property Source TLog Default Properties refreshed
2025-08-25 11:55:34.286  INFO 116336 --- [main] [] CachingDelegateEncryptablePropertySource: Property Source defaultProperties refreshed
2025-08-25 11:55:34.286  INFO 116336 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-08-25 11:55:34.287  INFO 116336 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-08-25 11:55:34.287  INFO 116336 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-08-25 11:55:34.543  INFO 116336 --- [main] [] com.mchz.nyx.pipeline.Application       : Started Application in 13.569 seconds (JVM running for 15.721)
2025-08-25 11:55:34.627  INFO 116336 --- [main] [] c.m.n.p.config.McDatasourceConfig       : 设置统一数据源版本:*******;路径:C:\file\mcdatasource_1681
2025-08-25 11:55:48.855  INFO 116336 --- [http-nio-8888-exec-1] [] o.a.c.c.C.[Tomcat].[localhost].[/]      : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-25 11:55:48.856  INFO 116336 --- [http-nio-8888-exec-1] [] o.s.web.servlet.DispatcherServlet       : Initializing Servlet 'dispatcherServlet'
2025-08-25 11:55:48.859  INFO 116336 --- [http-nio-8888-exec-1] [] o.s.web.servlet.DispatcherServlet       : Completed initialization in 3 ms
2025-08-25 11:55:49.570 DEBUG 116336 --- [http-nio-8888-exec-1] [] c.m.n.p.controller.TaskController       : <0.1><R0TewMeM> {"industryId":2,"planId":2,"jobId":4,"jobType":7,"source":{"streamDataTypes":[],"id":5,"configName":"文件","configType":130,"dataBaseType":"LOCAL_FILE","host":"","port":"","username":"","dbName":"","attachment":{"headline":"true","type":"0","compress":"false"},"advancedConfig":{},"properties":{}},"sampleLine":100,"sampleRate":100,"full":false,"saveStrategy":0}
2025-08-25 11:55:49.602  INFO 116336 --- [http-nio-8888-exec-1] [] c.m.n.p.job.TaskSchedulingManager       : <0.1><R0TewMeM> 【任务管理】全库
2025-08-25 11:55:49.624  INFO 116336 --- [dark-task-1] [] c.m.n.p.job.TaskSchedulingManager       : <0.1><R0TewMeM> 【作业执行】J4(P2-S5) 文件
2025-08-25 11:55:49.624 DEBUG 116336 --- [dark-task-1] [] c.m.n.p.s.impl.TaskCallbackServiceImpl  : <0.1><R0TewMeM> 【心跳】TaskHeartbeatBeatReq(taskGroup=COLLECT_META, taskName=4, status=1)
2025-08-25 11:55:49.625  WARN 116336 --- [dark-task-1] [] c.m.nyx.pipeline.http.HttpRemoteInvoke  : <0.1><R0TewMeM> 【地址获取】默认使用localhost
2025-08-25 11:55:49.692 DEBUG 116336 --- [dark-task-1] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : <0.1><R0TewMeM> [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 11:55:51.269 DEBUG 116336 --- [dark-task-1] [] c.m.n.p.m.DiscoveryJobMapper.selectList : <0.1><R0TewMeM> ==>  Preparing: SELECT `heartbeat` FROM discovery_job WHERE (`id` = ?)
2025-08-25 11:55:51.370 DEBUG 116336 --- [dark-task-1] [] c.m.n.p.m.DiscoveryJobMapper.selectList : <0.1><R0TewMeM> ==> Parameters: 4(Long)
2025-08-25 11:55:51.403 DEBUG 116336 --- [dark-task-1] [] c.m.n.p.m.DiscoveryJobMapper.selectList : <0.1><R0TewMeM> <==      Total: 1
2025-08-25 11:55:51.419  INFO 116336 --- [dark-task-1] [] c.m.n.p.job.collect.CollectMetaJob      : <0.1><R0TewMeM> 【分类分级】采集元数据
2025-08-25 11:55:51.655 DEBUG 116336 --- [dark-task-1] [] c.m.n.p.m.F.selectList                  : <0.1><R0TewMeM> ==>  Preparing: SELECT `id`,`config_id`,`uuid`,`compress_uuid`,`name`,`format`,`moved`,`temp_path`,`path` FROM file_upload_detail WHERE (`config_id` = ?)
2025-08-25 11:55:51.676 DEBUG 116336 --- [dark-task-1] [] c.m.n.p.m.F.selectList                  : <0.1><R0TewMeM> ==> Parameters: 5(Long)
2025-08-25 11:55:51.681 DEBUG 116336 --- [dark-task-1] [] c.m.n.p.m.F.selectList                  : <0.1><R0TewMeM> <==      Total: 1
2025-08-25 11:55:51.779 DEBUG 116336 --- [meta-5-1] [] c.m.n.p.m.DbMetaObjectMapper.selectList : <0.1><R0TewMeM> ==>  Preparing: SELECT `oid`,`name`,`deleted`,`revision`,`hash_value` FROM dbmeta_object WHERE (`source_id` = ? AND `pid` = ? AND `type` = ?)
2025-08-25 11:55:51.783 DEBUG 116336 --- [meta-5-1] [] c.m.n.p.m.DbMetaObjectMapper.selectList : <0.1><R0TewMeM> ==> Parameters: 5(Long), 0(Long), SCHEMA(String)
2025-08-25 11:55:51.791 DEBUG 116336 --- [meta-5-1] [] c.m.n.p.m.DbMetaObjectMapper.selectList : <0.1><R0TewMeM> <==      Total: 1
2025-08-25 11:55:51.821 DEBUG 116336 --- [Thread-22] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 11:55:51.842 DEBUG 116336 --- [Thread-22] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 11:55:51.842 DEBUG 116336 --- [Thread-23] [] c.m.n.p.m.DiscoveryLogMapper.insertList : ==>  Preparing: INSERT INTO discovery_log (`job_id`,`percent`,`content`) VALUES (?,?,?)
2025-08-25 11:55:51.845 DEBUG 116336 --- [Thread-23] [] c.m.n.p.m.DiscoveryLogMapper.insertList : ==> Parameters: 4(Long), 0.00(BigDecimal), [{"msg":"开始采集元数据信息","status":"INFO","time":"2025-08-25 11:55:51"}](String)
2025-08-25 11:55:51.852 DEBUG 116336 --- [Thread-22] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 11:55:51.858 DEBUG 116336 --- [Thread-22] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 11:55:51.860 DEBUG 116336 --- [meta-5-1] [] c.m.n.p.m.DbMetaObjectMapper.selectList : <0.1><R0TewMeM> ==>  Preparing: SELECT `oid`,`name`,`deleted`,`revision`,`hash_value` FROM dbmeta_object WHERE (`source_id` = ? AND `pid` = ? AND `type` = ? AND `name` = ?)
2025-08-25 11:55:51.861 DEBUG 116336 --- [meta-5-1] [] c.m.n.p.m.DbMetaObjectMapper.selectList : <0.1><R0TewMeM> ==> Parameters: 5(Long), 0(Long), SCHEMA(String), default(String)
2025-08-25 11:55:51.864 DEBUG 116336 --- [meta-5-1] [] c.m.n.p.m.DbMetaObjectMapper.selectList : <0.1><R0TewMeM> <==      Total: 1
2025-08-25 11:55:51.873 DEBUG 116336 --- [meta-5-1] [] c.m.n.p.m.DbMetaObjectMapper.selectList : <0.1><R0TewMeM> ==>  Preparing: SELECT `oid`,`name`,`type`,`deleted`,`revision`,`hash_value` FROM dbmeta_object WHERE (`source_id` = ? AND `pid` = ? AND `type` = ?)
2025-08-25 11:55:51.874 DEBUG 116336 --- [meta-5-1] [] c.m.n.p.m.DbMetaObjectMapper.selectList : <0.1><R0TewMeM> ==> Parameters: 5(Long), 1(Long), TABLE(String)
2025-08-25 11:55:51.877 DEBUG 116336 --- [meta-5-1] [] c.m.n.p.m.DbMetaObjectMapper.selectList : <0.1><R0TewMeM> <==      Total: 0
2025-08-25 11:55:51.881 DEBUG 116336 --- [Thread-23] [] c.m.n.p.m.DiscoveryLogMapper.insertList : <==    Updates: 1
2025-08-25 11:55:51.888 DEBUG 116336 --- [Thread-23] [] c.m.n.p.m.DiscoveryLogMapper.insertList : ==>  Preparing: INSERT INTO discovery_log (`job_id`,`percent`,`content`) VALUES (?,?,?)
2025-08-25 11:55:51.889 DEBUG 116336 --- [Thread-23] [] c.m.n.p.m.DiscoveryLogMapper.insertList : ==> Parameters: 4(Long), 0.00(BigDecimal), [{"msg":"采集 default","status":"INFO","time":"2025-08-25 11:55:51"}](String)
2025-08-25 11:55:51.925 DEBUG 116336 --- [Thread-23] [] c.m.n.p.m.DiscoveryLogMapper.insertList : <==    Updates: 1
2025-08-25 11:55:52.873 DEBUG 116336 --- [store-1] [] c.m.n.p.m.DbMetaObjectMapper.insertList : <0.1><R0TewMeM> ==>  Preparing: INSERT INTO dbmeta_object (`source_id`,`name`,`type`,`description`,`deleted`,`created_at`,`updated_at`,`revision`,`hash_value`,`pid`) VALUES (?,?,?,?,?,?,?,?,?,?)
2025-08-25 11:55:52.875 DEBUG 116336 --- [store-1] [] c.m.n.p.m.DbMetaObjectMapper.insertList : <0.1><R0TewMeM> ==> Parameters: 5(Long), A_DATA_100W(String), TABLE(String), null, false(Boolean), 2025-08-25 11:55:52.865(Timestamp), 2025-08-25 11:55:52.865(Timestamp), 1(Integer), 1c5d8fa3b9e6512ed95397860daac0bb(String), 1(Long)
2025-08-25 11:55:52.885 DEBUG 116336 --- [store-1] [] c.m.n.p.m.DbMetaObjectMapper.insertList : <0.1><R0TewMeM> <==    Updates: 1
2025-08-25 11:55:52.911 DEBUG 116336 --- [store-1] [] c.m.n.p.m.DbMetaObjectMapper.insertList : <0.1><R0TewMeM> ==>  Preparing: INSERT INTO dbmeta_object (`source_id`,`name`,`type`,`description`,`deleted`,`created_at`,`updated_at`,`revision`,`hash_value`,`pid`) VALUES (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?)
2025-08-25 11:55:52.931 DEBUG 116336 --- [store-1] [] c.m.n.p.m.DbMetaObjectMapper.insertList : <0.1><R0TewMeM> ==> Parameters: 5(Long), ID(String), COLUMN(String), null, false(Boolean), 2025-08-25 11:55:52.886(Timestamp), 2025-08-25 11:55:52.886(Timestamp), 1(Integer), af189e7aa69a896ef53e23bf3f085247(String), 2(Long), 5(Long), NAME(String), COLUMN(String), null, false(Boolean), 2025-08-25 11:55:52.886(Timestamp), 2025-08-25 11:55:52.886(Timestamp), 1(Integer), 28bc99a335ec8fb48ab54dbacd9c84e5(String), 2(Long), 5(Long), SFZID(String), COLUMN(String), null, false(Boolean), 2025-08-25 11:55:52.886(Timestamp), 2025-08-25 11:55:52.886(Timestamp), 1(Integer), 8332aa3e17e10368544e0f16b8c14fd9(String), 2(Long), 5(Long), MOBILE(String), COLUMN(String), null, false(Boolean), 2025-08-25 11:55:52.886(Timestamp), 2025-08-25 11:55:52.886(Timestamp), 1(Integer), e0917b1560f4184de14494c8137a28a3(String), 2(Long), 5(Long), CARDNO(String), COLUMN(String), null, false(Boolean), 2025-08-25 11:55:52.886(Timestamp), 2025-08-25 11:55:52.886(Timestamp), 1(Integer), 0817b61d46a60ba71fcf0bccc5395a21(String), 2(Long), 5(Long), EMAIL(String), COLUMN(String), null, false(Boolean), 2025-08-25 11:55:52.886(Timestamp), 2025-08-25 11:55:52.886(Timestamp), 1(Integer), addd8bc09a61428a7518f0e7166551bc(String), 2(Long), 5(Long), ADDRESS(String), COLUMN(String), null, false(Boolean), 2025-08-25 11:55:52.886(Timestamp), 2025-08-25 11:55:52.886(Timestamp), 1(Integer), 5eaecfcb97aaa7a1ac4990a0d6225dfd(String), 2(Long), 5(Long), POSTAL(String), COLUMN(String), null, false(Boolean), 2025-08-25 11:55:52.886(Timestamp), 2025-08-25 11:55:52.886(Timestamp), 1(Integer), 52060129cce2ee4053d31c28cccaa8cf(String), 2(Long), 5(Long), CVV(String), COLUMN(String), null, false(Boolean), 2025-08-25 11:55:52.886(Timestamp), 2025-08-25 11:55:52.886(Timestamp), 1(Integer), 47ff1e61d55d50d3a14c0f704a8ecb16(String), 2(Long), 5(Long), IP(String), COLUMN(String), null, false(Boolean), 2025-08-25 11:55:52.886(Timestamp), 2025-08-25 11:55:52.886(Timestamp), 1(Integer), 89e0d87eba9a413562f46a8890d1ff8e(String), 2(Long), 5(Long), ZZJGDM(String), COLUMN(String), null, false(Boolean), 2025-08-25 11:55:52.886(Timestamp), 2025-08-25 11:55:52.886(Timestamp), 1(Integer), b85ca0e3a358116283de46a4c3be35dd(String), 2(Long), 5(Long), ZZJGMC(String), COLUMN(String), null, false(Boolean), 2025-08-25 11:55:52.886(Timestamp), 2025-08-25 11:55:52.886(Timestamp), 1(Integer), 0e6a901629349810e96df93a1c520614(String), 2(Long), 5(Long), BIRTHDAY(String), COLUMN(String), null, false(Boolean), 2025-08-25 11:55:52.886(Timestamp), 2025-08-25 11:55:52.886(Timestamp), 1(Integer), 122c810b0ba50acddc5e6b33c7081dca(String), 2(Long), 5(Long), MONEY(String), COLUMN(String), null, false(Boolean), 2025-08-25 11:55:52.886(Timestamp), 2025-08-25 11:55:52.886(Timestamp), 1(Integer), bf7c0eaec2a37372657b2007f17017cd(String), 2(Long), 5(Long), STRING(String), COLUMN(String), null, false(Boolean), 2025-08-25 11:55:52.886(Timestamp), 2025-08-25 11:55:52.886(Timestamp), 1(Integer), d6fe81c1026adda9bbfcfdadef6676c3(String), 2(Long), 5(Long), NUM(String), COLUMN(String), null, false(Boolean), 2025-08-25 11:55:52.886(Timestamp), 2025-08-25 11:55:52.886(Timestamp), 1(Integer), e9db745463a6bdb56b84e5ab8a57806b(String), 2(Long), 5(Long), YLJGDJH(String), COLUMN(String), null, false(Boolean), 2025-08-25 11:55:52.886(Timestamp), 2025-08-25 11:55:52.886(Timestamp), 1(Integer), e271320c65c188abfc5eb1542945146f(String), 2(Long), 5(Long), YSZGZS(String), COLUMN(String), null, false(Boolean), 2025-08-25 11:55:52.886(Timestamp), 2025-08-25 11:55:52.886(Timestamp), 1(Integer), cc62c995344076d2a1abe08880e12650(String), 2(Long), 5(Long), YSZYZS(String), COLUMN(String), null, false(Boolean), 2025-08-25 11:55:52.886(Timestamp), 2025-08-25 11:55:52.886(Timestamp), 1(Integer), dd90758adfb931816970e2852cdf39eb(String), 2(Long), 5(Long), YYZZ(String), COLUMN(String), null, false(Boolean), 2025-08-25 11:55:52.886(Timestamp), 2025-08-25 11:55:52.886(Timestamp), 1(Integer), b8644e11e0b06ac580df8d0df89de216(String), 2(Long), 5(Long), SHTYXYDM(String), COLUMN(String), null, false(Boolean), 2025-08-25 11:55:52.886(Timestamp), 2025-08-25 11:55:52.886(Timestamp), 1(Integer), 773872ede392c081d878feb7ed77c291(String), 2(Long), 5(Long), PASSPORT(String), COLUMN(String), null, false(Boolean), 2025-08-25 11:55:52.886(Timestamp), 2025-08-25 11:55:52.886(Timestamp), 1(Integer), ab5054efce465fa48a5e0c53a9215bd1(String), 2(Long), 5(Long), SWDJZ(String), COLUMN(String), null, false(Boolean), 2025-08-25 11:55:52.886(Timestamp), 2025-08-25 11:55:52.886(Timestamp), 1(Integer), ec316b7ffb3e4585d21c65e91d592952(String), 2(Long), 5(Long), KHXKZ(String), COLUMN(String), null, false(Boolean), 2025-08-25 11:55:52.886(Timestamp), 2025-08-25 11:55:52.886(Timestamp), 1(Integer), bd5b1f8f614a38e1313af505ecce0f28(String), 2(Long), 5(Long), JGZ(String), COLUMN(String), null, false(Boolean), 2025-08-25 11:55:52.886(Timestamp), 2025-08-25 11:55:52.886(Timestamp), 1(Integer), cd06c0a0fa36b750b425b53f3964d1f1(String), 2(Long), 5(Long), CHINAPASSPORT(String), COLUMN(String), null, false(Boolean), 2025-08-25 11:55:52.886(Timestamp), 2025-08-25 11:55:52.886(Timestamp), 1(Integer), ba8a81f977213bae41bc65f8d1bdc1d0(String), 2(Long), 5(Long), GATXZ(String), COLUMN(String), null, false(Boolean), 2025-08-25 11:55:52.886(Timestamp), 2025-08-25 11:55:52.886(Timestamp), 1(Integer), 9cac2f8b7977a5cd875f5bdcd80e1dde(String), 2(Long), 5(Long), JJZZY(String), COLUMN(String), null, false(Boolean), 2025-08-25 11:55:52.886(Timestamp), 2025-08-25 11:55:52.886(Timestamp), 1(Integer), eea1e691931d43d875218db06bf11fdf(String), 2(Long), 5(Long), TWTBDLTXZ(String), COLUMN(String), null, false(Boolean), 2025-08-25 11:55:52.886(Timestamp), 2025-08-25 11:55:52.886(Timestamp), 1(Integer), 7483658af97ab5d1c75f9a8866bb9363(String), 2(Long), 5(Long), JJMC(String), COLUMN(String), null, false(Boolean), 2025-08-25 11:55:52.886(Timestamp), 2025-08-25 11:55:52.886(Timestamp), 1(Integer), 15a0a15f89d1edb06cdc4defd4b5f71f(String), 2(Long), 5(Long), JJDM(String), COLUMN(String), null, false(Boolean), 2025-08-25 11:55:52.886(Timestamp), 2025-08-25 11:55:52.886(Timestamp), 1(Integer), 61fc1bedab97c6b9cc5f60623a9dd5ca(String), 2(Long), 5(Long), MONEY2(String), COLUMN(String), null, false(Boolean), 2025-08-25 11:55:52.886(Timestamp), 2025-08-25 11:55:52.886(Timestamp), 1(Integer), 93a5b0f41b9e2dfc02f5bb6308395484(String), 2(Long), 5(Long), DATE1(String), COLUMN(String), null, false(Boolean), 2025-08-25 11:55:52.886(Timestamp), 2025-08-25 11:55:52.886(Timestamp), 1(Integer), 17cffb5afc402f26dd0e7568f5be23a9(String), 2(Long), 5(Long), DATE2(String), COLUMN(String), null, false(Boolean), 2025-08-25 11:55:52.886(Timestamp), 2025-08-25 11:55:52.886(Timestamp), 1(Integer), fbbabcab8d889a9a45300137ea35fc0a(String), 2(Long), 5(Long), ADDRESS2(String), COLUMN(String), null, false(Boolean), 2025-08-25 11:55:52.886(Timestamp), 2025-08-25 11:55:52.886(Timestamp), 1(Integer), d18aa25cf807da03fbca1e86c357eb05(String), 2(Long), 5(Long), ADDRESS3(String), COLUMN(String), null, false(Boolean), 2025-08-25 11:55:52.886(Timestamp), 2025-08-25 11:55:52.886(Timestamp), 1(Integer), dda3204cd8be41319ef1fb0e3dfdeecd(String), 2(Long), 5(Long), ADDRESS4(String), COLUMN(String), null, false(Boolean), 2025-08-25 11:55:52.886(Timestamp), 2025-08-25 11:55:52.886(Timestamp), 1(Integer), a8235be0bd70a76de8227ec6bdee9bed(String), 2(Long)
2025-08-25 11:55:52.953 DEBUG 116336 --- [store-1] [] c.m.n.p.m.DbMetaObjectMapper.insertList : <0.1><R0TewMeM> <==    Updates: 37
2025-08-25 11:55:52.961 DEBUG 116336 --- [store-1] [] c.m.n.p.m.DbMetaTableMapper.insertList  : <0.1><R0TewMeM> ==>  Preparing: INSERT INTO dbmeta_table (`oid`,`source_id`,`schema_id`,`type`,`column_num`,`partition_column`,`space`,`rows`,`comment`) VALUES (?,?,?,?,?,?,?,?,?)
2025-08-25 11:55:52.961 DEBUG 116336 --- [store-1] [] c.m.n.p.m.DbMetaTableMapper.insertList  : <0.1><R0TewMeM> ==> Parameters: 2(Long), 5(Long), 1(Long), TABLE(String), 37(Integer), null, null, null, null
2025-08-25 11:55:52.965 DEBUG 116336 --- [store-1] [] c.m.n.p.m.DbMetaTableMapper.insertList  : <0.1><R0TewMeM> <==    Updates: 1
2025-08-25 11:55:52.982 DEBUG 116336 --- [store-1] [] c.m.n.p.m.DbMetaColumnMapper.insertList : <0.1><R0TewMeM> ==>  Preparing: INSERT INTO dbmeta_column (`oid`,`source_id`,`schema_id`,`table_id`,`data_type`,`type_group`,`length`,`precision`,`scale`,`nullable`,`position`,`is_auto_incremented`,`is_generated`,`is_pk`,`is_unique`,`is_index`,`is_fk`,`comment`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)
2025-08-25 11:55:52.998 DEBUG 116336 --- [store-1] [] c.m.n.p.m.DbMetaColumnMapper.insertList : <0.1><R0TewMeM> ==> Parameters: 3(Long), 5(Long), 1(Long), 2(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 1(Integer), null, null, null, null, null, null, null, 4(Long), 5(Long), 1(Long), 2(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 2(Integer), null, null, null, null, null, null, null, 5(Long), 5(Long), 1(Long), 2(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 3(Integer), null, null, null, null, null, null, null, 6(Long), 5(Long), 1(Long), 2(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 4(Integer), null, null, null, null, null, null, null, 7(Long), 5(Long), 1(Long), 2(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 5(Integer), null, null, null, null, null, null, null, 8(Long), 5(Long), 1(Long), 2(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 6(Integer), null, null, null, null, null, null, null, 9(Long), 5(Long), 1(Long), 2(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 7(Integer), null, null, null, null, null, null, null, 10(Long), 5(Long), 1(Long), 2(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 8(Integer), null, null, null, null, null, null, null, 11(Long), 5(Long), 1(Long), 2(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 9(Integer), null, null, null, null, null, null, null, 12(Long), 5(Long), 1(Long), 2(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 10(Integer), null, null, null, null, null, null, null, 13(Long), 5(Long), 1(Long), 2(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 11(Integer), null, null, null, null, null, null, null, 14(Long), 5(Long), 1(Long), 2(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 12(Integer), null, null, null, null, null, null, null, 15(Long), 5(Long), 1(Long), 2(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 13(Integer), null, null, null, null, null, null, null, 16(Long), 5(Long), 1(Long), 2(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 14(Integer), null, null, null, null, null, null, null, 17(Long), 5(Long), 1(Long), 2(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 15(Integer), null, null, null, null, null, null, null, 18(Long), 5(Long), 1(Long), 2(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 16(Integer), null, null, null, null, null, null, null, 19(Long), 5(Long), 1(Long), 2(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 17(Integer), null, null, null, null, null, null, null, 20(Long), 5(Long), 1(Long), 2(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 18(Integer), null, null, null, null, null, null, null, 21(Long), 5(Long), 1(Long), 2(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 19(Integer), null, null, null, null, null, null, null, 22(Long), 5(Long), 1(Long), 2(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 20(Integer), null, null, null, null, null, null, null, 23(Long), 5(Long), 1(Long), 2(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 21(Integer), null, null, null, null, null, null, null, 24(Long), 5(Long), 1(Long), 2(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 22(Integer), null, null, null, null, null, null, null, 25(Long), 5(Long), 1(Long), 2(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 23(Integer), null, null, null, null, null, null, null, 26(Long), 5(Long), 1(Long), 2(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 24(Integer), null, null, null, null, null, null, null, 27(Long), 5(Long), 1(Long), 2(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 25(Integer), null, null, null, null, null, null, null, 28(Long), 5(Long), 1(Long), 2(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 26(Integer), null, null, null, null, null, null, null, 29(Long), 5(Long), 1(Long), 2(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 27(Integer), null, null, null, null, null, null, null, 30(Long), 5(Long), 1(Long), 2(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 28(Integer), null, null, null, null, null, null, null, 31(Long), 5(Long), 1(Long), 2(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 29(Integer), null, null, null, null, null, null, null, 32(Long), 5(Long), 1(Long), 2(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 30(Integer), null, null, null, null, null, null, null, 33(Long), 5(Long), 1(Long), 2(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 31(Integer), null, null, null, null, null, null, null, 34(Long), 5(Long), 1(Long), 2(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 32(Integer), null, null, null, null, null, null, null, 35(Long), 5(Long), 1(Long), 2(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 33(Integer), null, null, null, null, null, null, null, 36(Long), 5(Long), 1(Long), 2(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 34(Integer), null, null, null, null, null, null, null, 37(Long), 5(Long), 1(Long), 2(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 35(Integer), null, null, null, null, null, null, null, 38(Long), 5(Long), 1(Long), 2(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 36(Integer), null, null, null, null, null, null, null, 39(Long), 5(Long), 1(Long), 2(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 37(Integer), null, null, null, null, null, null, null
2025-08-25 11:55:53.012 DEBUG 116336 --- [store-1] [] c.m.n.p.m.DbMetaColumnMapper.insertList : <0.1><R0TewMeM> <==    Updates: 37
2025-08-25 11:55:53.263 DEBUG 116336 --- [dark-task-1] [] c.m.n.p.m.D.selectJoinList              : <0.1><R0TewMeM> ==>  Preparing: SELECT t1.`oid`,t1.`name`,t.`catalog`,t.`schema` FROM dbmeta_schema t INNER JOIN dbmeta_object t1 ON (t1.`oid` = t.`oid`) WHERE (t.`source_id` = ? AND t1.`deleted` = ?)
2025-08-25 11:55:53.263 DEBUG 116336 --- [dark-task-1] [] c.m.n.p.m.D.selectJoinList              : <0.1><R0TewMeM> ==> Parameters: 5(Long), false(Boolean)
2025-08-25 11:55:53.267 DEBUG 116336 --- [dark-task-1] [] c.m.n.p.m.D.selectJoinList              : <0.1><R0TewMeM> <==      Total: 1
2025-08-25 11:55:53.284 DEBUG 116336 --- [dark-task-1] [] c.m.n.p.m.D.selectJoinList              : <0.1><R0TewMeM> ==>  Preparing: SELECT t1.`oid`,t1.`name`,t.`column_num` FROM dbmeta_table t INNER JOIN dbmeta_object t1 ON (t1.`oid` = t.`oid`) WHERE (t.`schema_id` = ? AND t1.`deleted` = ?)
2025-08-25 11:55:53.284 DEBUG 116336 --- [dark-task-1] [] c.m.n.p.m.D.selectJoinList              : <0.1><R0TewMeM> ==> Parameters: 1(Long), false(Boolean)
2025-08-25 11:55:53.288 DEBUG 116336 --- [dark-task-1] [] c.m.n.p.m.D.selectJoinList              : <0.1><R0TewMeM> <==      Total: 1
2025-08-25 11:55:53.307 DEBUG 116336 --- [dark-task-1] [] c.m.n.p.m.SampleTableMapper.selectList  : <0.1><R0TewMeM> ==>  Preparing: SELECT `oid`,`data_empty` FROM sample_table WHERE (`oid` IN (?))
2025-08-25 11:55:53.308 DEBUG 116336 --- [dark-task-1] [] c.m.n.p.m.SampleTableMapper.selectList  : <0.1><R0TewMeM> ==> Parameters: 2(Long)
2025-08-25 11:55:53.311 DEBUG 116336 --- [dark-task-1] [] c.m.n.p.m.SampleTableMapper.selectList  : <0.1><R0TewMeM> <==      Total: 0
2025-08-25 11:55:53.326 DEBUG 116336 --- [dark-task-1] [] c.m.n.p.m.D.selectJoinList              : <0.1><R0TewMeM> ==>  Preparing: SELECT t.`oid`,t.`table_id`,t.`data_type`,t.`type_group`,t.`length`,t1.`name` FROM dbmeta_column t INNER JOIN dbmeta_object t1 ON (t1.`oid` = t.`oid`) WHERE (t.`table_id` IN (?) AND t1.`deleted` = ?)
2025-08-25 11:55:53.327 DEBUG 116336 --- [dark-task-1] [] c.m.n.p.m.D.selectJoinList              : <0.1><R0TewMeM> ==> Parameters: 2(Long), false(Boolean)
2025-08-25 11:55:53.341 DEBUG 116336 --- [dark-task-1] [] c.m.n.p.m.D.selectJoinList              : <0.1><R0TewMeM> <==      Total: 37
2025-08-25 11:55:53.354 DEBUG 116336 --- [Samp-1] [] c.m.n.p.m.F.selectList                  : <0.1><R0TewMeM> ==>  Preparing: SELECT `id`,`config_id`,`uuid`,`compress_uuid`,`name`,`format`,`moved`,`temp_path`,`path` FROM file_upload_detail WHERE (`config_id` = ?)
2025-08-25 11:55:53.355 DEBUG 116336 --- [Samp-1] [] c.m.n.p.m.F.selectList                  : <0.1><R0TewMeM> ==> Parameters: 5(Long)
2025-08-25 11:55:53.359 DEBUG 116336 --- [Samp-1] [] c.m.n.p.m.F.selectList                  : <0.1><R0TewMeM> <==      Total: 1
2025-08-25 11:55:53.382 DEBUG 116336 --- [Thread-22] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 11:55:53.394 DEBUG 116336 --- [Thread-22] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 11:55:53.426 DEBUG 116336 --- [Thread-22] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 11:55:53.432 DEBUG 116336 --- [Thread-23] [] c.m.n.p.m.DiscoveryLogMapper.insertList : ==>  Preparing: INSERT INTO discovery_log (`job_id`,`percent`,`content`) VALUES (?,?,?) , (?,?,?) , (?,?,?)
2025-08-25 11:55:53.433 DEBUG 116336 --- [Thread-23] [] c.m.n.p.m.DiscoveryLogMapper.insertList : ==> Parameters: 4(Long), 50.00(BigDecimal), [{"msg":"元数据采集完成","status":"INFO","time":"2025-08-25 11:55:53"}](String), 4(Long), 50.00(BigDecimal), [{"msg":"抽样开始 default.A_DATA_100W","status":"INFO","time":"2025-08-25 11:55:53"}](String), 4(Long), 50.00(BigDecimal), [{"msg":"抽样完成 default.A_DATA_100W","status":"INFO","time":"2025-08-25 11:55:53"}](String)
2025-08-25 11:55:53.437 DEBUG 116336 --- [Samp-1] [] c.m.n.p.m.SampleColumnMapper.selectList : <0.1><R0TewMeM> ==>  Preparing: SELECT `oid`,`status` FROM sample_column WHERE (`oid` IN (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?))
2025-08-25 11:55:53.437 DEBUG 116336 --- [Thread-22] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 11:55:53.437 DEBUG 116336 --- [Samp-1] [] c.m.n.p.m.SampleColumnMapper.selectList : <0.1><R0TewMeM> ==> Parameters: 3(Long), 4(Long), 5(Long), 6(Long), 7(Long), 8(Long), 9(Long), 10(Long), 11(Long), 12(Long), 13(Long), 14(Long), 15(Long), 16(Long), 17(Long), 18(Long), 19(Long), 20(Long), 21(Long), 22(Long), 23(Long), 24(Long), 25(Long), 26(Long), 27(Long), 28(Long), 29(Long), 30(Long), 31(Long), 32(Long), 33(Long), 34(Long), 35(Long), 36(Long), 37(Long), 38(Long), 39(Long)
2025-08-25 11:55:53.441 DEBUG 116336 --- [Samp-1] [] c.m.n.p.m.SampleColumnMapper.selectList : <0.1><R0TewMeM> <==      Total: 0
2025-08-25 11:55:53.454 DEBUG 116336 --- [Samp-1] [] c.m.n.p.m.SampleColumnMapper.insertList : <0.1><R0TewMeM> ==>  Preparing: INSERT INTO sample_column (`oid`,`source_id`,`table_id`,`status`,`updated_at`,`sample_data`) VALUES (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?)
2025-08-25 11:55:53.464 DEBUG 116336 --- [Samp-1] [] c.m.n.p.m.SampleColumnMapper.insertList : <0.1><R0TewMeM> ==> Parameters: 3(Long), 5(Long), 2(Long), 2(Integer), 2025-08-25 11:55:53.352(Timestamp), ["490639","499190","466779","462592","462521","464823","464642","464687","461080","461914"](String), 4(Long), 5(Long), 2(Long), 2(Integer), 2025-08-25 11:55:53.352(Timestamp), ["务滢琪","旅达乔","尔致溢","弥圣","田淇毅","言奕强","李云富","京羚东","宰泽","卢乔娟"](String), 5(Long), 5(Long), 2(Long), 2(Integer), 2025-08-25 11:55:53.352(Timestamp), ["847123132313148320","540326198103195077","430423199002148170","372900195808225761","110222199309244587","652302191503113362","150722195907171337","142200196806054499","41042619410110626X","610727198102258511"](String), 6(Long), 5(Long), 2(Long), 2(Integer), 2025-08-25 11:55:53.352(Timestamp), ["14551642308","17022004665","15020467964","15519038384","18211191825","18072940653","17513889925","18240542220","17057409873","14503348639"](String), 7(Long), 5(Long), 2(Long), 2(Integer), 2025-08-25 11:55:53.352(Timestamp), ["5149391333199760","5142219094046587","5142212840860054","5143280245518694","5144928438230856","5148367778705869","5143289894808734","5142056582615406","5158409904144166","5158408873067341"](String), 8(Long), 5(Long), 2(Long), 2(Integer), 2025-08-25 11:55:53.352(Timestamp), ["<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"](String), 9(Long), 5(Long), 2(Long), 2(Integer), 2025-08-25 11:55:53.352(Timestamp), ["陕西省咸阳武功县西单大街728号","内蒙古鄂尔多斯市达拉特旗球场街道904号","西藏自治区昌都类乌齐县王府井大街484号","云南省文山苗族自治州马关县景山前街602号","吉林省白山浑江区复兴门大街592号","上海大兴安岭地区青浦区上海街道497号","宁夏中卫海原县车站街道377号","江西省新余市分宜县长安街949号","河北张家口蔚县宣武门大街515号","内蒙古自治区包头九原区一元街道149号"](String), 10(Long), 5(Long), 2(Long), 2(Integer), 2025-08-25 11:55:53.352(Timestamp), ["496036","936236","350036","747936","313436","453936","253136","253536","323236","08553"](String), 11(Long), 5(Long), 2(Long), 2(Integer), 2025-08-25 11:55:53.352(Timestamp), ["814","029","367","790","754","212","1030","0485","9410","1554"](String), 12(Long), 5(Long), 2(Long), 2(Integer), 2025-08-25 11:55:53.352(Timestamp), ["138.82.90.159","230.89.141.241","188.173.64.113","231.17.203.70","250.32.115.0","160.150.242.195","41.175.94.182","234.88.135.37","68.29.101.178","91.68.93.37"](String), 13(Long), 5(Long), 2(Long), 2(Integer), 2025-08-25 11:55:53.352(Timestamp), ["050568956","610658897","144316530","964684515","15200297X","85700684X","194779082","879892299","243410688","42937358X"](String), 14(Long), 5(Long), 2(Long), 2(Integer), 2025-08-25 11:55:53.352(Timestamp), ["贵州铜仁市美的集团佳兴酒厂","河南省三门峡大众公用事业（集团）有限公司内贸部","临沂市控股物资经销处","南充中色（天津）有色金属器材商行","大川门业集团电器经销部","天泽煤化工集团股份公司星业皮具厂","江苏南京市第二公共汽车公司专线","四川遂宁市人居置业纺织机械有限公司","德州市对外贸易中心（集团）冲孔筛网厂","新疆伊犁哈萨克自治州蓝色光标品牌管理顾问角梳加工厂"](String), 15(Long), 5(Long), 2(Long), 2(Integer), 2025-08-25 11:55:53.352(Timestamp), ["2016-12-18 09:35:55","1986-11-20 14:19:38","1968-08-26 10:47:54","1998-11-30 01:30:16","1994-09-19 21:10:20","1998-07-26 09:39:34","1959-11-23 08:28:01","1990-02-24 08:55:31","1956-08-25 11:38:20","1985-09-04 15:51:14"](String), 16(Long), 5(Long), 2(Long), 2(Integer), 2025-08-25 11:55:53.352(Timestamp), ["546822.22","68543.51","593817.93","055950.78","39184.33","209239.59","840991.54","286544.4","198929.5","167023.85"](String), 17(Long), 5(Long), 2(Long), 2(Integer), 2025-08-25 11:55:53.352(Timestamp), ["field_14","field_14","field_14","field_14","field_14","field_14","field_14","field_14","field_14","field_14"](String), 18(Long), 5(Long), 2(Long), 2(Integer), 2025-08-25 11:55:53.352(Timestamp), ["field_15","field_15","field_15","field_15","field_15","field_15","field_15","field_15","field_15","field_15"](String), 19(Long), 5(Long), 2(Long), 2(Integer), 2025-08-25 11:55:53.352(Timestamp), ["49949841737900595A5222","20760173552270252A5192","30131686913072589A5121","39510027552252525J4009","31116817713028395L4009","37961234241162495J3001","51195247613073358D1521","71422135833038120H1129","70182226945273089F3062","81000187345030350H2182"](String), 20(Long), 5(Long), 2(Long), 2(Integer), 2025-08-25 11:55:53.352(Timestamp), ["200553489332725196501260822","201915453621226196201081811","201511444360421199405225774","692174L75579694280998685334","200245457450100199207225515","201112458652524192012238623","200381459230832198008030911","201336504342601196704111435","200437358432224195007258271","200851453142423196009157810"](String), 21(Long), 5(Long), 2(Long), 2(Integer), 2025-08-25 11:55:53.352(Timestamp), ["147512731886036","241532701856553","246372621687498","142452402090217","143410183719132","230429014068926","230620112527998","243220224841234","248371522677716","230512127623601"](String), 22(Long), 5(Long), 2(Long), 2(Integer), 2025-08-25 11:55:53.352(Timestamp), ["924267395896220","822613493445638","846330946064581","818133631691267","882465184456199","162467735239004","829149205939954","162414511343484","018457697959963","072651214265114"](String), 23(Long), 5(Long), 2(Long), 2(Integer), 2025-08-25 11:55:53.352(Timestamp), ["X55436411489407739","Q554364130833010542","C554364129502349741","S55436416253774227","W554364124158973348","R55436410435831520","255436412756065443","G554364108016960550","E554364199922124354","G554364176000444950"](String), 24(Long), 5(Long), 2(Long), 2(Integer), 2025-08-25 11:55:53.352(Timestamp), ["Y1B1B370B","94U755V3","TQ9Q45K86","26540O7SQ","V71PA9P2S","55MPE6TI","230Z1N25","JX360R05","R2T03HRN","WN339DT5Y"](String), 25(Long), 5(Long), 2(Long), 2(Integer), 2025-08-25 11:55:53.352(Timestamp), ["450326758394269","441826568884937","33092219940819435000","220519109828506","422202357059224","622725452055993","361030329261759","510102433979832","532530914107058","51332719601213963X03"](String), 26(Long), 5(Long), 2(Long), 2(Integer), 2025-08-25 11:55:53.352(Timestamp), ["J7951574206874","L3777918608575","Z6814805311472","J5030668525172","Z6410608674774","Z4667575865675","J8540557701872","J1635897673872","Z7364349260875","Z4231689494975"](String), 27(Long), 5(Long), 2(Long), 2(Integer), 2025-08-25 11:55:53.352(Timestamp), ["北字第84390862号","南字第02792678号","空字第82570365号","广字第7223766号","装字第49957965号","广字第66155505号","沈字第9450770号","北字第8637380号","空字第2558110号","济字第67886147号"](String), 28(Long), 5(Long), 2(Long), 2(Integer), 2025-08-25 11:55:53.352(Timestamp), ["G53173304","Q15506544","S69984032","Q87060196","Q56525170","C34084972","G64247625","C42702162","S53593417","M76313940"](String), 29(Long), 5(Long), 2(Long), 2(Integer), 2025-08-25 11:55:53.352(Timestamp), ["D9727823839","D4936167037","D9513067837","D4573976634","D0609870132","D5647809532","D3343599334","D2578455939","D6986341432","D7281518234"](String), 30(Long), 5(Long), 2(Long), 2(Integer), 2025-08-25 11:55:53.352(Timestamp), ["XVD924782666426","FXD335161321119","YLD714577103061","FJN416121156219","CSY748104432322","EFF183664939287","EGQ676373885093","JBM098050814881","DTT955275931757","EQC104886541063"](String), 31(Long), 5(Long), 2(Long), 2(Integer), 2025-08-25 11:55:53.352(Timestamp), ["D32999914","D05946362","D37328284","D09022543","D47605828","D32850474","D77266272","D30424520","D94155628","D84209197"](String), 32(Long), 5(Long), 2(Long), 2(Integer), 2025-08-25 11:55:53.352(Timestamp), ["field_29","field_29","field_29","field_29","field_29","field_29","field_29","field_29","field_29","field_29"](String), 33(Long), 5(Long), 2(Long), 2(Integer), 2025-08-25 11:55:53.352(Timestamp), ["field_30","field_30","field_30","field_30","field_30","field_30","field_30","field_30","field_30","field_30"](String), 34(Long), 5(Long), 2(Long), 2(Integer), 2025-08-25 11:55:53.352(Timestamp), ["2222200","2222200","2222200","2222200","field_31","field_31","field_31","field_31","field_31","field_31"](String), 35(Long), 5(Long), 2(Long), 2(Integer), 2025-08-25 11:55:53.352(Timestamp), ["2024/12/18 10:00:00","2024/12/18 10:00:00","field_32","field_32","field_32","field_32","field_32","field_32","field_32","field_32"](String), 36(Long), 5(Long), 2(Long), 2(Integer), 2025-08-25 11:55:53.352(Timestamp), ["2024/12/18 10:00:00.000","field_33","field_33","field_33","field_33","field_33","field_33","field_33","field_33","field_33"](String), 37(Long), 5(Long), 2(Long), 2(Integer), 2025-08-25 11:55:53.352(Timestamp), ["field_34","field_34","field_34","field_34","field_34","field_34","field_34","field_34","field_34","field_34"](String), 38(Long), 5(Long), 2(Long), 2(Integer), 2025-08-25 11:55:53.352(Timestamp), ["陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单"](String), 39(Long), 5(Long), 2(Long), 2(Integer), 2025-08-25 11:55:53.352(Timestamp), ["陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号"](String)
2025-08-25 11:55:53.656 DEBUG 116336 --- [Thread-23] [] c.m.n.p.m.DiscoveryLogMapper.insertList : <==    Updates: 3
2025-08-25 11:55:53.810 DEBUG 116336 --- [Samp-1] [] c.m.n.p.m.SampleColumnMapper.insertList : <0.1><R0TewMeM> <==    Updates: 37
2025-08-25 11:55:53.870 DEBUG 116336 --- [Samp-1] [] c.m.n.p.m.SampleTableMapper.insertList  : <0.1><R0TewMeM> ==>  Preparing: INSERT INTO sample_table (`oid`,`source_id`,`schema_id`,`data_empty`) VALUES (?,?,?,?)
2025-08-25 11:55:53.871 DEBUG 116336 --- [Samp-1] [] c.m.n.p.m.SampleTableMapper.insertList  : <0.1><R0TewMeM> ==> Parameters: 2(Long), 5(Long), 1(Long), 2(Integer)
2025-08-25 11:55:53.928 DEBUG 116336 --- [Samp-1] [] c.m.n.p.m.SampleTableMapper.insertList  : <0.1><R0TewMeM> <==    Updates: 1
2025-08-25 11:55:53.931 DEBUG 116336 --- [dark-task-1] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : <0.1><R0TewMeM> [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 11:55:53.942 DEBUG 116336 --- [dark-task-1] [] c.m.nyx.pipeline.http.HttpRemoteInvoke  : <0.1><R0TewMeM> {"code":200,"success":true,"msg":"操作成功","tranceId":"R0TewMeM"}
2025-08-25 11:55:53.951 DEBUG 116336 --- [dark-task-1] [] c.m.n.p.m.DiscoveryJobMapper.updateById : <0.1><R0TewMeM> ==>  Preparing: UPDATE discovery_job SET `heartbeat`=? WHERE `id`=?
2025-08-25 11:55:53.952 DEBUG 116336 --- [dark-task-1] [] c.m.n.p.m.DiscoveryJobMapper.updateById : <0.1><R0TewMeM> ==> Parameters: {"startTime":1756094151406,"step":3,"num":0}(String), 4(Long)
2025-08-25 11:55:54.022 DEBUG 116336 --- [dark-task-1] [] c.m.n.p.m.DiscoveryJobMapper.updateById : <0.1><R0TewMeM> <==    Updates: 1
2025-08-25 11:55:54.023 DEBUG 116336 --- [dark-task-1] [] c.m.n.p.s.impl.TaskCallbackServiceImpl  : <0.1><R0TewMeM> 【心跳】TaskHeartbeatBeatReq(taskGroup=COLLECT_META, taskName=4, status=3)
2025-08-25 11:55:54.024 DEBUG 116336 --- [dark-task-1] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : <0.1><R0TewMeM> [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 11:55:54.042  INFO 116336 --- [dark-task-1] [] c.m.n.p.job.TaskSchedulingManager       : <0.1><R0TewMeM> 【计时器】J4(P2-S5) [2]: running time = 2秒625毫秒(2625227900)
---------------------------------------------------------
  %   Task name        detail           ns
---------------------------------------------------------
60%   stage1           1秒585毫秒(1)  1585204500
32%   stage2           849毫秒(1)  849322800

2025-08-25 12:29:26.975 DEBUG 116336 --- [http-nio-8888-exec-3] [] c.m.n.p.controller.TaskController       : <0.1><aeK3lbtJ> {"industryId":2,"planId":2,"jobId":5,"jobType":7,"source":{"streamDataTypes":[],"id":5,"configName":"文件","configType":130,"dataBaseType":"LOCAL_FILE","host":"","port":"","username":"","dbName":"","attachment":{"headline":"true","type":"0","compress":"false"},"advancedConfig":{},"properties":{}},"sampleLine":100,"sampleRate":100,"full":false,"saveStrategy":0}
2025-08-25 12:29:27.008  INFO 116336 --- [http-nio-8888-exec-3] [] c.m.n.p.job.TaskSchedulingManager       : <0.1><aeK3lbtJ> 【任务管理】全库
2025-08-25 12:29:27.020  INFO 116336 --- [dark-task-2] [] c.m.n.p.job.TaskSchedulingManager       : <0.1><aeK3lbtJ> 【作业执行】J5(P2-S5) 文件
2025-08-25 12:29:27.023 DEBUG 116336 --- [dark-task-2] [] c.m.n.p.s.impl.TaskCallbackServiceImpl  : <0.1><aeK3lbtJ> 【心跳】TaskHeartbeatBeatReq(taskGroup=COLLECT_META, taskName=5, status=1)
2025-08-25 12:29:27.044 DEBUG 116336 --- [dark-task-2] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : <0.1><aeK3lbtJ> [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 12:29:27.312 DEBUG 116336 --- [dark-task-2] [] c.m.n.p.m.DiscoveryJobMapper.selectList : <0.1><aeK3lbtJ> ==>  Preparing: SELECT `heartbeat` FROM discovery_job WHERE (`id` = ?)
2025-08-25 12:29:27.319 DEBUG 116336 --- [dark-task-2] [] c.m.n.p.m.DiscoveryJobMapper.selectList : <0.1><aeK3lbtJ> ==> Parameters: 5(Long)
2025-08-25 12:29:27.327 DEBUG 116336 --- [dark-task-2] [] c.m.n.p.m.DiscoveryJobMapper.selectList : <0.1><aeK3lbtJ> <==      Total: 1
2025-08-25 12:29:27.333  INFO 116336 --- [dark-task-2] [] c.m.n.p.job.collect.CollectMetaJob      : <0.1><aeK3lbtJ> 【分类分级】采集元数据
2025-08-25 12:29:27.348 DEBUG 116336 --- [dark-task-2] [] c.m.n.p.m.F.selectList                  : <0.1><aeK3lbtJ> ==>  Preparing: SELECT `id`,`config_id`,`uuid`,`compress_uuid`,`name`,`format`,`moved`,`temp_path`,`path` FROM file_upload_detail WHERE (`config_id` = ?)
2025-08-25 12:29:27.349 DEBUG 116336 --- [dark-task-2] [] c.m.n.p.m.F.selectList                  : <0.1><aeK3lbtJ> ==> Parameters: 5(Long)
2025-08-25 12:29:27.353 DEBUG 116336 --- [dark-task-2] [] c.m.n.p.m.F.selectList                  : <0.1><aeK3lbtJ> <==      Total: 1
2025-08-25 12:29:27.366 DEBUG 116336 --- [meta-5-1] [] c.m.n.p.m.DbMetaObjectMapper.selectList : <0.1><aeK3lbtJ> ==>  Preparing: SELECT `oid`,`name`,`deleted`,`revision`,`hash_value` FROM dbmeta_object WHERE (`source_id` = ? AND `pid` = ? AND `type` = ?)
2025-08-25 12:29:27.367 DEBUG 116336 --- [meta-5-1] [] c.m.n.p.m.DbMetaObjectMapper.selectList : <0.1><aeK3lbtJ> ==> Parameters: 5(Long), 0(Long), SCHEMA(String)
2025-08-25 12:29:27.373 DEBUG 116336 --- [meta-5-1] [] c.m.n.p.m.DbMetaObjectMapper.selectList : <0.1><aeK3lbtJ> <==      Total: 1
2025-08-25 12:29:27.403 DEBUG 116336 --- [meta-5-1] [] c.m.n.p.m.DbMetaObjectMapper.selectList : <0.1><aeK3lbtJ> ==>  Preparing: SELECT `oid`,`name`,`deleted`,`revision`,`hash_value` FROM dbmeta_object WHERE (`source_id` = ? AND `pid` = ? AND `type` = ? AND `name` = ?)
2025-08-25 12:29:27.403 DEBUG 116336 --- [meta-5-1] [] c.m.n.p.m.DbMetaObjectMapper.selectList : <0.1><aeK3lbtJ> ==> Parameters: 5(Long), 0(Long), SCHEMA(String), default(String)
2025-08-25 12:29:27.405 DEBUG 116336 --- [meta-5-1] [] c.m.n.p.m.DbMetaObjectMapper.selectList : <0.1><aeK3lbtJ> <==      Total: 1
2025-08-25 12:29:27.409 DEBUG 116336 --- [meta-5-1] [] c.m.n.p.m.DbMetaObjectMapper.selectList : <0.1><aeK3lbtJ> ==>  Preparing: SELECT `oid`,`name`,`type`,`deleted`,`revision`,`hash_value` FROM dbmeta_object WHERE (`source_id` = ? AND `pid` = ? AND `type` = ?)
2025-08-25 12:29:27.410 DEBUG 116336 --- [meta-5-1] [] c.m.n.p.m.DbMetaObjectMapper.selectList : <0.1><aeK3lbtJ> ==> Parameters: 5(Long), 1(Long), TABLE(String)
2025-08-25 12:29:27.428 DEBUG 116336 --- [meta-5-1] [] c.m.n.p.m.DbMetaObjectMapper.selectList : <0.1><aeK3lbtJ> <==      Total: 1
2025-08-25 12:29:27.696 DEBUG 116336 --- [dark-task-2] [] c.m.n.p.m.D.selectJoinList              : <0.1><aeK3lbtJ> ==>  Preparing: SELECT t1.`oid`,t1.`name`,t.`catalog`,t.`schema` FROM dbmeta_schema t INNER JOIN dbmeta_object t1 ON (t1.`oid` = t.`oid`) WHERE (t.`source_id` = ? AND t1.`deleted` = ?)
2025-08-25 12:29:27.698 DEBUG 116336 --- [dark-task-2] [] c.m.n.p.m.D.selectJoinList              : <0.1><aeK3lbtJ> ==> Parameters: 5(Long), false(Boolean)
2025-08-25 12:29:27.702 DEBUG 116336 --- [dark-task-2] [] c.m.n.p.m.D.selectJoinList              : <0.1><aeK3lbtJ> <==      Total: 1
2025-08-25 12:29:27.709 DEBUG 116336 --- [Thread-22] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 12:29:27.710 DEBUG 116336 --- [dark-task-2] [] c.m.n.p.m.D.selectJoinList              : <0.1><aeK3lbtJ> ==>  Preparing: SELECT t1.`oid`,t1.`name`,t.`column_num` FROM dbmeta_table t INNER JOIN dbmeta_object t1 ON (t1.`oid` = t.`oid`) WHERE (t.`schema_id` = ? AND t1.`deleted` = ?)
2025-08-25 12:29:27.712 DEBUG 116336 --- [dark-task-2] [] c.m.n.p.m.D.selectJoinList              : <0.1><aeK3lbtJ> ==> Parameters: 1(Long), false(Boolean)
2025-08-25 12:29:27.716 DEBUG 116336 --- [dark-task-2] [] c.m.n.p.m.D.selectJoinList              : <0.1><aeK3lbtJ> <==      Total: 1
2025-08-25 12:29:27.723 DEBUG 116336 --- [dark-task-2] [] c.m.n.p.m.SampleTableMapper.selectList  : <0.1><aeK3lbtJ> ==>  Preparing: SELECT `oid`,`data_empty` FROM sample_table WHERE (`oid` IN (?))
2025-08-25 12:29:27.725 DEBUG 116336 --- [dark-task-2] [] c.m.n.p.m.SampleTableMapper.selectList  : <0.1><aeK3lbtJ> ==> Parameters: 2(Long)
2025-08-25 12:29:27.729 DEBUG 116336 --- [dark-task-2] [] c.m.n.p.m.SampleTableMapper.selectList  : <0.1><aeK3lbtJ> <==      Total: 1
2025-08-25 12:29:27.732 DEBUG 116336 --- [dark-task-2] [] c.m.n.p.m.D.selectJoinList              : <0.1><aeK3lbtJ> ==>  Preparing: SELECT t.`oid`,t.`table_id`,t.`data_type`,t.`type_group`,t.`length`,t1.`name` FROM dbmeta_column t INNER JOIN dbmeta_object t1 ON (t1.`oid` = t.`oid`) WHERE (t.`table_id` IN (?) AND t1.`deleted` = ?)
2025-08-25 12:29:27.732 DEBUG 116336 --- [dark-task-2] [] c.m.n.p.m.D.selectJoinList              : <0.1><aeK3lbtJ> ==> Parameters: 2(Long), false(Boolean)
2025-08-25 12:29:27.742 DEBUG 116336 --- [dark-task-2] [] c.m.n.p.m.D.selectJoinList              : <0.1><aeK3lbtJ> <==      Total: 37
2025-08-25 12:29:27.743 DEBUG 116336 --- [Thread-22] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 12:29:27.758 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.F.selectList                  : <0.1><aeK3lbtJ> ==>  Preparing: SELECT `id`,`config_id`,`uuid`,`compress_uuid`,`name`,`format`,`moved`,`temp_path`,`path` FROM file_upload_detail WHERE (`config_id` = ?)
2025-08-25 12:29:27.758 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.F.selectList                  : <0.1><aeK3lbtJ> ==> Parameters: 5(Long)
2025-08-25 12:29:27.761 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.F.selectList                  : <0.1><aeK3lbtJ> <==      Total: 1
2025-08-25 12:29:27.761 DEBUG 116336 --- [Thread-22] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 12:29:27.764 DEBUG 116336 --- [Thread-23] [] c.m.n.p.m.DiscoveryLogMapper.insertList : ==>  Preparing: INSERT INTO discovery_log (`job_id`,`percent`,`content`) VALUES (?,?,?) , (?,?,?) , (?,?,?)
2025-08-25 12:29:27.769 DEBUG 116336 --- [Thread-23] [] c.m.n.p.m.DiscoveryLogMapper.insertList : ==> Parameters: 5(Long), 0.00(BigDecimal), [{"msg":"开始采集元数据信息","status":"INFO","time":"2025-08-25 12:29:27"}](String), 5(Long), 0.00(BigDecimal), [{"msg":"采集 default","status":"INFO","time":"2025-08-25 12:29:27"}](String), 5(Long), 50.00(BigDecimal), [{"msg":"元数据采集完成","status":"INFO","time":"2025-08-25 12:29:27"}](String)
2025-08-25 12:29:27.769 DEBUG 116336 --- [Thread-22] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 12:29:27.797 DEBUG 116336 --- [Thread-23] [] c.m.n.p.m.DiscoveryLogMapper.insertList : <==    Updates: 3
2025-08-25 12:29:27.802 DEBUG 116336 --- [Thread-23] [] c.m.n.p.m.DiscoveryLogMapper.insertList : ==>  Preparing: INSERT INTO discovery_log (`job_id`,`percent`,`content`) VALUES (?,?,?)
2025-08-25 12:29:27.802 DEBUG 116336 --- [Thread-23] [] c.m.n.p.m.DiscoveryLogMapper.insertList : ==> Parameters: 5(Long), 50.00(BigDecimal), [{"msg":"抽样开始 default.A_DATA_100W","status":"INFO","time":"2025-08-25 12:29:27"}](String)
2025-08-25 12:29:27.813 DEBUG 116336 --- [Thread-22] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 12:29:27.826 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.selectList : <0.1><aeK3lbtJ> ==>  Preparing: SELECT `oid`,`status` FROM sample_column WHERE (`oid` IN (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?))
2025-08-25 12:29:27.826 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.selectList : <0.1><aeK3lbtJ> ==> Parameters: 3(Long), 4(Long), 5(Long), 6(Long), 7(Long), 8(Long), 9(Long), 10(Long), 11(Long), 12(Long), 13(Long), 14(Long), 15(Long), 16(Long), 17(Long), 18(Long), 19(Long), 20(Long), 21(Long), 22(Long), 23(Long), 24(Long), 25(Long), 26(Long), 27(Long), 28(Long), 29(Long), 30(Long), 31(Long), 32(Long), 33(Long), 34(Long), 35(Long), 36(Long), 37(Long), 38(Long), 39(Long)
2025-08-25 12:29:27.828 DEBUG 116336 --- [Thread-22] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 12:29:27.848 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.selectList : <0.1><aeK3lbtJ> <==      Total: 37
2025-08-25 12:29:27.859 DEBUG 116336 --- [Thread-23] [] c.m.n.p.m.DiscoveryLogMapper.insertList : <==    Updates: 1
2025-08-25 12:29:27.861 DEBUG 116336 --- [Thread-23] [] c.m.n.p.m.DiscoveryLogMapper.insertList : ==>  Preparing: INSERT INTO discovery_log (`job_id`,`percent`,`content`) VALUES (?,?,?)
2025-08-25 12:29:27.862 DEBUG 116336 --- [Thread-23] [] c.m.n.p.m.DiscoveryLogMapper.insertList : ==> Parameters: 5(Long), 50.00(BigDecimal), [{"msg":"抽样完成 default.A_DATA_100W","status":"INFO","time":"2025-08-25 12:29:27"}](String)
2025-08-25 12:29:27.879 DEBUG 116336 --- [Thread-23] [] c.m.n.p.m.DiscoveryLogMapper.insertList : <==    Updates: 1
2025-08-25 12:29:27.890 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><aeK3lbtJ> ==>  Preparing: UPDATE sample_column SET `status`=?, `updated_at`=?, `sample_data`=? WHERE `oid`=?
2025-08-25 12:29:27.903 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><aeK3lbtJ> ==> Parameters: 2(Integer), 2025-08-25 12:29:27.754(Timestamp), ["490639","499190","466779","462592","462521","464823","464642","464687","461080","461914"](String), 3(Long)
2025-08-25 12:29:27.904 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><aeK3lbtJ> ==> Parameters: 2(Integer), 2025-08-25 12:29:27.754(Timestamp), ["务滢琪","旅达乔","尔致溢","弥圣","田淇毅","言奕强","李云富","京羚东","宰泽","卢乔娟"](String), 4(Long)
2025-08-25 12:29:27.907 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><aeK3lbtJ> ==> Parameters: 2(Integer), 2025-08-25 12:29:27.754(Timestamp), ["847123132313148320","540326198103195077","430423199002148170","372900195808225761","110222199309244587","652302191503113362","150722195907171337","142200196806054499","41042619410110626X","610727198102258511"](String), 5(Long)
2025-08-25 12:29:27.909 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><aeK3lbtJ> ==> Parameters: 2(Integer), 2025-08-25 12:29:27.754(Timestamp), ["14551642308","17022004665","15020467964","15519038384","18211191825","18072940653","17513889925","18240542220","17057409873","14503348639"](String), 6(Long)
2025-08-25 12:29:27.911 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><aeK3lbtJ> ==> Parameters: 2(Integer), 2025-08-25 12:29:27.754(Timestamp), ["5149391333199760","5142219094046587","5142212840860054","5143280245518694","5144928438230856","5148367778705869","5143289894808734","5142056582615406","5158409904144166","5158408873067341"](String), 7(Long)
2025-08-25 12:29:27.911 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><aeK3lbtJ> ==> Parameters: 2(Integer), 2025-08-25 12:29:27.754(Timestamp), ["<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"](String), 8(Long)
2025-08-25 12:29:27.913 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><aeK3lbtJ> ==> Parameters: 2(Integer), 2025-08-25 12:29:27.754(Timestamp), ["陕西省咸阳武功县西单大街728号","内蒙古鄂尔多斯市达拉特旗球场街道904号","西藏自治区昌都类乌齐县王府井大街484号","云南省文山苗族自治州马关县景山前街602号","吉林省白山浑江区复兴门大街592号","上海大兴安岭地区青浦区上海街道497号","宁夏中卫海原县车站街道377号","江西省新余市分宜县长安街949号","河北张家口蔚县宣武门大街515号","内蒙古自治区包头九原区一元街道149号"](String), 9(Long)
2025-08-25 12:29:27.917 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><aeK3lbtJ> ==> Parameters: 2(Integer), 2025-08-25 12:29:27.754(Timestamp), ["496036","936236","350036","747936","313436","453936","253136","253536","323236","08553"](String), 10(Long)
2025-08-25 12:29:27.918 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><aeK3lbtJ> ==> Parameters: 2(Integer), 2025-08-25 12:29:27.754(Timestamp), ["814","029","367","790","754","212","1030","0485","9410","1554"](String), 11(Long)
2025-08-25 12:29:27.918 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><aeK3lbtJ> ==> Parameters: 2(Integer), 2025-08-25 12:29:27.754(Timestamp), ["138.82.90.159","230.89.141.241","188.173.64.113","231.17.203.70","250.32.115.0","160.150.242.195","41.175.94.182","234.88.135.37","68.29.101.178","91.68.93.37"](String), 12(Long)
2025-08-25 12:29:27.919 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><aeK3lbtJ> ==> Parameters: 2(Integer), 2025-08-25 12:29:27.754(Timestamp), ["050568956","610658897","144316530","964684515","15200297X","85700684X","194779082","879892299","243410688","42937358X"](String), 13(Long)
2025-08-25 12:29:27.920 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><aeK3lbtJ> ==> Parameters: 2(Integer), 2025-08-25 12:29:27.754(Timestamp), ["贵州铜仁市美的集团佳兴酒厂","河南省三门峡大众公用事业（集团）有限公司内贸部","临沂市控股物资经销处","南充中色（天津）有色金属器材商行","大川门业集团电器经销部","天泽煤化工集团股份公司星业皮具厂","江苏南京市第二公共汽车公司专线","四川遂宁市人居置业纺织机械有限公司","德州市对外贸易中心（集团）冲孔筛网厂","新疆伊犁哈萨克自治州蓝色光标品牌管理顾问角梳加工厂"](String), 14(Long)
2025-08-25 12:29:27.923 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><aeK3lbtJ> ==> Parameters: 2(Integer), 2025-08-25 12:29:27.754(Timestamp), ["2016-12-18 09:35:55","1986-11-20 14:19:38","1968-08-26 10:47:54","1998-11-30 01:30:16","1994-09-19 21:10:20","1998-07-26 09:39:34","1959-11-23 08:28:01","1990-02-24 08:55:31","1956-08-25 11:38:20","1985-09-04 15:51:14"](String), 15(Long)
2025-08-25 12:29:27.925 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><aeK3lbtJ> ==> Parameters: 2(Integer), 2025-08-25 12:29:27.754(Timestamp), ["546822.22","68543.51","593817.93","055950.78","39184.33","209239.59","840991.54","286544.4","198929.5","167023.85"](String), 16(Long)
2025-08-25 12:29:27.926 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><aeK3lbtJ> ==> Parameters: 2(Integer), 2025-08-25 12:29:27.754(Timestamp), ["field_14","field_14","field_14","field_14","field_14","field_14","field_14","field_14","field_14","field_14"](String), 17(Long)
2025-08-25 12:29:27.927 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><aeK3lbtJ> ==> Parameters: 2(Integer), 2025-08-25 12:29:27.754(Timestamp), ["field_15","field_15","field_15","field_15","field_15","field_15","field_15","field_15","field_15","field_15"](String), 18(Long)
2025-08-25 12:29:27.930 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><aeK3lbtJ> ==> Parameters: 2(Integer), 2025-08-25 12:29:27.754(Timestamp), ["49949841737900595A5222","20760173552270252A5192","30131686913072589A5121","39510027552252525J4009","31116817713028395L4009","37961234241162495J3001","51195247613073358D1521","71422135833038120H1129","70182226945273089F3062","81000187345030350H2182"](String), 19(Long)
2025-08-25 12:29:27.931 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><aeK3lbtJ> ==> Parameters: 2(Integer), 2025-08-25 12:29:27.754(Timestamp), ["200553489332725196501260822","201915453621226196201081811","201511444360421199405225774","692174L75579694280998685334","200245457450100199207225515","201112458652524192012238623","200381459230832198008030911","201336504342601196704111435","200437358432224195007258271","200851453142423196009157810"](String), 20(Long)
2025-08-25 12:29:27.933 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><aeK3lbtJ> ==> Parameters: 2(Integer), 2025-08-25 12:29:27.754(Timestamp), ["147512731886036","241532701856553","246372621687498","142452402090217","143410183719132","230429014068926","230620112527998","243220224841234","248371522677716","230512127623601"](String), 21(Long)
2025-08-25 12:29:27.933 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><aeK3lbtJ> ==> Parameters: 2(Integer), 2025-08-25 12:29:27.754(Timestamp), ["924267395896220","822613493445638","846330946064581","818133631691267","882465184456199","162467735239004","829149205939954","162414511343484","018457697959963","072651214265114"](String), 22(Long)
2025-08-25 12:29:27.934 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><aeK3lbtJ> ==> Parameters: 2(Integer), 2025-08-25 12:29:27.754(Timestamp), ["X55436411489407739","Q554364130833010542","C554364129502349741","S55436416253774227","W554364124158973348","R55436410435831520","255436412756065443","G554364108016960550","E554364199922124354","G554364176000444950"](String), 23(Long)
2025-08-25 12:29:27.936 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><aeK3lbtJ> ==> Parameters: 2(Integer), 2025-08-25 12:29:27.754(Timestamp), ["Y1B1B370B","94U755V3","TQ9Q45K86","26540O7SQ","V71PA9P2S","55MPE6TI","230Z1N25","JX360R05","R2T03HRN","WN339DT5Y"](String), 24(Long)
2025-08-25 12:29:27.936 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><aeK3lbtJ> ==> Parameters: 2(Integer), 2025-08-25 12:29:27.754(Timestamp), ["450326758394269","441826568884937","33092219940819435000","220519109828506","422202357059224","622725452055993","361030329261759","510102433979832","532530914107058","51332719601213963X03"](String), 25(Long)
2025-08-25 12:29:27.939 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><aeK3lbtJ> ==> Parameters: 2(Integer), 2025-08-25 12:29:27.754(Timestamp), ["J7951574206874","L3777918608575","Z6814805311472","J5030668525172","Z6410608674774","Z4667575865675","J8540557701872","J1635897673872","Z7364349260875","Z4231689494975"](String), 26(Long)
2025-08-25 12:29:27.940 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><aeK3lbtJ> ==> Parameters: 2(Integer), 2025-08-25 12:29:27.754(Timestamp), ["北字第84390862号","南字第02792678号","空字第82570365号","广字第7223766号","装字第49957965号","广字第66155505号","沈字第9450770号","北字第8637380号","空字第2558110号","济字第67886147号"](String), 27(Long)
2025-08-25 12:29:27.945 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><aeK3lbtJ> ==> Parameters: 2(Integer), 2025-08-25 12:29:27.754(Timestamp), ["G53173304","Q15506544","S69984032","Q87060196","Q56525170","C34084972","G64247625","C42702162","S53593417","M76313940"](String), 28(Long)
2025-08-25 12:29:27.946 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><aeK3lbtJ> ==> Parameters: 2(Integer), 2025-08-25 12:29:27.754(Timestamp), ["D9727823839","D4936167037","D9513067837","D4573976634","D0609870132","D5647809532","D3343599334","D2578455939","D6986341432","D7281518234"](String), 29(Long)
2025-08-25 12:29:27.947 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><aeK3lbtJ> ==> Parameters: 2(Integer), 2025-08-25 12:29:27.754(Timestamp), ["XVD924782666426","FXD335161321119","YLD714577103061","FJN416121156219","CSY748104432322","EFF183664939287","EGQ676373885093","JBM098050814881","DTT955275931757","EQC104886541063"](String), 30(Long)
2025-08-25 12:29:27.948 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><aeK3lbtJ> ==> Parameters: 2(Integer), 2025-08-25 12:29:27.754(Timestamp), ["D32999914","D05946362","D37328284","D09022543","D47605828","D32850474","D77266272","D30424520","D94155628","D84209197"](String), 31(Long)
2025-08-25 12:29:27.948 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><aeK3lbtJ> ==> Parameters: 2(Integer), 2025-08-25 12:29:27.754(Timestamp), ["field_29","field_29","field_29","field_29","field_29","field_29","field_29","field_29","field_29","field_29"](String), 32(Long)
2025-08-25 12:29:27.950 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><aeK3lbtJ> ==> Parameters: 2(Integer), 2025-08-25 12:29:27.754(Timestamp), ["field_30","field_30","field_30","field_30","field_30","field_30","field_30","field_30","field_30","field_30"](String), 33(Long)
2025-08-25 12:29:27.951 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><aeK3lbtJ> ==> Parameters: 2(Integer), 2025-08-25 12:29:27.754(Timestamp), ["2222200","2222200","2222200","2222200","field_31","field_31","field_31","field_31","field_31","field_31"](String), 34(Long)
2025-08-25 12:29:27.955 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><aeK3lbtJ> ==> Parameters: 2(Integer), 2025-08-25 12:29:27.754(Timestamp), ["2024/12/18 10:00:00","2024/12/18 10:00:00","field_32","field_32","field_32","field_32","field_32","field_32","field_32","field_32"](String), 35(Long)
2025-08-25 12:29:27.963 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><aeK3lbtJ> ==> Parameters: 2(Integer), 2025-08-25 12:29:27.754(Timestamp), ["2024/12/18 10:00:00.000","field_33","field_33","field_33","field_33","field_33","field_33","field_33","field_33","field_33"](String), 36(Long)
2025-08-25 12:29:27.965 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><aeK3lbtJ> ==> Parameters: 2(Integer), 2025-08-25 12:29:27.754(Timestamp), ["field_34","field_34","field_34","field_34","field_34","field_34","field_34","field_34","field_34","field_34"](String), 37(Long)
2025-08-25 12:29:27.966 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><aeK3lbtJ> ==> Parameters: 2(Integer), 2025-08-25 12:29:27.754(Timestamp), ["陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单"](String), 38(Long)
2025-08-25 12:29:27.973 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><aeK3lbtJ> ==> Parameters: 2(Integer), 2025-08-25 12:29:27.754(Timestamp), ["陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号"](String), 39(Long)
2025-08-25 12:29:28.151 DEBUG 116336 --- [dark-task-2] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : <0.1><aeK3lbtJ> [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 12:29:28.214 DEBUG 116336 --- [dark-task-2] [] c.m.nyx.pipeline.http.HttpRemoteInvoke  : <0.1><aeK3lbtJ> {"code":200,"success":true,"msg":"操作成功","tranceId":"aeK3lbtJ"}
2025-08-25 12:29:28.219 DEBUG 116336 --- [dark-task-2] [] c.m.n.p.m.DiscoveryJobMapper.updateById : <0.1><aeK3lbtJ> ==>  Preparing: UPDATE discovery_job SET `heartbeat`=? WHERE `id`=?
2025-08-25 12:29:28.221 DEBUG 116336 --- [dark-task-2] [] c.m.n.p.m.DiscoveryJobMapper.updateById : <0.1><aeK3lbtJ> ==> Parameters: {"startTime":1756096167330,"step":3,"num":0}(String), 5(Long)
2025-08-25 12:29:28.245 DEBUG 116336 --- [dark-task-2] [] c.m.n.p.m.DiscoveryJobMapper.updateById : <0.1><aeK3lbtJ> <==    Updates: 1
2025-08-25 12:29:28.245 DEBUG 116336 --- [dark-task-2] [] c.m.n.p.s.impl.TaskCallbackServiceImpl  : <0.1><aeK3lbtJ> 【心跳】TaskHeartbeatBeatReq(taskGroup=COLLECT_META, taskName=5, status=3)
2025-08-25 12:29:28.246 DEBUG 116336 --- [dark-task-2] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : <0.1><aeK3lbtJ> [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 12:29:28.256  INFO 116336 --- [dark-task-2] [] c.m.n.p.job.TaskSchedulingManager       : <0.1><aeK3lbtJ> 【计时器】J5(P2-S5) [2]: running time = 925毫秒(925750500)
---------------------------------------------------------
  %   Task name        detail           ns
---------------------------------------------------------
51%   stage2           475毫秒(1)  475223200
36%   stage1           333毫秒(1)  333666300

2025-08-25 12:37:10.285 DEBUG 116336 --- [http-nio-8888-exec-5] [] c.m.n.p.controller.TaskController       : <0.1><H7RGGW4Y> {"industryId":2,"planId":2,"jobId":6,"jobType":7,"source":{"streamDataTypes":[],"id":5,"configName":"文件","configType":130,"dataBaseType":"LOCAL_FILE","host":"","port":"","username":"","dbName":"","attachment":{"headline":"true","type":"0","compress":"false"},"advancedConfig":{},"properties":{}},"sampleLine":100,"sampleRate":100,"full":false,"saveStrategy":0}
2025-08-25 12:37:10.288  INFO 116336 --- [http-nio-8888-exec-5] [] c.m.n.p.job.TaskSchedulingManager       : <0.1><H7RGGW4Y> 【任务管理】全库
2025-08-25 12:37:10.290  INFO 116336 --- [dark-task-3] [] c.m.n.p.job.TaskSchedulingManager       : <0.1><H7RGGW4Y> 【作业执行】J6(P2-S5) 文件
2025-08-25 12:37:10.290 DEBUG 116336 --- [dark-task-3] [] c.m.n.p.s.impl.TaskCallbackServiceImpl  : <0.1><H7RGGW4Y> 【心跳】TaskHeartbeatBeatReq(taskGroup=COLLECT_META, taskName=6, status=1)
2025-08-25 12:37:10.294 DEBUG 116336 --- [dark-task-3] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : <0.1><H7RGGW4Y> [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 12:37:10.483 DEBUG 116336 --- [dark-task-3] [] c.m.n.p.m.DiscoveryJobMapper.selectList : <0.1><H7RGGW4Y> ==>  Preparing: SELECT `heartbeat` FROM discovery_job WHERE (`id` = ?)
2025-08-25 12:37:10.484 DEBUG 116336 --- [dark-task-3] [] c.m.n.p.m.DiscoveryJobMapper.selectList : <0.1><H7RGGW4Y> ==> Parameters: 6(Long)
2025-08-25 12:37:10.489 DEBUG 116336 --- [dark-task-3] [] c.m.n.p.m.DiscoveryJobMapper.selectList : <0.1><H7RGGW4Y> <==      Total: 1
2025-08-25 12:37:10.490  INFO 116336 --- [dark-task-3] [] c.m.n.p.job.collect.CollectMetaJob      : <0.1><H7RGGW4Y> 【分类分级】采集元数据
2025-08-25 12:37:10.495 DEBUG 116336 --- [dark-task-3] [] c.m.n.p.m.F.selectList                  : <0.1><H7RGGW4Y> ==>  Preparing: SELECT `id`,`config_id`,`uuid`,`compress_uuid`,`name`,`format`,`moved`,`temp_path`,`path` FROM file_upload_detail WHERE (`config_id` = ?)
2025-08-25 12:37:10.497 DEBUG 116336 --- [dark-task-3] [] c.m.n.p.m.F.selectList                  : <0.1><H7RGGW4Y> ==> Parameters: 5(Long)
2025-08-25 12:37:10.500 DEBUG 116336 --- [dark-task-3] [] c.m.n.p.m.F.selectList                  : <0.1><H7RGGW4Y> <==      Total: 1
2025-08-25 12:37:10.504 DEBUG 116336 --- [meta-5-1] [] c.m.n.p.m.DbMetaObjectMapper.selectList : <0.1><H7RGGW4Y> ==>  Preparing: SELECT `oid`,`name`,`deleted`,`revision`,`hash_value` FROM dbmeta_object WHERE (`source_id` = ? AND `pid` = ? AND `type` = ?)
2025-08-25 12:37:10.505 DEBUG 116336 --- [meta-5-1] [] c.m.n.p.m.DbMetaObjectMapper.selectList : <0.1><H7RGGW4Y> ==> Parameters: 5(Long), 0(Long), SCHEMA(String)
2025-08-25 12:37:10.510 DEBUG 116336 --- [meta-5-1] [] c.m.n.p.m.DbMetaObjectMapper.selectList : <0.1><H7RGGW4Y> <==      Total: 1
2025-08-25 12:37:10.529 DEBUG 116336 --- [meta-5-1] [] c.m.n.p.m.DbMetaObjectMapper.selectList : <0.1><H7RGGW4Y> ==>  Preparing: SELECT `oid`,`name`,`deleted`,`revision`,`hash_value` FROM dbmeta_object WHERE (`source_id` = ? AND `pid` = ? AND `type` = ? AND `name` = ?)
2025-08-25 12:37:10.529 DEBUG 116336 --- [meta-5-1] [] c.m.n.p.m.DbMetaObjectMapper.selectList : <0.1><H7RGGW4Y> ==> Parameters: 5(Long), 0(Long), SCHEMA(String), default(String)
2025-08-25 12:37:10.531 DEBUG 116336 --- [meta-5-1] [] c.m.n.p.m.DbMetaObjectMapper.selectList : <0.1><H7RGGW4Y> <==      Total: 1
2025-08-25 12:37:10.533 DEBUG 116336 --- [meta-5-1] [] c.m.n.p.m.DbMetaObjectMapper.selectList : <0.1><H7RGGW4Y> ==>  Preparing: SELECT `oid`,`name`,`type`,`deleted`,`revision`,`hash_value` FROM dbmeta_object WHERE (`source_id` = ? AND `pid` = ? AND `type` = ?)
2025-08-25 12:37:10.534 DEBUG 116336 --- [meta-5-1] [] c.m.n.p.m.DbMetaObjectMapper.selectList : <0.1><H7RGGW4Y> ==> Parameters: 5(Long), 1(Long), TABLE(String)
2025-08-25 12:37:10.537 DEBUG 116336 --- [meta-5-1] [] c.m.n.p.m.DbMetaObjectMapper.selectList : <0.1><H7RGGW4Y> <==      Total: 1
2025-08-25 12:37:10.565 DEBUG 116336 --- [dark-task-3] [] c.m.n.p.m.D.selectJoinList              : <0.1><H7RGGW4Y> ==>  Preparing: SELECT t1.`oid`,t1.`name`,t.`catalog`,t.`schema` FROM dbmeta_schema t INNER JOIN dbmeta_object t1 ON (t1.`oid` = t.`oid`) WHERE (t.`source_id` = ? AND t1.`deleted` = ?)
2025-08-25 12:37:10.566 DEBUG 116336 --- [dark-task-3] [] c.m.n.p.m.D.selectJoinList              : <0.1><H7RGGW4Y> ==> Parameters: 5(Long), false(Boolean)
2025-08-25 12:37:10.568 DEBUG 116336 --- [dark-task-3] [] c.m.n.p.m.D.selectJoinList              : <0.1><H7RGGW4Y> <==      Total: 1
2025-08-25 12:37:10.579 DEBUG 116336 --- [dark-task-3] [] c.m.n.p.m.D.selectJoinList              : <0.1><H7RGGW4Y> ==>  Preparing: SELECT t1.`oid`,t1.`name`,t.`column_num` FROM dbmeta_table t INNER JOIN dbmeta_object t1 ON (t1.`oid` = t.`oid`) WHERE (t.`schema_id` = ? AND t1.`deleted` = ?)
2025-08-25 12:37:10.579 DEBUG 116336 --- [dark-task-3] [] c.m.n.p.m.D.selectJoinList              : <0.1><H7RGGW4Y> ==> Parameters: 1(Long), false(Boolean)
2025-08-25 12:37:10.582 DEBUG 116336 --- [dark-task-3] [] c.m.n.p.m.D.selectJoinList              : <0.1><H7RGGW4Y> <==      Total: 1
2025-08-25 12:37:10.583 DEBUG 116336 --- [dark-task-3] [] c.m.n.p.m.SampleTableMapper.selectList  : <0.1><H7RGGW4Y> ==>  Preparing: SELECT `oid`,`data_empty` FROM sample_table WHERE (`oid` IN (?))
2025-08-25 12:37:10.583 DEBUG 116336 --- [dark-task-3] [] c.m.n.p.m.SampleTableMapper.selectList  : <0.1><H7RGGW4Y> ==> Parameters: 2(Long)
2025-08-25 12:37:10.586 DEBUG 116336 --- [dark-task-3] [] c.m.n.p.m.SampleTableMapper.selectList  : <0.1><H7RGGW4Y> <==      Total: 1
2025-08-25 12:37:10.589 DEBUG 116336 --- [dark-task-3] [] c.m.n.p.m.D.selectJoinList              : <0.1><H7RGGW4Y> ==>  Preparing: SELECT t.`oid`,t.`table_id`,t.`data_type`,t.`type_group`,t.`length`,t1.`name` FROM dbmeta_column t INNER JOIN dbmeta_object t1 ON (t1.`oid` = t.`oid`) WHERE (t.`table_id` IN (?) AND t1.`deleted` = ?)
2025-08-25 12:37:10.589 DEBUG 116336 --- [dark-task-3] [] c.m.n.p.m.D.selectJoinList              : <0.1><H7RGGW4Y> ==> Parameters: 2(Long), false(Boolean)
2025-08-25 12:37:10.596 DEBUG 116336 --- [dark-task-3] [] c.m.n.p.m.D.selectJoinList              : <0.1><H7RGGW4Y> <==      Total: 37
2025-08-25 12:37:10.596 DEBUG 116336 --- [Thread-23] [] c.m.n.p.m.DiscoveryLogMapper.insertList : ==>  Preparing: INSERT INTO discovery_log (`job_id`,`percent`,`content`) VALUES (?,?,?) , (?,?,?) , (?,?,?)
2025-08-25 12:37:10.597 DEBUG 116336 --- [Thread-23] [] c.m.n.p.m.DiscoveryLogMapper.insertList : ==> Parameters: 6(Long), 0.00(BigDecimal), [{"msg":"开始采集元数据信息","status":"INFO","time":"2025-08-25 12:37:10"}](String), 6(Long), 0.00(BigDecimal), [{"msg":"采集 default","status":"INFO","time":"2025-08-25 12:37:10"}](String), 6(Long), 50.00(BigDecimal), [{"msg":"元数据采集完成","status":"INFO","time":"2025-08-25 12:37:10"}](String)
2025-08-25 12:37:10.606 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.F.selectList                  : <0.1><H7RGGW4Y> ==>  Preparing: SELECT `id`,`config_id`,`uuid`,`compress_uuid`,`name`,`format`,`moved`,`temp_path`,`path` FROM file_upload_detail WHERE (`config_id` = ?)
2025-08-25 12:37:10.606 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.F.selectList                  : <0.1><H7RGGW4Y> ==> Parameters: 5(Long)
2025-08-25 12:37:10.609 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.F.selectList                  : <0.1><H7RGGW4Y> <==      Total: 1
2025-08-25 12:37:10.617 DEBUG 116336 --- [Thread-23] [] c.m.n.p.m.DiscoveryLogMapper.insertList : <==    Updates: 3
2025-08-25 12:37:10.620 DEBUG 116336 --- [Thread-23] [] c.m.n.p.m.DiscoveryLogMapper.insertList : ==>  Preparing: INSERT INTO discovery_log (`job_id`,`percent`,`content`) VALUES (?,?,?)
2025-08-25 12:37:10.621 DEBUG 116336 --- [Thread-23] [] c.m.n.p.m.DiscoveryLogMapper.insertList : ==> Parameters: 6(Long), 50.00(BigDecimal), [{"msg":"抽样开始 default.A_DATA_100W","status":"INFO","time":"2025-08-25 12:37:10"}](String)
2025-08-25 12:37:10.628 DEBUG 116336 --- [Thread-23] [] c.m.n.p.m.DiscoveryLogMapper.insertList : <==    Updates: 1
2025-08-25 12:37:10.645 DEBUG 116336 --- [Thread-23] [] c.m.n.p.m.DiscoveryLogMapper.insertList : ==>  Preparing: INSERT INTO discovery_log (`job_id`,`percent`,`content`) VALUES (?,?,?)
2025-08-25 12:37:10.645 DEBUG 116336 --- [Thread-23] [] c.m.n.p.m.DiscoveryLogMapper.insertList : ==> Parameters: 6(Long), 50.00(BigDecimal), [{"msg":"抽样完成 default.A_DATA_100W","status":"INFO","time":"2025-08-25 12:37:10"}](String)
2025-08-25 12:37:10.649 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.selectList : <0.1><H7RGGW4Y> ==>  Preparing: SELECT `oid`,`status` FROM sample_column WHERE (`oid` IN (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?))
2025-08-25 12:37:10.649 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.selectList : <0.1><H7RGGW4Y> ==> Parameters: 3(Long), 4(Long), 5(Long), 6(Long), 7(Long), 8(Long), 9(Long), 10(Long), 11(Long), 12(Long), 13(Long), 14(Long), 15(Long), 16(Long), 17(Long), 18(Long), 19(Long), 20(Long), 21(Long), 22(Long), 23(Long), 24(Long), 25(Long), 26(Long), 27(Long), 28(Long), 29(Long), 30(Long), 31(Long), 32(Long), 33(Long), 34(Long), 35(Long), 36(Long), 37(Long), 38(Long), 39(Long)
2025-08-25 12:37:10.653 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.selectList : <0.1><H7RGGW4Y> <==      Total: 37
2025-08-25 12:37:10.660 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><H7RGGW4Y> ==>  Preparing: UPDATE sample_column SET `status`=?, `updated_at`=?, `sample_data`=? WHERE `oid`=?
2025-08-25 12:37:10.661 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><H7RGGW4Y> ==> Parameters: 2(Integer), 2025-08-25 12:37:10.603(Timestamp), ["490639","499190","466779","462592","462521","464823","464642","464687","461080","461914"](String), 3(Long)
2025-08-25 12:37:10.662 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><H7RGGW4Y> ==> Parameters: 2(Integer), 2025-08-25 12:37:10.603(Timestamp), ["务滢琪","旅达乔","尔致溢","弥圣","田淇毅","言奕强","李云富","京羚东","宰泽","卢乔娟"](String), 4(Long)
2025-08-25 12:37:10.662 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><H7RGGW4Y> ==> Parameters: 2(Integer), 2025-08-25 12:37:10.603(Timestamp), ["847123132313148320","540326198103195077","430423199002148170","372900195808225761","110222199309244587","652302191503113362","150722195907171337","142200196806054499","41042619410110626X","610727198102258511"](String), 5(Long)
2025-08-25 12:37:10.664 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><H7RGGW4Y> ==> Parameters: 2(Integer), 2025-08-25 12:37:10.603(Timestamp), ["14551642308","17022004665","15020467964","15519038384","18211191825","18072940653","17513889925","18240542220","17057409873","14503348639"](String), 6(Long)
2025-08-25 12:37:10.664 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><H7RGGW4Y> ==> Parameters: 2(Integer), 2025-08-25 12:37:10.603(Timestamp), ["5149391333199760","5142219094046587","5142212840860054","5143280245518694","5144928438230856","5148367778705869","5143289894808734","5142056582615406","5158409904144166","5158408873067341"](String), 7(Long)
2025-08-25 12:37:10.665 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><H7RGGW4Y> ==> Parameters: 2(Integer), 2025-08-25 12:37:10.603(Timestamp), ["<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"](String), 8(Long)
2025-08-25 12:37:10.665 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><H7RGGW4Y> ==> Parameters: 2(Integer), 2025-08-25 12:37:10.603(Timestamp), ["陕西省咸阳武功县西单大街728号","内蒙古鄂尔多斯市达拉特旗球场街道904号","西藏自治区昌都类乌齐县王府井大街484号","云南省文山苗族自治州马关县景山前街602号","吉林省白山浑江区复兴门大街592号","上海大兴安岭地区青浦区上海街道497号","宁夏中卫海原县车站街道377号","江西省新余市分宜县长安街949号","河北张家口蔚县宣武门大街515号","内蒙古自治区包头九原区一元街道149号"](String), 9(Long)
2025-08-25 12:37:10.666 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><H7RGGW4Y> ==> Parameters: 2(Integer), 2025-08-25 12:37:10.603(Timestamp), ["496036","936236","350036","747936","313436","453936","253136","253536","323236","08553"](String), 10(Long)
2025-08-25 12:37:10.666 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><H7RGGW4Y> ==> Parameters: 2(Integer), 2025-08-25 12:37:10.603(Timestamp), ["814","029","367","790","754","212","1030","0485","9410","1554"](String), 11(Long)
2025-08-25 12:37:10.666 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><H7RGGW4Y> ==> Parameters: 2(Integer), 2025-08-25 12:37:10.603(Timestamp), ["138.82.90.159","230.89.141.241","188.173.64.113","231.17.203.70","250.32.115.0","160.150.242.195","41.175.94.182","234.88.135.37","68.29.101.178","91.68.93.37"](String), 12(Long)
2025-08-25 12:37:10.667 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><H7RGGW4Y> ==> Parameters: 2(Integer), 2025-08-25 12:37:10.603(Timestamp), ["050568956","610658897","144316530","964684515","15200297X","85700684X","194779082","879892299","243410688","42937358X"](String), 13(Long)
2025-08-25 12:37:10.667 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><H7RGGW4Y> ==> Parameters: 2(Integer), 2025-08-25 12:37:10.603(Timestamp), ["贵州铜仁市美的集团佳兴酒厂","河南省三门峡大众公用事业（集团）有限公司内贸部","临沂市控股物资经销处","南充中色（天津）有色金属器材商行","大川门业集团电器经销部","天泽煤化工集团股份公司星业皮具厂","江苏南京市第二公共汽车公司专线","四川遂宁市人居置业纺织机械有限公司","德州市对外贸易中心（集团）冲孔筛网厂","新疆伊犁哈萨克自治州蓝色光标品牌管理顾问角梳加工厂"](String), 14(Long)
2025-08-25 12:37:10.667 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><H7RGGW4Y> ==> Parameters: 2(Integer), 2025-08-25 12:37:10.603(Timestamp), ["2016-12-18 09:35:55","1986-11-20 14:19:38","1968-08-26 10:47:54","1998-11-30 01:30:16","1994-09-19 21:10:20","1998-07-26 09:39:34","1959-11-23 08:28:01","1990-02-24 08:55:31","1956-08-25 11:38:20","1985-09-04 15:51:14"](String), 15(Long)
2025-08-25 12:37:10.668 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><H7RGGW4Y> ==> Parameters: 2(Integer), 2025-08-25 12:37:10.603(Timestamp), ["546822.22","68543.51","593817.93","055950.78","39184.33","209239.59","840991.54","286544.4","198929.5","167023.85"](String), 16(Long)
2025-08-25 12:37:10.668 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><H7RGGW4Y> ==> Parameters: 2(Integer), 2025-08-25 12:37:10.603(Timestamp), ["field_14","field_14","field_14","field_14","field_14","field_14","field_14","field_14","field_14","field_14"](String), 17(Long)
2025-08-25 12:37:10.668 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><H7RGGW4Y> ==> Parameters: 2(Integer), 2025-08-25 12:37:10.603(Timestamp), ["field_15","field_15","field_15","field_15","field_15","field_15","field_15","field_15","field_15","field_15"](String), 18(Long)
2025-08-25 12:37:10.668 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><H7RGGW4Y> ==> Parameters: 2(Integer), 2025-08-25 12:37:10.603(Timestamp), ["49949841737900595A5222","20760173552270252A5192","30131686913072589A5121","39510027552252525J4009","31116817713028395L4009","37961234241162495J3001","51195247613073358D1521","71422135833038120H1129","70182226945273089F3062","81000187345030350H2182"](String), 19(Long)
2025-08-25 12:37:10.669 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><H7RGGW4Y> ==> Parameters: 2(Integer), 2025-08-25 12:37:10.603(Timestamp), ["200553489332725196501260822","201915453621226196201081811","201511444360421199405225774","692174L75579694280998685334","200245457450100199207225515","201112458652524192012238623","200381459230832198008030911","201336504342601196704111435","200437358432224195007258271","200851453142423196009157810"](String), 20(Long)
2025-08-25 12:37:10.669 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><H7RGGW4Y> ==> Parameters: 2(Integer), 2025-08-25 12:37:10.603(Timestamp), ["147512731886036","241532701856553","246372621687498","142452402090217","143410183719132","230429014068926","230620112527998","243220224841234","248371522677716","230512127623601"](String), 21(Long)
2025-08-25 12:37:10.670 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><H7RGGW4Y> ==> Parameters: 2(Integer), 2025-08-25 12:37:10.603(Timestamp), ["924267395896220","822613493445638","846330946064581","818133631691267","882465184456199","162467735239004","829149205939954","162414511343484","018457697959963","072651214265114"](String), 22(Long)
2025-08-25 12:37:10.670 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><H7RGGW4Y> ==> Parameters: 2(Integer), 2025-08-25 12:37:10.603(Timestamp), ["X55436411489407739","Q554364130833010542","C554364129502349741","S55436416253774227","W554364124158973348","R55436410435831520","255436412756065443","G554364108016960550","E554364199922124354","G554364176000444950"](String), 23(Long)
2025-08-25 12:37:10.671 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><H7RGGW4Y> ==> Parameters: 2(Integer), 2025-08-25 12:37:10.603(Timestamp), ["Y1B1B370B","94U755V3","TQ9Q45K86","26540O7SQ","V71PA9P2S","55MPE6TI","230Z1N25","JX360R05","R2T03HRN","WN339DT5Y"](String), 24(Long)
2025-08-25 12:37:10.671 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><H7RGGW4Y> ==> Parameters: 2(Integer), 2025-08-25 12:37:10.603(Timestamp), ["450326758394269","441826568884937","33092219940819435000","220519109828506","422202357059224","622725452055993","361030329261759","510102433979832","532530914107058","51332719601213963X03"](String), 25(Long)
2025-08-25 12:37:10.673 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><H7RGGW4Y> ==> Parameters: 2(Integer), 2025-08-25 12:37:10.603(Timestamp), ["J7951574206874","L3777918608575","Z6814805311472","J5030668525172","Z6410608674774","Z4667575865675","J8540557701872","J1635897673872","Z7364349260875","Z4231689494975"](String), 26(Long)
2025-08-25 12:37:10.673 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><H7RGGW4Y> ==> Parameters: 2(Integer), 2025-08-25 12:37:10.603(Timestamp), ["北字第84390862号","南字第02792678号","空字第82570365号","广字第7223766号","装字第49957965号","广字第66155505号","沈字第9450770号","北字第8637380号","空字第2558110号","济字第67886147号"](String), 27(Long)
2025-08-25 12:37:10.673 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><H7RGGW4Y> ==> Parameters: 2(Integer), 2025-08-25 12:37:10.603(Timestamp), ["G53173304","Q15506544","S69984032","Q87060196","Q56525170","C34084972","G64247625","C42702162","S53593417","M76313940"](String), 28(Long)
2025-08-25 12:37:10.674 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><H7RGGW4Y> ==> Parameters: 2(Integer), 2025-08-25 12:37:10.603(Timestamp), ["D9727823839","D4936167037","D9513067837","D4573976634","D0609870132","D5647809532","D3343599334","D2578455939","D6986341432","D7281518234"](String), 29(Long)
2025-08-25 12:37:10.675 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><H7RGGW4Y> ==> Parameters: 2(Integer), 2025-08-25 12:37:10.603(Timestamp), ["XVD924782666426","FXD335161321119","YLD714577103061","FJN416121156219","CSY748104432322","EFF183664939287","EGQ676373885093","JBM098050814881","DTT955275931757","EQC104886541063"](String), 30(Long)
2025-08-25 12:37:10.675 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><H7RGGW4Y> ==> Parameters: 2(Integer), 2025-08-25 12:37:10.603(Timestamp), ["D32999914","D05946362","D37328284","D09022543","D47605828","D32850474","D77266272","D30424520","D94155628","D84209197"](String), 31(Long)
2025-08-25 12:37:10.677 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><H7RGGW4Y> ==> Parameters: 2(Integer), 2025-08-25 12:37:10.603(Timestamp), ["field_29","field_29","field_29","field_29","field_29","field_29","field_29","field_29","field_29","field_29"](String), 32(Long)
2025-08-25 12:37:10.678 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><H7RGGW4Y> ==> Parameters: 2(Integer), 2025-08-25 12:37:10.603(Timestamp), ["field_30","field_30","field_30","field_30","field_30","field_30","field_30","field_30","field_30","field_30"](String), 33(Long)
2025-08-25 12:37:10.678 DEBUG 116336 --- [Thread-23] [] c.m.n.p.m.DiscoveryLogMapper.insertList : <==    Updates: 1
2025-08-25 12:37:10.678 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><H7RGGW4Y> ==> Parameters: 2(Integer), 2025-08-25 12:37:10.603(Timestamp), ["2222200","2222200","2222200","2222200","field_31","field_31","field_31","field_31","field_31","field_31"](String), 34(Long)
2025-08-25 12:37:10.679 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><H7RGGW4Y> ==> Parameters: 2(Integer), 2025-08-25 12:37:10.603(Timestamp), ["2024/12/18 10:00:00","2024/12/18 10:00:00","field_32","field_32","field_32","field_32","field_32","field_32","field_32","field_32"](String), 35(Long)
2025-08-25 12:37:10.680 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><H7RGGW4Y> ==> Parameters: 2(Integer), 2025-08-25 12:37:10.603(Timestamp), ["2024/12/18 10:00:00.000","field_33","field_33","field_33","field_33","field_33","field_33","field_33","field_33","field_33"](String), 36(Long)
2025-08-25 12:37:10.680 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><H7RGGW4Y> ==> Parameters: 2(Integer), 2025-08-25 12:37:10.603(Timestamp), ["field_34","field_34","field_34","field_34","field_34","field_34","field_34","field_34","field_34","field_34"](String), 37(Long)
2025-08-25 12:37:10.680 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><H7RGGW4Y> ==> Parameters: 2(Integer), 2025-08-25 12:37:10.603(Timestamp), ["陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单"](String), 38(Long)
2025-08-25 12:37:10.682 DEBUG 116336 --- [Samp-2] [] c.m.n.p.m.SampleColumnMapper.updateById : <0.1><H7RGGW4Y> ==> Parameters: 2(Integer), 2025-08-25 12:37:10.603(Timestamp), ["陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号","陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号陕西省咸阳武功县西单大街728号"](String), 39(Long)
2025-08-25 12:37:10.698 DEBUG 116336 --- [Thread-22] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 12:37:10.725 DEBUG 116336 --- [Thread-22] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 12:37:10.765 DEBUG 116336 --- [dark-task-3] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : <0.1><H7RGGW4Y> [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 12:37:10.783 DEBUG 116336 --- [dark-task-3] [] c.m.nyx.pipeline.http.HttpRemoteInvoke  : <0.1><H7RGGW4Y> {"code":200,"success":true,"msg":"操作成功","tranceId":"H7RGGW4Y"}
2025-08-25 12:37:10.785 DEBUG 116336 --- [dark-task-3] [] c.m.n.p.m.DiscoveryJobMapper.updateById : <0.1><H7RGGW4Y> ==>  Preparing: UPDATE discovery_job SET `heartbeat`=? WHERE `id`=?
2025-08-25 12:37:10.786 DEBUG 116336 --- [dark-task-3] [] c.m.n.p.m.DiscoveryJobMapper.updateById : <0.1><H7RGGW4Y> ==> Parameters: {"startTime":1756096630490,"step":3,"num":0}(String), 6(Long)
2025-08-25 12:37:10.800 DEBUG 116336 --- [dark-task-3] [] c.m.n.p.m.DiscoveryJobMapper.updateById : <0.1><H7RGGW4Y> <==    Updates: 1
2025-08-25 12:37:10.801 DEBUG 116336 --- [dark-task-3] [] c.m.n.p.s.impl.TaskCallbackServiceImpl  : <0.1><H7RGGW4Y> 【心跳】TaskHeartbeatBeatReq(taskGroup=COLLECT_META, taskName=6, status=3)
2025-08-25 12:37:10.801 DEBUG 116336 --- [dark-task-3] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : <0.1><H7RGGW4Y> [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 12:37:10.806  INFO 116336 --- [dark-task-3] [] c.m.n.p.job.TaskSchedulingManager       : <0.1><H7RGGW4Y> 【计时器】J6(P2-S5) [2]: running time = 316毫秒(316323400)
---------------------------------------------------------
  %   Task name        detail           ns
---------------------------------------------------------
64%   stage2           202毫秒(1)  *********
23%   stage1           71毫秒(1)  *********

2025-08-25 12:38:11.253  INFO 116336 --- [SpringApplicationShutdownHook] [] c.m.n.p.job.TaskSchedulingManager       : Shutting down task manager executor
2025-08-25 12:38:11.380  INFO 116336 --- [SpringApplicationShutdownHook] [] c.b.d.d.DynamicRoutingDataSource        : dynamic-datasource start closing ....
2025-08-25 12:38:11.404  INFO 116336 --- [SpringApplicationShutdownHook] [] com.alibaba.druid.pool.DruidDataSource  : {dataSource-1} closing ...
2025-08-25 12:38:11.419  INFO 116336 --- [SpringApplicationShutdownHook] [] com.alibaba.druid.pool.DruidDataSource  : {dataSource-1} closed
2025-08-25 12:38:11.419  INFO 116336 --- [SpringApplicationShutdownHook] [] c.b.d.d.d.DefaultDataSourceDestroyer    : dynamic-datasource close the datasource named [embed] success,
2025-08-25 12:38:11.420  INFO 116336 --- [SpringApplicationShutdownHook] [] com.zaxxer.hikari.HikariDataSource      : dark-dbms - Shutdown initiated...
2025-08-25 12:38:11.426  INFO 116336 --- [SpringApplicationShutdownHook] [] com.zaxxer.hikari.HikariDataSource      : dark-dbms - Shutdown completed.
2025-08-25 12:38:11.426  INFO 116336 --- [SpringApplicationShutdownHook] [] c.b.d.d.d.DefaultDataSourceDestroyer    : dynamic-datasource close the datasource named [master] success,
2025-08-25 12:38:11.426  INFO 116336 --- [SpringApplicationShutdownHook] [] c.b.d.d.DynamicRoutingDataSource        : dynamic-datasource all closed success,bye
2025-08-25 14:25:15.587  INFO 63940 --- [main] [] c.a.n.client.env.SearchableProperties   : properties search order:PROPERTIES->JVM->ENV->DEFAULT_SETTING
2025-08-25 14:25:15.809  INFO 63940 --- [main] [] com.mchz.nyx.pipeline.Application       : Starting Application using Java 1.8.0_301 on DESKTOP-TQMNVVF with PID 63940 (C:\projects\nyx\pipeline\target\classes started by admin in C:\projects\nyx\pipeline)
2025-08-25 14:25:15.810 DEBUG 63940 --- [main] [] com.mchz.nyx.pipeline.Application       : Running with Spring Boot v2.7.18, Spring v5.3.31
2025-08-25 14:25:15.811  INFO 63940 --- [main] [] com.mchz.nyx.pipeline.Application       : The following 1 profile is active: "standalone"
2025-08-25 14:25:20.480  INFO 63940 --- [main] [] o.s.cloud.context.scope.GenericScope    : BeanFactory id=894701e2-e985-33a1-851d-e7f0611b4c97
2025-08-25 14:25:20.622  INFO 63940 --- [main] [] ptablePropertiesBeanFactoryPostProcessor: Post-processing PropertySource instances
2025-08-25 14:25:20.623  INFO 63940 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-08-25 14:25:20.625  INFO 63940 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-08-25 14:25:20.625  INFO 63940 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Skipping PropertySource servletContextInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-08-25 14:25:20.627  INFO 63940 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Converting PropertySource systemProperties [org.springframework.core.env.PropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-08-25 14:25:20.629  INFO 63940 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Converting PropertySource systemEnvironment [org.springframework.boot.env.SystemEnvironmentPropertySourceEnvironmentPostProcessor$OriginAwareSystemEnvironmentPropertySource] to EncryptableSystemEnvironmentPropertySourceWrapper
2025-08-25 14:25:20.630  INFO 63940 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Converting PropertySource random [org.springframework.boot.env.RandomValuePropertySource] to EncryptablePropertySourceWrapper
2025-08-25 14:25:20.630  INFO 63940 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Converting PropertySource cachedrandom [org.springframework.cloud.util.random.CachedRandomPropertySource] to EncryptablePropertySourceWrapper
2025-08-25 14:25:20.630  INFO 63940 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Converting PropertySource springCloudClientHostInfo [org.springframework.core.env.MapPropertySource] to EncryptableMapPropertySourceWrapper
2025-08-25 14:25:20.631  INFO 63940 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Converting PropertySource Config resource 'file [config\sourcedata.properties]' via location './config/sourcedata.properties' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-08-25 14:25:20.631  INFO 63940 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Converting PropertySource Config resource 'class path resource [application-standalone.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-08-25 14:25:20.631  INFO 63940 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Converting PropertySource Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' [org.springframework.boot.env.OriginTrackedMapPropertySource] to EncryptableMapPropertySourceWrapper
2025-08-25 14:25:20.631  INFO 63940 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Converting PropertySource TLog Default Properties [org.springframework.core.io.support.ResourcePropertySource] to EncryptableMapPropertySourceWrapper
2025-08-25 14:25:20.632  INFO 63940 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Converting PropertySource defaultProperties [org.springframework.boot.DefaultPropertiesPropertySource] to EncryptableMapPropertySourceWrapper
2025-08-25 14:25:20.642  INFO 63940 --- [main] [] c.g.y.a.MybatisPlusJoinAutoConfiguration: MPJ SqlSessionFactory bean definition: sqlSessionFactory factoryBeanName: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration factoryMethodName: sqlSessionFactory source: com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration.sqlSessionFactory(javax.sql.DataSource)
2025-08-25 14:25:21.186  INFO 63940 --- [main] [] c.u.j.filter.DefaultLazyPropertyFilter  : Property Filter custom Bean not found with name 'encryptablePropertyFilter'. Initializing Default Property Filter
2025-08-25 14:25:21.207  INFO 63940 --- [main] [] c.u.j.r.DefaultLazyPropertyResolver     : Property Resolver custom Bean not found with name 'encryptablePropertyResolver'. Initializing Default Property Resolver
2025-08-25 14:25:21.210  INFO 63940 --- [main] [] c.u.j.d.DefaultLazyPropertyDetector     : Property Detector custom Bean not found with name 'encryptablePropertyDetector'. Initializing Default Property Detector
2025-08-25 14:25:21.772 DEBUG 63940 --- [main] [] c.m.n.p.c.EmbeddedTomcatConfiguration$1 : Code archive: C:\repository\org\springframework\boot\spring-boot\2.7.18\spring-boot-2.7.18.jar
2025-08-25 14:25:21.772 DEBUG 63940 --- [main] [] c.m.n.p.c.EmbeddedTomcatConfiguration$1 : Code archive: C:\repository\org\springframework\boot\spring-boot\2.7.18\spring-boot-2.7.18.jar
2025-08-25 14:25:21.773 DEBUG 63940 --- [main] [] c.m.n.p.c.EmbeddedTomcatConfiguration$1 : None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored.
2025-08-25 14:25:21.831  INFO 63940 --- [main] [] o.s.b.w.embedded.tomcat.TomcatWebServer : Tomcat initialized with port(s): 8888 (http)
2025-08-25 14:25:21.853  INFO 63940 --- [main] [] o.apache.catalina.core.StandardService  : Starting service [Tomcat]
2025-08-25 14:25:21.854  INFO 63940 --- [main] [] org.apache.catalina.core.StandardEngine : Starting Servlet engine: [Apache Tomcat/9.0.106]
2025-08-25 14:25:21.873  WARN 63940 --- [main] [] o.a.c.webresources.DirResourceSet       : Disabled the global canonical file name cache to protect against CVE-2024-56337 when starting the WebResourceSet at [C:\Users\<USER>\AppData\Local\Temp\tomcat-docbase.8888.4726358430917150895] which is part of the web application []
2025-08-25 14:25:22.204  INFO 63940 --- [main] [] o.a.c.c.C.[Tomcat].[localhost].[/]      : Initializing Spring embedded WebApplicationContext
2025-08-25 14:25:22.204  INFO 63940 --- [main] [] w.s.c.ServletWebServerApplicationContext: Root WebApplicationContext: initialization completed in 4948 ms
2025-08-25 14:25:22.922 ERROR 63940 --- [main] [] com.alibaba.druid.pool.DruidDataSource  : testWhileIdle is true, validationQuery not set
2025-08-25 14:25:22.936  INFO 63940 --- [main] [] com.alibaba.druid.pool.DruidDataSource  : {dataSource-1,embed} inited
2025-08-25 14:25:22.939  INFO 63940 --- [main] [] c.b.d.d.DynamicRoutingDataSource        : dynamic-datasource - add a datasource named [embed] success
2025-08-25 14:25:22.939  INFO 63940 --- [main] [] c.b.d.d.DynamicRoutingDataSource        : dynamic-datasource - add a datasource named [master] success
2025-08-25 14:25:22.939  INFO 63940 --- [main] [] c.b.d.d.DynamicRoutingDataSource        : dynamic-datasource initial loaded [2] datasource,primary datasource named [master]
2025-08-25 14:25:27.155  INFO 63940 --- [main] [] c.m.n.p.job.TaskSchedulingManager       : DISCOVERY: DiscoveryJob
2025-08-25 14:25:27.156  INFO 63940 --- [main] [] c.m.n.p.job.TaskSchedulingManager       : ASSET_RELATION: AssetRelationJob
2025-08-25 14:25:27.156  INFO 63940 --- [main] [] c.m.n.p.job.TaskSchedulingManager       : COLLECT_META: CollectMetaJob
2025-08-25 14:25:28.175  INFO 63940 --- [main] [] m.e.s.MybatisPlusApplicationContextAware: Register ApplicationContext instances org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@3b1bb3ab
2025-08-25 14:25:28.586  INFO 63940 --- [main] [] com.zaxxer.hikari.HikariDataSource      : dark-dbms - Starting...
2025-08-25 14:25:29.268  INFO 63940 --- [main] [] com.zaxxer.hikari.HikariDataSource      : dark-dbms - Start completed.
2025-08-25 14:25:31.340  INFO 63940 --- [main] [] o.s.b.w.embedded.tomcat.TomcatWebServer : Tomcat started on port(s): 8888 (http) with context path ''
2025-08-25 14:25:31.342  INFO 63940 --- [main] [] u.j.c.RefreshScopeRefreshedEventListener: Refreshing cached encryptable property sources on ServletWebServerInitializedEvent
2025-08-25 14:25:31.344  INFO 63940 --- [main] [] CachingDelegateEncryptablePropertySource: Property Source systemProperties refreshed
2025-08-25 14:25:31.344  INFO 63940 --- [main] [] CachingDelegateEncryptablePropertySource: Property Source systemEnvironment refreshed
2025-08-25 14:25:31.345  INFO 63940 --- [main] [] CachingDelegateEncryptablePropertySource: Property Source random refreshed
2025-08-25 14:25:31.345  INFO 63940 --- [main] [] CachingDelegateEncryptablePropertySource: Property Source cachedrandom refreshed
2025-08-25 14:25:31.345  INFO 63940 --- [main] [] CachingDelegateEncryptablePropertySource: Property Source springCloudClientHostInfo refreshed
2025-08-25 14:25:31.345  INFO 63940 --- [main] [] CachingDelegateEncryptablePropertySource: Property Source Config resource 'file [config\sourcedata.properties]' via location './config/sourcedata.properties' refreshed
2025-08-25 14:25:31.346  INFO 63940 --- [main] [] CachingDelegateEncryptablePropertySource: Property Source Config resource 'class path resource [application-standalone.yml]' via location 'optional:classpath:/' refreshed
2025-08-25 14:25:31.346  INFO 63940 --- [main] [] CachingDelegateEncryptablePropertySource: Property Source Config resource 'class path resource [application.yml]' via location 'optional:classpath:/' refreshed
2025-08-25 14:25:31.346  INFO 63940 --- [main] [] CachingDelegateEncryptablePropertySource: Property Source TLog Default Properties refreshed
2025-08-25 14:25:31.346  INFO 63940 --- [main] [] CachingDelegateEncryptablePropertySource: Property Source defaultProperties refreshed
2025-08-25 14:25:31.347  INFO 63940 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Skipping PropertySource configurationProperties [class org.springframework.boot.context.properties.source.ConfigurationPropertySourcesPropertySource
2025-08-25 14:25:31.349  INFO 63940 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Skipping PropertySource servletConfigInitParams [class org.springframework.core.env.PropertySource$StubPropertySource
2025-08-25 14:25:31.352  INFO 63940 --- [main] [] c.u.j.EncryptablePropertySourceConverter: Converting PropertySource servletContextInitParams [org.springframework.web.context.support.ServletContextPropertySource] to EncryptableEnumerablePropertySourceWrapper
2025-08-25 14:25:31.843  INFO 63940 --- [main] [] com.mchz.nyx.pipeline.Application       : Started Application in 20.903 seconds (JVM running for 35.584)
2025-08-25 14:25:31.976  INFO 63940 --- [main] [] c.m.n.p.config.McDatasourceConfig       : 设置统一数据源版本:*******;路径:C:\file\mcdatasource_1681
2025-08-25 14:44:31.171  INFO 63940 --- [http-nio-8888-exec-2] [] o.a.c.c.C.[Tomcat].[localhost].[/]      : Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-08-25 14:44:31.192  INFO 63940 --- [http-nio-8888-exec-2] [] o.s.web.servlet.DispatcherServlet       : Initializing Servlet 'dispatcherServlet'
2025-08-25 14:44:31.375  INFO 63940 --- [http-nio-8888-exec-2] [] o.s.web.servlet.DispatcherServlet       : Completed initialization in 181 ms
2025-08-25 14:44:35.791 DEBUG 63940 --- [http-nio-8888-exec-2] [] c.m.n.p.controller.TaskController       : <0.1><9JdVWWYZ> {"industryId":2,"planId":3,"jobId":7,"jobType":7,"source":{"streamDataTypes":[],"id":6,"configName":"csv","configType":128,"dataBaseType":"LOCAL_FILE","host":"","port":"","username":"","dbName":"","attachment":{"charset":"1","headline":"true","type":"0","compress":"false"},"advancedConfig":{},"properties":{}},"sampleLine":100,"sampleRate":100,"full":false,"saveStrategy":0}
2025-08-25 14:44:35.830  INFO 63940 --- [http-nio-8888-exec-2] [] c.m.n.p.job.TaskSchedulingManager       : <0.1><9JdVWWYZ> 【任务管理】全库
2025-08-25 14:44:35.857  INFO 63940 --- [dark-task-1] [] c.m.n.p.job.TaskSchedulingManager       : <0.1><9JdVWWYZ> 【作业执行】J7(P3-S6) csv
2025-08-25 14:44:35.859 DEBUG 63940 --- [dark-task-1] [] c.m.n.p.s.impl.TaskCallbackServiceImpl  : <0.1><9JdVWWYZ> 【心跳】TaskHeartbeatBeatReq(taskGroup=COLLECT_META, taskName=7, status=1)
2025-08-25 14:44:35.861  WARN 63940 --- [dark-task-1] [] c.m.nyx.pipeline.http.HttpRemoteInvoke  : <0.1><9JdVWWYZ> 【地址获取】默认使用localhost
2025-08-25 14:44:35.958 DEBUG 63940 --- [dark-task-1] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : <0.1><9JdVWWYZ> [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 14:44:49.074 DEBUG 63940 --- [dark-task-1] [] c.m.n.p.m.DiscoveryJobMapper.selectList : <0.1><9JdVWWYZ> ==>  Preparing: SELECT `heartbeat` FROM discovery_job WHERE (`id` = ?)
2025-08-25 14:44:49.196 DEBUG 63940 --- [dark-task-1] [] c.m.n.p.m.DiscoveryJobMapper.selectList : <0.1><9JdVWWYZ> ==> Parameters: 7(Long)
2025-08-25 14:44:49.604 DEBUG 63940 --- [dark-task-1] [] c.m.n.p.m.DiscoveryJobMapper.selectList : <0.1><9JdVWWYZ> <==      Total: 1
2025-08-25 14:44:49.654  INFO 63940 --- [dark-task-1] [] c.m.n.p.job.collect.CollectMetaJob      : <0.1><9JdVWWYZ> 【分类分级】采集元数据
2025-08-25 14:44:49.838 DEBUG 63940 --- [dark-task-1] [] c.m.n.p.m.F.selectList                  : <0.1><9JdVWWYZ> ==>  Preparing: SELECT `id`,`config_id`,`uuid`,`compress_uuid`,`name`,`format`,`moved`,`temp_path`,`path` FROM file_upload_detail WHERE (`config_id` = ?)
2025-08-25 14:44:49.839 DEBUG 63940 --- [dark-task-1] [] c.m.n.p.m.F.selectList                  : <0.1><9JdVWWYZ> ==> Parameters: 6(Long)
2025-08-25 14:44:49.849 DEBUG 63940 --- [dark-task-1] [] c.m.n.p.m.F.selectList                  : <0.1><9JdVWWYZ> <==      Total: 1
2025-08-25 14:44:49.973 DEBUG 63940 --- [meta-6-1] [] c.m.n.p.m.DbMetaObjectMapper.selectList : <0.1><9JdVWWYZ> ==>  Preparing: SELECT `oid`,`name`,`deleted`,`revision`,`hash_value` FROM dbmeta_object WHERE (`source_id` = ? AND `pid` = ? AND `type` = ?)
2025-08-25 14:44:49.975 DEBUG 63940 --- [meta-6-1] [] c.m.n.p.m.DbMetaObjectMapper.selectList : <0.1><9JdVWWYZ> ==> Parameters: 6(Long), 0(Long), SCHEMA(String)
2025-08-25 14:44:49.982 DEBUG 63940 --- [meta-6-1] [] c.m.n.p.m.DbMetaObjectMapper.selectList : <0.1><9JdVWWYZ> <==      Total: 0
2025-08-25 14:44:50.165 DEBUG 63940 --- [meta-6-1] [] c.m.n.p.m.DbMetaObjectMapper.insertList : <0.1><9JdVWWYZ> ==>  Preparing: INSERT INTO dbmeta_object (`source_id`,`name`,`type`,`description`,`deleted`,`created_at`,`updated_at`,`revision`,`hash_value`,`pid`) VALUES (?,?,?,?,?,?,?,?,?,?)
2025-08-25 14:44:50.170 DEBUG 63940 --- [meta-6-1] [] c.m.n.p.m.DbMetaObjectMapper.insertList : <0.1><9JdVWWYZ> ==> Parameters: 6(Long), default(String), SCHEMA(String), null, false(Boolean), 2025-08-25 14:44:50.13(Timestamp), 2025-08-25 14:44:50.13(Timestamp), 1(Integer), (String), 0(Long)
2025-08-25 14:44:50.193 DEBUG 63940 --- [meta-6-1] [] c.m.n.p.m.DbMetaObjectMapper.insertList : <0.1><9JdVWWYZ> <==    Updates: 1
2025-08-25 14:44:50.212 DEBUG 63940 --- [meta-6-1] [] c.m.n.p.m.DbMetaSchemaMapper.insertList : <0.1><9JdVWWYZ> ==>  Preparing: INSERT INTO dbmeta_schema (`oid`,`source_id`,`catalog`,`schema`,`charset`,`collation`) VALUES (?,?,?,?,?,?)
2025-08-25 14:44:50.214 DEBUG 63940 --- [meta-6-1] [] c.m.n.p.m.DbMetaSchemaMapper.insertList : <0.1><9JdVWWYZ> ==> Parameters: 40(Long), 6(Long), null, null, null, null
2025-08-25 14:44:50.219 DEBUG 63940 --- [meta-6-1] [] c.m.n.p.m.DbMetaSchemaMapper.insertList : <0.1><9JdVWWYZ> <==    Updates: 1
2025-08-25 14:44:50.273 DEBUG 63940 --- [meta-6-1] [] c.m.n.p.m.DbMetaObjectMapper.selectList : <0.1><9JdVWWYZ> ==>  Preparing: SELECT `oid`,`name`,`deleted`,`revision`,`hash_value` FROM dbmeta_object WHERE (`source_id` = ? AND `pid` = ? AND `type` = ? AND `name` = ?)
2025-08-25 14:44:50.274 DEBUG 63940 --- [meta-6-1] [] c.m.n.p.m.DbMetaObjectMapper.selectList : <0.1><9JdVWWYZ> ==> Parameters: 6(Long), 0(Long), SCHEMA(String), default(String)
2025-08-25 14:44:50.279 DEBUG 63940 --- [meta-6-1] [] c.m.n.p.m.DbMetaObjectMapper.selectList : <0.1><9JdVWWYZ> <==      Total: 1
2025-08-25 14:44:50.295 DEBUG 63940 --- [meta-6-1] [] c.m.n.p.m.DbMetaObjectMapper.selectList : <0.1><9JdVWWYZ> ==>  Preparing: SELECT `oid`,`name`,`type`,`deleted`,`revision`,`hash_value` FROM dbmeta_object WHERE (`source_id` = ? AND `pid` = ? AND `type` = ?)
2025-08-25 14:44:50.295 DEBUG 63940 --- [meta-6-1] [] c.m.n.p.m.DbMetaObjectMapper.selectList : <0.1><9JdVWWYZ> ==> Parameters: 6(Long), 40(Long), TABLE(String)
2025-08-25 14:44:50.297 DEBUG 63940 --- [meta-6-1] [] c.m.n.p.m.DbMetaObjectMapper.selectList : <0.1><9JdVWWYZ> <==      Total: 0
2025-08-25 14:44:50.382 DEBUG 63940 --- [store-1] [] c.m.n.p.m.DbMetaObjectMapper.insertList : <0.1><9JdVWWYZ> ==>  Preparing: INSERT INTO dbmeta_object (`source_id`,`name`,`type`,`description`,`deleted`,`created_at`,`updated_at`,`revision`,`hash_value`,`pid`) VALUES (?,?,?,?,?,?,?,?,?,?)
2025-08-25 14:44:50.384 DEBUG 63940 --- [store-1] [] c.m.n.p.m.DbMetaObjectMapper.insertList : <0.1><9JdVWWYZ> ==> Parameters: 6(Long), test(String), TABLE(String), null, false(Boolean), 2025-08-25 14:44:50.372(Timestamp), 2025-08-25 14:44:50.372(Timestamp), 1(Integer), b9e16166586aaf95483acb0de8d9e492(String), 40(Long)
2025-08-25 14:44:50.392 DEBUG 63940 --- [store-1] [] c.m.n.p.m.DbMetaObjectMapper.insertList : <0.1><9JdVWWYZ> <==    Updates: 1
2025-08-25 14:44:50.415 DEBUG 63940 --- [Thread-32] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 14:44:50.449 DEBUG 63940 --- [Thread-33] [] c.m.n.p.m.DiscoveryLogMapper.insertList : ==>  Preparing: INSERT INTO discovery_log (`job_id`,`percent`,`content`) VALUES (?,?,?) , (?,?,?)
2025-08-25 14:44:50.453 DEBUG 63940 --- [Thread-33] [] c.m.n.p.m.DiscoveryLogMapper.insertList : ==> Parameters: 7(Long), 0.00(BigDecimal), [{"msg":"开始采集元数据信息","status":"INFO","time":"2025-08-25 14:44:49"}](String), 7(Long), 0.00(BigDecimal), [{"msg":"采集 default","status":"INFO","time":"2025-08-25 14:44:49"}](String)
2025-08-25 14:44:50.477 DEBUG 63940 --- [Thread-33] [] c.m.n.p.m.DiscoveryLogMapper.insertList : <==    Updates: 2
2025-08-25 14:44:50.489 DEBUG 63940 --- [store-1] [] c.m.n.p.m.DbMetaObjectMapper.insertList : <0.1><9JdVWWYZ> ==>  Preparing: INSERT INTO dbmeta_object (`source_id`,`name`,`type`,`description`,`deleted`,`created_at`,`updated_at`,`revision`,`hash_value`,`pid`) VALUES (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?)
2025-08-25 14:44:50.520 DEBUG 63940 --- [store-1] [] c.m.n.p.m.DbMetaObjectMapper.insertList : <0.1><9JdVWWYZ> ==> Parameters: 6(Long), name(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), c22e45dfaccc4e295ff702df974f4675(String), 41(Long), 6(Long), IDCard(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), b176b145a4640c80dfea24087b35ff25(String), 41(Long), 6(Long), Passport(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), 1a0cafb4cbdb0f07d0ce8b50e8511c25(String), 41(Long), 6(Long), Address(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), b6d9c596eb6831ffbd790acfaf0c5370(String), 41(Long), 6(Long), Phone_Number(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), e2ec6e1284cdb617144a10731aad75ee(String), 41(Long), 6(Long), Mobile(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), ed029b1b71b919361c111089e3b9e507(String), 41(Long), 6(Long), Mail(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), 3f8aafcf7d30b36b0f08a52465ce9229(String), 41(Long), 6(Long), social_code(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), 5cba923acbded30d201f8366f713f608(String), 41(Long), 6(Long), native_place(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), 452389cc3ed2d80df74c7d105b01de61(String), 41(Long), 6(Long), gender(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), 92d4d3a211dc872ae8b8b7d9fed471b1(String), 41(Long), 6(Long), age(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), 93c300a3c77ae6422d7d2db942cb164c(String), 41(Long), 6(Long), Lucky_color(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), 8b24760c69935905cdcc6f09c3317f2e(String), 41(Long), 6(Long), account(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), 23eebe6a9bf3499b1f54003de49cb8df(String), 41(Long), 6(Long), resident_certification(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), 94c30fc6c0004f5746cea509f68ee640(String), 41(Long), 6(Long), IMSI(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), f2c875e9c5b7a1a92aa5462d12cb7dc8(String), 41(Long), 6(Long), mail139(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), f64ac67403cfb4810c5c028a9a37a242(String), 41(Long), 6(Long), HongKong_pass_code(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), 8481a537d617f74722905203ff6eb1aa(String), 41(Long), 6(Long), electronic_HongKong(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), 2561a40f5aa8f07693883f93f1f73d6f(String), 41(Long), 6(Long), birthday(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), 274e90045adb4827196da84be1adbc2c(String), 41(Long), 6(Long), officer(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), 39112b88fd38247fbcad81c21ae39104(String), 41(Long), 6(Long), relationship(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), 669af69a5ba4cf8e2ebd93fd7bc6e128(String), 41(Long), 6(Long), nation(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), bbb7fa6706e82d8671f6f28c3b214889(String), 41(Long), 6(Long), marriage_status(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), 804143f8f7b7900f5dbef43085c04c4e(String), 41(Long), 6(Long), person_unit_relationship(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), dbae160992e0be23c87045a51d8b44c6(String), 41(Long), 6(Long), business_type(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), 981de62f556cf7097ddf4a866a67aebc(String), 41(Long), 6(Long), business_way(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), ef04500f225affd22f994a6e9c5d69a5(String), 41(Long), 6(Long), business_way_2(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), 7e2dc27979ad02e8e05338099cb89b16(String), 41(Long), 6(Long), dead_line(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), 1204ce6a46687f3343ba2ef8198f03dc(String), 41(Long), 6(Long), car_num(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), be359dabfa027a0dd2af2537e6bdd342(String), 41(Long), 6(Long), political(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), 975fb40d40d66003b5c13c4359de2342(String), 41(Long), 6(Long), education(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), 747b4ef76f3b6c4b91a796c155d8567d(String), 41(Long), 6(Long), university(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), 96ad227d288dc5ba516db85d27214c68(String), 41(Long), 6(Long), id_card(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), 356029edd98dd89449d95dd13f763ce1(String), 41(Long), 6(Long), party(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), 34128ee02e955a8ef88c093d7ed8b8b1(String), 41(Long), 6(Long), Currency(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), d85fbdaa06e99a36f8372db4ebadfc54(String), 41(Long), 6(Long), unit_relationship(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), 4cc09f76a29ccab3a12b041aebf4088d(String), 41(Long), 6(Long), driver_code(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), 2e22b82f69cf63358699af5dd9bd1c83(String), 41(Long), 6(Long), car_id(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), 07635893d84781ecfdd5f9dc4e6e8164(String), 41(Long), 6(Long), new_energy_car_id(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), 53f6f82189224fed00920836eafeb43f(String), 41(Long), 6(Long), civil_car_id(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), 23281590498203d3423998516421fbc2(String), 41(Long), 6(Long), date_time(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), 1a57ac537a550cf8cb96d96be22a261d(String), 41(Long), 6(Long), ipv4(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), ebd8e01c10177b6e758017a6bc428d38(String), 41(Long), 6(Long), ipv6(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), 7b325ecc484947ddfd10b220c925f343(String), 41(Long), 6(Long), MAC(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), 8292a9e179410182467e851a02aa7afe(String), 41(Long), 6(Long), QQ(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), 61d285a99b73ee4bd3a52c45f44d5ff9(String), 41(Long), 6(Long), wechat(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), fe22b97a9e3a1f614c1129d702d04e0e(String), 41(Long), 6(Long), date_test(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), b7d5f40acb8008b78de2cc414129bda2(String), 41(Long), 6(Long), time_test(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), 45bc944332fe799f991b62a8d0bd238b(String), 41(Long), 6(Long), time_or_date(String), COLUMN(String), null, false(Boolean), 2025-08-25 14:44:50.401(Timestamp), 2025-08-25 14:44:50.401(Timestamp), 1(Integer), e854223fe8fb55d01f8d5bc1636c52d1(String), 41(Long)
2025-08-25 14:44:50.569 DEBUG 63940 --- [store-1] [] c.m.n.p.m.DbMetaObjectMapper.insertList : <0.1><9JdVWWYZ> <==    Updates: 49
2025-08-25 14:44:50.575 DEBUG 63940 --- [Thread-32] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 14:44:50.583 DEBUG 63940 --- [store-1] [] c.m.n.p.m.DbMetaTableMapper.insertList  : <0.1><9JdVWWYZ> ==>  Preparing: INSERT INTO dbmeta_table (`oid`,`source_id`,`schema_id`,`type`,`column_num`,`partition_column`,`space`,`rows`,`comment`) VALUES (?,?,?,?,?,?,?,?,?)
2025-08-25 14:44:50.584 DEBUG 63940 --- [store-1] [] c.m.n.p.m.DbMetaTableMapper.insertList  : <0.1><9JdVWWYZ> ==> Parameters: 41(Long), 6(Long), 40(Long), TABLE(String), 49(Integer), null, null, null, null
2025-08-25 14:44:50.609 DEBUG 63940 --- [store-1] [] c.m.n.p.m.DbMetaTableMapper.insertList  : <0.1><9JdVWWYZ> <==    Updates: 1
2025-08-25 14:44:50.863 DEBUG 63940 --- [store-1] [] c.m.n.p.m.DbMetaColumnMapper.insertList : <0.1><9JdVWWYZ> ==>  Preparing: INSERT INTO dbmeta_column (`oid`,`source_id`,`schema_id`,`table_id`,`data_type`,`type_group`,`length`,`precision`,`scale`,`nullable`,`position`,`is_auto_incremented`,`is_generated`,`is_pk`,`is_unique`,`is_index`,`is_fk`,`comment`) VALUES (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) , (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)
2025-08-25 14:44:50.908 DEBUG 63940 --- [store-1] [] c.m.n.p.m.DbMetaColumnMapper.insertList : <0.1><9JdVWWYZ> ==> Parameters: 42(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 1(Integer), null, null, null, null, null, null, null, 43(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 2(Integer), null, null, null, null, null, null, null, 44(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 3(Integer), null, null, null, null, null, null, null, 45(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 4(Integer), null, null, null, null, null, null, null, 46(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 5(Integer), null, null, null, null, null, null, null, 47(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 6(Integer), null, null, null, null, null, null, null, 48(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 7(Integer), null, null, null, null, null, null, null, 49(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 8(Integer), null, null, null, null, null, null, null, 50(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 9(Integer), null, null, null, null, null, null, null, 51(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 10(Integer), null, null, null, null, null, null, null, 52(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 11(Integer), null, null, null, null, null, null, null, 53(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 12(Integer), null, null, null, null, null, null, null, 54(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 13(Integer), null, null, null, null, null, null, null, 55(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 14(Integer), null, null, null, null, null, null, null, 56(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 15(Integer), null, null, null, null, null, null, null, 57(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 16(Integer), null, null, null, null, null, null, null, 58(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 17(Integer), null, null, null, null, null, null, null, 59(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 18(Integer), null, null, null, null, null, null, null, 60(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 19(Integer), null, null, null, null, null, null, null, 61(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 20(Integer), null, null, null, null, null, null, null, 62(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 21(Integer), null, null, null, null, null, null, null, 63(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 22(Integer), null, null, null, null, null, null, null, 64(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 23(Integer), null, null, null, null, null, null, null, 65(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 24(Integer), null, null, null, null, null, null, null, 66(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 25(Integer), null, null, null, null, null, null, null, 67(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 26(Integer), null, null, null, null, null, null, null, 68(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 27(Integer), null, null, null, null, null, null, null, 69(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 28(Integer), null, null, null, null, null, null, null, 70(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 29(Integer), null, null, null, null, null, null, null, 71(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 30(Integer), null, null, null, null, null, null, null, 72(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 31(Integer), null, null, null, null, null, null, null, 73(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 32(Integer), null, null, null, null, null, null, null, 74(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 33(Integer), null, null, null, null, null, null, null, 75(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 34(Integer), null, null, null, null, null, null, null, 76(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 35(Integer), null, null, null, null, null, null, null, 77(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 36(Integer), null, null, null, null, null, null, null, 78(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 37(Integer), null, null, null, null, null, null, null, 79(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 38(Integer), null, null, null, null, null, null, null, 80(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 39(Integer), null, null, null, null, null, null, null, 81(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 40(Integer), null, null, null, null, null, null, null, 82(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 41(Integer), null, null, null, null, null, null, null, 83(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 42(Integer), null, null, null, null, null, null, null, 84(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 43(Integer), null, null, null, null, null, null, null, 85(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 44(Integer), null, null, null, null, null, null, null, 86(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 45(Integer), null, null, null, null, null, null, null, 87(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 46(Integer), null, null, null, null, null, null, null, 88(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 47(Integer), null, null, null, null, null, null, null, 89(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 48(Integer), null, null, null, null, null, null, null, 90(Long), 6(Long), 40(Long), 41(Long), CHARACTER(String), CHARACTER(String), null, null, null, null, 49(Integer), null, null, null, null, null, null, null
2025-08-25 14:44:50.921 DEBUG 63940 --- [store-1] [] c.m.n.p.m.DbMetaColumnMapper.insertList : <0.1><9JdVWWYZ> <==    Updates: 49
2025-08-25 14:44:50.971 DEBUG 63940 --- [Thread-32] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 14:44:50.977 DEBUG 63940 --- [Thread-33] [] c.m.n.p.m.DiscoveryLogMapper.insertList : ==>  Preparing: INSERT INTO discovery_log (`job_id`,`percent`,`content`) VALUES (?,?,?)
2025-08-25 14:44:50.978 DEBUG 63940 --- [Thread-33] [] c.m.n.p.m.DiscoveryLogMapper.insertList : ==> Parameters: 7(Long), 50.00(BigDecimal), [{"msg":"元数据采集完成","status":"INFO","time":"2025-08-25 14:44:50"}](String)
2025-08-25 14:44:50.982 DEBUG 63940 --- [Thread-32] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 14:44:50.988 DEBUG 63940 --- [Thread-33] [] c.m.n.p.m.DiscoveryLogMapper.insertList : <==    Updates: 1
2025-08-25 14:44:53.521 DEBUG 63940 --- [dark-task-1] [] c.m.n.p.m.D.selectJoinList              : <0.1><9JdVWWYZ> ==>  Preparing: SELECT t1.`oid`,t1.`name`,t.`catalog`,t.`schema` FROM dbmeta_schema t INNER JOIN dbmeta_object t1 ON (t1.`oid` = t.`oid`) WHERE (t.`source_id` = ? AND t1.`deleted` = ?)
2025-08-25 14:44:53.523 DEBUG 63940 --- [dark-task-1] [] c.m.n.p.m.D.selectJoinList              : <0.1><9JdVWWYZ> ==> Parameters: 6(Long), false(Boolean)
2025-08-25 14:44:53.551 DEBUG 63940 --- [dark-task-1] [] c.m.n.p.m.D.selectJoinList              : <0.1><9JdVWWYZ> <==      Total: 1
2025-08-25 14:44:56.035 DEBUG 63940 --- [dark-task-1] [] c.m.n.p.m.D.selectJoinList              : <0.1><9JdVWWYZ> ==>  Preparing: SELECT t1.`oid`,t1.`name`,t.`column_num` FROM dbmeta_table t INNER JOIN dbmeta_object t1 ON (t1.`oid` = t.`oid`) WHERE (t.`schema_id` = ? AND t1.`deleted` = ?)
2025-08-25 14:44:56.590 DEBUG 63940 --- [dark-task-1] [] c.m.n.p.m.D.selectJoinList              : <0.1><9JdVWWYZ> ==> Parameters: 40(Long), false(Boolean)
2025-08-25 14:44:56.610 DEBUG 63940 --- [dark-task-1] [] c.m.n.p.m.D.selectJoinList              : <0.1><9JdVWWYZ> <==      Total: 1
2025-08-25 14:44:58.669 DEBUG 63940 --- [dark-task-1] [] c.m.n.p.m.SampleTableMapper.selectList  : <0.1><9JdVWWYZ> ==>  Preparing: SELECT `oid`,`data_empty` FROM sample_table WHERE (`oid` IN (?))
2025-08-25 14:44:58.685 DEBUG 63940 --- [dark-task-1] [] c.m.n.p.m.SampleTableMapper.selectList  : <0.1><9JdVWWYZ> ==> Parameters: 41(Long)
2025-08-25 14:44:58.688 DEBUG 63940 --- [dark-task-1] [] c.m.n.p.m.SampleTableMapper.selectList  : <0.1><9JdVWWYZ> <==      Total: 0
2025-08-25 14:44:58.696 DEBUG 63940 --- [pool-1-thread-1] [] c.m.n.p.m.DiscoveryJobMapper.updateById : <0.1><9JdVWWYZ> ==>  Preparing: UPDATE discovery_job SET `heartbeat`=? WHERE `id`=?
2025-08-25 14:44:58.697 DEBUG 63940 --- [pool-1-thread-1] [] c.m.n.p.m.DiscoveryJobMapper.updateById : <0.1><9JdVWWYZ> ==> Parameters: {"startTime":1756104289612,"step":2,"num":0,"detail":{}}(String), 7(Long)
2025-08-25 14:44:58.714 DEBUG 63940 --- [dark-task-1] [] c.m.n.p.m.D.selectJoinList              : <0.1><9JdVWWYZ> ==>  Preparing: SELECT t.`oid`,t.`table_id`,t.`data_type`,t.`type_group`,t.`length`,t1.`name` FROM dbmeta_column t INNER JOIN dbmeta_object t1 ON (t1.`oid` = t.`oid`) WHERE (t.`table_id` IN (?) AND t1.`deleted` = ?)
2025-08-25 14:44:58.715 DEBUG 63940 --- [dark-task-1] [] c.m.n.p.m.D.selectJoinList              : <0.1><9JdVWWYZ> ==> Parameters: 41(Long), false(Boolean)
2025-08-25 14:44:58.732 DEBUG 63940 --- [dark-task-1] [] c.m.n.p.m.D.selectJoinList              : <0.1><9JdVWWYZ> <==      Total: 49
2025-08-25 14:44:58.732 DEBUG 63940 --- [pool-1-thread-1] [] c.m.n.p.m.DiscoveryJobMapper.updateById : <0.1><9JdVWWYZ> <==    Updates: 1
2025-08-25 14:44:58.733 DEBUG 63940 --- [pool-1-thread-1] [] c.m.n.p.s.impl.TaskCallbackServiceImpl  : <0.1><9JdVWWYZ> 【心跳】TaskHeartbeatBeatReq(taskGroup=COLLECT_META, taskName=7, status=2)
2025-08-25 14:44:58.734 DEBUG 63940 --- [pool-1-thread-1] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : <0.1><9JdVWWYZ> [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 14:44:58.760 DEBUG 63940 --- [Samp-3] [] c.m.n.p.m.F.selectList                  : <0.1><9JdVWWYZ> ==>  Preparing: SELECT `id`,`config_id`,`uuid`,`compress_uuid`,`name`,`format`,`moved`,`temp_path`,`path` FROM file_upload_detail WHERE (`config_id` = ?)
2025-08-25 14:44:58.765 DEBUG 63940 --- [Samp-3] [] c.m.n.p.m.F.selectList                  : <0.1><9JdVWWYZ> ==> Parameters: 6(Long)
2025-08-25 14:44:58.767 DEBUG 63940 --- [Samp-3] [] c.m.n.p.m.F.selectList                  : <0.1><9JdVWWYZ> <==      Total: 1
2025-08-25 14:44:58.923 DEBUG 63940 --- [Thread-32] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 14:44:58.926 DEBUG 63940 --- [Thread-33] [] c.m.n.p.m.DiscoveryLogMapper.insertList : ==>  Preparing: INSERT INTO discovery_log (`job_id`,`percent`,`content`) VALUES (?,?,?)
2025-08-25 14:44:58.926 DEBUG 63940 --- [Thread-33] [] c.m.n.p.m.DiscoveryLogMapper.insertList : ==> Parameters: 7(Long), 50.00(BigDecimal), [{"msg":"抽样开始 default.test","status":"INFO","time":"2025-08-25 14:44:58"}](String)
2025-08-25 14:44:58.932 DEBUG 63940 --- [Thread-32] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 14:44:58.999 DEBUG 63940 --- [Thread-33] [] c.m.n.p.m.DiscoveryLogMapper.insertList : <==    Updates: 1
2025-08-25 14:44:59.006 DEBUG 63940 --- [Thread-33] [] c.m.n.p.m.DiscoveryLogMapper.insertList : ==>  Preparing: INSERT INTO discovery_log (`job_id`,`percent`,`content`) VALUES (?,?,?)
2025-08-25 14:44:59.009 DEBUG 63940 --- [Thread-33] [] c.m.n.p.m.DiscoveryLogMapper.insertList : ==> Parameters: 7(Long), 50.00(BigDecimal), [{"msg":"抽样完成 default.test","status":"INFO","time":"2025-08-25 14:44:58"}](String)
2025-08-25 14:44:59.019 DEBUG 63940 --- [Thread-33] [] c.m.n.p.m.DiscoveryLogMapper.insertList : <==    Updates: 1
2025-08-25 14:44:59.089 DEBUG 63940 --- [Thread-32] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 14:44:59.343 DEBUG 63940 --- [Thread-32] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 14:45:00.414 DEBUG 63940 --- [Samp-3] [] c.m.n.p.m.SampleColumnMapper.selectList : <0.1><9JdVWWYZ> ==>  Preparing: SELECT `oid`,`status` FROM sample_column WHERE (`oid` IN (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?))
2025-08-25 14:45:00.418 DEBUG 63940 --- [Samp-3] [] c.m.n.p.m.SampleColumnMapper.selectList : <0.1><9JdVWWYZ> ==> Parameters: 42(Long), 43(Long), 44(Long), 45(Long), 46(Long), 47(Long), 48(Long), 49(Long), 50(Long), 51(Long), 52(Long), 53(Long), 54(Long), 55(Long), 56(Long), 57(Long), 58(Long), 59(Long), 60(Long), 61(Long), 62(Long), 63(Long), 64(Long), 65(Long), 66(Long), 67(Long), 68(Long), 69(Long), 70(Long), 71(Long), 72(Long), 73(Long), 74(Long), 75(Long), 76(Long), 77(Long), 78(Long), 79(Long), 80(Long), 81(Long), 82(Long), 83(Long), 84(Long), 85(Long), 86(Long), 87(Long), 88(Long), 89(Long), 90(Long)
2025-08-25 14:45:00.424 DEBUG 63940 --- [Samp-3] [] c.m.n.p.m.SampleColumnMapper.selectList : <0.1><9JdVWWYZ> <==      Total: 0
2025-08-25 14:45:00.619 DEBUG 63940 --- [Samp-3] [] c.m.n.p.m.SampleColumnMapper.insertList : <0.1><9JdVWWYZ> ==>  Preparing: INSERT INTO sample_column (`oid`,`source_id`,`table_id`,`status`,`updated_at`,`sample_data`) VALUES (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?) , (?,?,?,?,?,?)
2025-08-25 14:45:00.642 DEBUG 63940 --- [Samp-3] [] c.m.n.p.m.SampleColumnMapper.insertList : <0.1><9JdVWWYZ> ==> Parameters: 42(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["张飞","张英","郑秀珍","李璐","庄秀英","张娜","倪瑞","刘金凤","陈洁","刘玉英","朱亮","薛冬梅","丁秀梅","张瑞","王波","朱鹏","杨刚","刘龙","任瑞","李涛","马柳","邵梅","李飞","陈坤","张红梅","王丽丽","阎淑兰","林建华","王雷","朱晨","郭慧","刘浩","王芳","郭秀荣","龚玉华","许海燕","刘燕","熊超","李玉珍","王平","何华","房荣","张婷婷","何亮","姚璐","王刚","王玉华","刘华","曾秀华","沈杰","杨倩","刘文","李阳","吴萍","冯琳","艾桂花","韩婷","高敏","余凤兰","翟玉梅","徐博","赵岩","张刚","陈晶","董萍","王华","张峰","王宁","李艳","杨彬","蔡冬梅","黎兵","张志强","易林","李旭","张丹丹","王桂香","梁凤英","徐瑜","蒋玉华","王丹","罗秀兰","陈桂兰","张浩","陈璐","刘晨","许成","贾芳","张欢","张佳","李芳","原雪","朱磊","郑阳","黄桂兰","祝桂珍","屈建华","冉萍","张玉华","张飞"](String), 43(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["4.11E+17","2.15E+17","3.77E+17","5.88E+17","5.34E+17","4.20E+17","8.30E+17","2.05E+17","6.28E+17","8.67E+17","8.81E+17","22095818150630430x","9.59E+17","1.36E+17","9.37E+17","3.90E+17","7.09E+17","9.47E+17","2.20E+17","4.81E+17","4.90E+17","8.65E+17","7.57E+17","2.48E+17","52026218761030994x","7.05E+17","8.97E+17","4.77E+17","2.61E+17","6.79E+17","71736019600810277X","1.00E+17","2.17E+17","63323819980610630X","7.30E+17","6.54E+17","87853219200720515X","5.22E+17","34039619280212691x","7.21E+17","2.17E+17","8.17E+17","3.52E+17","7.35E+17","9.07E+17","6.83E+17","67983318591208086x","62355030271131575x","2.62E+17","35161919310310749x","1.04E+17","1.92E+17","4.31E+17","56340419230131635x","74900319420910544X","4.91E+17","3.59E+17","7.84E+17","8.86E+17","75106018091121449x","7.54E+17","1.17E+17","6.57E+17","4.94E+17","15594335781110907X","8.36E+17","1.69E+17","68235831140610272x","9.33E+17","2.43E+17","1.38E+17","1.65E+17","8.71E+17","7.66E+17","9.38E+17","3.68E+17","5.95E+17","7.34E+17","8.96E+17","2.04E+17","5.63E+17","6.69E+17","2.26E+17","30555118811030714X","5.00E+17","70158425990731050x","5.67E+17","29880333091020713x","1.27E+17","3.08E+17","1.18E+17","4.26E+17","2.62E+17","9.09E+17","9.85E+17","4.13E+17","4.24E+17","5.28E+17","6.86E+17","4.11E+17"](String), 44(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["SE2006987","153653930","s83703122","145529118","143316101","D5399300","P00642578","153415296","154827075","Ma6889036","e88764961","P0594350","154732315","150297027","154475475","E85842540","h72405360","D4068368","S23221299","157180472","152260275","S80748882","D75667407","Se3315489","S20776688","S1099036","h75455055","P19598702","EB9273962","k19722290","153458634","G4555182","153318814","H14903563","g19624207","kj7970050","P6954704","k30327737","145426711","kj7946768","K99839618","E04277417","KJ2001336","Ec9533476","KJ2984365","143164091","s92900602","eE8860340","P5678820","MA0308705","150145741","eA2039047","de2076060","159507401","140528089","141789804","H59632092","e62696168","D66976588","E40701298","157976630","153534355","150503696","S86533114","144997941","152231083","K42493833","EC8523502","144529985","ef0694929","p95377018","G85661450","D8094357","K46317500","g96475849","k62100503","S6563667","D70155545","S46901226","E17474117","P97901666","141059465","141685129","145211344","153739185","156975054","p89601052","S3786396","144937537","G70233116","H55156674","kJ6312961","146965687","152832521","141833069","159183180","PE7845127","P79453496","P5606573","SE2006987"](String), 45(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["浙江省杭州市拱墅区教育路","北京市市辖区延庆区文化路","北京市市辖区昌平区人民东路","陕西省宝鸡市眉县裕华路","重庆市市辖区南川区襄阳大道","重庆市县城口县团结路","河北省保定市容城县兴隆路","广东省汕尾市陆丰市人民南路","吉林省长春市长春净月高新技术产业开发区爱民路","福建省宁德市蕉城区环城西路","重庆市县彭水苗族土家族自治县沿河路","宁夏回族自治区银川市兴庆区釜山路","陕西省西安市新城区民主路","甘肃省张掖市市辖区新建路","海南省省直辖县级行政区划临高县文昌路","四川省成都市崇州市金山路","广东省揭阳市普宁市振兴路","河北省沧州市南皮县北环路","云南省楚雄彝族自治州元谋县新民路","江苏省盐城市市辖区富民路","西藏自治区那曲地区比如县南大街","湖南省株洲市醴陵市人民西路","安徽省马鞍山市雨山区新华路","天津市市辖区西青区平安路","吉林省辽源市东辽县创业路","河北省邯郸市馆陶县花园路","广东省江门市蓬江区红旗路","安徽省池州市石台县浆洗街","北京市市辖区房山区南街","山东省莱芜市市辖区建设街","天津市市辖区和平区长青路","吉林省延边朝鲜族自治州延吉市兴隆路","辽宁省锦州市市辖区利民路","江苏省无锡市新吴区雁滩路","新疆维吾尔自治区和田地区策勒县祥和路","安徽省六安市霍山县南京路","重庆市市辖区武隆区南街","山东省临沂市市辖区站前路","福建省莆田市城厢区新华街","辽宁省辽阳市太子河区南京路","西藏自治区林芝市察隅县白沙路","青海省海东市民和回族土族自治县文明路","四川省德阳市市辖区利民路","安徽省六安市舒城县工农路","安徽省淮北市市辖区合作路","河南省濮阳市南乐县釜山路","辽宁省锦州市凌河区西四环北路","黑龙江省黑河市北安市胜利街","上海市市辖区长宁区和平路","河南省郑州市新密市环城西路","吉林省吉林市龙潭区机场路","广东省佛山市三水区学府路","云南省文山壮族苗族自治州富宁县民主路","湖北省襄阳市南漳县和平路","江苏省南通市海门市浆洗街","贵州省铜仁市松桃苗族自治县人民南路","山西省吕梁市市辖区人民东路","云南省迪庆藏族自治州维西傈僳族自治县市场路","河南省郑州市上街区公园路","山西省忻州市保德县浆洗街","吉林省吉林市龙潭区和平路","湖北省武汉市硚口区文昌路","山东省莱芜市莱城区利民路","山西省运城市万荣县排洪南路","湖北省荆州市江陵县新民路","河南省南阳市唐河县光明路","湖北省省直辖县级行政区划潜江市团结路","辽宁省营口市大石桥市花园路","河北省保定市高阳县站前路","宁夏回族自治区吴忠市利通区同心路","天津市市辖区武清区团结路","浙江省丽水市景宁畲族自治县兴隆街","重庆市市辖区开州区东环路","云南省曲靖市师宗县机场路","河南省商丘市柘城县友谊路","陕西省西安市临潼区长安路","湖南省长沙市开福区南大街","云南省迪庆藏族自治州维西傈僳族自治县金山路","甘肃省武威市古浪县北大街","青海省海西蒙古族藏族自治州格尔木市工农路","甘肃省张掖市肃南裕固族自治县文昌路","湖南省邵阳市城步苗族自治县东兴路","四川省南充市市辖区南街","内蒙古自治区呼和浩特市市辖区祥和路","内蒙古自治区呼和浩特市土默特左旗文化路","江西省赣州市定南县和平路","云南省昭通市永善县惊奇路","江苏省无锡市市辖区站前路","河南省郑州市惠济区兴隆路","贵州省贵阳市云岩区人民大道","甘肃省甘南藏族自治州舟曲县民生路","广东省中山市东区街道兴隆路","广东省揭阳市揭东区曙光路","安徽省马鞍山市市辖区交通路","吉林省辽源市西安区滨河路","江西省景德镇市市辖区平安路","重庆市市辖区綦江区文明路","青海省海西蒙古族藏族自治州冷湖行政委员会朝阳路","青海省黄南藏族自治州河南蒙古族自治县凤凰路","浙江省杭州市拱墅区教育路"](String), 46(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["0876-5206929","0733-8825190","0710-8416718","0464-2780613","0773-9777160","0837-3571795","023-4724258","0398-5848834","0527-5676685","0914-6934478","0993-2946055","0941-2973353","0775-7034838","0458-5225832","0978-4992454","0998-7727868","0995-6337466","0634-5230121","0996-3926611","0859-3256045","0951-4968523","0353-5376356","0376-84208380","0518-8592504","0539-9075732","0930-5727697","0831-0318849","0817-4858105","0954-9084518","0819-5051284","0996-0617314","0836-5147666","0835-4196837","0556-0610319","0593-0501936","0897-0597419","0893-6632987","0775-4100053","028-8260868","0433-4531925","0396-3955427","0994-1994943","0532-06828350","0894-7172072","0746-7943781","0475-0892443","0936-5837419","0768-2618636","0779-1195628","0855-2934009","021-2249274","0858-5220442","0939-7542484","0826-3672786","0464-4443159","0372-3799723","0730-6919981","0930-8057248","0886-1941896","0878-5270527","0834-8785848","0779-9830597","0598-6538713","0744-4473084","0991-7241767","0993-4006072","0517-4154214","0977-7103233","0886-6546306","0519-6403614","0919-9936914","0556-3703126","0970-7022938","0352-5268993","0719-6953304","020-6917836","0737-3234239","0432-97021766","0973-1098891","0750-9583490","0919-7839560","0818-8618716","0756-1221246","0357-0643145","0558-0550281","0433-3759640","020-6946671","0897-8793692","0857-8406420","0798-2146072","0517-8852907","0873-2320142","020-1862517","0893-4703734","0392-7917706","0519-2521270","0895-1827078","0574-69222552","0483-8200292","0876-5206929"](String), 47(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["13695026728","13683883693","15025721691","13103919316","13508733618","18112614047","13379182607","18577090340","18901355295","18272628466","13352037071","18616755575","18916837510","15764330635","13471819640","13703248726","14537955640","15637669743","18921633122","18056803684","13086645213","15801999360","14586285988","15822021192","13099858538","15990300805","18515778716","15606576319","14523452426","18226803654","14564092874","15847692092","18566001042","13788652570","18082236214","13849521976","15677964690","15590211912","15624718151","13898168577","15134926855","18770463315","18050706041","13401836558","15572823150","18579527452","15944185269","15202482552","14535782632","18546769620","13888623374","15302053999","13384609786","14743389121","18201157592","18781289035","14570488189","13574001952","13092912591","13558208497","13955929697","18748445297","18042925002","15587928823","14573029897","13168346077","15837229087","15666493945","15597651435","18815531813","15060855155","18568547851","18171636410","15595823784","14591810869","18177211601","15987965844","18869048549","14514360898","13859562077","13937722159","18801425867","18195998018","18514552131","13742307558","18289631755","15058899309","13926120926","13206732017","13624612158","18725916153","13030914965","18284394094","14512398095","13418443360","15173165661","18221960480","13145711065","13229963510","13695026728"](String), 48(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","6173929777qq.com","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","4637193577qq.com","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"](String), 49(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["48375028J4T0JB5XRJ","5.44E+14","L6986204CC9R6LYH35","P56654888KYT2HK54U","6.48E+14","EF541605G0D10NPH4U","4.34E+14","7.77E+14","E6801421XFQW10X1WD","7G389995U4AUQ2CRG9","Y2881277FTA4RFQN05","5.14E+14","3.98E+14","DN541458A2E5JU0HFD","7.26E+14","9.56E+14","5.48E+14","7.46E+14","6.96E+14","EC962764EAYWB8FTEF","6.79E+14","HL197455PM5HWGM2FC","1.99E+14","KB355184R4RJEDDM1H","W22321499J925953F8","9.98E+14","1.30E+14","2.84E+14","7.14E+14","8.32E+14","9.13E+14","7.81E+14","8.84E+14","8.68E+14","1.91E+14","U29121601TLKP8J2N6","8.78E+14","1H680466WTRA71LU6X","CG238478J3CMG13NTW","5.51E+14","5.26E+14","7.68E+14","LL597094EYPHTB9TYH","5.76E+14","4J091743CTYRGYKYT4","2.21E+14","7L3281445AAN1TFYG1","9.45E+14","LK089856A1JPLN4G8R","UQ2215174WA4NTD3F8","8.32E+14","FF1412887H2ANA4U3P","9.30E+14","M2913901D9RE6E5XQT","PL496872TX0HT1NR0W","2.65E+14","3.14E+14","3.43E+14","5.77E+14","6.08E+14","W63412452QNR7QY1HU","Q6382006TX8BEAYTKQ","4.31E+14","1.71E+14","4.36E+14","B4411286FCJUT9LM8G","H2714177DRH6RGMLJ4","9.80E+14","N924848776TL74W885","P29123793XGTAKEC2R","4.46E+14","4.21E+14","5.98E+14","3.97E+14","2Q93117059MNXKMM9H","8.32E+14","7.77E+14","7.55E+14","5.49E+14","FD278329UR5MGXMCXG","WF528369EBX2D2C5GL","7.67E+14","4.02E+14","WD269951NE5U3TYJMN","2.08E+14","4.56E+14","XC686832GRT37WCX79","84127915XR4HW68RLJ","2.74E+14","5.88E+14","6.37E+14","4G607595HGADJM7T00","4.36E+14","AK5664114UXCQBJ8YW","8.10E+14","1.41E+14","QK481936M3K24H7Y6G","7.31E+14","7.24E+14","48375028J4T0JB5XRJ"](String), 50(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["浙江省湖州市","北京市东城区","海南省海口市","西藏自治区班戈县","内蒙古自治区通辽市","陕西省安康市","西藏自治区日喀则市","宁夏回族自治区中卫市","天津市市辖区","福建省永定区","广西壮族自治区市辖区","北京市市辖区","江西省九江市","海南省三亚市","福建省霞浦县","北京市海淀区","贵州省黔东南苗族侗族自治州","新疆维吾尔自治区吐鲁番市","河北省魏县","陕西省宜君县","福建省古田县","河北省海港区","江苏省镇江市","四川省市辖区","山西省朔城区","宁夏回族自治区固原市","福建省漳州市","山东省滨城区","甘肃省嘉峪关市","内蒙古自治区察哈尔右翼中旗","陕西省西乡县","北京市市辖区","陕西省汉中市","宁夏回族自治区隆德县","贵州省铜仁市","湖南省益阳市","内蒙古自治区呼和浩特市","黑龙江省五常市","湖北省市辖区","河南省豫东综合物流产业聚集区","河南省扶沟县","四川省资阳市","山西省吕梁市","天津市红桥区","湖北省恩施土家族苗族自治州","青海省西宁市","吉林省延边朝鲜族自治州","湖南省益阳市","重庆市市辖区","贵州省六盘水市","青海省循化撒拉族自治县","江西省井冈山市","湖北省省直辖县级行政区划","宁夏回族自治区兴庆区","四川省仪陇县","重庆市市辖区","内蒙古自治区五原县","广东省浈江区","福建省厦门市","贵州省遵义市","贵州省瓮安县","山西省永济市","青海省黄南藏族自治州","上海市黄浦区","云南省昭通市","江西省贵溪市","江西省市辖区","河南省舞阳县","辽宁省朝阳市","安徽省安庆市","湖南省常德市","河北省高碑店市","宁夏回族自治区石嘴山市","海南省中沙群岛的岛礁及其海域","吉林省白城市","西藏自治区林芝市","甘肃省甘南藏族自治州","黑龙江省哈尔滨市","江西省宜春市","新疆维吾尔自治区喀什地区","贵州省花溪区","湖南省武陵源区","天津市静海区","湖南省新晃侗族自治县","云南省红河哈尼族彝族自治州","重庆市云阳县","江西省吉安市","甘肃省庆阳市","广西壮族自治区钦州市","浙江省嘉兴市","上海市杨浦区","黑龙江省黑河市","重庆市渝北区","山西省晋中市","浙江省金华市","广西壮族自治区岑溪市","宁夏回族自治区市辖区","新疆维吾尔自治区自治区直辖县级行政区划","吉林省市辖区","浙江省湖州市"](String), 51(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["男","女","男","男","男","女","女","女","女","男","男","女","女","女","男","女","男","男","女","男","男","女","男","女","男","女","女","女","女","女","男","男","男","女","女","女","女","女","女","女","男","女","男","男","男","男","男","男","女","女","男","女","女","女","女","男","女","女","男","男","男","女","男","男","男","男","男","男","男","女","男","男","女","女","男","女","女","男","女","女","男","女","女","女","男","男","女","男","女","女","男","女","女","男","男","男","男","男","男","男"](String), 52(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["周岁293","0周岁","年纪78","360周岁","898周岁","年龄1","5岁","85周岁","1周岁","年纪95","4岁","52岁","年龄3","016岁","15周岁","5周岁","5岁","400周岁","年龄9","周岁313","427岁","年龄896","年龄4","124周岁","年纪56","年龄971","5周岁","9周岁","752岁","年龄04","周岁302","947周岁","周岁545","年龄7","51岁","384周岁","1岁","3岁","年纪585","年纪31","年龄47","359周岁","年龄881","962岁","年龄0","年纪94","周岁082","406周岁","23岁","年龄92","5岁","203岁","925岁","96岁","006周岁","05岁","年龄1","年龄117","年纪84","年纪4","54岁","年纪44","年龄26","周岁6","7周岁","年龄6","年纪6","年龄7","年纪124","周岁5","年龄95","6岁","年龄41","24周岁","年纪729","35岁","13岁","786周岁","7岁","周岁17","周岁851","652岁","年纪87","742周岁","周岁30","年纪864","2周岁","年纪1","75岁","周岁859","416岁","274岁","857周岁","年纪01","年纪4","8岁","89岁","年纪6","033周岁","周岁293"](String), 53(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["红色","黄色","绿色","浅蓝","棕色","银色","金色","米白","深咖色","浅青绿色","青绿色","水绿色","玫瑰红","紫红色","粉红色","灰","白","黑","紫红色","紫","橙","黄色","绿色","浅蓝","棕色","银色","金色","米白","深咖色","浅青绿色","青绿色","水绿色","玫瑰红","紫红色","粉红色","灰","白","黑","紫红色","紫","橙","黄色","绿色","浅蓝","棕色","银色","金色","米白","深咖色","浅青绿色","青绿色","水绿色","玫瑰红","紫红色","粉红色","灰","白","黑","紫红色","紫","橙","墨绿","金黄色","青","蓝","米白","深咖色","浅青绿色","青绿色","水绿色","玫瑰红","紫红色","粉红色","灰","白","黑","紫红色","紫","橙","墨绿","金黄色","青","蓝","米白","深咖色","浅青绿色","青绿色","水绿色","玫瑰红","紫红色","粉红色","灰","白","黑","紫红色","紫","橙","墨绿","金黄色","青"](String), 54(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["1.01E+18","6.26E+18","6.22E+18","6.23E+15","6.21E+15","6.21E+18","6.23E+14","6.23E+15","3.75E+15","6.22E+15","4.03E+14","6.22E+14","6.03E+14","4.39E+15","6.22E+15","5.59E+15","6.26E+14","9.40E+15","6.22E+18","6.21E+14","6.21E+14","4.04E+18","6.22E+14","4.37E+14","6.04E+18","6.25E+15","6.01E+18","6.22E+14","6.26E+14","6.21E+18","2.12E+15","6.23E+14","6.23E+18","8.70E+18","1.07E+18","6.23E+15","6.26E+15","6.21E+15","6.23E+15","6.23E+18","6.26E+14","6.22E+15","6.22E+18","6.23E+18","6.21E+15","4.27E+18","6.22E+15","6.20E+14","6.21E+14","6.22E+15","2.85E+18","6.51E+14","6.22E+15","6.07E+14","6.01E+15","4.37E+15","6.22E+14","6.22E+15","6.22E+14","6.26E+14","6.26E+18","6.26E+18","6.22E+18","6.03E+15","6.08E+18","6.28E+14","6.23E+14","5.49E+15","2.00E+14","5.25E+15","6.22E+15","4.04E+18","2.30E+15","6.22E+18","6.22E+14","2.36E+15","5.48E+18","6.21E+15","6.28E+14","6.22E+18","6.26E+18","5.53E+18","6.23E+18","4.04E+14","6.22E+14","6.01E+15","6.26E+14","6.21E+14","3.57E+18","6.23E+14","6.23E+15","6.22E+18","6.20E+14","6.23E+14","6.06E+18","2.62E+15","6.23E+14","6.25E+14","6.22E+15","1.01E+18"](String), 55(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["RbK207427695327","fYr965654639271","BTp707960677023","ViL891835556463","GAU602123071571","Jop563229261084","Cow440107095784","fFD250136501478","fFP521696208025","KLF209007441826","vUP345307185109","amT416182954547","ICF976892432637","itk193950530738","YZV828426887517","arX168579904790","Koz435680252172","aGL131637001434","vHj215947799642","hej172070534016","vCV265218332437","BKM500666667160","ozs948963259291","KQH554944241656","FLA025084801015","tNc083480061091","clv642080480352","XoS124798374496","VIP929457499138","qtN021287712776","Aks584747607367","Ske631885610540","pfh919890959011","VMs303965714274","kMn629267843662","BkY132309554029","XDb053447007588","QQg914855221659","oyJ820803508935","GrS726598618049","aCv524858390718","lgL652412556754","pmS075048081056","sCQ613724981200","bWW646595618204","Zbe596325015233","JVo548216150114","dim736894293034","sFK331263436096","DbH256261125109","lSz385846920144","urL460059400893","WHZ861707432366","wjk685437025010","xws569541521556","QJJ207345559811","yTG868216510866","kDE387188095685","bML572002773266","LlG925576467219","fjX068161086472","XZo752129381221","iiJ711631082485","OYL420403496731","kQq262651871562","Sip403237292390","yEA794855844479","CvP411782258517","sMF042912882047","rQV832357091359","BXe896820474828","fqA444919937480","jHX072564407346","bBn298470591890","gCK683719561160","zrQ809508818025","TuH385366540317","HLi136629619401","rrk708923798702","EjG093333869009","iFZ992690739219","Bmi927284000172","ePb378242577861","VhY784906541106","XWI406984065813","Sys733503343782","Znk896760147392","AUc019852940717","oij622120339192","RfL909955296072","DTl925929513300","JPV161326955770","nyf529592394991","RfR658196741546","jVI167786170934","xTr515394423781","yGR229319162603","xnu518302140256","rky355729528931","RbK207427695327"](String), 56(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14","4.60E+14"](String), 57(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>","<EMAIL>"](String), 58(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["M3041984577","M5839447189","M2890869179","M9485524204","H5982128829","M2287398041","H2213310365","M8255214732","M1824863893","M2961200889","H3422856927","H3657135524","M6897730057","M6160994072","H4166031117","H3299801466","H2542516917","H9442181846","H1096651435","H9654997307","M1444417719","H7460501539","M2196563793","H0034596829","H7936260848","M0365167933","M3721370170","M4630167274","M7488566545","M5032977399","M9643340101","M2510252727","M4906610954","H6589063809","H5543391370","M9962839695","M7812401369","M7654972575","H9450579375","M2180560568","M3734450589","H2696742135","M6094907523","M2467874062","H7079705430","M2891252187","H5537185137","M6330752513","M5085846643","H3289408099","H1170819542","M1893961501","H4065855450","M4586451366","H1558434459","H4143826933","H1396952011","H2585216729","H3095768792","H4418555994","H5549714189","H3851933151","M7742567199","H3349520692","M8209547867","H0413593580","M8595212230","H4500340250","M0685228379","H4960493225","H7582158195","M1177160883","H1706179291","M9166528435","H5388708830","M9185517293","M4175081851","H2980528048","M2278401883","M0457253138","H8553505400","M9331252019","M9509888480","M8273835860","H3961202307","M8583376018","H2189391480","M7278911685","H4270890778","H8059141214","H8278223107","M6904620439","H6521942615","H0440198955","H7282597413","M6121001959","H5609402726","M4046439821","M1190464015","M3041984577"](String), 59(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["C57966565","C54459065","CP9040565","C74072108","CN8167404","C65914955","C56393925","C08932334","C05464413","C64803697","C10684531","CQ2962549","C70948890","C58276464","CF2477059","C87495225","C93574454","C27828947","CW2133779","C69604699","CT8851373","CV0695203","C41557230","C95200758","C39625998","C57973885","C25265204","C62888339","CC2426066","CG6287879","C39038273","C69357043","CM2985404","CX8639098","C91855882","C53174515","CD6510862","CX2820408","C81665129","CV4144180","C87518311","C49415601","C62766812","C14905717","C15059990","C23214670","CR0731090","CE0914909","CK1541189","CP6802914","C23347189","CT9231938","CQ0515605","CV5500968","CL0944878","C35830645","C61707217","C29350044","CH4597405","C71266676","CP8691133","CJ1440574","C18669907","C24846549","CT4080050","C54507034","CR5014764","CX7927120","CU7125874","C09772828","CM8316295","CS5800325","CD1996356","C72243424","C73105259","CW1276091","C64464680","C24533138","C81444619","CC3153510","CJ3238600","C32802718","C35550922","CD1025146","CP8620752","C31628507","CB9769896","C65663325","C81661066","C98659152","C75102780","CG4040330","C29962369","CE7229856","CY5595566","C96558472","C52431270","CV5217679","C02968958","C57966565"](String), 60(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["1970/9/9","2017/8/9","1991/2/8","1971/1/11","1972/7/13","2004/4/10","1990/1/16","2012/6/29","1978/12/4","2001/1/19","1974/10/27","1970/12/3","1986/6/9","2004/3/17","2012/9/30","1974/5/13","2008/2/6","2006/6/11","2013/9/7","1995/6/17","2017/5/1","2017/7/24","1984/11/12","1993/8/9","1999/3/27","1995/4/14","1981/12/31","1974/6/14","2013/5/15","1977/4/25","1983/3/15","2016/5/10","1987/5/15","2009/11/7","1994/1/23","1977/2/10","2006/4/20","2018/8/19","2009/3/5","1992/8/3","1972/12/14","1979/2/22","2000/8/29","1999/11/18","2009/6/1","1998/9/18","2017/4/2","2009/11/24","1990/11/17","1979/9/7","2019/10/24","1981/1/27","1975/12/4","2008/10/30","1990/5/23","1992/9/14","2011/8/21","1995/6/5","1995/12/10","2004/7/14","1986/4/4","1973/9/30","2015/3/23","1988/8/21","2001/1/8","1984/1/1","1994/10/5","1995/10/14","1980/11/8","2011/4/5","2001/3/31","1995/6/29","1999/11/22","1989/9/14","2006/3/15","1982/8/25","2002/10/4","2008/7/11","2014/10/31","2021/9/9","2014/6/28","1973/6/3","2007/5/12","2016/7/17","1996/12/27","1996/8/28","1985/1/24","1974/12/12","2001/8/1","1996/4/16","1990/6/4","2003/7/6","1995/4/30","2012/4/6","2007/12/16","1970/7/5","1972/2/21","1981/12/9","1992/7/19","1970/9/9"](String), 61(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["文字第NsA4dj1号","兵字第0RZFs号","军字第IwDmh号","文字第yq2L号","文字第EOmj23e号","文字第soa1号","兵字第m7g6kK号","文字第LgRWKMu号","职字第Y4hLRd号","兵字第p8h2号","文字第Doo5jkDa号","兵字第dVbtQjHH号","士字第RyjsiAO号","职字第bGHRX1qP号","军字第BmIceq号","广字第kbNk号","士字第p8fEU号","文字第4xgDvL号","职字第oYwm号","兵字第D2lEsO3号","文字第EXEHf号","文字第zKMRt号","士字第2RQMp号","职字第vnS5G号","职字第jVRdthIh号","军字第scT7Ax号","广字第e0of号","文字第zGmU号","文字第PcIHUJD号","兵字第0eDVxU60号","广字第z8NIXaQ号","广字第n0DgGb24号","广字第03a2LT号","广字第xC8J号","职字第uwrMFPki号","军字第SyBroYyI号","士字第51L7号","广字第8OUK407r号","士字第Pm9uWurZ号","兵字第fmMK号","军字第ZEX6gH号","军字第0jXbOYK号","士字第4Qg8AB1号","兵字第BFp7zB5号","广字第RrI77P号","兵字第BcETUge4号","广字第nrudf号","士字第acmKh号","军字第6NAamd号","军字第9IX5G号","文字第NKsJ号","兵字第H4vU8vD号","职字第POQoFC号","兵字第jaahGG号","广字第9bKyYKqs号","兵字第NDJs号","兵字第yPlQh号","文字第RMAC5w号","兵字第kZM6AuX2号","职字第3I6s号","文字第fEq7号","广字第IhFai6G号","文字第JmX6iZI号","职字第jFmypq号","兵字第JF7Ry1号","军字第9ICQJYf号","兵字第25NpzPT号","士字第9afqVa7v号","广字第RWai号","兵字第FUeYpw0号","士字第cXWHfIX号","士字第AD23l号","职字第smJw9x号","兵字第NTTBEK号","职字第7BmtoMyj号","广字第ozGuF号","职字第yy9Y号","广字第lACI号","文字第ANWtfRk5号","文字第xBme9xtg号","士字第qEa4x7rF号","职字第XRGEf9号","广字第5UTr号","广字第oQ3Thz号","军字第XOBGU号","兵字第w0T6Yu号","士字第cryd0号","文字第A3it号","兵字第NmHl1Db号","军字第Zshe7号","文字第1JhD3Zq号","文字第FmJJ7Eq6号","文字第LkcV号","军字第J0E3号","广字第SMexgQB号","士字第z20HFvx号","军字第cXVYhqbj号","兵字第iVD0号","士字第lPKre号","文字第NsA4dj1号"](String), 62(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["女","父","社交关系","社交关系","姐妹","社交关系","母","女","子","女","母","姐妹","父","配偶","兄弟","父","兄弟","子","兄弟","女","父","女","社交关系","子","兄弟","姐妹","姐妹","社交关系","母","姐妹","姐妹","父","子","社交关系","兄弟","姐妹","父","姐妹","兄弟","子","兄弟","母","母","父","子","女","社交关系","社交关系","子","父","母","配偶","父","女","配偶","姐妹","子","社交关系","兄弟","母","姐妹","社交关系","配偶","母","女","子","配偶","父","子","母","女","母","父","父","姐妹","子","母","姐妹","子","子","女","配偶","配偶","父","父","子","母","父","配偶","女","父","母","母","父","兄弟","父","母","社交关系","父","女"](String), 63(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["仫佬族","汉族","景颇族","彝族","裕固族","汉族","纳西族","乌孜别克族","高山族","塔塔尔族","乌孜别克族","藏族","珞巴族","畲族","白族","阿昌族","怒族","基诺族","俄罗斯族","毛南族","德昂族","景颇族","鄂伦春族","土族","黎族","裕固族","佤族","佤族","畲族","普米族","德昂族","满族","鄂温克族","珞巴族","满族","白族","哈萨克族","黎族","门巴族","珞巴族","鄂温克族","裕固族","保安族","普米族","傈僳族","水族","怒族","侗族","赫哲族","德昂族","塔塔尔族","维吾尔族","彝族","达斡尔族","傈僳族","乌孜别克族","土家族","回族","畲族","塔塔尔族","京族","赫哲族","普米族","毛南族","门巴族","回族","俄罗斯族","苗族","回族","彝族","仡佬族","纳西族","珞巴族","阿昌族","汉族","阿昌族","纳西族","赫哲族","京族","羌族","布依族","撒拉族","土族","珞巴族","布朗族","达斡尔族","仫佬族","布依族","高山族","塔塔尔族","保安族","回族","东乡族","塔塔尔族","珞巴族","柯尔克孜族","赫哲族","锡伯族","乌孜别克族","仫佬族"](String), 64(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["丧偶","离异","已婚","未婚","已婚","已婚","未婚","离异","离异","已婚","离异","未婚","未婚","已婚","已婚","已婚","未婚","丧偶","未婚","已婚","离异","已婚","丧偶","丧偶","丧偶","未婚","离异","离异","丧偶","离异","离异","未婚","离异","离异","未婚","丧偶","离异","丧偶","未婚","未婚","未婚","已婚","丧偶","丧偶","离异","离异","已婚","已婚","离异","已婚","丧偶","已婚","未婚","丧偶","已婚","丧偶","未婚","未婚","未婚","已婚","丧偶","离异","离异","已婚","未婚","未婚","已婚","未婚","离异","已婚","已婚","丧偶","未婚","丧偶","丧偶","离异","未婚","丧偶","未婚","未婚","未婚","离异","离异","丧偶","丧偶","丧偶","未婚","丧偶","已婚","离异","丧偶","离异","丧偶","离异","已婚","未婚","已婚","丧偶","已婚","丧偶"](String), 65(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["高管人员","业务经办人","高管人员","财务负责人","高管人员","一般雇员","法定代表人","财务负责人","财务负责人","财务负责人","高管人员","法定代表人","法定代表人","业务经办人","高管人员","高管人员","法定代表人","一般雇员","法定代表人","业务经办人","一般雇员","一般雇员","法定代表人","财务负责人","财务负责人","法定代表人","财务负责人","业务经办人","财务负责人","法定代表人","法定代表人","财务负责人","高管人员","法定代表人","业务经办人","业务经办人","高管人员","法定代表人","高管人员","高管人员","财务负责人","法定代表人","财务负责人","高管人员","法定代表人","财务负责人","一般雇员","财务负责人","法定代表人","财务负责人","法定代表人","业务经办人","一般雇员","高管人员","法定代表人","一般雇员","财务负责人","一般雇员","法定代表人","一般雇员","法定代表人","财务负责人","业务经办人","一般雇员","法定代表人","财务负责人","高管人员","法定代表人","业务经办人","业务经办人","法定代表人","高管人员","高管人员","一般雇员","法定代表人","财务负责人","一般雇员","高管人员","财务负责人","高管人员","业务经办人","财务负责人","法定代表人","一般雇员","业务经办人","一般雇员","高管人员","一般雇员","法定代表人","业务经办人","法定代表人","高管人员","一般雇员","财务负责人","财务负责人","一般雇员","高管人员","一般雇员","法定代表人","高管人员"](String), 66(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["财产险","财产险","人身险","人身险","财产险","财产险","人身险","财产险","财产险","人身险","财产险","财产险","财产险","人身险","财产险","财产险","财产险","财产险","人身险","人身险","财产险","财产险","财产险","人身险","财产险","财产险","财产险","人身险","财产险","人身险","财产险","人身险","财产险","人身险","人身险","财产险","人身险","财产险","人身险","财产险","人身险","人身险","人身险","人身险","人身险","人身险","人身险","人身险","财产险","人身险","人身险","财产险","人身险","人身险","财产险","财产险","人身险","人身险","财产险","财产险","财产险","财产险","人身险","财产险","人身险","人身险","人身险","人身险","人身险","人身险","人身险","财产险","财产险","人身险","财产险","人身险","财产险","财产险","人身险","财产险","财产险","人身险","人身险","财产险","财产险","人身险","财产险","财产险","人身险","财产险","财产险","财产险","财产险","人身险","人身险","财产险","人身险","财产险","人身险","财产险"](String), 67(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["临分","临分","合约","临分","临分","临分","合约","临分","合约","临分","合约","合约","临分","合约","临分","合约","临分","临分","合约","临分","临分","合约","合约","临分","合约","合约","合约","临分","合约","合约","临分","合约","临分","合约","临分","临分","合约","合约","临分","临分","合约","合约","合约","临分","临分","合约","合约","合约","临分","合约","临分","临分","合约","临分","临分","临分","临分","合约","临分","临分","临分","合约","合约","合约","合约","合约","临分","临分","合约","临分","临分","合约","合约","临分","合约","临分","合约","临分","临分","临分","合约","临分","合约","临分","合约","临分","合约","临分","临分","合约","临分","合约","合约","合约","临分","临分","合约","临分","合约","临分"](String), 68(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["比例","比例","非比例","比例","非比例","比例","比例","非比例","非比例","非比例","非比例","比例","比例","比例","非比例","比例","非比例","比例","比例","比例","比例","非比例","比例","比例","非比例","比例","比例","比例","比例","比例","非比例","非比例","非比例","比例","非比例","比例","非比例","非比例","比例","比例","非比例","比例","比例","比例","比例","非比例","非比例","非比例","非比例","比例","比例","非比例","比例","非比例","非比例","比例","比例","非比例","比例","非比例","非比例","非比例","比例","比例","非比例","非比例","非比例","比例","比例","比例","非比例","非比例","非比例","比例","非比例","非比例","非比例","比例","比例","比例","比例","比例","非比例","比例","非比例","比例","非比例","非比例","比例","比例","比例","非比例","非比例","比例","比例","比例","非比例","比例","非比例","比例"](String), 69(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["3-Jan","Oct-73","Jan-93","May-81","Feb-71","16-Jun","Apr-91","17-Dec","Nov-76","15-Aug","17-Mar","19-Apr","Oct-76","Dec-73","Mar-87","Oct-88","Jan-89","Apr-81","Feb-92","Feb-76","2-Feb","Jul-78","14-Jul","15-Aug","Nov-88","5-Jul","Feb-93","May-96","7-Jan","11-Oct","18-Jan","Dec-88","8-Apr","May-93","Mar-92","8-Aug","Oct-70","Dec-85","3-Apr","Jun-74","10-Feb","Aug-83","Mar-75","3-Aug","Mar-86","10-Nov","5-Sep","Apr-78","4-Mar","Jul-76","Oct-74","Nov-92","6-May","14-Oct","Sep-71","9-Apr","Oct-72","Oct-87","7-Apr","15-May","12-Jan","8-Dec","14-Aug","Sep-88","15-Jan","2-Aug","Dec-94","Apr-94","Jul-85","2-Feb","Aug-82","Jul-71","Aug-81","Apr-76","Nov-98","Dec-78","7-Mar","Nov-87","4-Jan","Mar-84","Aug-87","Jan-70","16-Aug","8-Jul","21-Dec","1-Jun","9-Jul","Aug-99","May-76","9-Mar","4-May","5-Sep","14-Feb","19-Aug","Nov-86","Jul-78","Dec-96","Apr-95","9-Sep","3-Jan"](String), 70(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["J2Oq8ABCuShD1MIDy","mNr5NzqLD9wsk4pdt","lEiZJi48omD9eIA5C","ORDwERQyLlW0jV7cD","9ElP4k1gG3DkkjAN3","NilhpAecUkfNRZ1nk","im7V9Ew0A0lxILFpw","ZrvuKawYjWbkaVWim","jXk87hzLccd4TZzfO","EPPQgMrwSzppgXGzc","ML59Nx9gOQ3BkS1B4","N7ifywoolaGEArdoh","04h3BzjNpSeEGCDGN","xyTwgIk9mlb5Fr33E","weAI5IpOtWkPdgYTG","l0Hv0Babm5rAi0D4f","joYaBcZW1gQhDxJCG","F1DYflDx8WBCK56sv","BOltM5aMfkbPLg2TL","cavba9M3eoCEqEorF","Y0zHQ0oDct1yL7QPn","wcg52NupnHxL0YgIZ","hESaZm3QUmLM7K3tK","Nwqo5es6qUjGHsxjL","E4A4WGKTTz908TZ1c","Pos7E33J3MDWNxQ61","m5sErXNpvvjS0yPhG","CynimvOK3yjnbimow","XaWKxzTult3n83BBr","95ixOofRaEl227kpZ","2EHBbOBwvnQEEolJn","wfKqXfQM6STeHU9t6","ineDmbtli7EOEQm2o","kuAj4QHQnPyzy2TbO","2D4iKoNR2obBNyg5V","xhKo1DuqdbzpLBnBr","MllHPgQiyaQ1NgATL","ugS96YM6r4Eor147n","ZCAsR4KhAC3vI3Zct","PZ66Hm6oBXM6jh79R","uYoi7iakf9Uq5BbvU","5eCiwmHzZLq9ARtzG","fYjEH5pFaJ7WaiPKG","ov9p2Vb1Qhyp25RcL","lBbomAhueLJ1xVWiK","5IRxqO41Q5g3PGA3o","aeggztfmp9StD1YFi","EdpBjeJwNpUuslPl9","RNkzEc0l8XC9Xygyx","04n3eNxGjTodYpPZN","1ZmXe6UYlfHgGvYyc","Wdk7N2Aa9pLCqLbW6","Qinv7LlPwvvjPK5zT","DcU4FoKPlYcynKMEq","zSy9BJH3Tgg6ZkKJ4","ou7UoGu71F8YJTH0B","chDHxxGd2q7QRX3XC","mJf43b1gvO46hi3pK","GJ5WXg7RZ9jjYnOl2","nu0ZQkG2qIW9ci5VP","BeDzygfMVZgCmz4pf","Zk8zyPRuAnH9l7FB8","U2J84ILSwimjn8ixM","cYrlNTfqFdqI3c4ud","ozbd2UCebx4l7YWKu","cvGybaxso4akUtf7N","JYWGjIklslZ2PDypV","3ZsdELEAVE0o0ofrB","yFb1znqn6RjkbVd4Z","FeoRe7EAczFUSFTKy","Q1wFYQJaHvuzRyZmK","yXrLEN3gbr7GkAPc0","leyVEELZrDafAK8xs","72XmXp3gorQbGmfT1","QFAXqWcppinHluHfh","roBlRINqZabMnlysK","ATQjUDBrQv9QneHND","PmWp1xjoTqdGsBKZT","Edth3QPz113WB7dH6","gEEteY5PMKhevSlC3","Vxyi5wChm6dpQclxn","s1Hd6oheTTYU9m0yJ","7JAZo2ekeuqycqf2N","23qtY0I9qXzbNRUEm","MLxTpV7dlKPryO9PO","PlWVs8VFx6j66oAo0","yGmH2sbpqpi0HIGJB","JcniUPt7qxdGaOTqz","cd4t3zc1Uk5937L7u","V6glHvz3PeEMYlMce","aj7Vnbwnb91dFq9u3","OyansvpykAPvbuxHD","uV9spCwcn18zgftsh","kNUmQ5BDdEKJSQ08y","KnZ1uNL8S4jbN9hey","CXEBC7oiFA6PSOdGB","1lzudsFXOkzPl7teh","7T8RMuf77bM18Rjiv","BvqyzZqdYnKXy3bUx","J2Oq8ABCuShD1MIDy"](String), 71(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["少先队员","党员","党员","党员","党员","少先队员","少先队员","党员","少先队员","团员","党员","团员","团员","团员","团员","党员","少先队员","团员","党员","党员","团员","党员","党员","团员","少先队员","团员","党员","少先队员","少先队员","少先队员","团员","党员","党员","团员","党员","少先队员","团员","党员","少先队员","少先队员","党员","党员","党员","少先队员","少先队员","团员","党员","团员","团员","党员","党员","少先队员","少先队员","党员","团员","团员","少先队员","少先队员","团员","团员","党员","少先队员","团员","党员","少先队员","少先队员","少先队员","少先队员","少先队员","少先队员","团员","少先队员","少先队员","少先队员","党员","团员","党员","党员","党员","少先队员","少先队员","团员","党员","少先队员","党员","团员","党员","少先队员","党员","团员","党员","团员","少先队员","团员","团员","党员","少先队员","少先队员","少先队员","少先队员"](String), 72(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["小学","大专","博士研究生","大专","小学","中学","中学","本科","硕士研究生","中学","本科","硕士研究生","博士研究生","本科","博士研究生","中学","博士研究生","硕士研究生","博士研究生","大专","博士研究生","硕士研究生","本科","本科","博士研究生","小学","大专","小学","大专","硕士研究生","本科","大专","小学","小学","硕士研究生","硕士研究生","本科","小学","硕士研究生","小学","硕士研究生","博士研究生","大专","本科","小学","中学","博士研究生","博士研究生","本科","中学","本科","硕士研究生","本科","博士研究生","本科","大专","博士研究生","博士研究生","博士研究生","小学","硕士研究生","小学","大专","硕士研究生","大专","本科","中学","大专","大专","小学","大专","本科","中学","中学","硕士研究生","本科","本科","博士研究生","本科","中学","博士研究生","中学","大专","大专","博士研究生","中学","小学","大专","本科","硕士研究生","中学","硕士研究生","本科","本科","博士研究生","硕士研究生","本科","硕士研究生","小学","小学"](String), 73(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["南开大学","北京理工大学","华中科技大学","中国人民大学","北京理工大学","清华大学","浙江大学","山东大学","哈尔滨工业大学","北京理工大学","中国人民大学","北京大学","清华大学","南开大学","浙江大学","中国科学技术大学","哈尔滨工业大学","天津大学","南京大学","东南大学","湖南大学","中国海洋大学","北京大学","中国海洋大学","武汉大学","湖南大学","天津大学","吉林大学","哈尔滨工业大学","湖南大学","中国海洋大学","北京理工大学","中国海洋大学","厦门大学","中国科学技术大学","东南大学","南京大学","浙江大学","北京理工大学","武汉大学","中国科学技术大学","清华大学","武汉大学","北京大学","哈尔滨工业大学","浙江大学","武汉大学","北京大学","北京理工大学","吉林大学","东南大学","东南大学","北京理工大学","厦门大学","哈尔滨工业大学","浙江大学","复旦大学","复旦大学","中国人民大学","武汉大学","山东大学","天津大学","华中科技大学","南京大学","西安交通大学","清华大学","西安交通大学","山东大学","吉林大学","浙江大学","中国海洋大学","南开大学","哈尔滨工业大学","复旦大学","厦门大学","东南大学","北京大学","北京理工大学","中国人民大学","南开大学","东南大学","天津大学","上海交通大学","吉林大学","华中科技大学","北京大学","北京理工大学","北京大学","吉林大学","山东大学","东南大学","武汉大学","南开大学","北京理工大学","厦门大学","山东大学","清华大学","复旦大学","中国科学技术大学","南开大学"](String), 74(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["6.33E+17","3.31E+17","4.02E+17","4.25E+17","7.51E+17","2.18E+17","5.16E+17","32437723770920798x","7.33E+17","4.10E+17","3.49E+17","6.46E+17","26070419211110926x","6.71E+17","3.74E+17","9.28E+17","2.38E+17","4.05E+17","8.22E+17","8.64E+17","5.63E+17","1.98E+17","98296524231130598X","6.90E+17","5.87E+17","3.09E+17","5.76E+17","43587919990210875X","2.40E+17","1.87E+17","3.88E+17","9.33E+17","4.92E+17","4.43E+17","4.46E+17","2.00E+17","7.59E+17","1.91E+17","87062418351010086x","66116819731025280X","9.25E+17","3.88E+17","7.15E+17","6.88E+17","3.70E+17","24680618651120371x","8.13E+17","3.46E+17","8.14E+17","8.96E+17","2.30E+17","7.99E+17","2.46E+17","8.95E+17","98221819381030990x","6.41E+17","3.09E+17","96174539901215401X","4.22E+17","2.91E+17","3.02E+17","5.78E+17","9.45E+17","6.15E+17","4.17E+17","4.05E+17","5.76E+17","29991918420730654X","1.63E+17","2.65E+17","5.83E+17","3.45E+17","8.85E+17","2.76E+17","5.09E+17","8.32E+17","48289523500730578X","3.07E+17","95338519400520629X","5.17E+17","5.35E+17","2.97E+17","3.43E+17","9.03E+17","67158018420509518x","7.54E+17","2.37E+17","4.27E+17","3.07E+17","6.83E+17","28369419381110857x","5.23E+17","2.91E+17","41457119950830276x","8.43E+17","3.36E+17","2.48E+17","2.74E+17","8.64E+17","6.33E+17"](String), 75(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["中国农工民主党","中国国民党革命委员会","中国农工民主党","台湾民主自治同盟","九三学社","中国国民党革命委员会","中国致公党","中国农工民主党","中国民主促进会","中国农工民主党","台湾民主自治同盟","中国民主同盟","中国民主促进会","九三学社","中国农工民主党","中国致公党","中国民主建国会","中国民主促进会","中国国民党革命委员会","九三学社","中国致公党","中国民主同盟","中国民主建国会","中国民主同盟","中国民主促进会","中国致公党","中国农工民主党","中国致公党","中国国民党革命委员会","中国农工民主党","中国民主促进会","中国民主建国会","中国致公党","中国民主同盟","九三学社","中国农工民主党","中国国民党革命委员会","中国民主促进会","中国民主同盟","中国民主建国会","中国农工民主党","中国民主同盟","中国民主同盟","中国民主促进会","中国致公党","中国国民党革命委员会","中国民主建国会","中国民主促进会","中国民主同盟","九三学社","九三学社","中国民主促进会","中国民主建国会","中国致公党","中国农工民主党","九三学社","中国民主同盟","中国民主建国会","中国民主同盟","中国民主促进会","中国国民党革命委员会","台湾民主自治同盟","台湾民主自治同盟","中国民主同盟","中国民主建国会","中国致公党","九三学社","中国农工民主党","中国农工民主党","中国民主促进会","九三学社","中国致公党","中国农工民主党","九三学社","中国国民党革命委员会","台湾民主自治同盟","九三学社","中国民主促进会","中国民主同盟","中国民主同盟","中国民主同盟","中国民主同盟","中国致公党","中国民主建国会","中国民主促进会","中国民主促进会","台湾民主自治同盟","台湾民主自治同盟","九三学社","中国农工民主党","中国民主建国会","中国民主促进会","中国农工民主党","中国国民党革命委员会","中国致公党","中国农工民主党","台湾民主自治同盟","九三学社","台湾民主自治同盟","中国农工民主党"](String), 76(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["港元","日元","欧元","台币","人民币","台币","日元","台币","人民币","日元","欧元","人民币","港元","欧元","人民币","港元","日元","美元","港元","美元","台币","台币","日元","台币","日元","美元","美元","台币","日元","台币","美元","日元","美元","台币","日元","港元","台币","台币","日元","欧元","美元","欧元","港元","人民币","日元","港元","欧元","人民币","人民币","美元","台币","欧元","港元","日元","港元","美元","台币","人民币","人民币","欧元","人民币","港元","美元","美元","美元","美元","欧元","港元","日元","台币","人民币","日元","美元","欧元","欧元","台币","港元","台币","台币","日元","人民币","美元","美元","欧元","人民币","日元","欧元","台币","日元","港元","港元","欧元","人民币","美元","港元","港元","港元","港元","美元","港元"](String), 77(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["家族企业","家族企业","家族企业","家族企业","家族企业","集团关系","户持股情况","家族企业","家族企业","集团关系","家族企业","户持股情况","家族企业","户持股情况","集团关系","集团关系","家族企业","家族企业","家族企业","家族企业","集团关系","户持股情况","集团关系","家族企业","家族企业","家族企业","户持股情况","户持股情况","家族企业","户持股情况","集团关系","户持股情况","户持股情况","家族企业","户持股情况","集团关系","户持股情况","集团关系","集团关系","家族企业","家族企业","家族企业","户持股情况","家族企业","户持股情况","家族企业","户持股情况","家族企业","户持股情况","家族企业","户持股情况","集团关系","集团关系","集团关系","集团关系","户持股情况","户持股情况","家族企业","家族企业","家族企业","家族企业","家族企业","集团关系","集团关系","家族企业","家族企业","户持股情况","户持股情况","户持股情况","家族企业","家族企业","集团关系","集团关系","集团关系","集团关系","家族企业","家族企业","家族企业","集团关系","家族企业","集团关系","户持股情况","户持股情况","户持股情况","家族企业","集团关系","集团关系","户持股情况","户持股情况","家族企业","家族企业","户持股情况","集团关系","集团关系","集团关系","家族企业","户持股情况","家族企业","家族企业","家族企业"](String), 78(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["5.05E+17","1.36E+17","2.37E+17","1.95E+17","4.34E+17","2.56E+17","3.49E+17","59990439271031118x","9.04E+17","6.05E+17","76130619741031850X","3.55E+17","75655037970824514x","1.51E+17","4.25E+17","6.13E+17","2.38E+17","2.96E+17","58400619651102252x","6.69E+17","3.20E+17","4.69E+17","6.09E+17","3.31E+17","68530527380330463x","5.36E+17","3.43E+17","8.97E+17","5.99E+17","1.34E+17","36997418910505286x","9.40E+17","1.01E+17","1.83E+17","73380132571210589X","3.72E+17","73265134991025995X","17534719911031847X","2.06E+17","37348018140330843X","7.47E+17","8.15E+17","6.22E+17","3.21E+17","9.67E+17","9.32E+17","9.97E+17","3.65E+17","1.42E+17","4.06E+17","42055633140320138x","4.27E+17","9.52E+17","66606019951220248X","4.29E+17","6.77E+17","5.91E+17","57397219110210533X","2.03E+17","57462120660730453X","4.34E+17","2.73E+17","8.58E+17","4.36E+17","2.15E+17","1.01E+17","59029231200631977X","5.51E+17","3.48E+17","9.61E+17","7.08E+17","3.37E+17","24559319250714497X","4.89E+17","1.35E+17","1.92E+17","7.89E+17","3.20E+17","5.10E+17","1.81E+17","3.00E+17","5.06E+17","6.57E+17","4.75E+17","9.43E+17","1.76E+17","7.04E+17","7.35E+17","9.57E+17","38687419880131076X","4.51E+17","6.26E+17","1.70E+17","5.60E+17","6.23E+17","5.64E+17","7.09E+17","9.65E+17","4.38E+17","5.05E+17"](String), 79(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["桂W1SLKT","新VTAL41","湘DW8xL2","湘KYLT5x","苏W1PLBN","琼L1QQ5U","皖S2CQ54","琼JL5G7Z","甘B24MY8","皖RA3PUX","藏QEK7xA","皖C8NXM5","浙RVYSx8","吉ADMJBR","鄂LP7ETD","黑CSD79N","宁BJK4MS","藏X5SW5L","宁L6X347","贵QJ1R3E","鄂FPK7HA","豫GUFP3U","陕JG1BGX","甘D9GY1W","桂C54HZE","陕EB4M6H","蒙F3X1RA","鄂VTC23E","辽JMLG3U","贵RNVUN3","京W6DLES","皖QCL6GW","川HH4RQJ","辽HEB070","甘U626DT","晋CGR6BC","鄂QWV3CU","湘QLUB9T","陕BWKxK6","冀YV5H26","藏MTDJQN","桂GFYDBS","皖KV2X7P","冀HW5B1J","津UXZKMV","浙L1MCWW","渝FCGVAY","津PM8S7R","赣XY2XDQ","藏KN5AT4","京STUHPx","蒙HK0NZx","晋VWS9AE","皖S127PV","皖RQVFXB","青AU4HNU","新WM5U4G","豫T0T46x","津VQERES","贵KQ99WY","湘GC3P90","琼QHTADN","青AYWCH0","津Q7UU68","陕WCANWP","云CNZALG","粤JP221H","鲁J6NRKH","新JX9JE1","川JNKAD4","宁LTHD35","晋DS9K67","赣S99Q6C","鄂HYH2SW","冀D40WNU","鲁PS4ZW4","新P5Q30T","蒙NxY9RC","黑Y6Q9BW","贵KQB4BP","鄂VQMVEP","藏MNJ407","冀QJAYJX","宁SEL02V","浙CX0KY7","湘YZYNEX","苏CFC7PS","新QZKAFY","苏SQ1xRG","津EMSxEX","辽VVGB9M","辽EH5NN9","晋Y6YUUA","吉YXDW7L","桂SG2RLS","湘B3LJLW","新S89KU4","苏GJDM99","豫TW7LS3","桂W1SLKT"](String), 80(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["甘U88081F","粤Y179532","陕U8Y2131","冀C3U132D","京E5W4703","吉B22068F","陕X627972","蒙C826057","甘T5P9513","桂R5W1395","渝D54497F","苏J31924F","豫H980671","贵G1K3571","蒙V5R3098","陕TFL7313","苏D942594","藏T62323D","晋N3Y0478","鲁G1A296D","青K8N270F","琼L8N969F","苏TDP192D","藏KFN4344","渝V9G5351","粤W4C926F","琼U5B5848","苏D165815","浙Q167882","琼R246382","闽X9K7618","宁P3C3859","川D8L0738","贵R82166F","赣Q7T1986","吉Y552507","津NDY5181","黑AFY4544","宁W5C5446","冀K850182","鄂T248072","闽W9Q0176","沪W9K791D","冀G2U2505","鲁Q973854","黑Y770958","琼F4G1718","宁AFM9053","鄂U2F901F","湘B6M644F","鲁W47119F","吉B450218","桂U9M924F","甘G7R3294","桂D3P7343","桂W1S0865","鄂B1P0021","粤L72927D","鄂E5H7729","鲁W6W4206","冀D8Z8162","川D950748","黑EDJ3949","津D1R8256","津B711668","京EFA2749","豫W7V1277","桂X3H6732","蒙G441784","贵J3U8824","甘W2X6096","豫G3V9004","京ED21609","川S1Y3128","吉R2J2469","闽K1S2877","桂E8K1552","黑EF3181D","甘V2A7977","赣V4Y4995","粤C7R3566","宁DDR784D","京Y3Y3599","豫V927068","沪FD63355","吉E5M2867","辽U1J877D","津RD36657","津S119372","宁X947114","云K4V3637","新W3Y8244","湘T5W8739","津F1X0641","黑K9X7848","浙U6T1214","湘L451488","云Q8G5438","鲁D4N057D","甘U88081F"](String), 81(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["吉GFSxUM","沪VFXHFA","琼NDGM3H","藏KMJXJ7","辽E67CY4","宁JAHEYV","冀N3HJR2","陕LU219D","鄂N6Bx7L","冀ExH5DH","沪C2VLEA","京H1MZ0Q","渝RT7EGP","渝ELP8T0","苏BLZD9B","津PVxW77","晋FDYExx","京LRGGNH","闽TWERV6","吉DJ9K0Q","宁PAxYU3","晋HEFTCF","渝VBNQEU","青J7Y8D1","皖M1XxJV","湘TT0CPQ","皖WLNMB0","渝EKKK8S","鄂DZ6LVY","鄂D71W4J","川PCQ7Gx","渝R9YBF8","陕U7ZFJN","黑EX93WL","蒙KxBJPF","京HJ3LP7","桂Y8NBP7","闽NU4YZJ","吉Y6xL4C","藏YALAWZ","川GU1MAN","晋RJBJZM","藏HVNLXU","晋SWUDSG","陕GYD8SK","豫UFxTW9","冀PV56J3","皖P68RD9","湘H2DC6F","京ANxQHG","甘DFPJP0","贵UMZEN2","冀M5X2VP","藏B0SAY9","湘SG2xBM","粤MMXGGx","云JZU0RF","粤N3MJ84","冀BTLxBD","琼WJxxZD","贵XTx2LK","鲁KD13GN","吉J8CYUY","蒙ACA1QH","新RA7L4L","湘RVWQVQ","青FYX607","云XHJCYN","蒙JTJ5P7","藏QxZNHL","黑NC1F0U","湘PPEEXH","湘RTRGX6","京AJA0KZ","桂FHG4xM","陕R98J1x","苏XQMS03","鄂F3B0CB","琼X4V38H","粤RKPWCS","冀A1BB3G","藏J6GZGM","琼PV0G7Y","皖MJ0G9M","甘BH3AW2","宁Q8TVxC","皖KL0AMP","鄂EDU1LJ","辽KP1CJT","皖BWLMJW","浙SMJWF1","贵WPLJ5W","湘B80NB3","鄂K73EWD","渝MRQ9V9","辽B3FMGB","陕WVMKBQ","津KTZAEX","黑WYQBA1","吉GFSxUM"](String), 82(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["2021/10/28 1:20","2021/10/11 23:40","2021/10/11 2:34","2021/10/4 3:36","2021/10/25 4:00","2021/10/16 10:20","2021/10/22 17:06","2021/10/31 1:36","2021/10/8 1:56","2021/10/21 0:53","2021/10/21 8:55","2021/10/18 15:46","2021/10/19 11:38","2021/10/5 22:35","2021/10/31 5:55","2021/10/28 19:13","2021/10/17 4:35","2021/10/24 21:26","2021/10/2 22:18","2021/10/26 23:41","2021/10/28 14:48","2021/10/5 10:56","2021/10/7 15:18","2021/10/2 15:21","2021/10/18 18:17","2021/10/27 5:29","2021/10/7 11:46","2021/10/11 22:44","2021/10/27 13:30","2021/10/9 5:45","2021/10/17 17:35","2021/10/23 18:18","2021/10/6 23:10","2021/10/4 19:12","2021/10/14 3:17","2021/10/10 7:05","2021/10/9 21:55","2021/10/17 14:37","2021/10/22 15:32","2021/10/27 12:20","2021/10/5 4:46","2021/10/25 1:18","2021/10/4 13:31","2021/10/24 13:55","2021/10/10 4:11","2021/10/14 20:13","2021/10/25 3:30","2021/10/26 20:02","2021/10/24 18:40","2021/10/11 23:37","2021/10/27 3:26","2021/10/15 11:05","2021/10/2 12:12","2021/10/15 17:01","2021/10/9 16:53","2021/10/19 18:21","2021/10/19 23:16","2021/10/20 11:10","2021/10/31 9:55","2021/10/28 14:03","2021/10/26 8:55","2021/10/26 19:32","2021/10/26 8:28","2021/10/27 11:42","2021/10/30 12:01","2021/10/31 13:29","2021/10/3 7:14","2021/11/1 10:40","2021/10/11 8:18","2021/10/16 19:52","2021/10/25 14:22","2021/10/15 20:47","2021/10/31 17:10","2021/10/30 9:11","2021/10/26 22:57","2021/10/4 17:37","2021/10/8 4:11","2021/10/16 7:34","2021/10/12 4:43","2021/10/3 9:31","2021/10/29 8:01","2021/10/28 12:01","2021/10/14 6:02","2021/10/10 22:49","2021/10/8 11:26","2021/10/22 9:37","2021/10/5 18:00","2021/11/1 1:44","2021/10/18 8:02","2021/10/10 9:45","2021/10/2 13:49","2021/10/5 21:45","2021/10/17 8:13","2021/10/15 5:12","2021/10/30 13:35","2021/10/11 10:49","2021/10/23 3:15","2021/10/6 6:37","2021/10/7 16:47","2021/10/28 1:20"](String), 83(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["173.70.57.175","197.229.152.132","16.97.42.255","17.129.217.152","182.213.11.233","103.41.225.205","167.58.241.118","143.254.82.134","55.57.188.175","78.81.92.112","141.217.47.142","172.231.217.121","102.185.121.60","79.70.127.207","35.92.95.82","142.93.218.95","47.45.52.107","153.200.88.59","166.234.142.12","84.4.8.216","175.215.33.140","135.45.6.218","20.203.207.104","40.229.15.178","175.252.234.124","200.237.146.224","136.8.52.202","53.205.190.14","42.75.33.159","154.99.197.216","179.221.112.148","208.95.63.187","120.201.20.253","121.136.34.109","196.45.164.154","70.181.63.89","165.19.119.211","24.123.14.97","135.109.139.233","88.65.211.101","70.231.222.29","26.137.218.235","117.187.62.3","33.230.31.136","51.109.1.186","28.232.47.153","26.122.200.184","77.164.151.183","119.226.70.17","195.242.137.90","37.111.163.50","34.214.13.43","189.62.34.81","147.108.77.61","221.230.74.7","180.102.113.216","88.160.179.135","39.117.104.165","134.170.123.41","98.230.87.33","83.143.246.177","16.231.19.119","104.30.98.242","75.137.186.223","198.141.188.104","166.123.55.173","154.93.146.56","59.58.213.205","189.251.56.148","17.124.101.38","216.144.123.235","11.113.31.121","54.100.58.244","132.54.186.128","114.89.4.85","65.219.71.224","42.140.153.252","61.110.79.121","133.105.192.59","134.41.166.237","93.186.2.133","68.170.174.249","54.132.27.188","114.207.41.64","25.79.137.82","7.118.142.126","53.7.71.168","187.152.83.255","201.28.247.133","21.82.211.229","184.178.167.188","213.81.108.104","5.124.168.178","44.131.31.174","150.15.26.30","70.13.29.188","14.91.237.44","194.49.170.70","142.241.163.54","173.70.57.175"](String), 84(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["be1d:279c:e5fb:1f82:5e28:5114:fe50:8a0f","dbd2:c83e:5150:92d5:f6f7:1360:586d:ea7d","6ccd:57de:8132:2fc0:195d:1c6d:1f1c:51b7","877a:ad0:cde4:ff0:b305:f56c:14ac:e782","438a:9c5e:9653:55cb:9084:91f0:3da7:687b","b897:ec28:2061:4e19:90ae:36b4:bb52:65a4","e17e:f47a:99a4:da0:265b:9fb6:b59a:189e","4461:6055:b9ee:9311:fc27:2823:10c7:d005","7917:46a0:7c59:5c9b:8947:9b46:50ce:94b7","43d2:3c0b:45a8:d783:5445:1551:b605:fa0a","67f5:985e:b081:bd66:d2b3:cc58:73d2:6a70","f0eb:617b:f6e8:89e9:5853:40aa:4da3:79","2314:d1b7:4081:9362:2394:1bb9:bcd0:c31f","fe62:c0a4:f9d1:5fac:c95d:a12a:4abf:b8db","3ece:d352:ab95:8b74:1f87:c163:29de:ca2f","10a9:5cbf:8300:24a9:8192:ce85:17a2:bd59","dbf4:ecbf:a307:1b04:8944:dc73:3c5c:f57b","8a69:7250:c208:3793:d169:a47b:ae51:8c06","8706:a17f:e352:7890:1d21:da02:d720:2bb0","3b51:cd2b:d3be:815:6318:304c:65c8:8c01","fc2f:34c6:2172:d28:1a40:1113:d9ec:e886","e9a9:24fc:b129:3b31:5251:7b4b:ead6:bb57","79d6:2e5e:2532:4ba5:920:cd6c:9d06:db0f","3c51:f5c0:331d:53de:bf53:3d71:448:dae8","dccf:7969:f437:8313:2373:282c:a40f:d460","6366:ad50:8f11:e127:dbe2:e35e:dec0:fa28","88cc:907e:efeb:6fc:eb34:a11a:80ea:f570","611:2d35:d145:ee15:2741:769b:5ac8:8f51","72c5:8d24:a077:4f3b:5e60:cc52:cdf9:d672","16b2:c27f:8a68:6975:5564:5ffa:d102:e6e9","2c:e8c:95a9:71f8:dc95:f82e:b011:a1e","3b26:3086:8a9d:a304:fda:bfc2:161b:e9b1","94da:f16e:374a:29b2:c2e0:5618:6ab6:ad6","acee:f7ab:8ed7:9e30:64c:b145:69a9:b56a","b3d1:5d8c:6d54:5dc1:aefd:e6e0:d4c2:7d3e","1779:fdc9:7482:778b:7bf9:405b:974d:5956","e560:974d:44a:3e98:2fe5:e15:d03e:f222","162e:87a0:ffbc:8b4a:9528:b444:f1f3:bcf8","519d:a17:3dc5:f818:bbfb:d179:27ac:2647","6ce2:dd4d:bbef:42a0:7004:7c95:5fc:d3d1","924d:1f49:54fb:7c80:f1a6:e047:1b35:87b8","56c4:a66d:8bb9:abf3:266d:619c:8fac:27ac","8503:9701:fde:b426:6800:77f2:b122:7a8c","8ba6:f39a:ce8d:b864:e023:7daa:2c62:34a6","676c:66c7:70dc:ee0:e10c:4b82:722b:8180","7ac2:46b1:5585:255d:1ecb:c590:82f8:83b2","de6:c5d5:6dd5:3f2c:61e3:8fa4:81a8:5eed","6d37:263:c178:9848:7e05:d63a:ec7a:ebac","5112:9fda:dd3f:eb73:617e:7213:932a:4923","4cf0:5459:84db:eb53:cc6e:72e5:dd07:9911","3c61:7dc0:b591:5dc:d6c:a8b3:7cb7:ce60","a6ac:563d:99fd:2d61:96f3:8a26:cbf7:c210","49c6:574c:9148:ba4c:7af8:fdec:d443:27f9","2d0a:9b08:ec3c:de23:1046:d85b:1cbb:2c50","ed3c:4148:e367:f483:9035:9560:a03b:a33d","eead:8c3f:bb79:f668:e33f:8c0c:893d:81f8","ce69:496e:798e:e8c9:a22:8f4e:c581:4e18","df6:5411:6549:27f9:d999:20b0:61de:d6c6","1ede:babc:d936:60be:6403:f952:82c2:55b3","d99a:6b7c:cc30:6e1a:8a71:52ab:e512:b7db","269a:5cea:83cf:1507:749e:e3dd:b242:2ac1","7ff7:6f8:f14c:5683:23f8:4478:49cd:2e3a","4f2a:7fb2:93c6:46b9:5ef9:2f03:63bb:9cc0","3c69:fef5:dbaa:d684:26e6:83a4:15b5:b155","96b0:afa4:4377:13e7:832f:23ab:259a:724d","7a21:72d5:cb20:c344:2b28:17c3:ab51:1168","57d2:5020:96e9:3d22:ad4f:2196:ab7:464d","f1ac:7826:ced6:8e0a:421:9258:f75:52e9","3ce9:ca7b:499c:ea3e:b44c:7337:9b64:d150","88dc:cede:8e7f:d696:31c7:63ce:4167:6ae7","cb33:4b88:82:6072:e085:a495:9437:f717","42b7:7171:2e7c:2f59:7488:53a0:b584:131a","3604:8c30:9a98:ea0a:9d93:8b2e:3457:781a","2bae:bf8b:44a6:4664:7c81:3720:3bb0:4e40","847c:eed4:4b81:cfd9:aa39:1b74:1e3e:6297","dadc:6604:c069:9e2c:d8f1:d43b:3d51:66f4","b4b5:3d34:c30d:630d:854:e612:f1c1:83bf","a8f0:e4ff:9e09:7969:c193:c202:f9aa:b554","3eb8:c505:6f22:d6a6:2c03:6210:8c:b2ad","d01d:41f8:868a:d828:6537:ce2d:844c:d2d","b44f:a9e0:ece4:7a5a:65e7:48e5:93bf:67e9","3cb5:38d5:62ef:f457:74b2:7d49:3e40:2a52","3195:8b2c:3d3a:b97d:b4c:f2ed:bd4:7f51","2842:fbe6:a473:a0e8:b258:30ff:1040:d9d2","120e:bd64:60d0:57e2:bbc8:890e:8253:2ef5","bcb9:c099:a5b3:1fd:3924:e4ec:7fff:a216","456c:48b8:c3c:edf:be71:9c5b:e977:f94f","7dc2:22f1:91a8:9448:3be3:c63:a8b2:a328","2852:da53:a50f:6230:bcd7:132c:7793:17ad","40e3:83e1:a489:e857:5cd2:e0f9:8684:d6a6","4208:9ea8:db72:5e61:a047:d987:d62c:50f4","326:b9c6:45e9:9b57:50a:e747:ea8b:cdf1","3fc3:a07e:89f0:111c:c5a:deb7:aed0:a68a","9c31:100a:b07e:b58b:3600:96e5:3d1e:aa41","a8b2:3d36:fbfb:280b:356b:7054:71b6:d675","3fb6:28f3:55d5:f532:9e63:fae1:e989:5cfc","4186:e192:bdc3:a248:9c1b:9a72:458d:88b6","66f:ff46:b358:7aa2:ccdf:609c:f94b:38de","427b:df6a:c80c:8066:f50d:b664:b3a0:d735","be1d:279c:e5fb:1f82:5e28:5114:fe50:8a0f"](String), 85(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["8a:8e:91:0d:54:e3","03:42:1b:1c:e9:6d","83:a3:bd:e9:73:99","bd:40:fd:64:d4:09","c7:ed:10:bb:e3:1b","f5:6e:a4:65:96:23","a5:39:26:a1:71:a3","d4:e9:68:65:61:f4","a9:43:a2:fb:1a:e3","93:88:10:41:05:d9","0c:4a:26:f1:9a:a9","da:10:ba:05:0f:c0","ac:64:c4:1b:39:fd","19:ae:d3:bb:40:af","f5:a7:c2:1c:20:b7","3c:f3:5a:7f:b8:5d","a1:ac:9e:cc:34:d7","54:b0:20:dd:e0:d7","75:60:50:11:12:fc","7b:2c:99:91:c7:f7","87:ad:2e:f7:52:44","00:31:62:44:50:80","32:47:1b:73:59:08","a4:d9:8e:95:56:86","e1:d1:96:69:f2:ae","51:98:ae:74:ff:04","cf:de:78:c9:67:38","7a:88:70:8e:11:15","49:e8:e8:7d:b3:32","8d:60:ed:62:26:de","65:25:70:5c:46:82","b3:25:cb:c3:20:03","b4:4c:b9:3c:aa:70","7d:92:e9:03:a2:df","da:b6:b3:f1:1a:c8","02:57:8f:09:61:07","e7:1d:7e:07:5d:ab","82:06:17:2a:fa:e7","86:f9:0c:8f:2f:13","d0:33:f6:d7:b6:34","1e:21:4c:6f:c0:12","2b:97:3a:9b:4c:dc","d0:93:44:7a:e1:cd","23:a3:d4:13:a5:ec","89:58:e2:90:cf:db","ac:af:81:38:47:9b","80:7d:50:9e:43:7b","37:22:70:85:f5:77","07:93:ef:ec:1c:7c","fb:61:5c:ea:52:97","d1:ae:7c:e1:a4:47","1c:f3:39:10:09:73","7e:a8:a1:a9:92:df","74:4a:a3:6e:17:a9","2d:d5:32:10:33:78","5d:0f:30:fd:80:4d","30:29:1e:27:ae:f6","c8:d0:9b:10:ac:0c","05:13:19:a9:76:b7","14:ef:27:da:fb:41","6d:cc:8c:42:01:15","34:3b:c5:11:5c:61","ba:0e:cb:bc:65:b4","08:7d:01:72:33:9c","92:ba:ec:6e:55:f1","f0:48:6a:3c:27:30","1a:f8:fd:80:e2:f0","4c:60:f5:96:6e:45","0d:16:57:b1:93:ae","8c:6a:da:e9:07:3a","63:12:b1:20:86:47","2e:c8:9a:3d:2b:49","4e:82:e3:f0:c8:21","0b:21:98:ef:3f:30","5a:0e:fc:4a:98:ed","2b:35:e0:24:67:71","7d:f8:1b:d5:05:61","b9:a4:1b:c8:75:c4","32:0c:01:29:a4:e7","84:08:10:76:98:8b","b6:ad:0b:4d:6c:d7","6c:80:90:be:cb:37","13:26:f1:b7:3f:26","15:5d:06:81:ff:a9","fb:d4:d9:3b:48:cd","9f:db:c7:9f:82:f0","ff:20:3c:55:60:20","63:db:c2:3c:af:0d","e1:8c:c8:2d:38:20","7a:d2:44:67:2c:e4","3b:66:c9:22:2b:ee","27:b2:ac:ef:98:15","10:04:a9:e0:1e:1c","2b:7e:48:77:d6:9c","31:53:63:44:8f:89","70:2c:f9:f3:30:4e","54:c5:a7:b8:12:26","d5:b8:62:e4:a4:aa","c3:50:17:cb:6c:65","8a:8e:91:0d:54:e3"](String), 86(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["71408522143","8357067","8597462858","3952108","3373060274","176348","975062","547791","9645133077","2716112","8823082","477487","590957376","60663","38058878796","2929088615","12626026156","438794991","47648575607","1257096259","4503070592","32538291","93187","410471729","673054","4685802399","2763373","8145013333","91040327","6463053","218770153","572363619","491771","7631273","240960","1589296639","497595334","61820465","90327522","95914","30890410244","66572","6197472","403919","1069993342","49972","72503365809","36176","42483","574931536","93364","1748002626","76228139","37097049768","968269315","2734763456","958007","314982","12948371","884149","33371601","24396145","584334217","728731159","421226","8387347","2053933","72114815","5467040","8700632","91288249700","78621","86582809173","20734746706","2173922157","22134104","6316264","78783","316080471","78437789","209785822","44612","7799455","224669379","61109","8942172883","2318649","85714632077","98021673","41306497115","28691628","214583714","5533021159","8061002","117497","51061481916","182035","733933","63997078","71408522143"](String), 87(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["Q4JRdz_","JtBFr8RW","ChAk3Yl","rCOBvX","FEe3B_Yc8Au","i5VynUSl","XV8GiY_","byc1f5FOfG1","NBcnx6qcj","BdHWV-m","EDm685HUI","bXIGg5","Cqkg2VVfX","ZRBq9baP2g","iG9_YIY","SKFNnqH7","MjAmlms","kloH8v5Wq8","novNlS6ODGB","hzIV03gc078","krmQFI","a2wTn2NSS","oHvsZn","Z8y6FGLO","oD0U_dqZjm","n3YUgkey","NlIfPekPX","LB2_ma1n","gyapBsIT","mDtO85S7xx","nAvmKq","m1mHD1","r5LZUtWN","kCpSj_D","xcxsNcQbkGE","Fg_EkLoEW_Y","OWAACaal","AZFwF5a","ig6YkpA","ZgtEEASsbm-","PUf0YW-U","sj8l2CP","nRnN2z4HN","JVseD7oH0Z","X05a7jd","vH8wdD","Ccm-JP7fFeb","LHhPOSsZ4","fBSDz1b","v840VQYA1","E_VYjvX4","XZJJbmO7WJ","X3JtxMQlf","meecXMU8Ch","ZpuhNfhh","XvxjYPh","j5kd32zc","b_sZG_epJCv","J5HYaY3","Ph9NmEvQW","bSb9cOm","UtZzxCO","yQzrk_","cDIZw66St","NCYMAGBI10g","iU7KD7sNg8","kefN4rOr66","tUG7zmdHdc","fii5cIRWx","O6TUm2k_","DgScjR","puJK16","L9XhVDu","BdOXJKAL","M5bYAYpwKg","LeIzAceMt","gma2bkTsJxQ","VUknecrKiD","jpXGtHvq-_","RMw540ed","ol-JOZ","YdnOORaxFT","ZCun6afdtNX","A8F_vkhk","Ia9Nsu","Mo9oXErWHZC","Ute8IjGwo_","fMfzzdP9Y","nxiLGN","j7wRJtaz","laSPaMLD","Je7tiPNpmr","mhu3ifHTa8C","yRHPVDb-","KtTM0tw","YFfa1rA","Osub-4ETnhx","ISqoMsQfMJY","qvDCqJGQQ","Q4JRdz_"](String), 88(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["1972/11/18","2010/7/6","1983/11/21","1994/9/19","2019/4/19","1992/11/27","2018/1/2","1980/1/8","2006/6/13","1984/10/20","1972/2/24","1986/6/16","1996/12/18","1997/4/14","1990/5/16","1993/10/28","2016/6/13","1984/7/6","1998/3/31","2016/3/15","2005/6/25","1983/9/9","1985/4/4","2007/2/25","1993/9/15","1984/6/11","1994/12/19","2012/2/17","1990/5/31","2013/8/12","2005/2/26","2010/4/8","1979/5/18","2018/12/12","1998/10/1","2019/3/9","1984/1/23","2006/11/22","2012/9/11","1977/8/25","1982/3/11","2011/8/17","2012/1/1","2021/10/5","1984/8/15","2006/11/8","2005/10/23","1980/12/30","1989/5/4","1982/1/14","2014/12/14","1978/1/22","1987/6/18","2010/8/16","1991/6/19","1986/1/2","1974/3/9","1972/11/16","2003/10/2","1999/12/24","1988/1/24","2003/2/23","2012/5/20","1980/5/9","1983/3/18","2008/8/12","2010/9/18","1980/10/23","1978/8/15","2005/3/6","2017/3/26","1991/1/14","2018/4/1","1992/1/22","2012/12/21","2014/9/17","2010/5/3","1981/4/9","2009/8/11","2020/1/20","2013/2/5","2008/12/19","1980/9/23","1974/5/21","1973/8/28","2004/2/16","2018/4/17","2007/1/22","2018/11/7","1980/10/3","1993/9/4","1979/5/9","2009/5/13","2021/3/29","1984/8/4","2004/1/23","1975/3/31","1979/1/7","1989/12/12","1972/11/18"](String), 89(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["6:21:36","1:29:27","23:32:39","3:37:27","7:25:50","15:49:37","10:55:28","16:19:53","17:31:12","23:54:53","12:19:44","11:07:22","4:19:59","17:19:05","17:58:36","16:06:09","20:19:30","11:03:00","4:51:43","9:39:07","5:22:12","3:19:09","8:46:55","21:23:52","4:06:34","5:10:50","23:03:02","6:11:23","13:08:00","7:32:55","23:27:12","14:06:55","7:31:19","6:11:26","16:37:34","9:03:47","2:47:22","21:42:18","8:07:27","23:54:53","2:15:34","22:22:28","17:17:59","6:08:00","0:19:46","17:48:21","23:24:42","11:00:09","5:54:23","8:41:27","15:12:15","14:48:38","6:24:16","4:15:01","7:44:35","23:14:23","17:54:35","5:18:02","9:49:12","16:30:26","5:27:58","10:30:29","6:22:09","6:54:08","18:18:48","16:35:23","19:34:49","19:15:54","6:58:41","7:19:48","10:04:12","5:44:17","8:16:54","1:40:07","6:22:29","3:37:40","17:23:11","18:24:37","13:37:13","3:44:43","11:41:00","19:13:55","7:40:33","13:59:00","12:29:48","16:57:18","23:56:08","16:50:10","12:35:04","11:50:00","23:38:43","22:47:18","2:36:55","21:24:10","5:33:57","13:10:06","1:40:10","4:15:29","20:40:01","6:21:36"](String), 90(Long), 6(Long), 41(Long), 2(Integer), 2025-08-25 14:44:58.758(Timestamp), ["1989/7/5","13:43:03","1989/7/25","21:47:36","8:34:48","13:04:14","2015/11/14","23:45:57","20:32:36","1983/10/23","2016/8/20","1988/5/1","2:53:16","7:51:44","1984/7/11","9:17:23","1983/9/5","2008/10/9","12:16:42","14:48:10","2000/9/17","1979/7/2","20:08:08","1970/6/1","2013/11/4","18:33:51","1989/1/26","1982/12/3","13:37:46","13:55:30","6:42:38","20:33:29","2015/9/22","1983/6/8","15:52:32","1991/4/5","1977/5/16","1971/12/27","1976/10/15","0:58:32","1981/7/7","20:14:49","1979/2/16","1998/4/13","1995/11/11","14:46:14","1972/12/13","1986/2/15","3:01:01","1991/4/4","1985/1/25","8:47:39","14:14:16","2020/12/3","14:18:52","5:17:11","2:30:49","16:55:51","1999/12/27","1983/6/10","2007/2/3","3:12:23","2011/9/10","7:57:16","15:50:14","14:33:03","1978/7/25","20:10:14","1985/6/6","4:46:32","2:15:55","2015/12/7","12:17:35","1973/10/17","2012/6/12","1988/4/20","1976/9/20","2001/4/29","2007/10/1","1980/1/22","2006/1/18","8:36:14","1971/3/1","16:51:40","19:37:38","1985/9/17","2001/2/20","9:57:57","1973/12/18","1996/11/8","1985/1/2","1992/4/23","2012/10/12","2004/5/19","1977/3/11","1976/4/26","1975/1/9","9:18:08","2017/4/15","1989/7/5"](String)
2025-08-25 14:45:01.868 DEBUG 63940 --- [Samp-3] [] c.m.n.p.m.SampleColumnMapper.insertList : <0.1><9JdVWWYZ> <==    Updates: 49
2025-08-25 14:45:01.967 DEBUG 63940 --- [Samp-3] [] c.m.n.p.m.SampleTableMapper.insertList  : <0.1><9JdVWWYZ> ==>  Preparing: INSERT INTO sample_table (`oid`,`source_id`,`schema_id`,`data_empty`) VALUES (?,?,?,?)
2025-08-25 14:45:01.968 DEBUG 63940 --- [Samp-3] [] c.m.n.p.m.SampleTableMapper.insertList  : <0.1><9JdVWWYZ> ==> Parameters: 41(Long), 6(Long), 40(Long), 2(Integer)
2025-08-25 14:45:02.107 DEBUG 63940 --- [Samp-3] [] c.m.n.p.m.SampleTableMapper.insertList  : <0.1><9JdVWWYZ> <==    Updates: 1
2025-08-25 14:45:02.309 DEBUG 63940 --- [dark-task-1] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : <0.1><9JdVWWYZ> [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 14:45:02.653 DEBUG 63940 --- [dark-task-1] [] c.m.nyx.pipeline.http.HttpRemoteInvoke  : <0.1><9JdVWWYZ> {"code":200,"success":true,"msg":"操作成功","tranceId":"9JdVWWYZ"}
2025-08-25 14:45:02.660 DEBUG 63940 --- [dark-task-1] [] c.m.n.p.m.DiscoveryJobMapper.updateById : <0.1><9JdVWWYZ> ==>  Preparing: UPDATE discovery_job SET `heartbeat`=? WHERE `id`=?
2025-08-25 14:45:02.660 DEBUG 63940 --- [dark-task-1] [] c.m.n.p.m.DiscoveryJobMapper.updateById : <0.1><9JdVWWYZ> ==> Parameters: {"startTime":1756104289612,"step":3,"num":0}(String), 7(Long)
2025-08-25 14:45:02.685 DEBUG 63940 --- [dark-task-1] [] c.m.n.p.m.DiscoveryJobMapper.updateById : <0.1><9JdVWWYZ> <==    Updates: 1
2025-08-25 14:45:02.686 DEBUG 63940 --- [dark-task-1] [] c.m.n.p.s.impl.TaskCallbackServiceImpl  : <0.1><9JdVWWYZ> 【心跳】TaskHeartbeatBeatReq(taskGroup=COLLECT_META, taskName=7, status=3)
2025-08-25 14:45:02.688 DEBUG 63940 --- [dark-task-1] [] c.m.s.t.s.TenantHutoolhttpInterceptor   : <0.1><9JdVWWYZ> [tenant]本地threadLocal变量没有正确传递tenantId,本次调用不传递tenantId
2025-08-25 14:45:02.836  INFO 63940 --- [dark-task-1] [] c.m.n.p.job.TaskSchedulingManager       : <0.1><9JdVWWYZ> 【计时器】J7(P3-S6) [2]: running time = 13秒109毫秒(13109657300)
---------------------------------------------------------
  %   Task name        detail           ns
---------------------------------------------------------
86%   stage2           11秒293毫秒(1)  ***********
09%   stage1           1秒199毫秒(1)  **********

2025-08-25 14:58:54.472  INFO 63940 --- [SpringApplicationShutdownHook] [] c.m.n.p.job.TaskSchedulingManager       : Shutting down task manager executor
2025-08-25 14:58:54.591  INFO 63940 --- [SpringApplicationShutdownHook] [] c.b.d.d.DynamicRoutingDataSource        : dynamic-datasource start closing ....
2025-08-25 14:58:54.616  INFO 63940 --- [SpringApplicationShutdownHook] [] com.alibaba.druid.pool.DruidDataSource  : {dataSource-1} closing ...
2025-08-25 14:58:54.626  INFO 63940 --- [SpringApplicationShutdownHook] [] com.alibaba.druid.pool.DruidDataSource  : {dataSource-1} closed
2025-08-25 14:58:54.627  INFO 63940 --- [SpringApplicationShutdownHook] [] c.b.d.d.d.DefaultDataSourceDestroyer    : dynamic-datasource close the datasource named [embed] success,
2025-08-25 14:58:54.627  INFO 63940 --- [SpringApplicationShutdownHook] [] com.zaxxer.hikari.HikariDataSource      : dark-dbms - Shutdown initiated...
2025-08-25 14:58:54.632  INFO 63940 --- [SpringApplicationShutdownHook] [] com.zaxxer.hikari.HikariDataSource      : dark-dbms - Shutdown completed.
2025-08-25 14:58:54.632  INFO 63940 --- [SpringApplicationShutdownHook] [] c.b.d.d.d.DefaultDataSourceDestroyer    : dynamic-datasource close the datasource named [master] success,
2025-08-25 14:58:54.632  INFO 63940 --- [SpringApplicationShutdownHook] [] c.b.d.d.DynamicRoutingDataSource        : dynamic-datasource all closed success,bye
