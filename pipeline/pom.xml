<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.mchz.nyx</groupId>
    <artifactId>nyx</artifactId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>

  <artifactId>pipeline</artifactId>
  <packaging>${packaging}</packaging>

  <dependencies>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
      <exclusions>
        <exclusion>
          <groupId>ch.qos.logback</groupId>
          <artifactId>logback-core</artifactId>
        </exclusion>
        <exclusion>
          <groupId>ch.qos.logback</groupId>
          <artifactId>logback-classic</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>ch.qos.logback</groupId>
      <artifactId>logback-core</artifactId>
      <version>${logback.version}</version>
    </dependency>
    <dependency>
      <groupId>ch.qos.logback</groupId>
      <artifactId>logback-classic</artifactId>
      <version>${logback.version}</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-validation</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>tomcat-embed-el</artifactId>
          <groupId>org.apache.tomcat.embed</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>mybatis-plus-boot-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>mybatis-plus-jsqlparser-4.9</artifactId>
      <version>${mybatis-plus.version}</version>
    </dependency>

    <dependency>
      <groupId>com.github.yulichang</groupId>
      <artifactId>mybatis-plus-join-boot-starter</artifactId>
    </dependency>

    <dependency>
      <groupId>com.yomahub</groupId>
      <artifactId>tlog-web-spring-boot-starter</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.squareup.okio</groupId>
      <artifactId>okio</artifactId>
      <version>${okio.version}</version>
      <scope>runtime</scope>
    </dependency>

    <!--swagger-->
    <dependency>
      <groupId>com.github.xiaoymin</groupId>
      <artifactId>knife4j-openapi2-spring-boot-starter</artifactId>
      <version>${knife4j-spring-boot-starter.version}</version>
    </dependency>

    <!--jasypt配置文件加解密-->
    <dependency>
      <groupId>com.github.ulisesbocchio</groupId>
      <artifactId>jasypt-spring-boot-starter</artifactId>
      <version>${jasypt.version}</version>
    </dependency>

    <!-- Alibaba Nacos  -->
    <dependency>
      <groupId>com.alibaba.cloud</groupId>
      <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
    </dependency>
    <dependency>
      <groupId>com.alibaba.cloud</groupId>
      <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
    </dependency>

    <dependency>
      <groupId>com.mchz.base</groupId>
      <artifactId>tenant-spring-boot-starter</artifactId>
    </dependency>

    <dependency>
      <groupId>mysql</groupId>
      <artifactId>mysql-connector-java</artifactId>
    </dependency>

    <dependency>
      <groupId>commons-net</groupId>
      <artifactId>commons-net</artifactId>
      <version>${commons.net.version}</version>
    </dependency>
    <dependency>
      <groupId>com.jcraft</groupId>
      <artifactId>jsch</artifactId>
      <version>${jsch.version}</version>
    </dependency>

    <dependency>
      <groupId>cn.idev.excel</groupId>
      <artifactId>fastexcel</artifactId>
      <version>${fastexcel.version}</version>
    </dependency>
    <dependency>
      <groupId>org.jsoup</groupId>
      <artifactId>jsoup</artifactId>
      <version>1.17.2</version>
    </dependency>
    <dependency>
      <groupId>com.github.junrar</groupId>
      <artifactId>junrar</artifactId>
      <version>7.5.5</version>
    </dependency>
    <dependency>
      <groupId>com.belerweb</groupId>
      <artifactId>pinyin4j</artifactId>
      <version>${pinyin4j.version}</version>
    </dependency>

    <dependency>
      <groupId>net.java.dev.jna</groupId>
      <artifactId>jna</artifactId>
      <version>${jna.version}</version>
    </dependency>
    <dependency>
      <groupId>com.hankcs</groupId>
      <artifactId>hanlp</artifactId>
      <version>${hanlp.version}</version>
    </dependency>

    <dependency>
      <groupId>com.mchz</groupId>
      <artifactId>datasource-cli</artifactId>
    </dependency>
    <!-- 仅供统一数据源使用 -->
    <dependency>
      <groupId>com.alibaba</groupId>
      <artifactId>fastjson</artifactId>
      <version>1.2.83</version>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.mchz.nyx</groupId>
      <artifactId>metadata</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.mchz.nyx</groupId>
      <artifactId>darkdata</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.mchz.nyx</groupId>
      <artifactId>metis-core-engine</artifactId>
      <version>1.3.0</version>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
    </dependency>

    <dependency>
      <groupId>com.mchz</groupId>
      <artifactId>base-mybatis-sqltranslate-spring-boot-starter</artifactId>
      <version>1.1.2-SNAPSHOT</version>
    </dependency>
    <dependency>
      <groupId>com.dameng</groupId>
      <artifactId>DmJdbcDriver18</artifactId>
      <version>8.1.2.141</version>
    </dependency>
  </dependencies>

  <profiles>
    <profile>
      <id>standalone</id>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
      <properties>
        <profileActive>standalone</profileActive>
      </properties>
      <dependencies>
        <dependency>
          <groupId>com.h2database</groupId>
          <artifactId>h2</artifactId>
        </dependency>
        <dependency>
          <groupId>com.dameng</groupId>
          <artifactId>DmJdbcDriver18</artifactId>
          <version>8.1.2.141</version>
        </dependency>
      </dependencies>
    </profile>

    <profile>
      <id>cluster</id>
      <properties>
        <profileActive>cluster</profileActive>
      </properties>
      <dependencies>
        <dependency>
          <groupId>com.h2database</groupId>
          <artifactId>h2</artifactId>
        </dependency>
      </dependencies>
    </profile>

    <profile>
      <id>embed</id>
      <properties>
        <profileActive>embed</profileActive>
        <packaging>jar</packaging>
      </properties>
      <dependencies>
        <dependency>
          <groupId>com.h2database</groupId>
          <artifactId>h2</artifactId>
        </dependency>
      </dependencies>
    </profile>
  </profiles>

  <build>
    <resources>
      <resource>
        <directory>src/main/resources</directory>
        <!-- 过滤资源 -->
        <excludes>
          <exclude>application*.yml</exclude>
        </excludes>
      </resource>
      <resource>
        <directory>src/main/resources</directory>
        <!-- 是否替换@xx@表示的maven properties属性值 -->
        <filtering>true</filtering>
        <!-- 引入资源 -->
        <includes>
          <include>application.yml</include>
          <include>application-${profileActive}.yml</include>
        </includes>
      </resource>
    </resources>

    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <version>2.3.7.RELEASE</version>
        <executions>
          <execution>
            <goals>
              <goal>repackage</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-war-plugin</artifactId>
        <version>3.4.0</version>
      </plugin>
      <plugin>
        <groupId>pl.project13.maven</groupId>
        <artifactId>git-commit-id-plugin</artifactId>
        <version>4.0.3</version>
        <executions>
          <execution>
            <id>get-the-git-infos</id>
            <goals>
              <goal>revision</goal>
            </goals>
            <phase>initialize</phase>
          </execution>
        </executions>
        <configuration>
          <generateGitPropertiesFile>true</generateGitPropertiesFile>
          <generateGitPropertiesFilename>${project.build.outputDirectory}/git.properties</generateGitPropertiesFilename>
          <includeOnlyProperties>
            <includeOnlyProperty>^git.build.(time|version)$</includeOnlyProperty>
            <includeOnlyProperty>^git.commit.id.abbrev$</includeOnlyProperty>
          </includeOnlyProperties>
          <commitIdGenerationMode>full</commitIdGenerationMode>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
