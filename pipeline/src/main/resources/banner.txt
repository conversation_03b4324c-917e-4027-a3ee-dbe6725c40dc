${AnsiColor.BRIGHT_BLUE}  _____                           _____
${AnsiColor.BRIGHT_BLUE} ( ___ )                         ( ___ )
${AnsiColor.BRIGHT_CYAN}  |   |~~~~~~~~~~~~~~~~~~~~~~~~~~~|   |
${AnsiColor.BRIGHT_CYAN}  |   | ${AnsiColor.BRIGHT_GREEN} ____  ____    _    ____  ${AnsiColor.BRIGHT_CYAN}|   |
${AnsiColor.BRIGHT_CYAN}  |   | ${AnsiColor.BRIGHT_GREEN}|  _ \|  _ \  / \  / ___| ${AnsiColor.BRIGHT_CYAN}|   |
${AnsiColor.BRIGHT_CYAN}  |   | ${AnsiColor.BRIGHT_GREEN}| | | | | | |/ _ \| |     ${AnsiColor.BRIGHT_CYAN}|   |
${AnsiColor.BRIGHT_CYAN}  |   | ${AnsiColor.BRIGHT_GREEN}| |_| | |_| / ___ \ |___  ${AnsiColor.BRIGHT_CYAN}|   |
${AnsiColor.BRIGHT_CYAN}  |   | ${AnsiColor.BRIGHT_GREEN}|____/|____/_/   \_\____| ${AnsiColor.BRIGHT_CYAN}|   |
${AnsiColor.BRIGHT_CYAN}  |___|~~~~~~~~~~~~~~~~~~~~~~~~~~~|___|
${AnsiColor.BRIGHT_BLUE} (_____)             ${AnsiColor.BRIGHT_BLACK}(v${spring-boot.version})   ${AnsiColor.BRIGHT_BLUE}(_____)
${AnsiColor.BRIGHT_WHITE} :: ${spring.application.name} ::                  ${spring.application.version} ${AnsiColor.DEFAULT}
