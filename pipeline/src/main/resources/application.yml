server:
  port: 8888

spring:
  application:
    name: pipeline
    version: @pom.version@
  profiles:
    active: @profileActive@
  jackson:
    default-property-inclusion: non_null

mc:
  tenant:
    enabled: false

tlog:
  id-generator: com.mchz.nyx.pipeline.config.ShortUUIDTLogIdGenerator

mybatis-plus:
  global-config:
    banner: false
    db-config:
      id-type: auto
  configuration:
    variables:
      schema:

mybatis-plus-join:
  banner: false

knife4j:
  enable: false

jasypt:
  encryptor:
    password: 99TEyHgqQ

source:
  ehcache:
    root-dir: ${nyx.tmp}/ehcache

