spring:
  config:
    import:
      - nacos:sourcedata.properties
  cloud:
    nacos:
      discovery:
        server-addr: ${NACOS_URL:127.0.0.1:8848}
        namespace: public
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        namespace: ${spring.cloud.nacos.discovery.namespace}
        group: ${NACOS_DDAC_GROUP:ddac}

# 开启服务熔断并设置远程调用超时时间
feign:
  hystrix:
    enabled: true
  client:
    config:
      default:
        connectTimeout: 30000
        readTimeout: 30000
        loggerLevel: basic

#设置熔断超时时间
hystrix:
  command:
    default:
      execution:
        timeout:
          enabled: true
        isolation:
          thread:
            timeoutInMilliseconds: 30000
