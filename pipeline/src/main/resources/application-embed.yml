spring:
  cloud:
    nacos:
      discovery:
        enabled: false
      config:
        enabled: false
    loadbalancer:
      enabled: false
  autoconfigure:
    exclude:
      - org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration
  datasource:
    dynamic:
      enabled: true
      datasource:
        embed:
          url: jdbc:h2:${nyx.data}/ddac2;RECOVER=1;ACCESS_MODE_DATA=R;TRACE_LEVEL_FILE=0;AUTO_RECONNECT=TRUE;IFEXISTS=TRUE
          username: sourcedata
          password: Root-123
          driver-class-name: org.h2.Driver
  jmx:
    default-domain: com.mchz.pipeline

logging:
  file:
    path: ${metis.log.path:logs}

mybatis-plus:
  global-config:
    db-config:
      schema: ${metis.schema:}

nyx:
  home: ${metis.app.path:.}
  data: ${nyx.home}/data
  tmp: ${nyx.home}/data/tmp
