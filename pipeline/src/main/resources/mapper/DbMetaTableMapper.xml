<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.mchz.nyx.pipeline.mapper.DbMetaTableMapper">

  <update id="updateOtherByIdSp" databaseId="mysql">
    UPDATE `dbmeta_table` SET `rows` = CASE
    <foreach collection="list" item="item">
      WHEN `oid` = #{item.oid} THEN #{item.rows}
    </foreach>
    ELSE `rows` END
    WHERE `oid` IN
    <foreach collection="list" item="item" open="(" separator="," close=")">
      #{item.oid}
    </foreach>
  </update>

</mapper>
