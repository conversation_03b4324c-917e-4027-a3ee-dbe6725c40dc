spring:
  cloud:
    enabled: false
    nacos:
      discovery:
        enabled: false
      config:
        enabled: false
    loadbalancer:
      enabled: false
  autoconfigure:
    exclude:
      - org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration
  datasource:
    dynamic:
      enabled: true
      grace-destroy: true
      datasource:
        embed:
          url: jdbc:h2:${nyx.data}/rule/rule;RECOVER=1;ACCESS_MODE_DATA=R;TRACE_LEVEL_FILE=0;AUTO_RECONNECT=TRUE
          username: sourcedata
          password: Root-123
          driver-class-name: org.h2.Driver
  quartz:
    jdbc:
      initialize-schema: never
  flyway:
    enabled: false
server:
  tomcat:
    basedir: ${nyx.tmp}/pipeline.${server.port}

nyx:
  home: ${DDAC_HOME:.}
  data: ${DDAC_DATA_HOME:${nyx.home}/data}/pipeline
  tmp: ${DDAC_DATA_HOME:${nyx.home}/data}/tmp

source:
  engine:
    vector-path: ${nyx.data}/500000-small.txt
    trans-dict-path: ${nyx.data}/baseDictionary.json
    chinese-path: ${nyx.data}/chineseSyllable.json

logging:
  level:
    com.mchz: debug

#sqltranslate:
#  enable: true
#  srcDbType: mysql
#  targetDbType: dm
#  quoteSymbol: true
#  upperLowerCase: 1
    batch-classify-size: ${sourcedata.recommend.batch-classify-size:10}
