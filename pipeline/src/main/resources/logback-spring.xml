<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false" scan="false">
  <springProperty scop="context" name="LOG_NAME" source="spring.application.name" defaultValue="server"/>
  <springProperty scop="context" name="LOG_FILE" source="logging.file.path" defaultValue="logs"/>

  <conversionRule conversionWord="TENANT_ID" converterClass="com.mchz.nyx.pipeline.config.TenantIdTagConverter" />

  <property name="CONSOLE_LOG_PATTERN" value="%clr(%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss.SSS}}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} [%TENANT_ID] %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}"/>
  <property name="FILE_LOG_PATTERN"
            value="${FILE_LOG_PATTERN:-%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss.SSS}} ${LOG_LEVEL_PATTERN:-%5p} ${PID:- } --- [%t] [%TENANT_ID] %-40.40logger{39}: %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>
  <property name="FILE_LOG_PATTERN_ERROR"
            value="${FILE_LOG_PATTERN:-%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd HH:mm:ss.SSS}} ${LOG_LEVEL_PATTERN:-%5p} ${PID:- } --- [%t] [%TENANT_ID] %-40.40logger{39} %file:%line: %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>

  <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
  <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
    <filter class="ch.qos.logback.classic.filter.LevelFilter">
      <level>INFO</level>
    </filter>
    <encoder class="com.yomahub.tlog.core.enhance.logback.AspectLogbackEncoder">
      <pattern>${CONSOLE_LOG_PATTERN}</pattern>
      <charset>UTF-8</charset>
    </encoder>
  </appender>

  <appender name="FILE_INFO" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <File>${LOG_FILE}/${LOG_NAME}.log</File>
    <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
      <FileNamePattern>${LOG_FILE}/info/${LOG_NAME}.%d{yyyy-MM-dd}.%i.log.gz</FileNamePattern>
      <maxFileSize>50MB</maxFileSize>
      <maxHistory>14</maxHistory>
      <totalSizeCap>200MB</totalSizeCap>
    </rollingPolicy>
    <encoder class="com.yomahub.tlog.core.enhance.logback.AspectLogbackEncoder">
      <pattern>${FILE_LOG_PATTERN}</pattern>
      <charset>UTF-8</charset>
    </encoder>
  </appender>

  <appender name="FILE_ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
      <level>Error</level>
    </filter>
    <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
      <FileNamePattern>${LOG_FILE}/error/${LOG_NAME}.error.%d{yyyy-MM-dd}.%i.log.gz</FileNamePattern>
      <maxFileSize>50MB</maxFileSize>
      <maxHistory>90</maxHistory>
      <totalSizeCap>100MB</totalSizeCap>
    </rollingPolicy>
    <encoder class="com.yomahub.tlog.core.enhance.logback.AspectLogbackEncoder">
      <pattern>${FILE_LOG_PATTERN_ERROR}</pattern>
      <charset>UTF-8</charset>
    </encoder>
  </appender>

  <root level="INFO">
    <appender-ref ref="CONSOLE"/>
    <appender-ref ref="FILE_INFO"/>
    <appender-ref ref="FILE_ERROR"/>
  </root>
</configuration>
