package com.mchz.nyx.pipeline.common.api;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 * 服务状态码枚举
 * </p>
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum Status implements IResultCode {
    /**
     * 数据源连接失败
     */
    CONNECTION_FAILED(10005, "数据源连接失败"),

    /**
     * 未连接到远程服务器
     */
    CANNOT_CONNECT_REMOTE(10108, "无法连接到远程文件服务器"),
    UN_SUPPORTED_OPERATION(10109, "不支持的操作"),
    SQL_CAN_NOT_BE_EMPTY(10110, "SQL不能为空"),
    QUEUE_BUSY(20001, "队列无空闲资源"),
    HAS_PENDING_TASK(20002, "{} 存在未完成任务"),
    ;
    private final int code;
    private final String msg;
}

