package com.mchz.nyx.pipeline.thread.event;

import com.mchz.nyx.pipeline.model.log.JobLogItem;
import lombok.Data;

import java.util.Set;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/11
 */
@Data
public class LogItemEvent {
    private static final byte CLEAR = 3;
    private Set<String> processingTables;
    private JobLogItem logItem;
    private byte flag;

    public LogItemEvent() {
        this.flag = 0;
    }

    public void clear1() {
        clear((byte) 1);
    }

    public void clear2() {
        clear((byte) 2);
    }

    private void clear(byte i) {
        flag &= i;
        if (CLEAR == flag) {
            processingTables = null;
            logItem = null;
            flag = 0;
        }
    }
}
