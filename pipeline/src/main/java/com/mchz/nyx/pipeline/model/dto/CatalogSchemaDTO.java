package com.mchz.nyx.pipeline.model.dto;

import com.mchz.nyx.dark.model.meta.NyxMetaSchema;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/6/28
 */
@Data
public class CatalogSchemaDTO implements NyxMetaSchema {
    private Long oid;
    private String name;
    private String catalog;
    private String schema;

    @Override
    public String getCatalogSchema() {
        return name;
    }
}
