package com.mchz.nyx.pipeline.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/2/7
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ClassifyDTO {
    private Long typeId;
    private Integer level;
    private String according;
    private Double confidence;

    public ClassifyDTO(Long typeId, Integer level) {
        this.typeId = typeId;
        this.level = level;
    }
}
