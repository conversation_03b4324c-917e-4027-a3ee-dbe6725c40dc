package com.mchz.nyx.pipeline.service;


import com.mchz.nyx.pipeline.model.api.TaskHeartbeatBeatReq;
import com.mchz.nyx.pipeline.model.dto.JobLogInstantDTO;
import com.mchz.nyx.pipeline.model.log.JobLogVO;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/10/23 12:19
 */
public interface TaskCallbackService {

    /**
     * 任务执行结束
     *
     * @param jobId   作业id
     * @param success 是否成功
     * @param jobLog  日志
     */
    void finished(Long jobId, boolean success, JobLogVO jobLog);

    void sendProgress(JobLogInstantDTO logInfo);

    void heart(TaskHeartbeatBeatReq value);
}
