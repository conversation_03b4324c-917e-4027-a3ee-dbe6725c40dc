package com.mchz.nyx.pipeline.service;

import com.mchz.nyx.dark.model.dto.StandardLoadContext;
import com.mchz.nyx.pipeline.model.dto.ClassifyTypeDTO;
import com.mchz.nyx.pipeline.model.dto.StandardDetailDTO;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/29
 */
public interface RuleDetailService {

    default Integer getIndustryId(Long stdId, Integer industryId) {
        return industryId;
    }

    StandardDetailDTO loadRuleDetail(StandardLoadContext context, Long stdId, Integer industryId, boolean loadRule, boolean loadBase, Double hit);

    List<ClassifyTypeDTO> loadClassifyType(Long stdId);
}
