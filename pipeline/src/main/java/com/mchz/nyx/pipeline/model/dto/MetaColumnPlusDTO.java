package com.mchz.nyx.pipeline.model.dto;

import cn.hutool.core.util.StrUtil;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/10/21 16:44
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MetaColumnPlusDTO extends MetaColumnDTO {
    /**
     * 主键名
     */
    private String pkName;

    /**
     * 主键列数量
     */
    private Integer pkNum;

    /**
     * 列在主键中的位置
     */
    private Integer pkPos;

    /**
     * 唯一约束名字（包括唯一索引）
     */
    private String uniName;

    /**
     * 索引列数量
     */
    private Integer uniNum;

    /**
     * 列在唯一约束中的位置
     */
    private Integer uniPos;

    /**
     * 索引名字
     */
    private String idxName;

    /**
     * 列在索引中的位置
     */
    private Integer idxPos;

    /**
     * 外健名字
     */
    private String fkName;

    /**
     * 列在外健中的位置
     */
    private Integer fkPos;

    /** 扩展内容 */
    private String expandContent;

    @Override
    public Integer getPosition() {
        return null == super.getPosition() ? -1 : super.getPosition();
    }

    public String getConstraintType() {
        StringBuilder sb = new StringBuilder();
        if (null != getIsPk() && getIsPk()) {
            if (sb.length() != 0) {
                sb.append(StrUtil.C_COMMA);
            }
            sb.append("PK");
        } else if (null != getIsUnique() && getIsUnique()) {
            if (sb.length() != 0) {
                sb.append(StrUtil.C_COMMA);
            }
            sb.append("UN");
        }
        if (null != getIsFk() && getIsFk()) {
            if (sb.length() != 0) {
                sb.append(StrUtil.C_COMMA);
            }
            sb.append("F");
        }
        if (sb.length() == 0) {
            sb.append("N");
        }
        return sb.toString();
    }

    public Integer getPkNum() {
        return null == pkNum ? 0 : pkNum;
    }

    public Boolean isUpk() {
        if (null == getIsPk()) {
            return null;
        }
        return getIsPk() && getPkNum() == 1;
    }

    public Boolean isCpk() {
        if (null == getIsPk()) {
            return null;
        }
        return getIsPk() && getPkNum() > 1;
    }
}
