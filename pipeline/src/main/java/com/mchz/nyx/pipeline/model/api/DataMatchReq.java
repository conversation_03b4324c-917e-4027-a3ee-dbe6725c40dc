package com.mchz.nyx.pipeline.model.api;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2023/6/7 10:27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DataMatchReq {
    private boolean force;
    private boolean only;
    private boolean traffic;
    private boolean noHis;
    private Long stdId;
    private TableReq table;
    private List<ColumnReq> column;

    private List<String> data;
    private String sample;

    private Map<String, Object> config;
}
