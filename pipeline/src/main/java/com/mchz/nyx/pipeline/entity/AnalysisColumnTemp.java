package com.mchz.nyx.pipeline.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/1/24
 */
@Data
@TableName("analysis_column_temp")
public class AnalysisColumnTemp {
    @TableId
    private Long id;
    private Long planId;
    private Long jobId;
    private Long acId;
    private String name;
    /**
     * 类型: 0->翻译; 1->注释
     */
    private Integer type;
}
