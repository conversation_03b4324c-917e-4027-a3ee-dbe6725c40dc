package com.mchz.nyx.pipeline.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 抽样数据分析
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-27
 */
@Data
@TableName("analysis_column")
public class AnalysisColumn implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 配置id
     */
    private Long planId;

    /**
     * 执行作业id
     */
    private Long jobId;

    /**
     * 数据源id
     */
    private Long sourceId;

    /**
     * schema元数据id
     */
    private Long schemaId;

    /**
     * table元数据id
     */
    private Long tableId;

    /**
     * column元数据id
     */
    private Long columnId;

    /**
     * schema
     */
    private String schemaName;

    /**
     * 表名
     */
    private String tableName;

    /**
     * 列名
     */
    private String columnName;

    /**
     * 业务类型，字典表中业务类型id
     */
    private Long bizId;

    /**
     * 分类type id
     */
    private Long classifyId;

    /**
     * 分级级别
     */
    private Integer level;

    /**
     * 1-8
     */
    private Integer flag;

    /**
     * 状态1用户确认2自动确认3自动发现4没有发现5
     */
    private Integer status;

    private Integer repeatType;

    private Long repeatColumnId;

    /**
     * 备用
     */
    private String remark;
}
