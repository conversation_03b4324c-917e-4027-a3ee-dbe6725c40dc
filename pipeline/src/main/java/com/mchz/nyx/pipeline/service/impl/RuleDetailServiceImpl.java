package com.mchz.nyx.pipeline.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.toolkit.MPJWrappers;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.mchz.nyx.common.util.PartitionUtil;
import com.mchz.nyx.dark.common.enums.*;
import com.mchz.nyx.dark.model.TargetInfo;
import com.mchz.nyx.dark.model.definition.*;
import com.mchz.nyx.dark.model.dto.StandardLoadContext;
import com.mchz.nyx.dark.model.dto.StdDetailDTO;
import com.mchz.nyx.dark.model.dto.TargetWithTagGroup;
import com.mchz.nyx.dark.model.rule.RuleIdInfo;
import com.mchz.nyx.pipeline.common.constants.BusinessTypeConstants;
import com.mchz.nyx.pipeline.common.constants.PipelineConst;
import com.mchz.nyx.pipeline.entity.*;
import com.mchz.nyx.pipeline.mapper.*;
import com.mchz.nyx.pipeline.model.dto.ClassifyTypeDTO;
import com.mchz.nyx.pipeline.model.dto.ExtendInfoDTO;
import com.mchz.nyx.pipeline.model.dto.StandardDetailDTO;
import com.mchz.nyx.pipeline.service.InnerRuleService;
import com.mchz.nyx.pipeline.service.RuleDetailService;
import com.mchz.nyx.pipeline.util.Func;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/11/17 11:19
 */
@Slf4j
@Service
@AllArgsConstructor
@ConditionalOnMissingBean(value = RuleDetailService.class, ignored = RuleDetailServiceImpl.class)
public class RuleDetailServiceImpl implements RuleDetailService {
    private static final int INNER_SIZE = 210;

    private final InnerRuleService innerRuleService;

    private final CStandardMapper standardMapper;
    private final CSysBizDataMapper sysBizDataMapper;
    private final CSysClassifyTypeMapper sysClassifyTypeMapper;
    private final CSysClassifyDataMapper sysClassifyDataMapper;
    private final CSysRuleDetailMapper sysRuleDetailMapper;

    @Override
    public Integer getIndustryId(Long stdId, Integer industryId) {
        if (null != industryId) {
            return industryId;
        }
        CStandard cStandard = standardMapper.selectById(stdId);
        if (null != cStandard && null != cStandard.getIndustryId()) {
            return cStandard.getIndustryId();
        }
        return 9;
    }

    @Override
    public StandardDetailDTO loadRuleDetail(StandardLoadContext context, Long stdId, Integer industryId, boolean loadRule, boolean loadBase, Double hit) {
        StdDetailDTO detail = new StdDetailDTO();
        Map<Long, ExtendInfoDTO> map = listBizId(stdId, industryId, loadBase);
        List<CSysBizData> biz = listSysBizData(map.keySet());
        if (biz.isEmpty()) {
            return null;
        }
        Map<Long, TargetInfo> termInfoMap = new HashMap<>(biz.size());
        detail.setTargets(termInfoMap);
        for (CSysBizData bizData : biz) {
            ExtendInfoDTO info = map.get(bizData.getId());
            termInfoMap.put(bizData.getId(), new TargetInfo(bizData.getId(), bizData.getName(), null == info || info.isEntity()));
        }
        if (loadRule) {
            loadRuleDetail(detail, termInfoMap, hit);
        }
        detail.setClassify(loadClassifyType(stdId));
        return new StandardDetailDTO(detail, map);
    }

    @Override
    public List<ClassifyTypeDTO> loadClassifyType(Long stdId) {
        MPJLambdaWrapper<CSysClassifyType> q = MPJWrappers.lambdaJoin(CSysClassifyType.class).selectAsClass(CSysClassifyType.class, ClassifyTypeDTO.class).eq(CSysClassifyType::getStdId, stdId);
        List<ClassifyTypeDTO> list = sysClassifyTypeMapper.selectJoinList(ClassifyTypeDTO.class, q);
        Set<Long> set = list.stream().map(ClassifyTypeDTO::getParentId).collect(Collectors.toSet());
        return list.stream().filter(v -> !set.contains(v.getId())).collect(Collectors.toList());
    }

    private Map<Long, ExtendInfoDTO> listBizId(Long stdId, Integer industryId, boolean loadBase) {
        Wrapper<CSysClassifyData> q = Wrappers.lambdaQuery(CSysClassifyData.class).select(CSysClassifyData::getBizId, CSysClassifyData::getTypeId, CSysClassifyData::getLevel).eq(CSysClassifyData::getStdId, stdId);
        Map<Long, ExtendInfoDTO> res = new HashMap<>();
        sysClassifyDataMapper.selectList(q, rc -> res.computeIfAbsent(rc.getResultObject().getBizId(), k -> new ExtendInfoDTO(true)).addClassify(rc.getResultObject().getTypeId(), rc.getResultObject().getLevel()));
        if (loadBase) {
            LambdaQueryWrapper<CSysBizData> q2 = Wrappers.lambdaQuery(CSysBizData.class).select(CSysBizData::getId).eq(CSysBizData::getIndustryId, industryId).eq(CSysBizData::getBizType, 3).eq(CSysBizData::getStatus, 1);
            sysBizDataMapper.selectList(q2, rc -> res.computeIfAbsent(rc.getResultObject().getId(), k -> new ExtendInfoDTO(false)));
        }
        return res;
    }

    private List<CSysBizData> listSysBizData(Set<Long> bizIds) {
        List<CSysBizData> res = new ArrayList<>(bizIds.size());
        PartitionUtil.part(bizIds, 1000).forEach(v -> {
            LambdaQueryWrapper<CSysBizData> q = Wrappers.lambdaQuery(CSysBizData.class)
                .select(CSysBizData::getId, CSysBizData::getName)
                .eq(CSysBizData::getStatus, Boolean.TRUE)
                .in(CSysBizData::getId, v);
            sysBizDataMapper.selectList(q, rc -> res.add(rc.getResultObject()));
        });
        return res;
    }

    private void loadRuleDetail(StdDetailDTO dto, Map<Long, TargetInfo> termInfoMap, Double hit) {
        Map<Long, List<CSysRuleDetail>> map = listSysRuleDetail(termInfoMap.keySet());
        List<TargetWithTagGroup> tagGroups = new ArrayList<>(map.size());
        List<RuleDetail> ruleDetails = new ArrayList<>(termInfoMap.size());
        tagRule(map, termInfoMap, tagGroups, ruleDetails, hit);
        tagGroups.sort((o1, o2) -> {
            int res = o2.getScore().compareTo(o1.getScore());
            if (0 != res) {
                return res;
            }
            return o1.getId().compareTo(o2.getId());
        });
        dto.setTagGroups(tagGroups);
        dto.setRuleDetails(ruleDetails);
    }

    private Map<Long, List<CSysRuleDetail>> listSysRuleDetail(Collection<Long> bizIds) {
        Map<Long, List<CSysRuleDetail>> map = new HashMap<>(bizIds.size());
        PartitionUtil.part(bizIds, v -> {
            LambdaQueryWrapper<CSysRuleDetail> q = Wrappers.lambdaQuery(CSysRuleDetail.class)
                .eq(CSysRuleDetail::getStatus, Boolean.TRUE)
                .in(CSysRuleDetail::getBizId, v);
            List<CSysRuleDetail> list = sysRuleDetailMapper.selectList(q);
            list.forEach(e -> map.computeIfAbsent(e.getBizId(), k -> new ArrayList<>(1)).add(e));
        });
        return map;
    }

    private void tagRule(Map<Long, List<CSysRuleDetail>> rules, Map<Long, TargetInfo> termInfoMap, List<TargetWithTagGroup> tagGroups, List<RuleDetail> ruleDetails, Double hit) {
        Map<Long, List<TargetWithTagGroup>> innerRule = new HashMap<>(INNER_SIZE);
        for (Map.Entry<Long, List<CSysRuleDetail>> entry : rules.entrySet()) {
            TargetInfo targetInfo = termInfoMap.get(entry.getKey());
            if (null == targetInfo) {
                throw new IllegalArgumentException("无关联业务术语");
            }
            Map<Boolean, List<CSysRuleDetail>> map = entry.getValue().stream().collect(Collectors.partitioningBy(v -> BusinessTypeConstants.Expect.POSITIVE == v.getExpect()));
            List<CSysRuleDetail> list = map.get(Boolean.TRUE);
            if (CollUtil.isEmpty(list)) {
                continue;
            }
            List<Long> nonTag = buildTags(map.get(Boolean.FALSE), ruleDetails, hit);
            for (CSysRuleDetail rule : list) {
                TargetWithTagGroup tagGroup = new TargetWithTagGroup();
                tagGroup.setScore(rule.getDegree());
                tagGroup.setId(rule.getBizId());
                tagGroup.setNonTags(nonTag);
                if (BusinessTypeConstants.Algorithm.INNER == rule.getAlgorithm()) {
                    if (null != rule.getFunction()) {
                        try {
                            innerRule.computeIfAbsent(Long.valueOf(rule.getFunction()), k -> new ArrayList<>()).add(tagGroup);
                        } catch (Exception e) {
                            log.warn("【loadRule】无效内置规则{}", rule.getFunction());
                        }
                    }
                    continue;
                }
                List<Long> tags = new ArrayList<>(1);
                if (buildTag(rule, tags, ruleDetails, hit)) {
                    tagGroup.setTags(tags);
                    tagGroups.add(tagGroup);
                }
            }
        }
        try {
            loadInnerRule(innerRule, tagGroups, ruleDetails, hit);
        } catch (Exception e) {
            if (log.isDebugEnabled()) {
                log.debug("【loadRule】无额外内置规则,{}", e.getMessage());
            } else {
                log.warn("【loadRule】无额外内置规则");
            }
        }
    }

    private List<Long> buildTags(List<CSysRuleDetail> list, List<RuleDetail> ruleDetails, Double hit) {
        if (null == list) {
            return new ArrayList<>(0);
        }
        List<Long> tags = new ArrayList<>(list.size());
        list.forEach(v -> buildTag(v, tags, ruleDetails, hit));
        return tags;
    }

    private boolean buildTag(CSysRuleDetail rule, List<Long> tags, List<RuleDetail> ruleDetails, Double hit) {
        StrSegmentRule strRule = buildStrRule(rule);
        if (null == strRule) {
            return false;
        }
        RuleDetail detail = new RuleDetail();
        switch (rule.getRuleType()) {
            case BusinessTypeConstants.RuleType.NAME:
                detail.addColumnRule(ParamColumn.COLUMN_NAME, strRule);
                if (StrUtil.isNotEmpty(rule.getRelationTable())) {
                    StrRule sr = new StrRule();
                    FixedDetail fd = new FixedDetail();
                    fd.setExpect(Boolean.TRUE);
                    fd.setType(0);
                    fd.setValue(rule.getRelationTable());
                    sr.setFixed(Collections.singletonList(fd));
                    RuleDetail rd = new RuleDetail();
                    rd.setInfo(new RuleIdInfo(-rule.getId()));
                    rd.addTableRule(ParamTable.TABLE_NAME, sr);
                    ruleDetails.add(rd);
                    tags.add(-rule.getId());
                }
                break;
            case BusinessTypeConstants.RuleType.COMMENT:
                detail.addColumnRule(ParamColumn.COLUMN_COMMENT, strRule);
                break;
            case BusinessTypeConstants.RuleType.DETAIL:
            case BusinessTypeConstants.RuleType.CUSTOM_CODE:
                detail.setLoad(RuleLoad.TAG_SINGLE);
                SingleRuleDetail single = new SingleRuleDetail();
                single.setPriority(rule.getPriority());
                single.setData(strRule);
                single.setHit(hit);
                detail.setRule(single);
                break;
            default:
                //开发阶段需要处理
                log.warn("【loadRule】{} 无效的rule type :{}", rule.getId(), rule.getRuleType());
                return false;
        }
        Long tagId = rule.getId() + PipelineConst.REDIRECT;

        detail.setInfo(new RuleIdInfo(tagId));
        ruleDetails.add(detail);
        tags.add(tagId);
        return true;
    }

    private StrSegmentRule buildStrRule(CSysRuleDetail rule) {
        StrSegmentRule res = new StrSegmentRule();
        if (BusinessTypeConstants.RuleType.CUSTOM_CODE == rule.getRuleType()) {
            res.setCFun(rule.getCustomCode());
            return res;
        }
        MatchType matchType;
        switch (rule.getAlgorithm()) {
            case BusinessTypeConstants.Algorithm.REGEX:
                RegexDetail regexDetail = new RegexDetail();
                regexDetail.setValue(rule.getRegex());
                res.setRegex(Collections.singletonList(regexDetail));
                break;
            case BusinessTypeConstants.Algorithm.FUNCTION:
                FunctionStr type = FunctionStr.getType(rule.getFunction());
                if (null == type) {
                    log.warn("【loadRule】{}函数解析失败", rule.getFunction());
                    return null;
                }
                res.setFun(type);
                break;
            case BusinessTypeConstants.Algorithm.DICT:
                DictDetail dictDetail = new DictDetail();
                dictDetail.setDictId(rule.getDictId());
                dictDetail.setLoadType(LoadType.CUSTOM);
                String substr = rule.getSubstr();
                if (StrUtil.isNotEmpty(substr)) {
                    log.warn("【loadRule】未兼容subStr {}({})", rule.getId(), rule.getBizId());
                    return null;
                }
                matchType = toMatchType(rule);
                if (null == matchType) {
                    log.warn("【loadRule】无效的match type[{}]", rule.getMatchType());
                    return null;
                }
                res.setDict(Collections.singletonList(dictDetail));
                break;
            case BusinessTypeConstants.Algorithm.ENUM:
                if (StrUtil.isEmpty(rule.getEnumContent())) {
                    return null;
                }
                List<String> enums = Func.parseEnums(rule.getEnumContent());
                if (1 == enums.size() && BusinessTypeConstants.MatchType.EQUAL == rule.getMatchType()) {
                    FixedDetail detail = new FixedDetail();
                    detail.setType(0);
                    detail.setValue(enums.get(0));
                    res.setFixed(Collections.singletonList(detail));
                    break;
                }
                DictDetail enumsDetail = new DictDetail();
                enumsDetail.setDictId(rule.getId());
                enumsDetail.setLoadType(LoadType.KEYWORD);
                matchType = toMatchType(rule);
                if (null == matchType) {
                    log.warn("【loadRule】ENUM 无效的match type[{}]", rule.getMatchType());
                    return null;
                }
                enumsDetail.setMatchType(matchType);
                res.setDict(Collections.singletonList(enumsDetail));
                break;
            default:
                log.warn("【loadRule】不存在的类别{}", rule.getAlgorithm());
                return null;

        }
        res.setLenLimit(rule.getLengthLimit());
        return res;
    }

    private MatchType toMatchType(CSysRuleDetail rule) {
        switch (rule.getMatchType()) {
            case BusinessTypeConstants.MatchType.EQUAL:
                return MatchType.EQUAL;
            case BusinessTypeConstants.MatchType.CONTAINS:
                return MatchType.CONTAINS;
            case BusinessTypeConstants.MatchType.START_WITH:
                return MatchType.START_WITH;
            case BusinessTypeConstants.MatchType.END_WITH:
                return MatchType.END_WITH;
            default:
                return null;
        }
    }

    private void loadInnerRule(Map<Long, List<TargetWithTagGroup>> innerRule, List<TargetWithTagGroup> tagGroups, List<RuleDetail> ruleDetails, Double hit) {
        StdDetailDTO dto = innerRuleService.loadInnerRule(innerRule.keySet(), true, hit);
        for (TargetWithTagGroup temp : dto.getTagGroups()) {
            List<TargetWithTagGroup> list = innerRule.get(temp.getId());
            if (null == list) {
                continue;
            }
            if (1 == list.size()) {
                TargetWithTagGroup tagGroup = list.get(0);
                temp.setId(tagGroup.getId());
                temp.setScore(tagGroup.getScore());
                if (!tagGroup.getNonTags().isEmpty()) {
                    if (null == temp.getNonTags()) {
                        temp.setNonTags(tagGroup.getNonTags());
                    } else {
                        temp.getNonTags().addAll(tagGroup.getNonTags());
                    }
                }
                tagGroups.add(temp);
                continue;
            }
            list.forEach(v -> {
                TargetWithTagGroup tagGroup = new TargetWithTagGroup();
                tagGroup.setId(v.getId());
                tagGroup.setScore(v.getScore());
                tagGroup.setTags(temp.getTags());
                if (null == temp.getNonTags()) {
                    tagGroup.setNonTags(v.getNonTags());
                } else if (v.getNonTags().isEmpty()) {
                    tagGroup.setNonTags(temp.getNonTags());
                } else {
                    List<Long> tagIds = new ArrayList<>(temp.getNonTags());
                    tagIds.addAll(v.getNonTags());
                    tagGroup.setNonTags(tagIds);
                }
                tagGroups.add(tagGroup);
            });
        }
        ruleDetails.addAll(dto.getRuleDetails());
    }
}
