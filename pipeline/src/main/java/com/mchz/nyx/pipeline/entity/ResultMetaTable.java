package com.mchz.nyx.pipeline.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 表格元数据
 * </p>
 *
 * <AUTHOR>
 */
@Data
@TableName("result_meta_table_display")
public class ResultMetaTable implements Serializable {
    private static final long serialVersionUID = 1908634073961232212L;
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 源数据发现作业id
     */
    private Long jobId;

    /**
     * 数据源id
     */
    private Long configId;

    /**
     * schema
     */
    private String schema;

    /**
     * 表格名字
     */
    private String tableName;

    /**
     * 表格注释
     */
    private String tableComment;

    /**
     * 列数量
     */
    private Integer colNum;

    /**
     * 字符串列数量
     */
    private Integer charColNum;

    /**
     * 数值列数量
     */
    private Integer numColNum;

    /**
     * 文本和图像列数量
     */
    private Integer textColNum;

    /**
     * 包含时间列数量
     */
    private Integer dateColNum;

    /**
     * 第一个时间列的位置
     */
    private Integer fdDateColPos;

    /**
     * 表格物理规模
     */
    private Long blocks;

    /**
     * 表格行数
     */
    private Long rows;

    /**
     * 主键和唯一健、唯一索引的数量
     */
    private Integer pkUniNum;

    /**
     * 联合主键的最大列数，扣除时间列的列总数
     */
    private Integer maxPkColNum;

    /**
     * 外键数量
     */
    private Integer fkNum;

    /**
     * 索引数量
     */
    private Integer idxNum;

    /**
     * 第一个时间列位置在列数量前半部分还是后半部分，0 - 前半部分，1 - 后半部分
     */
    private Integer datePstn;

    /**
     * 时间列占比
     */
    private BigDecimal dateRatio;

    /**
     * 数值列占比
     */
    private BigDecimal numRatio;

    /**
     * 字符串列占比
     */
    private BigDecimal charRatio;

    /**
     * 文本和图像列占比
     */
    private BigDecimal textRatio;

    /**
     * 是否包含出生日期列，0 - 不包含，1 - 包含
     */
    private Boolean isCsny;

    /**
     * 是否包含其他表的外键对应的主键，0 - 不包含，1 - 包含
     */
    private Boolean berefferd;

    /**
     * 是否视图，0 - 表，1 - 视图
     */
    private Boolean isView;

    /**
     * 是否为空表: 0->不是, 1->是
     */
    private Byte isEmpty;

    /**
     * 重复表类型：0=非重复表；1=重复主表；2=重复副表；
     */
    private Byte repeatType;

    /**
     * 主表表名：同数据源、schema下
     */
    private String repeatTableName;

    /**
     * 重复率
     */
    private BigDecimal repeatRate;

    public static class RepeatType {
        /**
         * 非重复表
         */
        public static final Byte NOT = 0;
        /**
         * 重复主表
         */
        public static final Byte MAIN = 1;
        /**
         * 重复副表
         */
        public static final Byte MINOR = 2;
    }
}
