package com.mchz.nyx.pipeline.thread.event;

import com.mchz.nyx.pipeline.model.log.JobLogItem;
import lombok.Getter;
import org.springframework.context.ApplicationEvent;

import java.util.Set;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/9
 */
@Getter
public class JobLogEvent extends ApplicationEvent {

    private final Set<String> processingTables;
    private final JobLogItem log;

    public JobLogEvent(Object source, Set<String> processingTables, JobLogItem log) {
        super(source);
        this.processingTables = processingTables;
        this.log = log;
    }
}
