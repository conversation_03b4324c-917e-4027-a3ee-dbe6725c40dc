package com.mchz.nyx.pipeline.model.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/22
 */
@Data
@EqualsAndHashCode(callSuper = true)
@NoArgsConstructor
public class ClassifyFileDTO extends ClassifyDTO {
    private String content;
    private RefInfoDTO ref;

    public ClassifyFileDTO(Long typeId, Integer level, String according, Double confidence, String content, RefInfoDTO ref) {
        super(typeId, level, according, confidence);
        this.content = content;
        this.ref = ref;
    }
}
