package com.mchz.nyx.pipeline.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.mchz.datasource.cli.DatasourceDatabaseCli;
import com.mchz.datasource.cli.DatasourceMetabaseCli;
import com.mchz.datasource.cli.RsResult;
import com.mchz.mcdatasource.api.model.CustomMutableSchema;
import com.mchz.mcdatasource.api.model.CustomMutableTable;
import com.mchz.mcdatasource.api.model.RSMetaData;
import com.mchz.mcdatasource.core.DataBasePluginType;
import com.mchz.mcdatasource.core.DataBaseType;
import com.mchz.mcdatasource.core.DatasourceConstant;
import com.mchz.mcdatasource.core.DbMetaDataFactory;
import com.mchz.mcdatasource.enu.OperateEnum;
import com.mchz.mcdatasource.enu.ValueTypeEnum;
import com.mchz.mcdatasource.model.constant.DbObjectType;
import com.mchz.mcdatasource.model.db.DatasourceDatabase;
import com.mchz.mcdatasource.model.db.DatasourceDatabaseMeta;
import com.mchz.mcdatasource.model.dbmeta.HiveConfig;
import com.mchz.mcdatasource.model.request.ColumnRequest;
import com.mchz.mcdatasource.model.request.QuerySqlRequest;
import com.mchz.mcdatasource.model.request.Where;
import com.mchz.mcdatasource.utils.StringUtils;
import com.mchz.nyx.common.enums.DataTypeGroup;
import com.mchz.nyx.dark.model.meta.ColumnType;
import com.mchz.nyx.meta.support.DataSourceConfig;
import com.mchz.nyx.pipeline.config.props.SourceProperties;
import com.mchz.nyx.pipeline.entity.ResultMetaColumn;
import com.mchz.nyx.pipeline.exception.ServiceException;
import com.mchz.nyx.pipeline.job.JobLogManager;
import com.mchz.nyx.pipeline.model.api.DataSourceLimitParam;
import com.mchz.nyx.pipeline.model.api.DataSourceParam;
import com.mchz.nyx.pipeline.model.api.TableDataSetParam;
import com.mchz.nyx.pipeline.model.dto.MetaColumnDTO;
import com.mchz.nyx.pipeline.model.dto.MetaColumnPlusDTO;
import com.mchz.nyx.pipeline.model.dto.MetaTableDTO;
import com.mchz.nyx.pipeline.model.other.ColumnData;
import com.mchz.nyx.pipeline.model.other.MetaTableExpand;
import com.mchz.nyx.pipeline.model.other.TriConsumer;
import com.mchz.nyx.pipeline.model.vo.ResultSetVO;
import com.mchz.nyx.pipeline.model.vo.TableVO;
import com.mchz.nyx.pipeline.service.DatabaseService;
import com.mchz.nyx.pipeline.util.DbUtil;
import com.mchz.nyx.pipeline.util.Func;
import com.mchz.nyx.pipeline.util.Lists;
import com.mchz.nyx.pipeline.util.Timer;
import com.mchz.nyx.pipeline.util.cache.ListCache;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.metamodel.schema.Column;
import org.apache.metamodel.schema.Table;
import org.apache.metamodel.schema.TableType;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.stereotype.Service;

import java.sql.Statement;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

import static org.apache.metamodel.schema.ColumnType.*;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/10/20 16:47
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RpcDatabaseServiceImpl implements DatabaseService {

    private final Map<Long, Map<Thread, DatasourceDatabaseCli>> cliCache = new ConcurrentHashMap<>();

    private final SourceProperties properties;

    @Override
    public int priority() {
        return 5;
    }

    @Override
    public boolean crawlMetadata(DataSourceLimitParam source, JobLogManager jobLog,
                                 TriConsumer<MetaTableDTO, List<MetaColumnPlusDTO>, MetaTableExpand> consumer) throws Exception {
        DataSourceConfig p = DbUtil.newDatasourceParam(source);
        if (null == p) {
            throw new IllegalArgumentException("构建DatasourceParam失败");
        }
        DataBaseType type = source.getDataBaseType();
        List<TableVO> choosedTables = Lists.newArrayList();
        if (StrUtil.isNotBlank(source.getTables())) {
            choosedTables = JSONUtil.toList(source.getTables(), TableVO.class);
        }
        List<TableVO> excludedTables = Lists.newArrayList();
        if (StrUtil.isNotBlank(source.getExcludeTables())) {
            excludedTables = JSONUtil.toList(source.getExcludeTables(),TableVO.class);
        }
        List<CustomMutableSchema> schemas = new ArrayList<>();

        try {
            Timer timer = Timer.start("采集元数据");
            Timer child1 = timer.createChild("查询元数据");
            jobLog.info("开始采集元数据信息");
            List<String> querySchemas;
            //按schema筛选
            if (CollUtil.isNotEmpty(source.getSchemaSet())) {
                querySchemas = new ArrayList<>(source.getSchemaSet());
            } else if (CollUtil.isNotEmpty(choosedTables)) {
                querySchemas = choosedTables.stream().map(TableVO::getSchema).distinct().collect(Collectors.toList());
            } else {
                querySchemas = crawlSchema(source);
            }
            jobLog.processed(querySchemas.size());
            for (String schemaName : querySchemas) {
                child1.startChild(schemaName);
                List<String> queryTables = choosedTables.stream()
                    .filter(s -> Objects.equals(s.getSchema(), schemaName))
                    .map(TableVO::getTableName).collect(Collectors.toList());
                p.getAdvanced().setProperty(DatasourceConstant.KEY_DB_LIST, schemaName);
                p.getAdvanced().setProperty(DatasourceConstant.ONLY_NEED_COLUMNS, "true");
                p.getAdvanced().setProperty(DatasourceConstant.SUPPORT_GET_TABLE_COMMENTS, "true");
                if (CollUtil.isNotEmpty(queryTables)) {
                    p.getAdvanced()
                        .setProperty(DatasourceConstant.KEY_DB_AND_TABLE_LIST, String.join(StrUtil.COMMA, queryTables));
                    DatasourceMetabaseCli metadataCli = new DatasourceMetabaseCli(p.getType(), p.getHost(), p.getDb(),
                        p.getPort(), p.getUser(), p.getPass(), true, p.getAdvanced());
                    log.info("采集元数据 高级参数[{}]", JSONUtil.toJsonStr(p.getAdvanced()));
                    schemas.addAll(metadataCli.fetchColumns());
                } else {
                    DatasourceMetabaseCli metadataCli = new DatasourceMetabaseCli(p.getType(), p.getHost(), p.getDb(),
                        p.getPort(), p.getUser(), p.getPass(), true, p.getAdvanced());
                    log.info("采集元数据 高级参数[{}]", JSONUtil.toJsonStr(p.getAdvanced()));
                    schemas.addAll(metadataCli.fetchColumns());
                }

                List<Table> tableList = schemas.stream().filter(s -> Objects.equals(s.getName(), schemaName))
                    .map(o -> o.getTables()).collect(Collectors.toList()).get(0);
                tableList.forEach(t -> jobLog.info(String.format("正在采集 %s", schemaName + "." + t.getName())));
                jobLog.complete();
                child1.endChild();
            }
            child1.end();
            if (CollUtil.isEmpty(schemas)) {
                log.info("未获取到表格信息");
            }
            Timer child3 = timer.createChild("构造元数据");

            Map<String, List<TableVO>> schemaMap = excludedTables.stream().collect(Collectors.groupingBy(TableVO::getSchema));
            for (CustomMutableSchema schema : schemas) {
                List<Table> tables = schema.getTables();
                List<String> excludeTableNames = schemaMap.getOrDefault(schema.getName(), Lists.newArrayList())
                    .stream()//
                    .map(TableVO::getTableName).collect(Collectors.toList());
                for (Table table : tables) {
                    if (excludeTableNames.contains(table.getName())) {
                        continue;
                    }
                    TableType tableType = table.getType();
                    if (!TableType.TABLE.equals(tableType) && !TableType.VIEW.equals(tableType)) {
                        continue;
                    }
                    try {
                        consumerMeta(schema, table, table.getColumns(), type, consumer);
                    } catch (Throwable e) {
                        log.warn("表格={}抽取分析异常={}", table.getName(), e.getMessage());
                        throw e;
                    }
                }
            }
            child3.end();
            timer.end();
        } catch (Exception e) {
            log.warn("元数据获取失败", e);
            throw e;
        }
        return true;
    }

    @Override
    public boolean crawlViewMetadata(DataSourceLimitParam source, JobLogManager jobLog,
                                     TriConsumer<MetaTableDTO, List<MetaColumnPlusDTO>, MetaTableExpand> consumer) throws Exception {
        DataSourceConfig p = DbUtil.newDatasourceParam(source);
        if (null == p) {
            throw new IllegalArgumentException("构建DatasourceParam失败");
        }
        DataBaseType type = source.getDataBaseType();
        List<TableVO> choosedTables = Lists.newArrayList();
        if (StrUtil.isNotBlank(source.getTables())) {
            choosedTables = JSONUtil.toList(source.getTables(), TableVO.class);
        }
        List<TableVO> excludedTables = Lists.newArrayList();
        if (StrUtil.isNotBlank(source.getExcludeTables())) {
            excludedTables = JSONUtil.toList(source.getExcludeTables(),TableVO.class);
        }
        List<CustomMutableSchema> schemas = new ArrayList<>();

        try {
            Timer timer = Timer.start("采集元数据");
            Timer child1 = timer.createChild("查询元数据");
            List<String> querySchemas;
            //按schema筛选
            if (CollUtil.isNotEmpty(source.getSchemaSet())) {
                querySchemas = new ArrayList<>(source.getSchemaSet());
            } else if (CollUtil.isNotEmpty(choosedTables)) {
                querySchemas = choosedTables.stream().map(TableVO::getSchema).distinct().collect(Collectors.toList());
            } else {
                querySchemas = crawlSchema(source);
            }
            for (String schemaName : querySchemas) {
                child1.startChild(schemaName);
                List<String> queryTables = choosedTables.stream()
                    .filter(s -> Objects.equals(s.getSchema(), schemaName))
                    .map(TableVO::getTableName).collect(Collectors.toList());
                p.getAdvanced().setProperty(DatasourceConstant.KEY_DB_LIST, schemaName);
                p.getAdvanced().setProperty(DatasourceConstant.ONLY_NEED_COLUMNS, "true");
                p.getAdvanced().setProperty(DatasourceConstant.SUPPORT_GET_TABLE_COMMENTS, "true");
                p.getAdvanced().setProperty(DatasourceConstant.SUPPORT_BACK_VIEW_OBJECT, "true");
                if (CollUtil.isNotEmpty(queryTables)) {
                    p.getAdvanced()
                        .setProperty(DatasourceConstant.KEY_DB_AND_TABLE_LIST, String.join(StrUtil.COMMA, queryTables));
                    DatasourceMetabaseCli metadataCli = new DatasourceMetabaseCli(p.getType(), p.getHost(), p.getDb(),
                        p.getPort(), p.getUser(), p.getPass(), true, p.getAdvanced());
                    log.info("采集元数据 高级参数[{}]", JSONUtil.toJsonStr(p.getAdvanced()));
                    schemas.addAll(metadataCli.fetchColumns(DbObjectType.VIEW));
                } else {
                    DatasourceMetabaseCli metadataCli = new DatasourceMetabaseCli(p.getType(), p.getHost(), p.getDb(),
                        p.getPort(), p.getUser(), p.getPass(), true, p.getAdvanced());
                    log.info("采集元数据 高级参数[{}]", JSONUtil.toJsonStr(p.getAdvanced()));
                    schemas.addAll(metadataCli.fetchColumns(DbObjectType.VIEW));
                }

                List<Table> tableList = schemas.stream().filter(s -> Objects.equals(s.getName(), schemaName))
                    .map(o -> o.getTables()).collect(Collectors.toList()).get(0);
                tableList.forEach(t -> jobLog.info(String.format("正在采集 %s", schemaName + "." + t.getName())));
                child1.endChild();
            }
            child1.end();
            if (CollUtil.isEmpty(schemas)) {
                log.info("未获取到视图信息");
            }
            Timer child3 = timer.createChild("构造元数据");

            Map<String, List<TableVO>> schemaMap = excludedTables.stream().collect(Collectors.groupingBy(TableVO::getSchema));
            for (CustomMutableSchema schema : schemas) {
                List<Table> tables = schema.getTables();
                List<String> excludeTableNames = schemaMap.getOrDefault(schema.getName(), Lists.newArrayList())
                    .stream()//
                    .map(TableVO::getTableName).collect(Collectors.toList());
                for (Table table : tables) {
                    if (excludeTableNames.contains(table.getName())) {
                        continue;
                    }
                    TableType tableType = table.getType();
                    if (!TableType.VIEW.equals(tableType)) {
                        continue;
                    }
                    try {
                        consumerMeta(schema, table, table.getColumns(), type, consumer);
                    } catch (Throwable e) {
                        log.warn("表格={}抽取分析异常={}", table.getName(), e.getMessage());
                        throw e;
                    }
                }
            }
            child3.end();
            timer.end();
        } catch (Exception e) {
            log.warn("元数据获取失败", e);
            throw e;
        }
        return true;
    }

    private void consumerMeta(CustomMutableSchema schema, Table table, List<Column> metaColumns, DataBaseType type,
                              TriConsumer<MetaTableDTO, List<MetaColumnPlusDTO>, MetaTableExpand> consumer) {
        JobLogManager jobLog = Func.getJobLog();
        MetaTableDTO tableDTO = new MetaTableDTO();
        String schemaName = schema.getName();
        tableDTO.setSchema(schemaName);
        String tableName = table.getName();
        tableDTO.setTableName(tableName);
        tableDTO.setTableComment(table.getRemarks());
        tableDTO.setRows(((CustomMutableTable) table).getCount());
        tableDTO.setIsView(false);
        List<MetaColumnPlusDTO> columnPlusDTOList = metaColumns.stream().map(column -> {
            MetaColumnPlusDTO columnPlusDTO = new MetaColumnPlusDTO();
            columnPlusDTO.setSchema(schemaName);
            columnPlusDTO.setTableName(tableName);
            columnPlusDTO.setColumnName(column.getName());
            columnPlusDTO.setColumnComment(column.getRemarks());
            columnPlusDTO.setIsPk(column.isPrimaryKey());
            columnPlusDTO.setNullable(column.isNullable());
            columnPlusDTO.setIsIndex(column.isIndexed());
            ColumnType columnType = toColumnType(column.getType(), type);
            columnPlusDTO.setColumnType(columnType);
            columnPlusDTO.setNativeType(column.getNativeType());
            Integer columnLength = column.getColumnSize();
            if (Objects.nonNull(columnLength)) {
                columnPlusDTO.setLength(Long.valueOf(String.valueOf(columnLength)));
            }
            return columnPlusDTO;
        }).collect(Collectors.toList());
        int i = 1;
        for (MetaColumnPlusDTO dto : columnPlusDTOList) {
            dto.setPosition(i++);
        }
        ColumnType struct = new ColumnType("STRUCT", DataTypeGroup.UNKNOWN);
        String columns = columnPlusDTOList.stream().filter(v -> {
            if (null == v.getColumnType()) {
                v.setColumnType(struct);
                return true;
            }
            return false;
        }).map(MetaColumnDTO::getColumnName).collect(Collectors.joining(StrUtil.COMMA));
        if (StrUtil.isNotEmpty(columns)) {
            jobLog.warn(String.format("【%s.%s】存在不支持的类型,涉及%s", schemaName, tableName, columns));
            log.warn("【统一数据源】{}.{}存在不支持的列类型,{}", schemaName, tableName, columns);
        }

        MetaTableExpand tableExpand = new MetaTableExpand();
        tableExpand.setSchemaName(schemaName);
        tableExpand.setTableName(tableName);

        consumer.accept(tableDTO, columnPlusDTOList, tableExpand);
    }

    @Override
    public List<String> crawlSchema(DataSourceParam config) {
        DataSourceConfig p = DbUtil.newDatasourceParam(config);
        if (null == p) {
            return null;
        }
        try {
            DatasourceMetabaseCli metadataCli = new DatasourceMetabaseCli(p.getType(), p.getHost(), p.getDb(),
                p.getPort(), p.getUser(), p.getPass(), true, p.getAdvanced());
            return metadataCli.fetchSchemas();
        } catch (Exception e) {
            log.error("查询schema列表失败", e);
            return null;
        }
    }

    private List<TableVO> queryTables(DataSourceConfig p) {
        if (null == p) {
            return null;
        }
        List<TableVO> tableInfos = Lists.newArrayList();
        try {
            if (SupportAllTablesAndViewsDatabaseType.isSupport(p.getType())) {
                DatasourceMetabaseCli metadataCli = new DatasourceMetabaseCli(p.getType(), p.getHost(), p.getDb(),
                    p.getPort(), p.getUser(), p.getPass(), true, p.getAdvanced());
                metadataCli.getAllTablesAndViews(DbObjectType.TABLE).forEach((schemaName, tableNames) -> {
                    String catalog = schemaName.getCatalog();
                    String schema = schemaName.getSchema();
                    String catalogAndSchema = (Objects.isNull(catalog) ? StringUtils.EMPTY : catalog + ".") + schema;
                    tableNames.forEach(tableName -> {
                        TableVO vo = new TableVO();
                        vo.setSchema(catalogAndSchema);
                        vo.setTableName(tableName);
                        tableInfos.add(vo);
                    });
                });
                if (properties.getSys().isEnableCollectView()) {
                    metadataCli.getAllTablesAndViews(DbObjectType.VIEW).forEach((schemaName, tableNames) -> {
                        String catalog = schemaName.getCatalog();
                        String schema = schemaName.getSchema();
                        String catalogAndSchema = (Objects.isNull(catalog) ? StringUtils.EMPTY : catalog + ".") + schema;
                        tableNames.forEach(tableName -> {
                            TableVO vo = new TableVO();
                            vo.setSchema(catalogAndSchema);
                            vo.setTableName(tableName);
                            tableInfos.add(vo);
                        });
                    });
                }
            } else {
                DatasourceMetabaseCli metadataCli = new DatasourceMetabaseCli(p.getType(), p.getHost(), p.getDb(),
                    p.getPort(), p.getUser(), p.getPass(), true, p.getAdvanced());
                List<String> schemas = metadataCli.fetchSchemas();
                if (CollUtil.isNotEmpty(schemas)) {
                    for (String schema : schemas) {
                        List<String> tables;
                        p.getAdvanced().setProperty(DatasourceConstant.TABLE_NOT_JOIN_SCHEMA, "true");
                        p.getAdvanced().setProperty(DatasourceConstant.KEY_DB_LIST, schema);

                        metadataCli = new DatasourceMetabaseCli(p.getType(), p.getHost(), p.getDb(),
                            p.getPort(), p.getUser(), p.getPass(), true, p.getAdvanced());
                        tables = metadataCli.fetchTables();
                        if (properties.getSys().isEnableCollectView() && SupportViewDatabaseType.isSupport(p.getType())) {
                            p.getAdvanced().setProperty(DatasourceConstant.SUPPORT_BACK_VIEW_OBJECT, "true");
                            tables.addAll(metadataCli.fetchViews());
                        }
                        if (CollUtil.isNotEmpty(tables)) {
                            List<TableVO> tempTableVOS = tables.stream().map(tableName -> TableVO.builder().schema(schema).tableName(tableName).build()).collect(Collectors.toList());
                            tableInfos.addAll(tableInfos.size(), tempTableVOS);
                        }
                    }
                }
            }
            return tableInfos;
        } catch (Exception e) {
            log.error("查询table列表失败", e);
            return null;
        }
    }

    @Override
    public List<TableVO> getTables(DataSourceParam config) {
        DataSourceConfig p = DbUtil.newDatasourceParam(config);
        if (null == p) {
            return null;
        }
        return queryTables(p);
    }

    @Override
    public ResultSetVO getTableDataSet(TableDataSetParam param, boolean close) throws Exception {
        DataSourceParam source = param.getSource();
        DatasourceDatabaseCli cli = getDatasourceDatabaseCli(param, close, source);
        if (null == cli) return null;
        try {
            QuerySqlRequest querySqlRequest = getSampleQuery(param, cli);
            RsResult rsResult = cli.openSampleQuery(querySqlRequest, 1000);
            int size = rsResult.getHeadering().length;
            List<Integer> needIndex = new ArrayList<>(size);
            List<String> metadataList = new ArrayList<>(size);
            int i = 0;
            Set<String> needColumn = param.getColumns().stream()//
                .map(ResultMetaColumn::getColumnName).collect(Collectors.toSet());
            for (RSMetaData metaData : rsResult.getHeadering()) {
                if (needColumn.contains(metaData.getColumnName())) {
                    metadataList.add(metaData.getColumnName());
                    needIndex.add(i);
                }
                i++;
            }
            List<List<Object>> rowList = Lists.newArrayList();
            Iterator<Object[]> data = rsResult.getIterator();
            while (data.hasNext()) {
                Object[] next = data.next();
                if (next == null) {
                    log.debug("【统一数据源】row is null?");
                    continue;
                }
                if (next.length != size) {
                    log.debug("【统一数据源】元数据与数据对应错误({}-{}){}", metadataList.size(), next.length,
                        Arrays.toString(next));
                    continue;
                }
                rowList.add(needIndex.stream().map(idx -> next[idx]).collect(Collectors.toList()));
            }
            rowList = RandomUtil.randomEleList(rowList, param.getSize() * param.getSampleRate() / 100);
            ClassLoader classLoader = cli.getConnection().getClass().getClassLoader();
            return new ResultSetVO(classLoader, metadataList, rowList);
        } finally {
            close(close, source, cli);
        }
    }

    @NotNull
    private static List<ColumnRequest> getColumnRequests(TableDataSetParam param) {
        return param.getColumns().stream()//
            .map(column -> new ColumnRequest(column.getColumnName(), column.getColumnType(), true))//
            .collect(Collectors.toList());
    }

    @NotNull
    private QuerySqlRequest getSampleQuery(TableDataSetParam param, DatasourceDatabaseCli cli) throws Exception {
        QuerySqlRequest querySqlRequest = new QuerySqlRequest();
        List<ColumnRequest> columnRequests = getColumnRequests(param);
        SourceProperties sourceProperties = SpringUtil.getBean(SourceProperties.class);
        querySqlRequest.setSchemaName(param.getSchema());
        querySqlRequest.setTableName(param.getTable());
        querySqlRequest.setLimit(param.getSize());
        querySqlRequest.setColumnRequests(columnRequests);
        if (null != param.getCatalog()) {
            querySqlRequest.setCatalogName(param.getCatalog());
        }

        if (sourceProperties.getSys().isEnablePartitionCondition()
            && DataBasePluginType.HIVE.pluginId.equals(param.getSource().getDataBaseType().defaultPluginId)) {
            DatasourceDatabaseMeta databaseMeta = new DatasourceDatabaseMeta(param.getSource().getDataBaseType().pluginId, null, null, null, null, null);
            List<HiveConfig> list = DbMetaDataFactory.getDbMetaDataInterface(databaseMeta.getDatabaseMeta()).getPartitions(param.getSchema(), param.getTable(), cli.getConnection());
            if (list != null && list.size() > 0) {
                HiveConfig hiveConfig = list.get(list.size() - 1);
                String partitionName = hiveConfig.getPartitionName();
                String[] conditions = partitionName.split("/");
                List<Where> wheres = new ArrayList<>();
                Arrays.stream(conditions).forEach(element -> {
                    String[] str = element.split("=");
                    if (str.length > 1) {
                        wheres.add(new Where(str[0], OperateEnum.EQUAL, str[1], ValueTypeEnum.STRING));
                    }
                });
                querySqlRequest.setWheres(wheres);
            }
        }

        return querySqlRequest;
    }

    @Override
    public ClassLoader getTableDataSet2Cache(TableDataSetParam param,
                                             ListCache<ColumnData> resultDataCache,
                                             boolean close) throws Exception {
        DataSourceParam source = param.getSource();
        DatasourceDatabaseCli cli = getDatasourceDatabaseCli(param, close, source);
        if (null == cli) return null;
        try {
            QuerySqlRequest querySqlRequest = getSampleQuery(param, cli);
            RsResult rsResult = cli.openSampleQuery(querySqlRequest, Integer.MIN_VALUE);
            final int size = rsResult.getHeadering().length;
            List<Integer> needIndex = new ArrayList<>(size);
            List<String> metadataList = new ArrayList<>(size);
            int i = 0;
            Set<String> needColumn = param.getColumns().stream()//
                .map(ResultMetaColumn::getColumnName).collect(Collectors.toSet());
            for (RSMetaData metaData : rsResult.getHeadering()) {
                String columnName = metaData.getColumnName();
                if (needColumn.contains(columnName)) {
                    metadataList.add(columnName);
                    needIndex.add(i);
                }
                i++;
            }
            Iterator<Object[]> data = rsResult.getIterator();
            List<ColumnData> dataList = new ArrayList<>();
            while (data.hasNext()) {
                Object[] next = data.next();
                if (next == null) {
                    log.debug("【统一数据源】row is null?");
                    continue;
                }
                if (next.length != size) {
                    log.debug("【统一数据源】元数据与数据对应错误({}-{}){}", metadataList.size(), next.length,
                        Arrays.toString(next));
                    continue;
                }
                List<Object> row = needIndex.stream().map(idx -> next[idx]).collect(Collectors.toList());
                dataList.add(new ColumnData(row));
                if (dataList.size() >= resultDataCache.getPartSize()) {
                    resultDataCache.addAll(dataList);
                    dataList = new ArrayList<>();
                }
            }
            if (CollectionUtils.isNotEmpty(dataList)) {
                resultDataCache.addAll(dataList);
            }
            return cli.getConnection().getClass().getClassLoader();
        } finally {
            close(close, source, cli);
        }
    }

    private void close(boolean close, DataSourceParam source, DatasourceDatabaseCli cli) {
        if (close) {
            IoUtil.close(cli);
        } else {
            Map<Thread, DatasourceDatabaseCli> map = cliCache.get(source.getId());
            if (Objects.isNull(map)) {
                map = new ConcurrentHashMap<>();
                cliCache.put(source.getId(), map);
            }
            map.put(Thread.currentThread(), cli);
        }
    }

    @Nullable
    private DatasourceDatabaseCli getDatasourceDatabaseCli(TableDataSetParam param, boolean close, DataSourceParam source) throws Exception {
        DatasourceDatabaseCli cli = null;
        if (!close) {
            Map<Thread, DatasourceDatabaseCli> map = cliCache.get(source.getId());
            if (Objects.nonNull(map)) {
                cli = map.get(Thread.currentThread());
            }
        }
        if (null == cli) {
            DataSourceConfig p = DbUtil.newDatasourceParam(source);
            if (null == p) {
                return null;
            }
            if (p.getType().equals("Odps")) {
                p.getAdvanced().setProperty("project_name", param.getSchema());
            }
            if (p.getType().equals("mysql")) {
                p.getAdvanced().setProperty("useCursorFetch", "true");
            }
            p.getAdvanced().setProperty(DatasourceConstant.NOT_SUPPORT_BIG_COLUMN_TYPE, "true");
            cli = new DatasourceDatabaseCli(p.getType(), p.getHost(), p.getDb(), p.getPort(), p.getUser(), p.getPass(),
                true, p.getAdvanced());
            cli.connect(false);
            if (Objects.equals(source.getDataBaseType(), DataBaseType.ODPS)) {
                Statement statement = cli.getConnection().createStatement();
                statement.execute("set odps.sql.type.system.odps2=true");
                statement.execute("set odps.sql.allow.fullscan=true");
                statement.execute("set odps.sql.decimal.odps2=true");
                IoUtil.close(statement);
            }
        }
        return cli;
    }

    /**
     * 测试连接
     */
    @Override
    public boolean testConnection(DataSourceParam configParam) {
        DataSourceConfig p = DbUtil.newDatasourceParam(configParam);
        if (null == p) {
            return false;
        }
        try {
            DatasourceDatabaseMeta datasourceDatabaseMeta = new DatasourceDatabaseMeta(p.getType(), p.getHost(), p.getDb(),
                p.getPort(), p.getUser(), p.getPass(), p.getAdvanced());
            DatasourceDatabase datasourceDatabase = new DatasourceDatabase(datasourceDatabaseMeta);
            datasourceDatabase.connect();
        } catch (Exception e) {
            throw new ServiceException(e);
        }
        return true;
    }

    @Override
    public void closeConnection(Long key) {
        try {
            Map<Thread, DatasourceDatabaseCli> map = cliCache.get(key);
            if (Objects.isNull(map)) {
                return;
            }
            for (Map.Entry<Thread, DatasourceDatabaseCli> entry : map.entrySet()) {
                DatasourceDatabaseCli cli = entry.getValue();
                if (Objects.isNull(cli)) {
                    continue;
                }
                try {
                    cli.close();
                    log.info("【统一数据源】主动关闭连接 线程{} 数据源{} ", entry.getKey().getName(), key);
                } catch (Exception e) {
                    log.error("【统一数据源】主动关闭连接({})失败", key, e);
                }
            }
        } finally {
            cliCache.remove(key);
        }
    }

    ColumnType toColumnType(org.apache.metamodel.schema.ColumnType type, DataBaseType databaseType) {
        if (null == type) {
            return null;
        }
        DataTypeGroup dataTypeGroup;
        switch (type.getSuperType()) {
            case BOOLEAN_TYPE:
                dataTypeGroup = DataTypeGroup.BIT;
                break;
            case LITERAL_TYPE:
                dataTypeGroup = DataTypeGroup.CHARACTER;
                break;
            case TIME_TYPE:
                dataTypeGroup = DataTypeGroup.TEMPORAL;
                break;
            case BINARY_TYPE:
                dataTypeGroup = DataTypeGroup.BINARY;
                break;
            case NUMBER_TYPE:
                dataTypeGroup = DataTypeGroup.REAL;
                break;
            case OTHER_TYPE:
            default:
                if (DataBaseType.HBASE.equals(databaseType)) {
                    dataTypeGroup = DataTypeGroup.CHARACTER;
                    break;
                }
                dataTypeGroup = DataTypeGroup.UNKNOWN;
        }
        if (TINYINT.equals(type) || SMALLINT.equals(type) || INTEGER.equals(type) || BIGINT.equals(type)) {
            dataTypeGroup = DataTypeGroup.INTEGER;
        } else if (UUID.equals(type)) {
            dataTypeGroup = DataTypeGroup.ID;
        } else if (BLOB.equals(type) || CLOB.equals(type) || NCLOB.equals(type)) {
            dataTypeGroup = DataTypeGroup.LARGE_OBJECT;
        }

        return new ColumnType(type.getName(), dataTypeGroup);
    }
}
