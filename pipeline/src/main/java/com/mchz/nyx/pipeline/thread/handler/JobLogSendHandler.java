package com.mchz.nyx.pipeline.thread.handler;

import com.lmax.disruptor.EventHandler;
import com.mchz.nyx.pipeline.model.dto.JobLogInstantDTO;
import com.mchz.nyx.pipeline.model.log.JobLogInfoDTO;
import com.mchz.nyx.pipeline.model.log.JobLogItem;
import com.mchz.nyx.pipeline.service.TaskCallbackService;
import com.mchz.nyx.pipeline.thread.event.LogItemEvent;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/11
 */
@RequiredArgsConstructor
public class JobLogSendHandler implements EventHandler<LogItemEvent> {
    private final TaskCallbackService taskCallbackService;
    private final Map<List<Object>, JobLogInstantDTO> map = new HashMap<>();

    @Override
    public void onEvent(LogItemEvent event, long sequence, boolean endOfBatch) {
        JobLogItem logItem = event.getLogItem();
        JobLogInstantDTO dto = map.computeIfAbsent(Arrays.asList(logItem.getJobId(), logItem.getTenantId()), key -> new JobLogInstantDTO(logItem.getTenantId(), logItem.getJobId()));
        dto.setPercent(logItem.getPercent());
        dto.setTables(event.getProcessingTables());
        dto.addContent(new JobLogInfoDTO(logItem));
        event.clear2();
        if (endOfBatch) {
            for (JobLogInstantDTO value : map.values()) {
                taskCallbackService.sendProgress(value);
            }
            map.clear();
        }
    }
}
