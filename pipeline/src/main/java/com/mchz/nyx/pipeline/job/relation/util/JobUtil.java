package com.mchz.nyx.pipeline.job.relation.util;

import com.mchz.starter.tenant.util.TenantUtil;
import lombok.experimental.UtilityClass;

import java.util.Objects;

@UtilityClass
public class JobUtil {
    public static String buildTaskId(Long jobId) {
        String tenantId = TenantUtil.tenant();
        if (Objects.isNull(tenantId)) {
            return String.valueOf(jobId);
        }
        return String.format("%s_%s", tenantId, jobId);
    }
}
