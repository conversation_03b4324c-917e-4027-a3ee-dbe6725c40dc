package com.mchz.nyx.pipeline.generator;

import cn.hutool.core.util.StrUtil;
import com.mchz.nyx.common.util.NyxStopWatch;
import com.mchz.nyx.dark.model.meta.AdditionalInfo;
import com.mchz.nyx.pipeline.exception.AlertException;
import com.mchz.nyx.pipeline.job.JobLogManager;
import com.mchz.nyx.pipeline.job.discovery.param.DiscoveryJobState;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import lombok.experimental.Accessors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2022/4/29 16:12
 */
@Data
@Accessors(chain = true)
public class DarkContext {
    private final Long planId;
    private final Long jobId;
    private final Long stdId;
    private final Long lastJobId;
    private final Long sourceId;

    private boolean ignoreData;
    private boolean ignoreCandidate;
    private boolean saveSample;
    private boolean overlaySample;
    private boolean tableStatus;
    private boolean useLLM;
    private boolean increment;
    private int sampleLine;
    private int sampleRate;
    private int sampleSize;
    private int columnNum;
    private long maxColumnLength;

    private Long defaultClassifyId;
    private Integer defaultLevel;

    private AdditionalInfo addInfo;

    private volatile boolean alerted = false;
    private JobLogManager jobLogManager;
    @Getter(AccessLevel.PRIVATE)
    private NyxStopWatch stopWatch;

    public long[] startChild(String taskName) {
        return stopWatch.startChild(taskName);
    }

    public void stopChild(long[] key) {
        stopWatch.stopChild(key);
    }

    public void info(String msg) {
        jobLogManager.info(msg);
    }

    public void info(String template, Object... args) {
        jobLogManager.info(StrUtil.format(template, args));
    }

    public void info(boolean ignore, String template, Object... args) {
        if (ignore) {
            return;
        }
        jobLogManager.info(StrUtil.format(template, args));
    }

    public void warn(String msg) {
        jobLogManager.warn(msg);
    }

    public void warn(String template, Object... args) {
        jobLogManager.warn(StrUtil.format(template, args));
    }

    public void warn(String msg, Throwable e) {
        jobLogManager.warn(msg, e);
    }

    public void error(String msg, Throwable e) {
        jobLogManager.error(msg, e);
    }

    public void tables(int num) {
        jobLogManager.processed(num);
    }

    public void table(String tableName) {
        jobLogManager.addProcessingTable(tableName);
    }

    public void complete() {
        jobLogManager.complete();
    }

    public void complete(String tableName) {
        jobLogManager.removeProcessingTable(tableName);
        jobLogManager.complete();
    }

    public void interrupted() {
        if (alerted) {
            throw AlertException.INSTANCE;
        }
        jobLogManager.interrupted();
    }

    public boolean isAlerted() {
        if (alerted) {
            return true;
        }
        return jobLogManager.isInterrupted();
    }

    public void alert() {
        alerted = true;
    }

    public void heartbeat(Long schemaId, Long tableId) {
        jobLogManager.getHeartbeatDetails(DiscoveryJobState.class, DiscoveryJobState::of).setSId(schemaId).setTId(tableId);
    }

    public DiscoveryJobState getState() {
        return jobLogManager.getHeartbeatDetails(DiscoveryJobState.class);
    }
}
