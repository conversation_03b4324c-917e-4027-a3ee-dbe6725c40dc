package com.mchz.nyx.pipeline.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.mchz.nyx.pipeline.common.BaseEmbedMapper;
import com.mchz.nyx.pipeline.common.constants.PipelineConst;
import com.mchz.nyx.pipeline.entity.Artifact;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/29
 */
@DS(PipelineConst.EMBED)
public interface ArtifactMapper extends BaseEmbedMapper<Artifact> {
}
