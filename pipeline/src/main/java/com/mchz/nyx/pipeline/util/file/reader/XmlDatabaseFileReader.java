package com.mchz.nyx.pipeline.util.file.reader;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.dom4j.Node;
import org.dom4j.io.SAXReader;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class XmlDatabaseFileReader extends AbstractDatabaseFileReader {
    private final String dataNode;
    private Map<String, List<String>> map = new HashMap<>();

    public XmlDatabaseFileReader(InputStream inputStream, boolean hasHead, String dataNode) {
        super(inputStream, hasHead);
        this.dataNode = dataNode;
        init();
    }

    private void init() {
        try {
            SAXReader reader = new SAXReader();
            org.dom4j.Document document = reader.read(inputStream);
            org.dom4j.Element rootElement = document.getRootElement();
            List<String> dataNodes = StrUtil.split(dataNode, StrUtil.DOT);
            List<Node> elements = rootElement.selectNodes(StrUtil.SLASH + String.join(StrUtil.SLASH, dataNodes));
            elements.forEach(v -> map.computeIfAbsent(v.getName(), k -> new ArrayList<>()).add(v.getText()));
        } catch (Exception e) {
            log.error("解析xml文件失败: ", e);
        }
    }

    @Override
    public List<String> readHead() {
        return new ArrayList<>(map.keySet());
    }

    @Override
    public List<List<String>> readData(int num) {
        List<List<String>> res = new ArrayList<>();
        List<Integer> collect = map.keySet().stream().map(k -> map.get(k).size()).limit(1).collect(Collectors.toList());
        int size = CollUtil.getFirst(collect);
        for (int i = 0; i < size; i++) {
            List<String> obj = new ArrayList<>();
            for (List<String> str : map.values()) {
                if (i < str.size()) {
                    obj.add(str.get(i));
                }
            }
            res.add(obj);
        }
        return res.stream().limit(num).collect(Collectors.toList());
    }

    @Override
    public int count() {
        return this.map.size();
    }
}
