package com.mchz.nyx.pipeline.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 * 列元数据
 * </p>
 *
 * <AUTHOR>
 */
@Data
@TableName("result_meta_column_display")
public class ResultMetaColumn implements Serializable {
    private static final long serialVersionUID = -3607816427637886516L;
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 源数据发现作业id
     */
    private Long jobId;


    /**
     * 数据源id
     */
    private Long configId;

    /**
     * schema
     */
    private String schema;

    /**
     * 表格名字
     */
    private String tableName;

    /**
     * 列名字
     */
    private String columnName;

    /**
     * 列注释
     */
    private String columnComment;

    /**
     * 列类型
     */
    private String columnType;

    /**
     * 数据库原生类型
     */
    private String nativeType;

    /**
     * 列类型组
     */
    private String groupType;

    /**
     * 是否时间列，0 - 否，1 - 是
     */
    private Boolean isDate;

    /**
     * 列长度|精度值
     */
    private Long length;

    /**
     * 刻度值
     */
    private Integer scale;

    /**
     * 长度：十进制数字（数字）或二进制数字（浮点）
     */
    private Integer dataPrecision;

    /**
     * 列位置
     */
    private Integer position;

    /**
     * 是否主键列，0 - 否，1 - 是
     */
    private Boolean isPk;

    /**
     * 主键名
     */
    private String pkName;

    /**
     * 主键列数量
     */
    private Integer pkNum;

    /**
     * 列在主键中的位置
     */
    private Integer pkPos;

    /**
     * 是否唯一列，0 - 否，1 - 是
     */
    private Boolean isUnique;

    /**
     * 唯一约束名字（包括唯一索引）
     */
    private String uniName;

    /**
     * 索引列数量
     */
    private Integer uniNum;

    /**
     * 列在唯一约束中的位置
     */
    private Integer uniPos;

    /**
     * 是否索引列，0 - 否，1 - 是
     */
    private Boolean isIndex;

    /**
     * 索引名字
     */
    private String idxName;

    /**
     * 列在索引中的位置
     */
    private Integer idxPos;

    /**
     * 是否外键，0 - 否，1 - 是
     */
    private Boolean isFk;

    /**
     * 外健名字
     */
    private String fkName;

    /**
     * 列在外健中的位置
     */
    private Integer fkPos;

    /**
     * 外健引用的表格名
     */
    private String fkRefTable;

    /**
     * 外健引用的列名字
     */
    private String fkRefColumn;

    /**
     * 约束类型
     */
    private String constraintType;

    /**
     * 是否可以为空，0 - 否，1 - 是
     */
    private Boolean nullable;

    /**
     * 列内容去重后行数
     */
    private Integer numDistinct;

    /**
     * 样本数量
     */
    private Integer sampleSize;

    /**
     * 是否唯一主键，0 - 否，1 - 是
     */
    private Boolean isUPk;

    /**
     * 是否联合主键，0 - 否，1 - 是
     */
    private Boolean isCPk;

    /**
     * 是否视图，0 - 表，1 - 视图
     */
    private Boolean isView;

    /**
     * 是否为空字段: 0->不是, 1->是
     */
    private Byte isEmpty;

    /**
     * 重复表的字段类型：0=非重复表字段；1=重复表相同字段；2=重复表不同字段
     */
    private Byte repeatType;

    /**
     * 扩展内容
     */
    private String expandContent;

    public static class RepeatType {
        /**
         * 非重复表字段
         */
        public static final Byte NOT = 0;
        /**
         * 重复表相同字段
         */
        public static final Byte SAME = 1;
        /**
         * 重复表不同字段
         */
        public static final Byte DIFFERENT = 2;
    }
}
