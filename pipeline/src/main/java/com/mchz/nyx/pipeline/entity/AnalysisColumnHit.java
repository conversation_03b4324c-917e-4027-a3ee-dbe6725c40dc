package com.mchz.nyx.pipeline.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/2/6
 */
@Data
@TableName("analysis_column_hit")
public class AnalysisColumnHit {

    @TableId
    private Long id;

    private Long planId;

    private Long jobId;

    private Long acId;

    private Integer type;

    /**
     * 1.规则识别字段数
     * 2.注释理解
     * 3.智能拼音
     * 4.智能翻译
     * 5.大模型
     * 6.特征工程
     * 7.规则匹配
     * 8.语义向量
     * 9.大模型
     */
    private Integer ruleType;
}
