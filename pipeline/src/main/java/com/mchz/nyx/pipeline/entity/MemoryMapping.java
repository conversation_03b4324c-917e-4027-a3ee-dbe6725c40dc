package com.mchz.nyx.pipeline.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mchz.nyx.pipeline.common.constants.PipelineConst;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/7/9
 */
@Data
@TableName(value = "memory_mapping", schema = PipelineConst.EMBED_SCHEMA)
public class MemoryMapping {
    /**
     * 主键
     */
    @TableId(value = "id")
    private Integer id;

    private Integer industryId;

    /**
     * 表名
     */
    private String tableName;

    /**
     * 列名
     */
    private String columnName;

    /**
     * 业务术语
     */
    private String bizName;

    /**
     * 分类
     */
    private String classify;

    /**
     * 分级
     */
    private Integer level;
}
