package com.mchz.nyx.pipeline.job.collect.stage;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import com.mchz.mcdatasource.core.DataBaseType;
import com.mchz.nyx.common.exception.InterruptedJobException;
import com.mchz.nyx.common.util.PartitionUtil;
import com.mchz.nyx.common.util.SingleProducerQueue;
import com.mchz.nyx.common.util.WaitUtil;
import com.mchz.nyx.dark.model.meta.SampleResult;
import com.mchz.nyx.meta.support.DataSourceConfig;
import com.mchz.nyx.pipeline.common.constants.PipelineConst;
import com.mchz.nyx.pipeline.common.enums.SampleStatus;
import com.mchz.nyx.pipeline.entity.SampleColumn;
import com.mchz.nyx.pipeline.entity.SampleTable;
import com.mchz.nyx.pipeline.generator.DarkContext;
import com.mchz.nyx.pipeline.generator.meta.FileMetaAdapter;
import com.mchz.nyx.pipeline.generator.sample.SampleCommonHandler;
import com.mchz.nyx.pipeline.generator.sample.SampleFileHandler;
import com.mchz.nyx.pipeline.generator.sample.SampleHandler;
import com.mchz.nyx.pipeline.job.collect.param.CollectMetaState;
import com.mchz.nyx.pipeline.model.dto.*;
import com.mchz.nyx.pipeline.model.param.TableSamplingParam;
import com.mchz.nyx.pipeline.service.FileManagerService;
import com.mchz.nyx.pipeline.service.MetaExtendService;
import com.mchz.nyx.pipeline.service.MetadataService;
import com.mchz.nyx.pipeline.util.Func;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/1/2
 */
@Slf4j
@AllArgsConstructor
public class SamplingStage {
    private static final int QUEUE = 1 << 8;
    private static final int COLUMN_BATCH_SIZE = 500;
    private static final int CONSUMERS_NUM = 4;

    private final MetadataService metadataService;
    private final MetaExtendService metaExtendService;
    private final FileManagerService fileManagerService;

    public void execute(DarkContext context, DataSourceConfig source, Set<String> schemaSet) {
        SingleProducerQueue<TableItem> queue = new SingleProducerQueue<>(QUEUE);
        AtomicBoolean finish = new AtomicBoolean();
        Long[] status = new Long[CONSUMERS_NUM];

        ThreadFactory factory = ThreadUtil.newNamedThreadFactory("Samp-", new ThreadGroup("samp." + context.getSourceId()), true, (t, e) -> log.warn("UncaughtException:{}", e.getMessage()));
        List<Thread> threads = new ArrayList<>(status.length);
        for (int i = 0; i < status.length; i++) {
            final int index = i;
            threads.add(factory.newThread(() -> consume(context, getSampleHandler(source), queue, finish, status, index)));
        }
        try {
            CollectMetaState state = context.getJobLogManager().getHeartbeatDetails(CollectMetaState.class, CollectMetaState::of);
            List<CatalogSchemaDTO> list = metadataService.listMetaSchema(context.getSourceId(), state.getSId());
            Stream<CatalogSchemaDTO> stream = list.stream();
            if (null != schemaSet) {
                stream = stream.filter(v -> schemaSet.contains(v.getName()));
            }
            stream = stream.sorted(Comparator.comparing(CatalogSchemaDTO::getOid));
            threads.forEach(Thread::start);
            InnerContext innerContext = new InnerContext(context, state, queue, status);
            if (null != state.getSId()) {
                CatalogSchemaDTO schema = list.get(0);
                if (state.getSId().equals(schema.getOid())) {
                    List<SimpleTableDTO> tables = metadataService.listMetaTable(state.getTIds());
                    if (!tables.isEmpty()) {
                        produce(innerContext, schema, tables);
                    }
                    tables = metadataService.listMetaTable(context.getSourceId(), schema.getOid(), state.getTId());
                    produce(innerContext, schema, tables);
                    stream = stream.skip(1);
                }
            }
            stream.forEach(v -> produce(innerContext, v));
        } finally {
            finish.lazySet(true);
            threads.forEach(v -> {
                try {
                    v.join();
                } catch (InterruptedException ignore) {
                }
            });
        }
    }

    private void produce(InnerContext context, CatalogSchemaDTO schema) {
        List<SimpleTableDTO> tables = metadataService.listMetaTable(context.getSourceId(), schema.getOid(), null);
        produce(context, schema, tables);
    }

    private void produce(InnerContext context, CatalogSchemaDTO schema, List<SimpleTableDTO> tables) {
        tables.sort(Comparator.comparing(SimpleTableDTO::getOid));
        Map<Long, Integer> tableStatus = metaExtendService.getTableStatus(context.getSourceId(), IdsQueryDTO.ofOrderly(tables, SimpleTableDTO::getOid));
        context.tables(tables.size());
        Map<Long, SimpleTableDTO> tableMap = new HashMap<>();
        int num = 0;
        for (SimpleTableDTO table : tables) {
            table.setStatus(tableStatus.get(table.getOid()));
            if (table.getColumnNum() > COLUMN_BATCH_SIZE) {
                push(context, schema, tableMap, null);
                num = 0;
                tableMap.clear();
                push(context, schema, null, table);
                continue;
            }
            tableMap.put(table.getOid(), table);
            num += table.getColumnNum();
            if (num > COLUMN_BATCH_SIZE) {
                push(context, schema, tableMap, null);
                num = 0;
                tableMap.clear();
            }
        }
        push(context, schema, tableMap, null);
    }


    private void push(InnerContext context, CatalogSchemaDTO schema, Map<Long, SimpleTableDTO> tables, SimpleTableDTO tableDTO) {
        if (null != tableDTO) {
            List<SimpleColumnInfo> columns = metadataService.listSimpleMetaColumn(context.getSourceId(), Collections.singletonList(tableDTO.getOid()));
            tryPush(context, new TableItem(schema, tableDTO, columns));
            return;
        }
        if (tables.isEmpty()) {
            return;
        }
        List<SimpleColumnInfo> columns = metadataService.listSimpleMetaColumn(context.getSourceId(), tables.keySet());
        Map<Long, List<SimpleColumnInfo>> map = columns.stream().collect(Collectors.groupingBy(SimpleColumnInfo::getTableId));
        map.forEach((k, v) -> tryPush(context, new TableItem(schema, tables.get(k), v)));
    }

    private void tryPush(InnerContext context, TableItem item) {
        DarkContext darkContext = context.getContext();
        SingleProducerQueue<TableItem> queue = context.getQueue();
        CollectMetaState state = context.getState();
        Long[] status = context.getStatus();
        boolean push = queue.push(item);
        for (int i = 1; !push; i++) {
            darkContext.interrupted();
            WaitUtil.applyWait(i);
            push = queue.push(item);
        }
        TableItem first = queue.peek();
        if (null != first) {
            state.setSId(first.getSchema().getOid());
            state.setTId(first.getTableId());
        } else {
            state.setSId(item.getSchema().getOid());
            state.setTId(item.getTableId());
        }
        state.setTIds(Arrays.stream(status).filter(Objects::nonNull).collect(Collectors.toList()));
    }

    private void consume(DarkContext context, SampleHandler sampleHandler, SingleProducerQueue<TableItem> queue, AtomicBoolean finish, Long[] temp, int index) {
        UpdateTableStatus updateTableStatus = UpdateTableStatus.of();
        int count = 0;
        try {
            while (!context.isAlerted()) {
                TableItem data = queue.poll();
                if (null == data) {
                    for (int i = 0; null == data; i++) {
                        if (finish.get() || context.isAlerted()) {
                            return;
                        }
                        temp[index] = null;
                        WaitUtil.applyWait(i);
                        data = queue.poll();
                    }
                }
                temp[index] = data.getTableId();
                context.table(data.getTableName());
                try {
                    int status = sampling(context, sampleHandler, data);
                    count++;
                    if (null == data.getStatus()) {
                        SampleTable sampleTable = new SampleTable();
                        sampleTable.setOid(data.getTableId());
                        sampleTable.setSourceId(context.getSourceId());
                        sampleTable.setSchemaId(data.getSchema().getOid());
                        sampleTable.setDataEmpty(status);
                        updateTableStatus.getInsert().add(sampleTable);
                    } else if (!data.getStatus().equals(status)) {
                        if (1 == status) {
                            updateTableStatus.getIsEmpty().add(data.getTableId());
                        } else {
                            updateTableStatus.getNotEmpty().add(data.getTableId());
                        }
                    } else {
                        count--;
                    }
                } finally {
                    context.complete(data.getTableName());
                }
                if (count >= PartitionUtil.DEFAULT_PART_SIZE) {
                    metaExtendService.updateTableStatus(updateTableStatus);
                    updateTableStatus.clear();
                    count = 0;
                }
            }
        } finally {
            metaExtendService.updateTableStatus(updateTableStatus);
            sampleHandler.close();
        }
    }

    private int sampling(DarkContext context, SampleHandler sampleHandler, TableItem table) {
        List<SimpleColumnInfo> columns = table.getColumns();
        String tableName = table.getSchema().getName() + "." + table.getTableName();
        TableSamplingParam tableSamplingParam = SampleHandler.buildSamplingParam(table.getSchema(), table.getTableName(), columns, context.getMaxColumnLength());
        if (tableSamplingParam.getColumns().isEmpty()) {
            context.info("抽样跳过 {}", tableName);
            return 0;
        }
        DateTime now = DateUtil.date();
        List<SampleColumn> list;
        SampleResult result;
        int type;
        try {
            context.info("抽样开始 {}", tableName);
            result = sampleHandler.sampling(context, tableSamplingParam);
            if (null == result || null == result.getTotal()) {
                type = 0;
                list = buildSampleList(context.getSourceId(), table.getTableId(), columns, now, (c, v) -> v.setStatus(SampleStatus.OTHER));
            } else if (0 == result.getTotal()) {
                list = buildSampleList(context.getSourceId(), table.getTableId(), columns, now, (c, v) -> v.setStatus(SampleStatus.CRAWL_0ROWS));
                type = 1;
            } else {
                Map<String, List<Object>> sample = result.getSample();
                list = buildSampleList(context.getSourceId(), table.getTableId(), columns, now, (c, v) -> {
                    List<Object> data = sample.get(c.getName());
                    if (null == data) {
                        v.setStatus(SampleStatus.CRAWL_BIG);
                        return;
                    }
                    if (data.stream().anyMatch(Objects::nonNull)) {
                        v.setStatus(SampleStatus.CRAWL);
                        v.setSampleData(Func.sample(data));
                    } else {
                        v.setStatus(SampleStatus.CRAWL_NULL);
                        v.setSampleData(PipelineConst.SAMPLE_EMPTY);
                    }
                });
                type = 2;
            }
            context.info("抽样完成 {}", tableName);
        } catch (InterruptedJobException e) {
            throw e;
        } catch (Exception e) {
            type = 0;
            if (log.isDebugEnabled()) {
                log.warn("【抽样失败】{}", tableName, e);
            } else {
                log.warn("【抽样失败】{},{}", tableName, Func.getMessageWithStack(e));
            }
            context.warn("抽样失败 {}", tableName);
            list = buildSampleList(context.getSourceId(), table.getTableId(), columns, now, (c, v) -> v.setStatus(SampleStatus.CRAWL_ERROR));
        }
        metaExtendService.saveSample(list, context.isOverlaySample());
        return type;
    }

    private List<SampleColumn> buildSampleList(Long sourceId, Long tableId, List<SimpleColumnInfo> columns, Date now, BiConsumer<SimpleColumnInfo, SampleColumn> sampleConsumer) {
        return columns.stream().map(v -> {
            SampleColumn sample = new SampleColumn();
            sample.setOid(v.getOid());
            sample.setSourceId(sourceId);
            sample.setTableId(tableId);
            sample.setUpdatedAt(now);
            sampleConsumer.accept(v, sample);
            return sample;
        }).collect(Collectors.toList());
    }

    private SampleHandler getSampleHandler(DataSourceConfig source) {
        if (DataBaseType.LOCAL_FILE.pluginId.equals(source.getType())) {
            return new SampleFileHandler(source, new FileMetaAdapter(fileManagerService));
        }
        return new SampleCommonHandler(metadataService, source);
    }

    @Data
    private static class InnerContext {
        private final DarkContext context;
        private final CollectMetaState state;
        private final SingleProducerQueue<TableItem> queue;
        private final Long[] status;

        public Long getSourceId() {
            return context.getSourceId();
        }

        public void tables(int num) {
            context.tables(num);
        }

    }

    @Data
    private static class TableItem {
        private final Long tableId;
        private final CatalogSchemaDTO schema;
        private final String tableName;
        private final Integer status;
        private final List<SimpleColumnInfo> columns;

        public TableItem(CatalogSchemaDTO schema, SimpleTableDTO simpleTableDTO, List<SimpleColumnInfo> v) {
            this.tableId = simpleTableDTO.getOid();
            this.schema = schema;
            this.tableName = simpleTableDTO.getName();
            this.status = simpleTableDTO.getStatus();
            this.columns = v;
        }
    }
}
