package com.mchz.nyx.pipeline.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 业务术语规则表
 */
@Data
@TableName("c_sys_rule_detail")
@Accessors(chain = true)
public class CSysRuleDetail {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 类型
     */
    private String type;

    /**
     * 业务类型序列+业务类型类型
     */
    private Long bizId;

    /**
     * 规则类型：1=列内容；2=字段名；3=列注释；6=自定义代码
     */
    private Byte ruleType;

    /**
     * 算法：1=字典；2=正则；3=函数；4=枚举
     */
    private Byte algorithm;

    /**
     * 匹配方式：1=精确；2=模糊；3=前缀；4=后缀
     */
    private Byte matchType;

    /**
     * 期望：0=反向；1=正向
     */
    private Byte expect;

    /**
     * 字典类型序列+字典类型类型
     */
    private Long dictId;

    /**
     * 正则表达式
     */
    private String regex;

    /**
     * 函数
     */
    private String function;

    /**
     * 枚举
     */
    private String enumContent;

    /**
     * 优先级
     */
    private Integer priority;

    /**
     * 可信度
     */
    private Integer degree;

    /**
     * 状态：0=停用；1=正常
     */
    private Boolean status;

    /**
     * 定制状态：1=修改；2=删除
     */
    private Byte customStatus;

    /**
     * 长文本包含业务类型josn串
     */
    private String bizData;

    /**
     * 长文本样本数据长度大于规定字符
     */
    private Integer dataLen;

    /**
     * 关联表格
     */
    private String relationTable;

    /**
     * 长度限制
     */
    private String lengthLimit;

    /**
     * 子串表达式
     */
    private String substr;

    /**
     * 自定义代码
     */
    private String customCode;

    /**
     * 数据来源：1=内置；2=自定义
     */
    private Byte dataSource;

    /**
     * 内置主键
     */
    private String sId;
}
