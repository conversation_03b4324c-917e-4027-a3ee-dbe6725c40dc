package com.mchz.nyx.pipeline.mapper;

import com.baomidou.mybatisplus.core.batch.MybatisBatch;
import com.baomidou.mybatisplus.core.metadata.MapperProxyMetadata;
import com.baomidou.mybatisplus.core.toolkit.MybatisBatchUtils;
import com.baomidou.mybatisplus.core.toolkit.MybatisUtils;
import com.mchz.nyx.common.util.PartitionUtil;
import com.mchz.nyx.meta.entity.DbMetaTable;
import com.mchz.nyx.pipeline.common.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.SqlSessionFactory;

import java.util.Collection;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
public interface DbMetaTableMapper extends BaseMapper<DbMetaTable> {

    default void updateOtherById(Collection<DbMetaTable> list, int batchSize) {
        MapperProxyMetadata mapperProxyMetadata = MybatisUtils.getMapperProxy(this);
        MybatisBatch.Method<DbMetaTable> method = new MybatisBatch.Method<>(mapperProxyMetadata.getMapperInterface());
        SqlSessionFactory sqlSessionFactory = MybatisUtils.getSqlSessionFactory(mapperProxyMetadata.getSqlSession());
        if (sqlSessionFactory.getConfiguration().hasStatement(DbMetaTableMapper.class.getName() + ".updateOtherByIdSp", false)) {
            for (List<DbMetaTable> tables : PartitionUtil.part(list, batchSize)) {
                if (1 == tables.size()) {
                    updateById(tables.get(0));
                } else {
                    updateOtherByIdSp(tables);
                }
            }
        } else {
            MybatisBatchUtils.execute(sqlSessionFactory, list, method.updateById(), batchSize);
        }
    }

    void updateOtherByIdSp(@Param("list") Collection<DbMetaTable> entityList);
}
