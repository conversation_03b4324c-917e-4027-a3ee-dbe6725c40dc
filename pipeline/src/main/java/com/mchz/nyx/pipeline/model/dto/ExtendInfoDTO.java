package com.mchz.nyx.pipeline.model.dto;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/2/10
 */
@Data
public class ExtendInfoDTO {
    private final boolean entity;
    private final List<ClassifyDTO> classify;

    public ExtendInfoDTO(boolean entity) {
        this.entity = entity;
        this.classify = new ArrayList<>(entity ? 2 : 0);
    }

    public void addClassify(Long typeId, Integer level) {
        if (null != typeId) {
            classify.add(new ClassifyDTO(typeId, level));
        }
    }

}
