package com.mchz.nyx.pipeline.util;

import cn.hutool.extra.compress.CompressUtil;
import cn.hutool.extra.compress.extractor.Extractor;
import com.mchz.nyx.pipeline.common.enums.ArchiverEnum;
import com.mchz.nyx.pipeline.util.file.extractor.RarExtractor;
import lombok.experimental.UtilityClass;

import java.io.File;
import java.io.InputStream;
import java.nio.charset.Charset;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/17
 */
@UtilityClass
public class DecompressUtil {

    public Extractor createExtractor(Charset charset, ArchiverEnum archiverEnum, File file) {
        if (ArchiverEnum.RAR.equals(archiverEnum)) {
            return new RarExtractor(file);
        }
        return CompressUtil.createExtractor(charset, archiverEnum.getName(), file);
    }

    public Extractor createExtractor(Charset charset, ArchiverEnum archiverEnum, InputStream in) {
        if (ArchiverEnum.RAR.equals(archiverEnum)) {
            return new RarExtractor(in);
        }
        return CompressUtil.createExtractor(charset, archiverEnum.getName(), in);
    }
}
