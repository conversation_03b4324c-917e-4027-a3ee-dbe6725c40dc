package com.mchz.nyx.pipeline.model.dto;

import cn.hutool.core.lang.Pair;
import com.mchz.nyx.pipeline.entity.*;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/1
 */
@Data
public class AnalysisResultDTO {
    private final List<AnalysisTable> updateTable;
    private final List<AnalysisColumn> updateAnalysis;
    private final List<AnalysisColumnTemp> tempResult;
    private final List<Long> updateNotFound;
    private final List<Long> deleteAnalysisResultIds;
    private final List<AnalysisSampleResult> insertCandidate;
    private final List<AnalysisColumnHit> insertHit;
    private Map<Long, Pair<AnalysisColumn, AnalysisColumnTemp>> mainSubSync;

    public AnalysisResultDTO() {
        this.updateTable = new ArrayList<>(0);
        this.updateAnalysis = new ArrayList<>(0);
        this.tempResult = new ArrayList<>(0);
        this.updateNotFound = new ArrayList<>(0);
        this.deleteAnalysisResultIds = new ArrayList<>(0);
        this.insertCandidate = new ArrayList<>(0);
        this.insertHit = new ArrayList<>(0);
    }

    public void combine(AnalysisResultDTO analysisResult) {
        updateTable.addAll(analysisResult.getUpdateTable());
        updateAnalysis.addAll(analysisResult.getUpdateAnalysis());
        tempResult.addAll(analysisResult.getTempResult());
        updateNotFound.addAll(analysisResult.getUpdateNotFound());
        deleteAnalysisResultIds.addAll(analysisResult.getDeleteAnalysisResultIds());
        insertCandidate.addAll(analysisResult.getInsertCandidate());
        insertHit.addAll(analysisResult.getInsertHit());
        if (null != analysisResult.getMainSubSync()) {
            if (null == mainSubSync) {
                mainSubSync = analysisResult.getMainSubSync();
            } else {
                mainSubSync.putAll(analysisResult.getMainSubSync());
            }
        }
    }
}
