package com.mchz.nyx.pipeline.model.api;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
@AllArgsConstructor
public class RecommendClassifyResp {
    private List<RecommendRes> recommendRes;

    @Data
    public static class RecommendRes {
        private String schema;
        private String tableName;
        private String content;
        private List<Classify> classifies = new ArrayList<>();
    }

    @Data
    public static class Classify {
        private Long id;
        private String name;
        private List<Long> belongs;
        private List<String> belongTypes;
        private Double score;
        // 分级
        private Integer level;
    }
}
