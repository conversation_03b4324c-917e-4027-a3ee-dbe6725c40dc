package com.mchz.nyx.pipeline.service.impl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.ssh.JschUtil;
import cn.hutool.extra.ssh.Sftp;
import com.mchz.nyx.pipeline.common.api.Status;
import com.mchz.nyx.pipeline.config.props.SourceDataProperties;
import com.mchz.nyx.pipeline.exception.ServiceException;
import com.mchz.nyx.pipeline.service.FileSyncService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.File;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2022/4/22 15:29
 * @since 1.6.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FileSyncServiceImpl implements FileSyncService {

    private final SourceDataProperties properties;
    private Sftp sftp;

    @Override
    public void syncFile(String path, File target) {
        try {
            getSftp().download(getPath(path), target);
        } catch (Exception e) {
            log.error("【文件管理】文件下载失败{}", e.getMessage());
        }
    }

    private String getPath(String path) {
        String name = FileUtil.getName(path);
        String temp = StrUtil.appendIfMissing(properties.getFile().getPath(), StrUtil.SLASH) + name;
        if (path.endsWith(temp)) {
            return temp;
        }
        temp = StrUtil.appendIfMissing(properties.getFile().getConfig(), StrUtil.SLASH) + name;
        if (path.endsWith(temp)) {
            return temp;
        }
        return path;
    }

    /**
     * 连接SFTP服务器
     */
    private Sftp getSftp() {
        if (null != sftp) {
            return sftp.reconnectIfTimeout();
        }
        String host = properties.getFileServer().getHost();
        Integer port = properties.getFileServer().getPort();
        String username = properties.getFileServer().getUsername();
        String password = properties.getFileServer().getPassword();
        try {
            sftp = JschUtil.createSftp(host, port, username, password);
        } catch (Exception e) {
            log.error("连接远程SFTP服务器失败，msg：{}", e.getMessage());
            throw new ServiceException(Status.CANNOT_CONNECT_REMOTE);
        }
        return sftp;
    }
}
