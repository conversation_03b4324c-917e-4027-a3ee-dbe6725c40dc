package com.mchz.nyx.pipeline.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.mchz.datasource.cli.DatasourceDatabaseCli;
import com.mchz.datasource.cli.RsResult;
import com.mchz.mcdatasource.api.model.CustomColumnTypeImpl;
import com.mchz.mcdatasource.core.DataBaseType;
import com.mchz.mcdatasource.core.DatasourceConstant;
import com.mchz.mcdatasource.core.callback.MetaCallBackInterface;
import com.mchz.mcdatasource.core.engine.dbobject.DbObjectEngine;
import com.mchz.mcdatasource.model.MetaColumn;
import com.mchz.mcdatasource.model.MetaDataStreamRequest;
import com.mchz.mcdatasource.model.MetaSchema;
import com.mchz.mcdatasource.model.MetaTable;
import com.mchz.mcdatasource.model.core.StreamDataType;
import com.mchz.mcdatasource.model.db.DatasourceDatabaseMeta;
import com.mchz.mcdatasource.model.dbmeta.IndexMetaData;
import com.mchz.mcdatasource.model.dbmeta.StreamForeignKeyInfo;
import com.mchz.mcdatasource.model.dbmeta.StreamIndexMetaData;
import com.mchz.nyx.common.enums.DataTypeGroup;
import com.mchz.nyx.dark.model.meta.ColumnType;
import com.mchz.nyx.meta.support.DataSourceConfig;
import com.mchz.nyx.pipeline.common.api.Status;
import com.mchz.nyx.pipeline.exception.ServiceException;
import com.mchz.nyx.pipeline.job.JobLogManager;
import com.mchz.nyx.pipeline.model.api.DataSourceLimitParam;
import com.mchz.nyx.pipeline.model.api.DataSourceParam;
import com.mchz.nyx.pipeline.model.api.TableDataSetParam;
import com.mchz.nyx.pipeline.model.dto.MetaColumnDTO;
import com.mchz.nyx.pipeline.model.dto.MetaColumnPlusDTO;
import com.mchz.nyx.pipeline.model.dto.MetaTableDTO;
import com.mchz.nyx.pipeline.model.other.ColumnData;
import com.mchz.nyx.pipeline.model.other.MetaTableExpand;
import com.mchz.nyx.pipeline.model.other.TriConsumer;
import com.mchz.nyx.pipeline.model.vo.ResultSetVO;
import com.mchz.nyx.pipeline.model.vo.TableVO;
import com.mchz.nyx.pipeline.service.DatabaseService;
import com.mchz.nyx.pipeline.util.DbUtil;
import com.mchz.nyx.pipeline.util.Func;
import com.mchz.nyx.pipeline.util.Lists;
import com.mchz.nyx.pipeline.util.Timer;
import com.mchz.nyx.pipeline.util.cache.ListCache;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.pentaho.di.core.database.DatabaseMeta;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class RpcPipelineDatabaseServiceImpl implements DatabaseService {
    private final Map<Long, Map<Thread, DatasourceDatabaseCli>> cliCache = new ConcurrentHashMap<>();

    private final RpcDatabaseServiceImpl service;

    @Override
    public int priority() {
        return 4;
    }

    @Override
    public boolean crawlMetadata(DataSourceLimitParam source, JobLogManager jobLog,
                                 TriConsumer<MetaTableDTO, List<MetaColumnPlusDTO>, MetaTableExpand> consumer) throws Exception {
        if (notSupportDb(source)) {
            return false;
        }
        List<TableVO> chosenTables = Lists.newArrayList();
        if (StrUtil.isNotBlank(source.getTables())) {
            chosenTables = JSONUtil.toList(source.getTables(), TableVO.class);
        }
        List<String> querySchemas = getQuerySchemas(source, chosenTables);
        Map<String, List<TableVO>> excludedTableMap = new HashMap<>();
        if (StrUtil.isNotBlank(source.getExcludeTables())) {
            excludedTableMap = JSONUtil.toBean(source.getExcludeTables(), new TypeReference<List<TableVO>>() {}, true)
                .stream().collect(Collectors.groupingBy(TableVO::getSchema));
        }

        DataSourceConfig datasourceParam = DbUtil.newDatasourceParam(source);
        if (Objects.isNull(datasourceParam)) {
            return false;
        }
        jobLog.info("开始采集元数据信息");
        Timer timer = Timer.start("查询元数据");
        DataBaseType type = source.getDataBaseType();
        DbObjectEngine engine = DbObjectEngine.getInstance();
        DatabaseMeta databaseMeta = buildDatabaseMeta(datasourceParam);
        jobLog.processed(querySchemas.size());
        for (String schema : querySchemas) {
            Func.isInterrupted();
            timer.startChild(schema);
            MetaDataStreamRequest request = buildMetaDataStreamRequest(source.getStreamDataTypes(), schema,
                chosenTables, excludedTableMap);
            if (Objects.isNull(request)) {
                continue;
            }
            QueryMetaCallBack callBack = new QueryMetaCallBack(service, jobLog, type, consumer);
            log.info("流式采集元数据 高级参数[{}], MetaDataStreamRequest[{}]",
                JSONUtil.toJsonStr(datasourceParam.getAdvanced()), JSONUtil.toJsonStr(request));
            engine.getMetaDataByStream(databaseMeta, request, callBack);
            Exception exception = callBack.getException();
            jobLog.complete();
            timer.endChild();
            if (Objects.nonNull(exception)) {
                log.error("采集schema {} 失败", schema, exception);
                throw exception;
            }
        }
        timer.end();
        return true;
    }

    @Override
    public boolean crawlViewMetadata(DataSourceLimitParam source, JobLogManager jobLog,
        TriConsumer<MetaTableDTO, List<MetaColumnPlusDTO>, MetaTableExpand> consumer) throws Exception {
        return service.crawlViewMetadata(source, jobLog, consumer);
    }

    @Getter
    @RequiredArgsConstructor
    private static class QueryMetaCallBack implements MetaCallBackInterface<MetaSchema> {
        private final RpcDatabaseServiceImpl service;
        private final JobLogManager jobLog;
        private final DataBaseType type;
        private final TriConsumer<MetaTableDTO, List<MetaColumnPlusDTO>, MetaTableExpand> consumer;
        private Exception exception;

        @Override
        public void process(MetaSchema metaSchema) {
            check(metaSchema);
            MetaTable metaTable = metaSchema.getTables().get(0);
            List<MetaColumn> metaColumns = metaTable.getColumns();
            jobLog.info(String.format("正在采集 %s", metaSchema.getName() + "." + metaTable.getName()));
            process(metaSchema, metaTable, metaColumns);
        }

        private void process(MetaSchema metaSchema, MetaTable metaTable, List<MetaColumn> metaColumns) {
            JobLogManager jobLog = Func.getJobLog();
            MetaTableDTO tableDTO = new MetaTableDTO();
            String schemaName = metaSchema.getName();
            tableDTO.setSchema(schemaName);
            String tableName = metaTable.getName();
            tableDTO.setTableName(tableName);
            tableDTO.setTableComment(metaTable.getRemarks());
            tableDTO.setRows(metaTable.getCount());
            tableDTO.setIsView(false);
            List<MetaColumnPlusDTO> columnPlusDTOList = metaColumns.stream().map(column -> {
                MetaColumnPlusDTO columnPlusDTO = new MetaColumnPlusDTO();
                columnPlusDTO.setSchema(schemaName);
                columnPlusDTO.setTableName(tableName);
                columnPlusDTO.setColumnName(column.getName());
                columnPlusDTO.setColumnComment(column.getRemarks());
                columnPlusDTO.setIsPk(column.isPrimaryKeyEnable());
                columnPlusDTO.setNullable(column.getNullable());
                columnPlusDTO.setIsIndex(null);
                columnPlusDTO.setColumnType(service.toColumnType(CustomColumnTypeImpl.valueOf(column.getType()), type));
                columnPlusDTO.setNativeType(column.getDataType());
                if (Objects.nonNull(column.getColumnLength())) {
                    Long columnLength = null;
                    if (Objects.equals("MAX", column.getColumnLength())) {
                        columnLength = (long) Integer.MAX_VALUE;
                    } else {
                        try {
                            columnLength = Long.valueOf(column.getColumnLength());
                        } catch (NumberFormatException e) {
                            log.error("长度 强转异常", e);
                        }
                    }
                    columnPlusDTO.setLength(columnLength);
                }

                if (Objects.nonNull(column.getScale())) {
                    Integer scale = null;
                    try {
                        scale = Integer.valueOf(column.getScale());
                    } catch (NumberFormatException e) {
                        log.error("精度 强转异常", e);
                    }
                    columnPlusDTO.setScale(scale);
                }

                return columnPlusDTO;
            }).collect(Collectors.toList());
            int i = 1;
            for (MetaColumnPlusDTO dto : columnPlusDTOList) {
                dto.setPosition(i++);
            }
            ColumnType struct = new ColumnType("STRUCT", DataTypeGroup.UNKNOWN);
            String columns = columnPlusDTOList.stream().filter(v -> {
                if (null == v.getColumnType()) {
                    v.setColumnType(struct);
                    return true;
                }
                return false;
            }).map(MetaColumnDTO::getColumnName).collect(Collectors.joining(StrUtil.COMMA));
            if (StrUtil.isNotEmpty(columns)) {
                jobLog.warn(String.format("【%s.%s】存在不支持的类型,涉及%s", schemaName, tableName, columns));
                log.warn("【统一数据源】{}.{}存在不支持的列类型,{}", schemaName, tableName, columns);
            }
            MetaTableExpand tableExpand = new MetaTableExpand();
            tableExpand.setSchemaName(schemaName);
            tableExpand.setTableName(tableName);
            List<StreamIndexMetaData> indexInfos = metaTable.getIndexInfos();
            if (CollectionUtils.isNotEmpty(indexInfos)) {
                List<MetaTableExpand.MetaIndex> indices = indexInfos.stream().map(streamIndexMetaData -> {
                    MetaTableExpand.MetaIndex index = new MetaTableExpand.MetaIndex();
                    index.setName(streamIndexMetaData.getName());
                    index.setType(streamIndexMetaData.getType());
                    index.setColumnNames(
                        streamIndexMetaData.getIndexMetaDatas().stream().map(IndexMetaData::getColumnName)
                            .collect(Collectors.toList()));
                    return index;
                }).collect(Collectors.toList());
                tableExpand.setIndices(indices);
            }

            List<StreamForeignKeyInfo> foreignInfos = metaTable.getForeignInfos();
            if (CollectionUtils.isNotEmpty(foreignInfos)) {
                List<MetaTableExpand.MetaForeignKey> foreignKeys = foreignInfos.stream().map(streamForeignKeyInfo -> {
                    MetaTableExpand.MetaForeignKey foreignKey = new MetaTableExpand.MetaForeignKey();
                    foreignKey.setName(streamForeignKeyInfo.getName());
                    foreignKey.setType(streamForeignKeyInfo.getType());
                    foreignKey.setColumns(streamForeignKeyInfo.getForeignKeyInfoList().stream().map(foreignKeyInfo -> {
                        MetaTableExpand.MetaForeignColumn foreignColumn = new MetaTableExpand.MetaForeignColumn();
                        foreignColumn.setPrimaryTableName(foreignKeyInfo.getPrimaryTableName());
                        foreignColumn.setPrimaryColumnName(foreignKeyInfo.getPrimaryColumnName());
                        foreignColumn.setForeignTableName(foreignKeyInfo.getForeignTableName());
                        foreignColumn.setForeignColumnName(foreignKeyInfo.getForeignColumnName());
                        return foreignColumn;
                    }).collect(Collectors.toList()));
                    return foreignKey;
                }).collect(Collectors.toList());
                tableExpand.setForeignKeys(foreignKeys);
            }
            consumer.accept(tableDTO, columnPlusDTOList, tableExpand);
        }

        private void check(MetaSchema metaSchema) {
            Objects.requireNonNull(metaSchema);
            List<MetaTable> tables = metaSchema.getTables();
            if (CollectionUtils.isEmpty(tables)) {
                throw new IllegalArgumentException("MetaCallBackInterface.process未返回表数据");
            }
            if (tables.size() > 1) {
                List<String> tableNames = tables.stream().map(MetaTable::getName).filter(Objects::nonNull).distinct().collect(Collectors.toList());
                throw new IllegalArgumentException(
                    String.format("MetaCallBackInterface.process返回多张表数据 %s", JSONUtil.toJsonStr(tableNames)));
            }
        }

        @Override
        public void processError(Exception e) {
            this.exception = e;
        }

        @Override
        public void processFinish() {
        }
    }

    private MetaDataStreamRequest buildMetaDataStreamRequest(List<StreamDataType> streamDataTypes, String schema,
        List<TableVO> chosenTables, Map<String, List<TableVO>> excludedTableMap) {
        MetaDataStreamRequest request = new MetaDataStreamRequest();
        request.setSchema(schema);
        if (CollectionUtils.isNotEmpty(chosenTables)) {
            // 按表选择
            List<String> excludeTableNames = excludedTableMap.getOrDefault(schema, Lists.newArrayList())//
                .stream().map(TableVO::getTableName).collect(Collectors.toList());
            List<String> queryTables = chosenTables.stream()//
                .filter(e -> Objects.equals(e.getSchema(), schema) && !excludeTableNames.contains(e.getTableName()))//
                .map(TableVO::getTableName)//
                .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(queryTables)) {
                // 表又被排除
                log.warn("{} 所有选择的表都被排除", schema);
                return null;
            }
            request.setTables(queryTables);
        }
        request.setDataTypes(streamDataTypes);
        return request;
    }

    private DatabaseMeta buildDatabaseMeta(DataSourceConfig datasourceParam) {
        DatasourceDatabaseMeta databaseMeta = new DatasourceDatabaseMeta(datasourceParam.getType(),
            datasourceParam.getHost(), datasourceParam.getDb(), datasourceParam.getPort(), datasourceParam.getUser(),
            datasourceParam.getPass());
        datasourceParam.getAdvanced()
            .forEach((o, o2) -> databaseMeta.addExtraOption(String.valueOf(o), String.valueOf(o2)));
        databaseMeta.addExtraOption(DatasourceConstant.SUPPORT_GET_TABLE_COMMENTS, "true");
        databaseMeta.addExtraOption(DatasourceConstant.GET_ORIGIN_TYPE, "true");
        return databaseMeta.getDatabaseMeta();
    }

    private List<String> getQuerySchemas(DataSourceLimitParam configParam, List<TableVO> chosenTables) {
        if (CollUtil.isNotEmpty(configParam.getSchemaSet())) {
            // 按schema
            return new ArrayList<>(configParam.getSchemaSet());
        } else if (CollUtil.isNotEmpty(chosenTables)) {
            // 按table
            return chosenTables.stream()//
                .map(TableVO::getSchema)//
                .distinct().collect(Collectors.toList());
        } else {
            // 全部schema
            return crawlSchema(configParam);
        }
    }

    private boolean notSupportDb(DataSourceLimitParam source) {
        DataBaseType databaseType = source.getDataBaseType();
        switch (databaseType) {
            case ORACLE:
            case MYSQL:
            case MYSQL_5:
            case MSSQL:
            case DB2:
            case PGSQL:
            case HIVE:
            case HIVE_FHD653:
            case RDS_MYSQL:
                break;
            default:
                return true;
        }
        return false;
    }

    @Override
    public RsResult openQuery(DataSourceParam param, String sql) throws Exception {
        if (StrUtil.isBlank(sql)) {
            throw new ServiceException(Status.SQL_CAN_NOT_BE_EMPTY);
        }
        DatasourceDatabaseCli cli = null;
        try {
            Long sourceId = param.getId();
            Map<Thread, DatasourceDatabaseCli> map = cliCache.get(sourceId);
            if (Objects.nonNull(map)) {
                cli = map.get(Thread.currentThread());
            } else {
                map = new ConcurrentHashMap<>();
                cliCache.put(sourceId, map);
            }

            if (Objects.isNull(cli)) {
                DataSourceConfig source = DbUtil.newDatasourceParam(param);
                cli = new DatasourceDatabaseCli(source.getType(), source.getHost(), source.getDb(), source.getPort(),
                    source.getUser(), source.getPass(), true, source.getAdvanced());
                cli.connect(false);
                map.put(Thread.currentThread(), cli);
            }
            return cli.openQuery(sql);
        } catch (Exception e) {
            log.error("openQuery执行失败", e);
            throw e;
        }
    }

    @Override
    public void closeConnection(Long key) {
        try {
            Map<Thread, DatasourceDatabaseCli> map = cliCache.get(key);
            if (Objects.isNull(map)) {
                return;
            }
            for (Map.Entry<Thread, DatasourceDatabaseCli> entry : map.entrySet()) {
                DatasourceDatabaseCli cli = entry.getValue();
                if (Objects.isNull(cli)) {
                    continue;
                }
                try {
                    cli.close();
                    log.info("【统一数据源】主动关闭连接 线程{} 数据源{} ", entry.getKey().getName(), key);
                } catch (Exception e) {
                    log.error("【统一数据源】主动关闭连接({})失败", key, e);
                }
            }
        } finally {
            cliCache.remove(key);
        }
    }

    @Override
    public List<String> crawlSchema(DataSourceParam config) {
        return service.crawlSchema(config);
    }

    @Override
    public List<TableVO> getTables(DataSourceParam config) {
        return service.getTables(config);
    }

    @Override
    public boolean testConnection(DataSourceParam config) {
        return service.testConnection(config);
    }

    @Override
    public ResultSetVO getTableDataSet(TableDataSetParam param, boolean close) throws Exception {
        return service.getTableDataSet(param, close);
    }

    @Override
    public ClassLoader getTableDataSet2Cache(TableDataSetParam param,
                                      ListCache<ColumnData> resultDataCache,
                                      boolean close) throws Exception {
        return service.getTableDataSet2Cache(param, resultDataCache, close);
    }
}
