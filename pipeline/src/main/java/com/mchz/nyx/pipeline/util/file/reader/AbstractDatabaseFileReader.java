package com.mchz.nyx.pipeline.util.file.reader;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.io.InputStream;
import java.util.Objects;

@Slf4j
public abstract class AbstractDatabaseFileReader implements DatabaseFileReader {
    protected final InputStream inputStream;
    protected final boolean hasHead;

    protected AbstractDatabaseFileReader(InputStream inputStream, boolean hasHead) {
        this.inputStream = inputStream;
        this.hasHead = hasHead;
    }

    @Override
    public void close() throws IOException {
        if (Objects.nonNull(inputStream)) {
            try {
                inputStream.close();
            } catch (Exception e) {
                log.warn("文件关闭失败", e);
            }
        }
    }
}
