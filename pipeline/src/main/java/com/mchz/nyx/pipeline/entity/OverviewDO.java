package com.mchz.nyx.pipeline.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 总览
 */
@Data
@TableName("overview")
public class OverviewDO implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Integer id;

    /**
     * 主键
     */
    private Integer scId;

    /**
     * 分组主键
     */
    private Long scGroupId;

    /**
     * 数据源名称
     */
    private String scConfigName;

    /**
     * 主键
     */
    private Long asId;

    /**
     * 源数据发现作业id
     */
    private Long asJobId;

    /**
     * 业务类型，字典表中业务类型id
     */
    private Long asBizId;

    private Long asClassifyId;

    /**
     * 非空行数
     */
    private Integer asNotNullRows;

    /**
     * 自动配置-I 修改业务类型id
     */
    private String asAutoConfigBizId;

    /**
     * 是否保留: 0->否; 1->是
     */
    private Integer asReserved;

    /**
     * 样本数据类型(1.空数据; 2.脏数据; 3.样本字典; 4.样本正则; 5.未匹配, 6. ID,数量,权重)
     */
    private Integer asContentType;

    /**
     * 业务类型类别: 1->单一类型; 2->长文本
     */
    private Integer sbdBizType;

    /**
     * 名称
     */
    private String sbdName;

    /**
     * 主键
     */
    private Long scdId;

    /**
     * 分级
     */
    private Long scdTypeId;

    /**
     * 敏感等级
     */
    private Integer scdLevel;

    /**
     * 所属分类，逗号分隔
     */
    private String scdBelongs;

    /**
     * 所属分类
     */
    private String scdBelongType;

    /**
     * 是否敏感 0-不敏感 1-敏感
     */
    private Boolean sgIsSensitive;

    /**
     * 主键
     */
    private Long rmcdId;

    /**
     * schema
     */
    private String rmcdSchema;

    private String rmcdTableName;

    /**
     * 列名字
     */
    private String rmcdColumnName;

    /**
     * 列注释
     */
    private String rmcdColumnComment;

    /**
     * 表格注释
     */
    private String rmtdTableComment;

    /**
     * 是否是长文本包含的业务类型：0=不是；1=是
     */
    private Boolean scl;

    /**
     * 样本数据id->analysis_sample主键
     */
    private Long slcSampleId;

    /**
     * 业务类型id
     */
    private String slcBizId;

    /**
     * 业务类型，确认业务类型字典表中业务类型id
     */
    private String asConfirmBizId;

    /**
     * 主表名称
     */
    private String rmtdRepeatTableName;

    /**
     * 数据库原生类型
     */
    private String rmcdNativeType;

    /**
     * 列长度
     */
    private Long rmcdLength;

    /**
     * 是否主键
     */
    private Integer rmcdIsPk;

    /**
     * 精度
     */
    private Integer rmcdScale;
}
