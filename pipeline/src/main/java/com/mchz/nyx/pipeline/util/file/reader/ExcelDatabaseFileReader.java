package com.mchz.nyx.pipeline.util.file.reader;

import cn.hutool.core.util.StrUtil;
import cn.idev.excel.FastExcel;
import cn.idev.excel.context.AnalysisContext;
import cn.idev.excel.event.AnalysisEventListener;
import cn.idev.excel.read.builder.ExcelReaderBuilder;
import com.mchz.nyx.pipeline.common.constants.PipelineConst;
import lombok.Getter;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class ExcelDatabaseFileReader extends AbstractDatabaseFileReader {
    private final MyAnalysisEventListener listener = new MyAnalysisEventListener();

    public ExcelDatabaseFileReader(InputStream inputStream, boolean hasHead) {
        super(inputStream, hasHead);
        init();
    }

    private void init() {
        ExcelReaderBuilder builder = FastExcel.read(inputStream, listener);
        if (hasHead) {
            builder.headRowNumber(1);
        }
        builder.sheet().doRead();
    }

    @Override
    public List<String> readHead() {
        return listener.getHeadList();
    }

    @Override
    public List<List<String>> readData(int num) {
        List<List<String>> lists = listener.getList();
        return new ArrayList<>(lists.subList(0, Math.min(num, lists.size())));
    }

    @Override
    public int count() {
        return listener.getSize();
    }

    @Getter
    private static class MyAnalysisEventListener extends AnalysisEventListener<Map<Integer, String>> {
        private int size;
        private int heads;
        private final List<String> headList = new ArrayList<>();
        private final List<List<String>> list = new ArrayList<>();

        @Override
        public void invokeHeadMap(Map<Integer, String> map, AnalysisContext context) {
            int i = 0;
            List<String> temp = new ArrayList<>();
            for (Map.Entry<Integer, String> entry : map.entrySet()) {
                if (null != entry.getValue()) {
                    i = entry.getKey();
                }
                temp.add(StrUtil.nullToDefault(entry.getValue(), PipelineConst.DEFAULT_COLUMN_PREFIX + entry.getKey()));
            }
            headList.addAll(temp.subList(0, i));
            heads = i;
        }

        @Override
        public void invoke(Map<Integer, String> map, AnalysisContext context) {
            list.add(toList(map));
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
            this.size = context.readRowHolder().getRowIndex();
        }

        private List<String> toList(Map<Integer, String> map) {
            return map.entrySet().stream().filter(v -> v.getKey() <= heads).map(Map.Entry::getValue).collect(Collectors.toList());
        }
    }
}
