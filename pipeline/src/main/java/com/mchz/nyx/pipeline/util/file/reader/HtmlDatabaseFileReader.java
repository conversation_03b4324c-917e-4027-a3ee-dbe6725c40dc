package com.mchz.nyx.pipeline.util.file.reader;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;

import java.io.File;
import java.io.InputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @createTime 2023年04月18日 18:01:00
 */
@Slf4j
public class HtmlDatabaseFileReader extends AbstractDatabaseFileReader {
    private final File file;
    private Map<String, List<String>> map = new HashMap<>();

    public HtmlDatabaseFileReader(InputStream inputStream, File file, boolean hasHead) {
        super(inputStream, hasHead);
        this.file = file;
        init();
    }

    private void init() {
        try {
            Document document = Jsoup.parse(file, "utf-8");
            Elements trs = document.select("tr");
            for (Element tr : trs) {
                Elements tds = tr.select("td");
                for (int j = 0; j < tds.size(); j++) {
                    String key = String.format("field_%s", j + 1);
                    map.computeIfAbsent(key, k -> new ArrayList<>()).add(tds.get(j).html());
                }
            }
        } catch (Exception e) {
            log.error("解析html文件失败: ", e);
        }
    }

    @Override
    public List<String> readHead() {
        return CollUtil.isEmpty(map.keySet()) ? new ArrayList<>()
            : map.keySet().stream().sorted().collect(Collectors.toList());
    }

    @Override
    public List<List<String>> readData(int num) {
        List<List<String>> res = new ArrayList<>();
        List<Integer> collect = map.keySet().stream().map(k -> map.get(k).size()).limit(1).collect(Collectors.toList());
        int size = CollUtil.getFirst(collect);
        for (int i = 0; i < size; i++) {
            List<String> obj = new ArrayList<>();
            for (List<String> str : map.values()) {
                if (i < str.size()) {
                    obj.add(str.get(i));
                }
            }
            res.add(obj);
        }
        return res.stream().limit(num).collect(Collectors.toList());
    }

    @Override
    public int count() {
        return this.map.size();
    }
}
