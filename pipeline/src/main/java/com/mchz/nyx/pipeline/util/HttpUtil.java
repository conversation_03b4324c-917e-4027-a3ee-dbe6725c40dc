package com.mchz.nyx.pipeline.util;

import cn.hutool.http.HttpGlobalConfig;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.mchz.starter.tenant.support.TenantHutoolhttpInterceptor;
import com.yomahub.tlog.hutoolhttp.TLogHutoolhttpInterceptor;
import lombok.experimental.UtilityClass;

import java.util.Map;

/**
 * <p>
 * 基于hutool Http的全链路日志请求
 * </p>
 *
 * <AUTHOR>
 * @date 2022/4/20 15:09
 * @since 1.6.0
 */
@UtilityClass
public class HttpUtil {

    private final TLogHutoolhttpInterceptor interceptor = new TLogHutoolhttpInterceptor();
    private final TenantHutoolhttpInterceptor tenantHutoolhttpInterceptor = new TenantHutoolhttpInterceptor();

    // =====GET

    public String get(String url) {
        return get(url, HttpGlobalConfig.getTimeout());
    }

    public String get(String url, int timeout) {
        return send(HttpRequest.get(url).timeout(timeout));
    }

    public String get(String url, Map<String, Object> paramMap) {
        return send(HttpRequest.get(url).form(paramMap));
    }

    // =====POST

    public String post(String url, String body) {
        return post(url, body, HttpGlobalConfig.getTimeout());
    }

    public String post(String url, String body, int timeout) {
        return post(url, body, timeout, -1);
    }

    public String post(String url, String body, int timeout, int readTimeout) {
        return send(HttpRequest.post(url).timeout(timeout).setReadTimeout(readTimeout).body(body));
    }

    public String post(String url, Map<String, Object> paramMap) {
        return post(url, paramMap, HttpGlobalConfig.getTimeout());
    }

    public String post(String url, Map<String, Object> paramMap, int timeout) {
        return send(HttpRequest.post(url).form(paramMap).timeout(timeout));
    }

    @SuppressWarnings("unchecked")
    private String send(HttpRequest request) {
        request.addInterceptor(tenantHutoolhttpInterceptor);
        request.addInterceptor(interceptor);
        try (HttpResponse response = request.execute()) {
            return response.body();
        }
    }
}
