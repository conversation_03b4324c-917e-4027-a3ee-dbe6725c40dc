package com.mchz.nyx.pipeline.service;

import cn.hutool.cache.Cache;
import cn.hutool.cache.CacheUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.creator.DataSourceProperty;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties;
import com.mchz.nyx.common.enums.DataTypeGroup;
import com.mchz.nyx.dark.engine.VectorConvEngine;
import com.mchz.nyx.dark.model.meta.AdditionalInfo;
import com.mchz.nyx.dark.model.meta.ColumnType;
import com.mchz.nyx.dark.util.NlpUtil;
import com.mchz.nyx.pipeline.common.constants.PipelineConst;
import com.mchz.nyx.pipeline.generator.ActuatorGenerator;
import com.mchz.nyx.pipeline.generator.DarkQuickActuator;
import com.mchz.nyx.pipeline.job.JobStartConfig;
import com.mchz.nyx.pipeline.model.api.ColumnReq;
import com.mchz.nyx.pipeline.model.api.DataMatchReq;
import com.mchz.nyx.pipeline.model.api.MatchResp;
import com.mchz.nyx.pipeline.model.api.TableReq;
import com.mchz.nyx.pipeline.model.dto.TableDTO;
import com.mchz.starter.dynamic.model.DataSourceDTO;
import com.mchz.starter.dynamic.support.DataSourceHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/9/29
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class QuickTaskService implements InitializingBean {
    private final DynamicDataSourceProperties properties;
    private final DataSourceHandler dataSourceHandler;

    private final ActuatorGenerator actuatorGenerator;
    private final Cache<Long, DarkQuickActuator> cache = CacheUtil.newWeakCache(600000000);

    @Override
    public void afterPropertiesSet() {
        cache.setListener((k, v) -> v.close());
    }

    public List<String> similarity(Map<String, List<String>> data) {
        try (VectorConvEngine vectorConvEngine = actuatorGenerator.generateSimilarityEngine()) {
            return data.entrySet().stream().flatMap(entry -> {
                float[] v1 = vectorConvEngine.getPreVector(entry.getKey());
                return entry.getValue().stream().map(v -> {
                    float[] v2 = vectorConvEngine.getPreVector(v);
                    return String.format("%s:%s=%f", entry.getKey(), v, NlpUtil.squaredCosineSimilarity(v1, v2));
                });
            }).collect(Collectors.toList());
        }
    }

    public MatchResp match(DataMatchReq dataMatch) {
        TableReq tableReq = dataMatch.getTable();
        TableDTO table = new TableDTO();
        if (null == tableReq) {
            table.setName(StrUtil.EMPTY);
        } else {
            table.setName(tableReq.getName());
            table.setDescription(tableReq.getComment());
        }
        List<ColumnReq> columns = getColumn(dataMatch, table);
        if (columns == null) {
            return null;
        }

        DarkQuickActuator actuator = getDarkSimpleActuator(dataMatch);

        if (actuator.isEmptyEngine()) {
            return null;
        }

        return actuator.execute(new AdditionalInfo(), table, columns);
    }

    private JobStartConfig buildJobConfig(DataMatchReq dataMatch) {
        JobStartConfig param = new JobStartConfig();
        if (null != dataMatch.getConfig()) {
            BeanUtil.copyProperties(dataMatch.getConfig(), param);
        }
        param.setStdId(dataMatch.getStdId());
        if (null == param.getAutoRule()) {
            param.setAutoRule(Boolean.TRUE);
        }
        if (dataMatch.isOnly()) {
            param.setLoadBase(false);
        }
        if (dataMatch.isTraffic()) {
            param.setTraffic(Boolean.TRUE);
        }
        if (dataMatch.isNoHis()) {
            param.setIgnoreHis(Boolean.TRUE);
        }
        if (null == dataMatch.getColumn()) {
            param.setIgnoreCandidate(Boolean.TRUE);
            param.setTraffic(Boolean.TRUE);
            param.setIgnoreHis(Boolean.TRUE);
        }
        return param;
    }

    private List<ColumnReq> getColumn(DataMatchReq dataMatch, TableDTO table) {
        List<ColumnReq> column;
        if (null != dataMatch.getColumn()) {
            table.setOid(0L);
            column = dataMatch.getColumn();
        } else {
            ColumnType columnType = new ColumnType(null, DataTypeGroup.CHARACTER);
            List<String> data;
            if (null != dataMatch.getData()) {
                data = dataMatch.getData();
            } else if (StrUtil.isNotEmpty(dataMatch.getSample())) {
                data = StrUtil.split(dataMatch.getSample(), "、");
            } else {
                return null;
            }
            Set<String> set = new HashSet<>();
            column = data.stream().filter(v -> {
                if (set.contains(v)) {
                    return false;
                }
                set.add(v);
                return true;
            }).map(v -> {
                ColumnReq dto = new ColumnReq();
                dto.setName(v);
                dto.setColumnType(columnType);
                dto.setData(Collections.singletonList(v));
                return dto;
            }).collect(Collectors.toList());
        }
        return column;
    }

    private DarkQuickActuator getDarkSimpleActuator(DataMatchReq dataMatch) {
        Long stdId = ObjUtil.defaultIfNull(dataMatch.getStdId(), 1L);
        if (dataMatch.isForce()) {
            DarkQuickActuator actuator = actuatorGenerator.generateQuick(buildJobConfig(dataMatch));
            cache.put(stdId, actuator);
            return actuator;
        } else {
            return cache.get(stdId, () -> actuatorGenerator.generateQuick(buildJobConfig(dataMatch)));
        }
    }

    public void reload() {
        cache.clear();
        DataSourceProperty property = properties.getDatasource().get(PipelineConst.EMBED);
        DataSourceDTO dto = BeanUtil.toBean(property, DataSourceDTO.class);
        dto.setName(PipelineConst.EMBED);
        dataSourceHandler.add(dto, false);
    }
}
