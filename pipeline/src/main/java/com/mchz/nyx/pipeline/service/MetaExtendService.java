package com.mchz.nyx.pipeline.service;

import com.mchz.nyx.pipeline.entity.SampleColumn;
import com.mchz.nyx.pipeline.model.dto.IdsQueryDTO;
import com.mchz.nyx.pipeline.model.dto.UpdateTableStatus;

import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/1/16
 */
public interface MetaExtendService {

    /**
     * 插入
     */
    CompletableFuture<Boolean> asyncSaveSample(List<SampleColumn> list, boolean coverSample);

    /**
     * 插入
     */
    void saveSample(List<SampleColumn> list, boolean coverSample);

    /**
     * 查询样本数据
     */
    Map<String, List<Object>> getSample(Map<Long, String> id2ColumnName);

    /**
     * 更新表格状态
     */
    void updateTableStatus(UpdateTableStatus data);

    /**
     * 填充数据
     */
    void fillNewTable(Long sourceId);

    /**
     * 填充数据
     */
    void fillTable(Long sourceId);

    /**
     * 查询表格状态
     */
    Map<Long, Integer> getTableStatus(Long sourceId, IdsQueryDTO query);
}
