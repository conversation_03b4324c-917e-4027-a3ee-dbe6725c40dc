package com.mchz.nyx.pipeline.model.dto;

import com.mchz.nyx.dark.model.dto.StdDetailDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/2/10
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StandardDetailDTO {
    private StdDetailDTO rule;
    private Map<Long, ExtendInfoDTO> infoMap;

    public StandardDetailDTO(StdDetailDTO rule) {
        this.rule = rule;
        this.infoMap = Collections.emptyMap();
    }
}
