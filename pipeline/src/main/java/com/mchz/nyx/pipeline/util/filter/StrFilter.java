package com.mchz.nyx.pipeline.util.filter;

import cn.hutool.core.collection.CollUtil;

import java.util.Arrays;
import java.util.Collection;
import java.util.HashSet;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/7/3
 */
public interface StrFilter {
    boolean check(String value);

    StrFilter ALL = v -> true;

    static StrFilter of(String... value) {
        return new WhitelistFilter(new HashSet<>(Arrays.asList(value)));
    }

    static StrFilter ofBlackWhite(Collection<String> blackList, Collection<String> whiteList) {
        if (CollUtil.isNotEmpty(blackList)) {
            return new BlacklistFilter(new HashSet<>(blackList));
        }
        if (CollUtil.isNotEmpty(whiteList)) {
            return new WhitelistFilter(new HashSet<>(whiteList));
        }
        return ALL;
    }
}
