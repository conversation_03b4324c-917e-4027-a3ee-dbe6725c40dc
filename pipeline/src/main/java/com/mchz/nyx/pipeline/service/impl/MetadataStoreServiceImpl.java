package com.mchz.nyx.pipeline.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mchz.nyx.common.exception.NyxException;
import com.mchz.nyx.common.util.PartitionUtil;
import com.mchz.nyx.meta.common.enums.MetaObjType;
import com.mchz.nyx.meta.entity.*;
import com.mchz.nyx.meta.support.SchemaDiffDetail;
import com.mchz.nyx.meta.support.TableInsertDetail;
import com.mchz.nyx.meta.support.TableUpdateDetail;
import com.mchz.nyx.pipeline.common.constants.PipelineConst;
import com.mchz.nyx.pipeline.mapper.*;
import com.mchz.nyx.pipeline.service.MetadataStoreService;
import com.mchz.nyx.pipeline.util.Func;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@AllArgsConstructor
public class MetadataStoreServiceImpl implements MetadataStoreService {
    private final DbMetaObjectMapper objectMapper;
    private final DbMetaSchemaMapper schemaMapper;
    private final DbMetaTableMapper tableMapper;
    private final DbMetaColumnMapper columnMapper;
    private final DbMetaIndexMapper indexMapper;
    private final DbMetaIndexColumnMapper indexColumnMapper;
    private final DbMetaForeignKeyMapper foreignKeyMapper;
    private final DbMetaForeignKeyInfoMapper foreignKeyInfoMapper;

    @Override
    public DbMetaObject getSchemaObj(long sourceId, String schema) {
        Wrapper<DbMetaObject> q = Wrappers.lambdaQuery(DbMetaObject.class)
            .select(DbMetaObject::getOid, DbMetaObject::getName, DbMetaObject::getDeleted, DbMetaObject::getRevision, DbMetaObject::getHashValue)
            .eq(DbMetaObject::getSourceId, sourceId)
            .eq(DbMetaObject::getPid, PipelineConst.SCHEMA_PID)
            .eq(DbMetaObject::getType, MetaObjType.SCHEMA.name())
            .eq(DbMetaObject::getName, schema);
        return objectMapper.selectOne(q);
    }

    @Override
    public List<DbMetaObject> listSchemaObj(long sourceId) {
        Wrapper<DbMetaObject> q = Wrappers.lambdaQuery(DbMetaObject.class)
            .select(DbMetaObject::getOid, DbMetaObject::getName, DbMetaObject::getDeleted, DbMetaObject::getRevision, DbMetaObject::getHashValue)
            .eq(DbMetaObject::getSourceId, sourceId)
            .eq(DbMetaObject::getPid, PipelineConst.SCHEMA_PID)
            .eq(DbMetaObject::getType, MetaObjType.SCHEMA.name());
        return objectMapper.selectList(q);
    }

    @Override
    public List<DbMetaObject> listTableOrViewObj(long sourceId, long schemaId, int type, List<String> tables) {
        Wrapper<DbMetaObject> q = Wrappers.lambdaQuery(DbMetaObject.class)
            .select(DbMetaObject::getOid, DbMetaObject::getName, DbMetaObject::getType, DbMetaObject::getDeleted, DbMetaObject::getRevision, DbMetaObject::getHashValue)
            .eq(DbMetaObject::getSourceId, sourceId)
            .eq(DbMetaObject::getPid, schemaId)
            .eq(1 == type, DbMetaObject::getType, MetaObjType.TABLE.name())
            .eq(2 == type, DbMetaObject::getType, MetaObjType.VIEW.name())
            .and(3 == type, v -> v.eq(DbMetaObject::getType, MetaObjType.TABLE.name()).or().eq(DbMetaObject::getType, MetaObjType.VIEW.name()))
            .in(null != tables, DbMetaObject::getName, tables);
        return objectMapper.selectList(q);
    }

    @Override
    public List<DbMetaObject> listObjByPid(long sourceId, long pid) {
        Wrapper<DbMetaObject> q = Wrappers.lambdaQuery(DbMetaObject.class)
            .select(DbMetaObject::getOid, DbMetaObject::getName, DbMetaObject::getType, DbMetaObject::getDeleted, DbMetaObject::getRevision, DbMetaObject::getHashValue)
            .eq(DbMetaObject::getSourceId, sourceId)
            .eq(DbMetaObject::getPid, pid);
        return objectMapper.selectList(q);
    }

    @Override
    public Map<Long, String> getComment(List<Long> tIds, List<Long> cIds) {
        Map<Long, String> map = new HashMap<>(tIds.size() + cIds.size());
        PartitionUtil.part(tIds, v -> {
            Wrapper<DbMetaTable> q = Wrappers.lambdaQuery(DbMetaTable.class).select(DbMetaTable::getOid, DbMetaTable::getComment).in(DbMetaTable::getOid, v);
            tableMapper.selectList(q, rc -> put(map, rc.getResultObject().getOid(), rc.getResultObject().getComment()));
        });
        PartitionUtil.part(cIds, v -> {
            Wrapper<DbMetaColumn> q = Wrappers.lambdaQuery(DbMetaColumn.class).select(DbMetaColumn::getOid, DbMetaColumn::getComment).in(DbMetaColumn::getOid, v);
            columnMapper.selectList(q, rc -> put(map, rc.getResultObject().getOid(), rc.getResultObject().getComment()));
        });
        return map;
    }

    @Override
    @Transactional(rollbackFor = Throwable.class)
    public void insertSchema(long sourceId, SchemaDiffDetail detail) {
        PartitionUtil.part(detail.getDeleteObjIds(), v -> updateLogicDeletedBySchemaId(sourceId, true, v));
        PartitionUtil.part(detail.getRestoreObjIds(), v -> updateLogicDeletedBySchemaId(sourceId, false, v));
        schemaMapper.alwaysUpdateById(detail.getUpdate(), PartitionUtil.DEFAULT_PART_SIZE);
        DateTime now = DateUtil.date();
        objectMapper.batchUpdate(detail.getUpdateObj(), PartitionUtil.DEFAULT_PART_SIZE,
            v -> Wrappers.lambdaUpdate(DbMetaObject.class)
                .set(DbMetaObject::getUpdatedAt, now).set(DbMetaObject::getRevision, v.getRevision()).set(DbMetaObject::getHashValue, v.getHashValue())
                .eq(DbMetaObject::getOid, v.getOid()));
        insertObj(detail.getInsertObj());
        processBatches(detail.getInsert(), schemaMapper::insertList);
    }

    @Override
    @Async(PipelineConst.STORE_THREAD)
    @Transactional(rollbackFor = Throwable.class)
    public CompletableFuture<Boolean> insertTableDetail(long sourceId, TableInsertDetail detail) {
        insertObj(detail.getTableObjects());
        insertObj(detail.getObjects());
        processBatches(detail.getTables(), tableMapper::insertList);
        processBatches(detail.getColumns(), columnMapper::insertList);
        processBatches(detail.getIndex(), indexMapper::insertList);
        processBatches(detail.getIndexColumns(), indexColumnMapper::insertList);
        processBatches(detail.getForeignKeys(), foreignKeyMapper::insertList);
        processBatches(detail.getForeignKeyInfos(), foreignKeyInfoMapper::insertList);
        return CompletableFuture.completedFuture(Boolean.TRUE);
    }

    @Override
    @Async(PipelineConst.STORE_THREAD)
    @Transactional(rollbackFor = Throwable.class)
    public CompletableFuture<Boolean> updateTableDetail(long sourceId, TableUpdateDetail detail) {
        PartitionUtil.part(detail.getDeleteObjIds(), v -> deleteObject(sourceId, v));
        PartitionUtil.part(detail.getDeleteTableIds(), v -> deleteObjectAndRelated(sourceId, v));
        PartitionUtil.part(detail.getDeleteIndexColumnIds(), this::deleteIndexColumn);
        PartitionUtil.part(detail.getDeleteFkInfoIds(), this::deleteFkInfo);

        PartitionUtil.part(detail.getRestoreTableIds(), v -> restoreObjectAndRelated(sourceId, v));
        PartitionUtil.part(detail.getRestoreObjIds(), v -> restoreObject(sourceId, v));

        if (CollUtil.isNotEmpty(detail.getUpdateTable())) {
            tableMapper.alwaysUpdateById(detail.getUpdateTable(), PartitionUtil.DEFAULT_PART_SIZE);
        }
        if (CollUtil.isNotEmpty(detail.getUpdateTableOther())) {
            tableMapper.updateOtherById(detail.getUpdateTableOther(), PartitionUtil.DEFAULT_PART_SIZE);
        }
        if (CollUtil.isNotEmpty(detail.getUpdateColumn())) {
            columnMapper.alwaysUpdateById(detail.getUpdateColumn(), PartitionUtil.DEFAULT_PART_SIZE);
        }
        if (CollUtil.isNotEmpty(detail.getUpdateIndex())) {
            indexMapper.updateById(detail.getUpdateIndex(), PartitionUtil.DEFAULT_PART_SIZE);
        }

        if (CollUtil.isNotEmpty(detail.getUpdateFk())) {
            foreignKeyMapper.updateById(detail.getUpdateFk(), PartitionUtil.DEFAULT_PART_SIZE);
        }
        if (CollUtil.isNotEmpty(detail.getUpdateObj())) {
            DateTime now = DateUtil.date();
            Func.retry(() -> objectMapper.batchUpdate(detail.getUpdateObj(), PartitionUtil.DEFAULT_PART_SIZE,
                    v -> Wrappers.lambdaUpdate(DbMetaObject.class)
                        .set(null != v.getDescription(), DbMetaObject::getDescription, v.getDescription()).set(DbMetaObject::getUpdatedAt, now).set(DbMetaObject::getRevision, v.getRevision()).set(DbMetaObject::getHashValue, v.getHashValue())
                        .eq(DbMetaObject::getOid, v.getOid())),
                3, "【元数据】元数据更新失败：{}");
        }
        return insertTableDetail(sourceId, detail);
    }

    private void restoreObject(long sourceId, List<Long> ids) {
        updateLogicDeleted(sourceId, ids, false, false);
    }

    private void restoreObjectAndRelated(long sourceId, List<Long> ids) {
        updateLogicDeleted(sourceId, ids, false, true);
    }

    private void deleteObject(long sourceId, List<Long> ids) {
        updateLogicDeleted(sourceId, ids, true, false);
    }

    private void deleteObjectAndRelated(long sourceId, List<Long> ids) {
        updateLogicDeleted(sourceId, ids, true, true);
    }

    private void updateLogicDeleted(long sourceId, List<Long> ids, boolean deleted, boolean related) {
        DateTime now = DateUtil.date();
        if (related) {
            LambdaUpdateWrapper<DbMetaObject> update = Wrappers.lambdaUpdate(DbMetaObject.class)
                .set(DbMetaObject::getDeleted, deleted).set(!deleted, DbMetaObject::getCreatedAt, now).set(DbMetaObject::getUpdatedAt, now)
                .eq(DbMetaObject::getSourceId, sourceId).in(DbMetaObject::getPid, ids);
            objectMapper.update(update);
        }
        LambdaUpdateWrapper<DbMetaObject> update = Wrappers.lambdaUpdate(DbMetaObject.class)
            .set(DbMetaObject::getDeleted, deleted).set(!deleted, DbMetaObject::getCreatedAt, now).set(DbMetaObject::getUpdatedAt, now)
            .in(DbMetaObject::getOid, ids);
        objectMapper.update(update);
    }

    private void deleteIndexColumn(List<Long> indexIds) {
        Wrapper<DbMetaIndexColumn> wrapper = Wrappers.lambdaQuery(DbMetaIndexColumn.class).in(DbMetaIndexColumn::getIndexId, indexIds);
        indexColumnMapper.delete(wrapper);
    }

    private void deleteFkInfo(List<Long> fkIds) {
        Wrapper<DbMetaForeignKeyInfo> wrapper = Wrappers.lambdaQuery(DbMetaForeignKeyInfo.class).in(DbMetaForeignKeyInfo::getFkId, fkIds);
        foreignKeyInfoMapper.delete(wrapper);
    }

    private void updateLogicDeletedBySchemaId(long sourceId, boolean deleted, List<Long> ids) {
        Wrapper<DbMetaObject> update = Wrappers.lambdaUpdate(DbMetaObject.class).set(DbMetaObject::getDeleted, deleted).in(DbMetaObject::getOid, ids);
        objectMapper.update(update);
        Wrapper<DbMetaObject> q = Wrappers.lambdaQuery(DbMetaObject.class).select(DbMetaObject::getOid).eq(DbMetaObject::getSourceId, sourceId).in(DbMetaObject::getPid, ids);
        List<DbMetaObject> list = objectMapper.selectList(q);
        PartitionUtil.part(list.stream().map(DbMetaObject::getOid).collect(Collectors.toList()), v -> updateLogicDeleted(sourceId, v, deleted, true));
    }

    private void insertObj(List<DbMetaObject> list) {
        if (list.isEmpty()) {
            return;
        }
        Date now = DateUtil.date();
        list.forEach(v -> {
            v.setRevision(1);
            v.setDeleted(false);
            v.setCreatedAt(now);
            v.setUpdatedAt(now);
        });
        try {
            Func.processBatches(list, objectMapper::insertList, "【元数据】插入失败：{}");
        } catch (DuplicateKeyException e) {
            String key = StrUtil.subBetween(e.getMessage(), "'");
            List<String> keys = StrUtil.split(key, StrUtil.DASHED, 4, false, false);
            Long sourceId = Long.valueOf(keys.get(0));
            Long pid = Long.valueOf(keys.get(1));
            String type = keys.get(2);
            String name = keys.get(3);
            DbMetaObject parent = ObjUtil.defaultIfNull(objectMapper.selectOne(Wrappers.lambdaQuery(DbMetaObject.class).select(DbMetaObject::getOid, DbMetaObject::getName, DbMetaObject::getType).eq(DbMetaObject::getOid, pid)), DbMetaObject::new);
            try {
                AtomicInteger i = new AtomicInteger();
                list.stream().collect(Collectors.groupingBy(v -> Arrays.asList(v.getSourceId(), v.getPid(), v.getType(), v.getName()), Collectors.counting()))
                    .entrySet().stream().filter(v -> v.getValue() > 1).collect(Collectors.toList())
                    .forEach(v -> {
                        i.incrementAndGet();
                        log.error("【数据异常】采样 {}({})", v.getKey(), v.getValue());
                    });
                int num = i.get();
                if (num > 0) {
                    log.error("【数据异常】{}({}) {},{}", parent.getName(), parent.getType(), key, num);
                } else {
                    List<DbMetaObject> tmp = objectMapper.selectList(Wrappers.lambdaQuery(DbMetaObject.class).select(DbMetaObject::getOid, DbMetaObject::getName).eq(DbMetaObject::getSourceId, sourceId).eq(DbMetaObject::getPid, pid).eq(DbMetaObject::getType, type).eq(DbMetaObject::getName, name));
                    log.error("【数据异常】{}({})", parent.getName(), parent.getType());
                    tmp.forEach(v -> log.error("【数据异常】[{}]:[{}({})]", name, v.getName(), v.getOid()));
                }
            } catch (Exception ex) {
                log.warn("【数据异常】异常自动解析失败,{}", Func.getMessage(ex), e);
            }
            throw new NyxException(String.format("异常数据,[%s]", key));
        }
    }

    private <E> void processBatches(List<E> collection, Consumer<List<E>> consumer) {
        Func.processBatches(collection, consumer, "【元数据】{}");
    }

    private void put(Map<Long, String> map, Long key, String value) {
        if (null != value) {
            map.put(key, value);
        }
    }
}
