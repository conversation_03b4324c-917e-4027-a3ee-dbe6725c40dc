package com.mchz.nyx.pipeline.generator;

import cn.hutool.cache.Cache;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import com.mchz.nyx.common.util.WeakLFUCache;
import com.mchz.nyx.dark.common.constants.DarkConst;
import com.mchz.nyx.dark.engine.VectorConvEngine;
import com.mchz.nyx.dark.factory.AbstractDarkActuator;
import com.mchz.nyx.dark.model.dto.ActuatorParam;
import com.mchz.nyx.dark.model.meta.MetaColumnData;
import com.mchz.nyx.dark.util.NlpUtil;
import com.mchz.nyx.pipeline.common.constants.PipelineConst;
import com.mchz.nyx.pipeline.model.dto.*;
import com.mchz.nyx.pipeline.util.Func;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/6/28
 */
@Slf4j
public class DarkClassifyActuator extends AbstractDarkActuator<TableDTO, NyxMetaColumnDTO, ResultColumnDTO, ResultTableColumnDTO> {

    private final Cache<String, List<ClassifyTargetDTO>> cache;
    private final VectorConvEngine vectorConvEngine;
    private final List<ClassifyVectorDTO> types;

    private final float baseline;
    private final int num;
    private final ClassifyRecommender recommender;

    public DarkClassifyActuator(ActuatorParam stages, VectorConvEngine vectorConvEngine, List<ClassifyVectorDTO> types, float baseline, int num, ClassifyRecommender recommender) {
        super(stages);

        this.cache = new WeakLFUCache<>(500, DarkConst.CACHE_TIMEOUT);
        this.vectorConvEngine = vectorConvEngine;
        this.types = types;
        this.baseline = baseline;
        this.num = num;

        this.recommender = recommender;
    }

    @Override
    protected ResultColumnDTO buildColumnResultObj(NyxMetaColumnDTO meta, MetaColumnData columnData, List<Object> data) {
        boolean empty = columnData.isLoad() && 0 == columnData.getNonRepetitiveNum();
        return new ResultColumnDTO(columnData, meta, empty ? Collections.emptyList() : CollUtil.sub(data, 0, PipelineConst.MAX_SAMPLE_DATA_SIZE));
    }

    @Override
    protected ResultTableColumnDTO buildResult(TableDTO tableMeta, List<ResultColumnDTO> columns) {
        return new ResultTableColumnDTO(tableMeta, columns);
    }

    public List<ClassifyTargetDTO> recommendTableClassify(String name, String des) {
        if (null == vectorConvEngine) {
            return Collections.emptyList();
        }
        if (StrUtil.isEmpty(des)) {
            return Collections.emptyList();
        }
        return cache.get(des, () -> {
            try {
                float[] vector = vectorConvEngine.getVector(des);
                if (null == vector) {
                    return Collections.emptyList();
                }
                List<ClassifyTargetDTO> res = new ArrayList<>(num);
                for (ClassifyVectorDTO type : types) {
                    float score = NlpUtil.squaredCosineSimilarity(vector, type.getVector());
                    if (score > baseline) {
                        ClassifyTargetDTO classify = new ClassifyTargetDTO();
                        classify.setTarget(type.getTarget());
                        classify.setScore(Math.sqrt(score));
                        res.add(classify);
                        if (res.size() >= num) {
                            break;
                        }
                    }
                }
                res.sort((o1, o2) -> o2.getScore().compareTo(o1.getScore()));
                return res;
            } catch (Exception e) {
                log.warn("【表格分类推荐】{}({}),{}", name, des, Func.getMessage(e));
                return Collections.emptyList();
            }
        });
    }

    public void recommendColumnClassify(DarkContext context, ResultTableColumnDTO result) {
        recommender.recommendColumnClassify(context, result);
    }

    @Override
    public void close() {
        super.close();
        IoUtil.close(recommender);
        IoUtil.close(vectorConvEngine);
        cache.clear();
    }
}
