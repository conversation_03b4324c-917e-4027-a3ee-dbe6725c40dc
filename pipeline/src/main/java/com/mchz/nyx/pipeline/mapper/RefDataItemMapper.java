package com.mchz.nyx.pipeline.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.mchz.nyx.pipeline.common.BaseEmbedMapper;
import com.mchz.nyx.pipeline.common.constants.PipelineConst;
import com.mchz.nyx.pipeline.entity.RefDataItem;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/29
 */
@DS(PipelineConst.EMBED)
public interface RefDataItemMapper extends BaseEmbedMapper<RefDataItem> {
}
