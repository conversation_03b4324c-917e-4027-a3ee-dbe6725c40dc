package com.mchz.nyx.pipeline.config;

import com.mchz.nyx.pipeline.config.props.JdbcProperties;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.quartz.QuartzDataSource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.sql.DataSource;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/15
 */
@Slf4j
@Configuration
public class SourceDataConfig {

    @Bean
    @QuartzDataSource
    public DataSource dataSource(JdbcProperties properties) {
        HikariDataSource dataSource = new HikariDataSource();
        dataSource.setJdbcUrl(properties.getUrl());
        dataSource.setUsername(properties.getUsername());
        dataSource.setPassword(properties.getPassword());
        dataSource.setDriverClassName(properties.getDriverClassName());

        dataSource.setMinimumIdle(5);
        dataSource.setConnectionTestQuery("SELECT 1");
        dataSource.setMaximumPoolSize(properties.getMaxPoolSize());
        dataSource.setAutoCommit(true);
        dataSource.setIdleTimeout(30000);
        dataSource.setPoolName("dark-dbms");
        dataSource.setMaxLifetime(60000);
        dataSource.setConnectionTimeout(properties.getConnectionTimeout());
        return dataSource;
    }

}
