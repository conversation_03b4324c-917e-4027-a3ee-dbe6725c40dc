package com.mchz.nyx.pipeline.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mchz.nyx.pipeline.common.constants.PipelineConst;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-29
 */
@Data
@TableName(value = "term_tag", schema = PipelineConst.EMBED_SCHEMA)
public class TermTag implements Serializable {
    @TableId
    private Long id;

    private Long termId;

    private Long groupId;

    private Long tagId;

    private Boolean expect;
}
