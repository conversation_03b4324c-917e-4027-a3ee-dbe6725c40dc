package com.mchz.nyx.pipeline.model.dto;

import com.mchz.nyx.dark.model.TargetResult;
import com.mchz.nyx.dark.model.meta.MetaColumnData;
import com.mchz.nyx.pipeline.common.enums.ClassifyHitType;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/1/15
 */
@Data
public class ResultColumnDTO {
    @Getter(AccessLevel.PRIVATE)
    private final MetaColumnData columnData;
    private final NyxMetaColumnDTO meta;
    private final List<Object> data;
    @Setter(AccessLevel.PRIVATE)
    private ClassifyHitType ruleType;
    @Setter(AccessLevel.PRIVATE)
    private ClassifyDTO classify;

    private List<ClassifyDTO> classifyList;

    public List<TargetResult> getResult() {
        return columnData.getResult();
    }

    public TargetResult getFirst() {
        return columnData.getTop();
    }

    public String getBizName() {
        TargetResult rec = columnData.getTop();
        if (null != rec && null != rec.getInfo() && null != rec.getInfo().getName()) {
            return rec.getInfo().getName();
        }
        return columnData.getComment();
    }

    public void setClassify(ClassifyDTO classify, ClassifyHitType type) {
        this.classify = classify;
        this.ruleType = type;
        this.classifyList = null;
        TargetResult first = getFirst();
        if (null != first) {
            first.updateTargetInfo(classify.getTypeId(), classify.getLevel());
        }
    }
}
