package com.mchz.nyx.pipeline.generator.file;

import cn.hutool.core.io.FileUtil;
import com.mchz.nyx.pipeline.exception.ServiceException;
import lombok.AllArgsConstructor;

import java.io.File;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/29
 */
@AllArgsConstructor
public class LocalFileHandler implements FileHandler {

    @Override
    public void loadFile(String source, File target) {
        File file = new File(source);
        if (!file.exists()) {
            throw new ServiceException("文件不存在," + file.getName());
        }
        FileUtil.move(file, target, true);
    }

    @Override
    public void close() {
    }
}
