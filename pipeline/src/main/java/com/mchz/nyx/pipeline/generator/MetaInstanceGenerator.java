package com.mchz.nyx.pipeline.generator;

import cn.hutool.core.collection.ListUtil;
import com.mchz.mcdatasource.core.DataBaseType;
import com.mchz.nyx.meta.NyxInstance;
import com.mchz.nyx.meta.common.enums.MetaObjType;
import com.mchz.nyx.meta.instance.AbstractTaskInstanceGenerator;
import com.mchz.nyx.meta.instance.crawl.DsMetaAdapter;
import com.mchz.nyx.meta.instance.crawl.NyxEventCrawl;
import com.mchz.nyx.meta.instance.sink.NyxEventSink;
import com.mchz.nyx.meta.instance.store.NyxEventStore;
import com.mchz.nyx.meta.support.DataSourceConfig;
import com.mchz.nyx.meta.support.SchemaTables;
import com.mchz.nyx.pipeline.common.enums.DatabaseType;
import com.mchz.nyx.pipeline.config.props.SourceProperties;
import com.mchz.nyx.pipeline.generator.meta.FileMetaAdapter;
import com.mchz.nyx.pipeline.generator.meta.MetaFileMetaAdapter;
import com.mchz.nyx.pipeline.generator.meta.UnFileMetaAdapter;
import com.mchz.nyx.pipeline.job.JobLogManager;
import com.mchz.nyx.pipeline.service.FileManagerService;
import com.mchz.nyx.pipeline.service.MetadataStoreService;
import com.mchz.nyx.pipeline.util.DbUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;


/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
@Component
@AllArgsConstructor
public class MetaInstanceGenerator extends AbstractTaskInstanceGenerator<MetaCrawlParam> {
    private final SourceProperties properties;
    private final MetadataStoreService service;
    private final FileManagerService fileManagerService;

    public NyxInstance generate(DataSourceConfig source, JobLogManager jobLog, List<SchemaTables> schemaTables, String lastSchema, Consumer<String> callback) {
        List<MetaObjType> types = ListUtil.toList(MetaObjType.SCHEMA, MetaObjType.COLUMN);
        if (properties.getSys().isEnableCollectView()) {
            types.add(MetaObjType.VIEW);
        }
        MetaCrawlParam config = MetaCrawlParam.builder()
            .source(source)
            .schemaTables(null == schemaTables ? new ArrayList<>() : schemaTables)
            .lastSchema(lastSchema)
            .crawl(types)
            .jobLogManager(jobLog)
            .callback(callback)
            .build();
        return generate(config);
    }

    @Override
    protected NyxEventSink buildEventSink(MetaCrawlParam config, NyxEventStore store) {
        NyxEventSink nyxEventSink = super.buildEventSink(config, store);
        return new NyxEventSinkPoxy(nyxEventSink, config.getJobLogManager(), config.getCallback());
    }

    @Override
    protected NyxEventStore buildEventStore(MetaCrawlParam config) {
        return service;
    }

    @Override
    protected NyxEventCrawl buildEventCrawl(MetaCrawlParam config, NyxEventSink sink) {
        return super.buildEventCrawl(config, sink);
    }

    @Override
    protected DsMetaAdapter buildMetaAdapter(MetaCrawlParam config) {
        DatabaseType databaseType = DbUtil.fileType(config.getSource());
        switch (databaseType) {
            case WORD:
            case PDF:
                return new UnFileMetaAdapter(fileManagerService);
            case FILE_METADATA:
                return new MetaFileMetaAdapter(fileManagerService);
            case OTHER:
                if (DataBaseType.LOCAL_FILE.pluginId.equals(config.getSource().getType())) {
                    throw new IllegalArgumentException(databaseType.toString());
                }
                return null;
            default:
                return new FileMetaAdapter(fileManagerService);
        }
    }
}
