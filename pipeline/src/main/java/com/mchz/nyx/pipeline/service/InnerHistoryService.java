package com.mchz.nyx.pipeline.service;

import com.mchz.nyx.pipeline.entity.MemoryMapping;

import java.util.List;
import java.util.function.Consumer;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/12
 */
public interface InnerHistoryService {
    long loadAllHistoryCount( Integer industryId);

    void loadAllHistoryResult(Integer industryId, Consumer<MemoryMapping> resCall);

    List<Integer> loadHistoryResult(String table, String column);
}
