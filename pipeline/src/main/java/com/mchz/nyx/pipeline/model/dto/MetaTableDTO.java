package com.mchz.nyx.pipeline.model.dto;

import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/10/21 16:44
 */
@Data
public class MetaTableDTO {
    /** id */
    private Long id;
    /** schema */
    private String schema;
    /** 表格名字 */
    private String tableName;
    /** 表格注释 */
    private String tableComment;
    /** 表格物理规模 */
    private Long blocks;
    /** 表格行数 */
    private Long rows;
    /** 联合主键的最大列数，扣除时间列的列总数 */
    private Integer maxPkColNum;
    /** 外键数量 */
    private Integer fkNum;
    /** 索引数量 */
    private Integer idxNum;
    /** 是否包含其他表的外键对应的主键，0 - 不包含，1 - 包含 */
    private Boolean berefferd;
    /** 是否视图，0 - 表，1 - 视图 */
    private Boolean isView;
}
