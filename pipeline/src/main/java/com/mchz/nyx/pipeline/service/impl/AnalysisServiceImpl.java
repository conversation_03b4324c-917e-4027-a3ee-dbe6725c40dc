package com.mchz.nyx.pipeline.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mchz.nyx.common.util.PartitionUtil;
import com.mchz.nyx.pipeline.common.constants.PipelineConst;
import com.mchz.nyx.pipeline.common.enums.AnalysisColumnStatus;
import com.mchz.nyx.pipeline.common.enums.AnalysisTableStatus;
import com.mchz.nyx.pipeline.common.enums.RepeatColumnType;
import com.mchz.nyx.pipeline.entity.*;
import com.mchz.nyx.pipeline.mapper.*;
import com.mchz.nyx.pipeline.model.dto.AnalysisResultDTO;
import com.mchz.nyx.pipeline.service.AnalysisService;
import com.mchz.nyx.pipeline.util.Func;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.CompletableFuture;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/6/28
 */
@Slf4j
@Service
@AllArgsConstructor
public class AnalysisServiceImpl implements AnalysisService {
    private final AnalysisTableMapper analysisTableMapper;
    private final AnalysisColumnMapper analysisColumnMapper;
    private final AnalysisSampleResultMapper analysisSampleResultMapper;
    private final AnalysisColumnTempMapper analysisColumnTempMapper;
    private final AnalysisColumnHitMapper analysisColumnHitMapper;
    private final AnalysisTableClassifyMapper analysisTableClassifyMapper;
    private final AnalysisFileResultMapper analysisFileResultMapper;

    @Override
    public Set<Long> listJobSchemas(Long planId) {
        Set<Long> set = new HashSet<>();
        analysisTableMapper.selectMaps(Wrappers.lambdaQuery(AnalysisTable.class).select(AnalysisTable::getSchemaId).eq(AnalysisTable::getPlanId, planId), rc -> set.add((Long) rc.getResultObject().values().iterator().next()));
        return set;
    }

    @Override
    public List<AnalysisTable> listJobTable(Long planId, Long schemaId) {
        LambdaQueryWrapper<AnalysisTable> q = Wrappers.lambdaQuery(AnalysisTable.class)
            .select(AnalysisTable::getId, AnalysisTable::getTableId, AnalysisTable::getVersion, AnalysisTable::getRepeatType, AnalysisTable::getRepeatTableId, AnalysisTable::getStatus)
            .eq(AnalysisTable::getPlanId, planId)
            .eq(AnalysisTable::getSchemaId, schemaId);
        return analysisTableMapper.selectList(q);
    }

    @Override
    public List<AnalysisTable> listJobTable(Long planId, Long schemaId, Long tId, boolean increment) {
        LambdaQueryWrapper<AnalysisTable> q = Wrappers.lambdaQuery(AnalysisTable.class)
            .select(AnalysisTable::getId, AnalysisTable::getTableId, AnalysisTable::getRepeatType, AnalysisTable::getClassifyId, AnalysisTable::getLevel, AnalysisTable::getStatus)
            .eq(AnalysisTable::getPlanId, planId)
            .eq(AnalysisTable::getSchemaId, schemaId)
            .ge(null != tId, AnalysisTable::getTableId, tId)
            .in(increment, AnalysisTable::getStatus, AnalysisTableStatus.S00.getCode(), AnalysisTableStatus.S10.getCode());
        return analysisTableMapper.selectList(q);
    }

    @Override
    public List<AnalysisTable> listJobFile(Long planId, Long schemaId, Long tId, boolean increment) {
        LambdaQueryWrapper<AnalysisTable> q = Wrappers.lambdaQuery(AnalysisTable.class)
            .select(AnalysisTable::getId, AnalysisTable::getTableId, AnalysisTable::getTableName)
            .eq(AnalysisTable::getPlanId, planId)
            .eq(AnalysisTable::getSchemaId, schemaId)
            .ge(null != tId, AnalysisTable::getTableId, tId)
            .lt(increment, AnalysisTable::getStatus, AnalysisTableStatus.S10.getCode());
        return analysisTableMapper.selectList(q);
    }

    @Override
    public List<AnalysisColumn> listJobColumnAll(Long planId, Long tableId) {
        LambdaQueryWrapper<AnalysisColumn> q = Wrappers.lambdaQuery(AnalysisColumn.class)
            .select(AnalysisColumn::getId, AnalysisColumn::getJobId, AnalysisColumn::getColumnId, AnalysisColumn::getStatus)
            .eq(AnalysisColumn::getPlanId, planId)
            .eq(AnalysisColumn::getTableId, tableId);
        return analysisColumnMapper.selectList(q);
    }

    @Override
    public List<AnalysisColumn> listJobColumn(Long planId, Long tableId) {
        LambdaQueryWrapper<AnalysisColumn> q = Wrappers.lambdaQuery(AnalysisColumn.class)
            .select(AnalysisColumn::getId, AnalysisColumn::getJobId, AnalysisColumn::getColumnId, AnalysisColumn::getClassifyId, AnalysisColumn::getStatus, AnalysisColumn::getRepeatType)
            .eq(AnalysisColumn::getPlanId, planId)
            .eq(AnalysisColumn::getTableId, tableId)
            .ne(AnalysisColumn::getRepeatType, RepeatColumnType.SUB.getCode());
        return analysisColumnMapper.selectList(q);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveAnalysisTableColumn(List<AnalysisTable> tables, List<AnalysisColumn> columns, List<AnalysisTable> updateTables) {
        if (CollUtil.isNotEmpty(tables)) {
            analysisTableMapper.insertList(tables);
        }
        if (CollUtil.isNotEmpty(columns)) {
            analysisColumnMapper.insertList(columns);
        }
        if (CollUtil.isNotEmpty(updateTables)) {
            analysisTableMapper.updateById(updateTables);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveAnalysisTableColumnIncremental(Long planId, List<AnalysisTable> tables, List<AnalysisTable> updateTable, List<Long> delTableIds, Collection<Long> delColByTableIds, List<AnalysisColumn> columns, List<Long> deleteColumnIds, boolean ignoreCandidate) {
        PartitionUtil.part(delColByTableIds, v -> {
            List<Long> ids = new ArrayList<>();
            LambdaQueryWrapper<AnalysisColumn> q = Wrappers.lambdaQuery(AnalysisColumn.class).select(AnalysisColumn::getId).eq(AnalysisColumn::getPlanId, planId).in(AnalysisColumn::getTableId, v);
            analysisColumnMapper.selectList(q, resultContext -> ids.add(resultContext.getResultObject().getId()));
            deleteJobColumnByIds(ids, ignoreCandidate);
        });
        deleteJobColumnByIds(deleteColumnIds, ignoreCandidate);
        PartitionUtil.part(delTableIds, analysisTableMapper::deleteByIds);

        if (CollUtil.isNotEmpty(updateTable)) {
            analysisTableMapper.updateById(updateTable, PartitionUtil.DEFAULT_PART_SIZE);
        }

        PartitionUtil.part(tables, analysisTableMapper::insertList);
        PartitionUtil.part(columns, analysisColumnMapper::insertList);
    }

    @Override
    public void updateSubColumn(Long planId, Long mid, List<Long> tableIds, String columnName) {
        if (null == mid || CollUtil.isEmpty(tableIds)) {
            return;
        }
        LambdaUpdateWrapper<AnalysisColumn> u = Wrappers.lambdaUpdate(AnalysisColumn.class)
            .set(AnalysisColumn::getRepeatType, RepeatColumnType.SUB.getCode())
            .set(AnalysisColumn::getRepeatColumnId, mid)
            .eq(AnalysisColumn::getPlanId, planId);
        if (1 == tableIds.size()) {
            u.eq(AnalysisColumn::getTableId, tableIds.get(0));
        } else {
            u.in(AnalysisColumn::getTableId, tableIds);
        }
        u.eq(AnalysisColumn::getColumnName, columnName);
        analysisColumnMapper.update(u);
    }

    @Override
    public void unlinkSubTableColumn(Long planId, Collection<Long> mid) {
        if (CollUtil.isEmpty(mid)) {
            return;
        }
        LambdaUpdateWrapper<AnalysisColumn> u = Wrappers.lambdaUpdate(AnalysisColumn.class)
            .set(AnalysisColumn::getRepeatType, RepeatColumnType.NORMAL.getCode())
            .set(AnalysisColumn::getRepeatColumnId, null)
            .eq(AnalysisColumn::getPlanId, planId)
            .eq(AnalysisColumn::getRepeatType, RepeatColumnType.SUB.getCode())
            .in(AnalysisColumn::getRepeatColumnId, mid);
        analysisColumnMapper.update(u);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateToNormalTable(Long planId, List<Long> tableIds) {
        if (CollUtil.isEmpty(tableIds) || null == tableIds.get(0)) {
            return;
        }
        LambdaUpdateWrapper<AnalysisTable> u1 = Wrappers.lambdaUpdate(AnalysisTable.class)
            .set(AnalysisTable::getRepeatType, RepeatColumnType.NORMAL.getCode())
            .eq(AnalysisTable::getPlanId, planId);
        if (1 == tableIds.size()) {
            u1.eq(AnalysisTable::getTableId, tableIds.get(0));
        } else {
            u1.in(AnalysisTable::getTableId, tableIds);
        }
        analysisTableMapper.update(u1);
        LambdaUpdateWrapper<AnalysisColumn> u2 = Wrappers.lambdaUpdate(AnalysisColumn.class)
            .set(AnalysisColumn::getRepeatType, RepeatColumnType.NORMAL.getCode())
            .set(AnalysisColumn::getRepeatColumnId, null)
            .eq(AnalysisColumn::getPlanId, planId);
        if (1 == tableIds.size()) {
            u2.eq(AnalysisColumn::getTableId, tableIds.get(0));
        } else {
            u2.in(AnalysisColumn::getTableId, tableIds);
        }
        analysisColumnMapper.update(u2);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteJobColumnByIds(List<Long> ids, boolean ignoreCandidate) {
        if (ignoreCandidate) {
            PartitionUtil.part(ids, analysisColumnMapper::deleteByIds);
            return;
        }
        PartitionUtil.part(ids, v -> {
            analysisColumnMapper.deleteByIds(v);
            Wrapper<AnalysisSampleResult> delete = Wrappers.lambdaQuery(AnalysisSampleResult.class).in(AnalysisSampleResult::getResultId, v);
            analysisSampleResultMapper.delete(delete);
        });
    }

    @Override
    public void deleteJobWithSchemaId(Long planId, Collection<Long> schemaIds) {
        PartitionUtil.part(schemaIds, v -> {
            analysisColumnMapper.delete(Wrappers.lambdaQuery(AnalysisColumn.class).eq(AnalysisColumn::getPlanId, planId).in(AnalysisColumn::getSchemaId, v));
            analysisTableMapper.delete(Wrappers.lambdaQuery(AnalysisTable.class).eq(AnalysisTable::getPlanId, planId).in(AnalysisTable::getSchemaId, v));
        });
    }

    @Override
    public void deleteTableClassify(Long planId, Collection<Long> list) {
        PartitionUtil.part(list, v -> analysisTableClassifyMapper.delete(Wrappers.lambdaQuery(AnalysisTableClassify.class).eq(AnalysisTableClassify::getPlanId, planId).in(AnalysisTableClassify::getTableId, v)));
    }

    @Override
    public void saveTableClassify(List<AnalysisTableClassify> list) {
        PartitionUtil.part(list, analysisTableClassifyMapper::insertList);
    }

    @Override
    public void saveFileClassify(AnalysisTable at, List<AnalysisFileResult> list) {
        analysisTableMapper.updateById(at);
        PartitionUtil.part(list, analysisFileResultMapper::insertList);
    }

    @Override
    @Async(PipelineConst.STORE_THREAD)
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public CompletableFuture<Boolean> saveAnalysisResult(Long planId, Long jobId, AnalysisResultDTO analysisResult, Long defaultClassifyId, Integer defaultLevel) {
        List<Long> deleteIds = new ArrayList<>();
        PartitionUtil.part(analysisResult.getDeleteAnalysisResultIds(), v -> {
            Wrapper<AnalysisSampleResult> q = Wrappers.lambdaQuery(AnalysisSampleResult.class)
                .select(AnalysisSampleResult::getId)
                .in(AnalysisSampleResult::getResultId, v);
            analysisSampleResultMapper.selectList(q, rc -> deleteIds.add(rc.getResultObject().getId()));
        });
        Func.processBatches(deleteIds, analysisSampleResultMapper::deleteByIds, "【分析结果存储】候选清理失败：{}");
        //主副表逻辑
        Map<Long, Pair<AnalysisColumn, AnalysisColumnTemp>> map = analysisResult.getMainSubSync();
        List<AnalysisColumn> updateAnalysis = analysisResult.getUpdateAnalysis();
        List<Long> updateNotFound = analysisResult.getUpdateNotFound();
        List<AnalysisColumnTemp> tempResult = analysisResult.getTempResult();
        if (MapUtil.isNotEmpty(map)) {
            LambdaQueryWrapper<AnalysisColumn> q = Wrappers.lambdaQuery(AnalysisColumn.class)
                .select(AnalysisColumn::getId, AnalysisColumn::getRepeatColumnId)
                .eq(AnalysisColumn::getPlanId, planId).in(AnalysisColumn::getRepeatColumnId, new ArrayList<>(map.keySet()));
            analysisColumnMapper.selectList(q, rc -> {
                Pair<AnalysisColumn, AnalysisColumnTemp> column = map.get(rc.getResultObject().getRepeatColumnId());
                Long id = rc.getResultObject().getId();
                if (null == column) {
                    updateNotFound.add(id);
                } else {
                    AnalysisColumn update = BeanUtil.toBean(column.getKey(), AnalysisColumn.class);
                    update.setId(id);
                    updateAnalysis.add(update);
                    if (null != column.getValue()) {
                        AnalysisColumnTemp temp = BeanUtil.toBean(column.getValue(), AnalysisColumnTemp.class);
                        temp.setAcId(id);
                        tempResult.add(temp);
                    }
                }
            });
        }

        PartitionUtil.part(updateNotFound, v -> {
            LambdaUpdateWrapper<AnalysisColumn> update = Wrappers.lambdaUpdate(AnalysisColumn.class)
                .set(AnalysisColumn::getJobId, jobId)
                .set(AnalysisColumn::getBizId, null)
                .set(AnalysisColumn::getClassifyId, defaultClassifyId)
                .set(AnalysisColumn::getLevel, defaultLevel)
                .set(AnalysisColumn::getStatus, AnalysisColumnStatus.S4.getCode())
                .set(AnalysisColumn::getFlag, 0)
                .in(AnalysisColumn::getId, v);
            analysisColumnMapper.update(update);
        });
        if (CollUtil.isNotEmpty(updateAnalysis)) {
            analysisColumnMapper.batchUpdate(updateAnalysis, PartitionUtil.DEFAULT_PART_SIZE, v -> Wrappers.lambdaUpdate(AnalysisColumn.class)
                .set(AnalysisColumn::getJobId, jobId)
                .set(AnalysisColumn::getBizId, v.getBizId())
                .set(AnalysisColumn::getClassifyId, ObjUtil.defaultIfNull(v.getClassifyId(), defaultClassifyId))
                .set(AnalysisColumn::getLevel, ObjUtil.defaultIfNull(v.getLevel(), defaultLevel))
                .set(AnalysisColumn::getStatus, v.getStatus())
                .set(AnalysisColumn::getFlag, 0)
                .eq(AnalysisColumn::getId, v.getId()));
        }
        if (CollUtil.isNotEmpty(tempResult)) {
            analysisColumnTempMapper.insertList(tempResult);
        }
        Func.processBatches(analysisResult.getInsertCandidate(), analysisSampleResultMapper::insertList, 100, "【分析结果存储】候选插入失败：{}");
        PartitionUtil.part(analysisResult.getInsertHit(), analysisColumnHitMapper::insertList);
        PartitionUtil.part(analysisResult.getUpdateTable(), analysisTableMapper::updateById);
        return CompletableFuture.completedFuture(Boolean.TRUE);
    }
}
