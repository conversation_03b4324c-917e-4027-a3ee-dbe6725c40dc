package com.mchz.nyx.pipeline.util;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.mchz.mcdatasource.core.DataBaseType;
import com.mchz.nyx.meta.support.CharsetConvert;
import com.mchz.nyx.meta.support.DataSourceConfig;
import com.mchz.nyx.pipeline.common.enums.DatabaseType;
import com.mchz.nyx.pipeline.model.api.DataSourceParam;
import com.mchz.nyx.pipeline.model.param.McDataSourceParam;
import com.mchz.nyx.pipeline.model.param.McFileSourceParam;
import com.mchz.nyx.pipeline.util.file.manager.FileAttach;
import lombok.experimental.UtilityClass;

import java.util.Map;
import java.util.Properties;
import java.util.function.Supplier;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/19
 */
@UtilityClass
public class DbUtil {
    private final String DS_PARAM_PRE = "dac.";

    public DataSourceConfig newDatasourceParam(DataSourceParam param) {
        if (DataBaseType.LOCAL_FILE.equals(param.getDataBaseType()) || null == param.getDataBaseType()) {
            return new McFileSourceParam().setSourceId(param.getId()).setFileType(getType(param.getConfigType()))
                .setRemote(param.getRemote()).setFileAttach(FileAttach.of(param.getAttachment()));
        }
        Properties properties = new Properties();
        if (null != param.getProperties()) {
            properties.putAll(param.getProperties());
        }
        return new McDataSourceParam().setSourceId(param.getId()).setType(param.getDataBaseType().pluginId).setHost(param.getHost())
            .setPort(param.getPort()).setDb(ObjUtil.defaultIfNull(param.getDbName(), StrUtil.EMPTY))
            .setUser(param.getUsername()).setPass(param.getPassword()).setAdvanced(properties)
            .setCharsetConvert(getCharsetConvert(param));
    }

    public CharsetConvert getCharsetConvert(DataSourceConfig config) {
        if (config instanceof McDataSourceParam) {
            return ((McDataSourceParam) config).getCharsetConvert();
        }
        return null;
    }

    public McFileSourceParam fileSource(DataSourceConfig config) {
        if (config instanceof McFileSourceParam) {
            return (McFileSourceParam) config;
        }
        return null;
    }

    public DatabaseType fileType(DataSourceConfig config) {
        if (config instanceof McFileSourceParam) {
            return ((McFileSourceParam) config).getFileType();
        }
        return DatabaseType.OTHER;
    }

    private CharsetConvert getCharsetConvert(DataSourceParam param) {
        Map<String, String> config = param.getAdvancedConfig();
        if (null == config) {
            return null;
        }
        Map<String, String> properties = param.getProperties();
        String c = ObjUtil.defaultIfEmpty(config.get(DS_PARAM_PRE + "clientEncoding"), (Supplier<String>) () -> null == properties ? null : properties.get("clientEncoding"));
        if (StrUtil.isEmpty(c)) {
            return null;
        }
        String s = ObjUtil.defaultIfEmpty(config.get(DS_PARAM_PRE + "serverEncoding"), (Supplier<String>) () -> null == properties ? null : properties.get("serverEncoding"));
        if (StrUtil.isEmpty(s)) {
            return null;
        }
        return new CharsetConvert(c, s);
    }

    private DatabaseType getType(Integer type) {
        if (null == type) {
            return DatabaseType.OTHER;
        }
        for (DatabaseType value : DatabaseType.values()) {
            if (type.equals(value.getCode())) {
                return value;
            }
        }
        return DatabaseType.OTHER;
    }
}
