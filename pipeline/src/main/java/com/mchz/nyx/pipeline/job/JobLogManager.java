package com.mchz.nyx.pipeline.job;

import cn.hutool.core.collection.ConcurrentHashSet;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.mchz.nyx.common.exception.InterruptedJobException;
import com.mchz.nyx.common.exception.NyxException;
import com.mchz.nyx.common.util.NyxStopWatch;
import com.mchz.nyx.pipeline.common.constants.PipelineConst;
import com.mchz.nyx.pipeline.exception.ServiceException;
import com.mchz.nyx.pipeline.model.log.JobLogItem;
import com.mchz.nyx.pipeline.model.log.JobLogVO;
import com.mchz.nyx.pipeline.model.log.ProcessingTable;
import com.mchz.nyx.pipeline.thread.event.JobLogEvent;
import com.mchz.nyx.pipeline.util.Func;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.logging.LogLevel;
import org.springframework.dao.DataAccessException;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Set;
import java.util.concurrent.CompletionException;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Supplier;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/12/22 13:02
 */
@Slf4j
public class JobLogManager {
    /**
     * jobId
     */
    @Getter
    private final Long jobId;
    /**
     * 租户Id
     */
    @Getter
    private final String tenantId;
    /**
     * 开始时间
     */
    @Getter
    private long startTime;
    /**
     * 阶段占比
     */
    private final int[] stages;
    private int progress;
    /**
     * 当前阶段
     */
    private int cStep;
    /**
     * 总共需要处理的数量
     */
    private final AtomicInteger totalNum;
    /**
     * 已处理数量
     */
    private final AtomicInteger num;
    /**
     * 处理中表格
     */
    private final Set<String> processingTables;

    @Getter
    private final NyxStopWatch stopWatch;

    @Getter
    private volatile boolean interrupted;

    @Getter
    private Object detail;

    public JobLogManager(String id, Long jobId, String tenantId, int[] stages) {
        this.jobId = jobId;
        this.tenantId = tenantId;
        this.stages = stages;
        this.progress = 0;
        this.cStep = 1;
        this.startTime = -1;
        this.totalNum = new AtomicInteger(0);
        this.num = new AtomicInteger(0);
        this.processingTables = new ConcurrentHashSet<>();
        this.stopWatch = NyxStopWatch.of(log, id);
        this.interrupted = false;
    }

    public String getId() {
        return stopWatch.getId();
    }

    public void start(String state) {
        stopWatch.start();
        init(state);
        if (startTime < 0) {
            startTime = System.currentTimeMillis();
        }
    }

    public void info(String msg) {
        message(LogLevel.INFO, msg, null);
    }

    public void warn(String msg) {
        message(LogLevel.WARN, msg, null);
    }

    public void warn(String msg, Throwable e) {
        log.warn(msg, e);
        message(LogLevel.WARN, msg, null);
    }

    public void error(String msg, Throwable e) {
        message(LogLevel.ERROR, msg, e);
    }

    public void message(LogLevel level, String msg, Throwable e) {
        JobLogItem item = new JobLogItem();
        item.setJobId(jobId);
        item.setTenantId(tenantId);
        item.setStartTime(startTime);
        item.setTime(System.currentTimeMillis());
        item.setPercent(getPercent());
        item.setLevel(level);
        if (null != e) {
            if (e instanceof CompletionException) {
                if (null != e.getCause()) {
                    e = e.getCause();
                }
            }
            if (e instanceof DataAccessException) {
                item.setRemark("遇到管理库异常,查看服务日志");
            } else if (e instanceof ServiceException) {
                item.setRemark(e.getMessage());
            } else if (e instanceof NyxException) {
                item.setRemark(Func.getMessage(e));
            }
            if (null == item.getRemark()) {
                Throwable cause = Func.getCause(e);
                msg += "," + StrUtil.truncateUtf8(cause.getMessage(), 150);
                item.setRemark(StrUtil.truncateUtf8(Func.getStackTrace(cause), PipelineConst.ERROR_MSG_MAX_LEN));
            }
        }
        item.setMessage(msg);
        SpringUtil.publishEvent(new JobLogEvent(this, processingTables, item));
        interrupted();
    }

    public boolean setStep(int step) {
        interrupted();
        if (cStep > step) {
            return false;
        }
        progress = Arrays.stream(stages, 0, Math.min(step, stages.length)).sum();
        if (cStep == step) {
            return true;
        }
        detail = null;
        cStep = step;
        totalNum.lazySet(0);
        num.lazySet(0);
        return true;
    }

    public void processed(int size) {
        if (0 == totalNum.get()) {
            totalNum.compareAndSet(0, num.get());
        }
        totalNum.addAndGet(size);
    }

    public void complete() {
        num.incrementAndGet();
        interrupted();
    }

    public void addProcessingTable(String processingTable) {
        processingTables.add(processingTable);
    }

    public void removeProcessingTable(String processingTable) {
        processingTables.remove(processingTable);
    }

    public JobLogVO getHistoryLog() {
        JobLogVO vo = new JobLogVO();
        vo.setJobId(jobId);
        vo.setStartTime(startTime);
        vo.setNowTime(System.currentTimeMillis());
        vo.setPercent(getPercent());
        vo.setTenantId(tenantId);
        return vo;
    }

    public ProcessingTable getProcessingTable() {
        ProcessingTable table = new ProcessingTable();
        table.setJobId(jobId);
        table.setTables(new ArrayList<>(processingTables));
        table.setTenantId(tenantId);
        return table;
    }

    public void interrupt() {
        interrupted = true;
    }

    public void interrupted() {
        if (interrupted) {
            throw new InterruptedJobException();
        }
        if (Thread.currentThread().isInterrupted()) {
            interrupted = true;
            throw new InterruptedJobException();
        }
    }

    public <V> V getHeartbeatDetails(Class<V> clazz) {
        return getHeartbeatDetails(clazz, null);
    }

    public <V> V getHeartbeatDetails(Class<V> clazz, Supplier<V> defaultValue) {
        if (null == detail) {
            if (null == defaultValue) {
                return null;
            }
            V tmp = defaultValue.get();
            detail = tmp;
            return tmp;
        }
        if (clazz.isInstance(detail)) {
            return clazz.cast(detail);
        }
        V res = JSONUtil.toBean(detail.toString(), clazz);
        detail = res;
        return res;
    }

    public <V extends Serializable> void heartbeat(V details) {
        detail = details;
    }

    public String getSnapshot() {
        JobVirtualSnapshot info = new JobVirtualSnapshot();
        info.setStartTime(startTime);
        info.setStep(cStep);
        info.setNum(num.get());
        info.setDetail(detail);
        return JSONUtil.toJsonStr(info);
    }

    private void init(String state) {
        if (StrUtil.isEmpty(state)) {
            return;
        }
        JobVirtualSnapshot info = JSONUtil.toBean(state, JobVirtualSnapshot.class);
        if (null == info.getStep()) {
            return;
        }
        this.cStep = info.getStep();
        this.startTime = info.getStartTime();
        this.detail = info.getDetail();
        if (null != info.getNum()) {
            num.lazySet(info.getNum());
        }
    }

    private BigDecimal getPercent() {
        if (cStep >= stages.length) {
            return new BigDecimal(progress);
        }
        double value;
        if (0 == totalNum.get()) {
            value = 0;
        } else {
            value = Math.min((double) num.get() / totalNum.get(), 1);
        }
        return new BigDecimal(progress + stages[cStep] * value).setScale(2, RoundingMode.DOWN);
    }
}
