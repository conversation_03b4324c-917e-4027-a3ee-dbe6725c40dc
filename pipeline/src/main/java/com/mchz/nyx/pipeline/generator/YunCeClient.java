package com.mchz.nyx.pipeline.generator;

import cn.hutool.core.annotation.Alias;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.net.url.UrlBuilder;
import cn.hutool.core.util.CharUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.Method;
import cn.hutool.json.*;
import com.mchz.nyx.common.exception.NyxException;
import com.mchz.nyx.common.util.PartitionUtil;
import com.mchz.nyx.dark.engine.YunCeEngine;
import com.mchz.nyx.dark.model.EngineContext;
import com.mchz.nyx.dark.model.TargetResult;
import com.mchz.nyx.dark.model.meta.MetaColumnData;
import com.mchz.nyx.pipeline.common.constants.PipelineConst;
import com.mchz.nyx.pipeline.config.props.EngineProperties;
import com.mchz.nyx.pipeline.model.dto.*;
import com.mchz.nyx.pipeline.util.Func;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/1/21
 */
@Slf4j
public class YunCeClient implements YunCeEngine {
    private static final int LEN_LIMIT_BGE = 1400;
    private static final int LEN_LIMIT_LLM = 1000;
    private static final int CONNECTION_TIMEOUT = 1000;

    private final EngineProperties properties;
    /**
     * /embeddings/businessterm/
     */
    private final UrlBuilder url;
    private final UrlBuilder llmUrl;

    private final AtomicInteger retryCount;

    private final String termMore;
    private final String categoryMore;
    private final String industry;

    @Setter
    private String callbackUrl;
    private CategoryReq categoryReq;
    private FileCategoryReq fileCategoryReq;

    public static YunCeClient of(EngineProperties properties, String baseUrl, String llmUrl, Integer industryId, String industry) {
        return new YunCeClient(properties, null != baseUrl ? UrlBuilder.of(baseUrl) : null, null != llmUrl ? UrlBuilder.of(llmUrl) : null, null != industryId ? " and industry_id=" + industryId : StrUtil.EMPTY, null, industry);
    }

    public static YunCeClient of(EngineProperties properties, String baseUrl, String llmUrl, String industry, String industry2) {
        return new YunCeClient(properties, null != baseUrl ? UrlBuilder.of(baseUrl) : null, null != llmUrl ? UrlBuilder.of(llmUrl) : null,
            null, null != industry2 ? " and industry='" + industry2 + "'" : StrUtil.EMPTY,
            industry);
    }

    private YunCeClient(EngineProperties properties, UrlBuilder url, UrlBuilder llmUrl, String termMore, String categoryMore, String industry) {
        this.properties = properties;
        this.url = url;
        this.llmUrl = llmUrl;
        this.termMore = termMore;
        this.categoryMore = categoryMore;
        this.industry = industry;
        this.retryCount = new AtomicInteger();
    }

    //*******************************************************************************************************************************************************************************************

    @Override
    public List<EmbeddingDTO> askTerm(String str) {
        return askEmbedding(baseUrl(url).addPath("embeddings/businessterm/search"), str, termMore, 8);
    }

    @Override
    public <T> List<T> askTerm(String str, List<T> candidate, Function<T, String> getText) {
        if (properties.isIgnoreCheck()) {
            return candidate;
        }
        List<T> ts = checkTerm(str, candidate, getText);
        if (CollUtil.isEmpty(ts)) {
            return candidate;
        }
        int i = 0;
        for (T item : ts) {
            String name = getText.apply(item);
            if (!name.contains("人") || checkTerm(str, name)) {
                break;
            }
            i++;
        }
        if (0 == i) {
            return ts;
        }
        if (i == ts.size()) {
            return Collections.emptyList();
        }
        return ts.subList(i, ts.size());
    }

    @Override
    public void rerank(List<MetaColumnData> column) {
        List<Pair<MetaColumnData, List<String>>> temp = column.stream().map(v -> {
            if (v.isNotMultiple() || StrUtil.isEmpty(v.getComment())) {
                return null;
            }
            List<String> documents = new ArrayList<>(v.getResult().size());
            for (TargetResult item : v.getResult()) {
                String name = item.getInfo().getName();
                if (StrUtil.isEmpty(name)) {
                    return null;
                }
                documents.add(name);
            }
            return Pair.of(v, documents);
        }).filter(Objects::nonNull).collect(Collectors.toList());
        execRerank(temp);
    }

    @Override
    public <T> List<Pair<T, String>> askLLM(EngineContext context, List<T> columns, Function<T, String> columnName) {
        if (null == llmUrl) {
            return Collections.emptyList();
        }
        int tryCount = properties.getTryCount();
        if (retryCount.get() >= tryCount) {
            log.warn("【LLM】达到最大重试次数，停止处理");
            return Collections.emptyList();
        }

        UrlBuilder llm = baseUrl(llmUrl).addPath("discovery").addPath("smart");
        LLMReq req = new LLMReq();
        req.setIndustry(industry);
        if (null != context.getTable().getSchema()) {
            req.setSchema(context.getTable().getSchema().getCatalogSchema());
        }
        req.setTable(context.getTable().getTableName());
        List<Pair<T, String>> result = new ArrayList<>(columns.size());
        for (List<T> batchColumns : PartitionUtil.part(columns, 8)) {
            if (retryCount.get() >= tryCount) {
                log.warn("【LLM】达到最大重试次数，停止处理");
                break;
            }
            List<String> colNames = new ArrayList<>(batchColumns.size());
            List<T> list = new ArrayList<>(batchColumns.size());
            batchColumns.forEach(v -> {
                String name = columnName.apply(v);
                if (name.startsWith(PipelineConst.DEFAULT_COLUMN_PREFIX) || isSimpleSameChar(name)) {
                    return;
                }
                list.add(v);
                colNames.add(name);
            });
            req.setColumns(colNames);
            handleLLMRequest(llm, req, list, result, retryCount);
        }
        return result;
    }

    //分类 ****************************************************************************************************************************************************************************************

    /**
     * 非一体机分类推荐
     */
    public List<EmbeddingDTO> askCategory(String str) {
        return askEmbedding(baseUrl(url).addPath("embeddings/category/search"), str, categoryMore, 3);
    }

    public Map<String, ClassifyDTO> askCategoryLLM(DarkContext context, TableDTO table, List<ResultColumnDTO> columns, boolean limit) {
        CategoryReq req = getCategoryReq(context);
        if (limit) {
            req.setMode("reranker");
        }
        Map<String, ClassifyDTO> res = new HashMap<>(columns.size());
        for (List<ResultColumnDTO> batch : PartitionUtil.part(columns, properties.getBatchClassifySize())) {
            context.interrupted();
            if (limit) {
                req.setColumns(batch.stream().map(v -> {
                    Column column = new Column(table, v);
                    column.setCategories(v.getClassifyList().stream().map(c -> new Categories(c.getTypeId())).collect(Collectors.toList()));
                    return column;
                }).collect(Collectors.toList()));
            } else {
                req.setColumns(batch.stream().map(v -> new Column(table, v)).collect(Collectors.toList()));
            }
            List<ResponseItem> data = askClassify(req);
            if (null != data) {
                data.forEach(v -> res.put(v.getColumnName(), new ClassifyDTO(v.getCategoryId(), null, v.getAccording(), v.getConfidence())));
            } else {
                context.warn("智能推荐分类异常 {}.{}", table.getCatalogSchema().getName(), table.getName());
            }
        }
        return res;
    }

    public List<ClassifyFileDTO> askCategoryLLM(DarkContext context, String docId, String path) {
        FileCategoryReq req = getFileCategoryReq(context);
        req.setFiles(new LocalFile(docId, path, null));
        Document document = askFileClassify(req);
        if (null == document || null == document.getCategories() || null == document.getRefs()) {
            return null;
        }
        return document.getCategories().stream().filter(v -> null != v.getCategoryId()).map(v -> {
            Ref ref = document.getRefs().get(v.getRefIndex());
            if (null == ref) {
                return null;
            }
            RefInfoDTO refInfo = new RefInfoDTO(document.getTxtFilePath(), ref.getStartLine(), ref.getEndLine(), ref.getStartByte(), ref.getEndByte());
            return new ClassifyFileDTO(v.getCategoryId(), null, v.getAccording(), v.getConfidence(), v.getContent(), refInfo);
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public void taskFinish() {
        if (null != categoryReq) {
            categoryReq.setStop(Boolean.TRUE);
            askClassify(categoryReq);
        }
        if (null != fileCategoryReq) {
            fileCategoryReq.setStop(Boolean.TRUE);
            askFileClassify(fileCategoryReq);
        }
    }

    //*******************************************************************************************************************************************************************************************

    private List<EmbeddingDTO> askEmbedding(UrlBuilder url, String similar, String andMore, int limit) {
        String q = similar.length() > LEN_LIMIT_BGE ? StrUtil.subPre(similar, LEN_LIMIT_BGE) : similar;
        q = q.replaceAll(";", " ").replaceAll("'", "");
        String query = String.format("select id,text,score from txtai where similar('%s') %s limit " + limit, q, andMore);
        HttpRequest request = HttpRequest.of(url.addQuery("query", query)).method(Method.GET).setConnectionTimeout(CONNECTION_TIMEOUT).setReadTimeout(2000);
        try (HttpResponse execute = request.execute()) {
            String body = execute.body();
            try {
                return JSONUtil.toList(body, EmbeddingDTO.class);
            } catch (Exception e) {
                log.warn("【数据推荐】{} 解析失败:{} q: {} r: {}", urlLog(url), e.getMessage(), similar, body);
            }
        } catch (Exception e) {
            log.warn("【数据推荐】{} 服务调用失败:{}", urlLog(url), e.getMessage());
        }
        return Collections.emptyList();
    }


    private <T> void handleLLMRequest(UrlBuilder llm, LLMReq req, List<T> batchColumns, List<Pair<T, String>> result, AtomicInteger retryCount) {
        String query = JSONUtil.toJsonStr(req);

        HttpRequest request = HttpRequest.of(llm)
            .method(Method.POST)
            .setConnectionTimeout(CONNECTION_TIMEOUT)
            .setReadTimeout(properties.getTermTimeout())
            .body(query);
        log.debug("【LLM】q: {}", request);
        try (HttpResponse response = request.execute()) {
            String body = response.body();
            log.debug("【LLM】r: {}", body);
            if (response.isOk()) {
                try {
                    checkoutTerm(body, batchColumns, result);
                    retryCount.lazySet(0);
                } catch (NyxException e) {
                    log.warn("【LLM】解析异常: {} q: {} r: {}", e.getMessage(), query, body);
                }
                return;
            }
            log.error("【LLM】{} 服务调用失败 q: {} r({}): {}", urlLog(llm), query, response.getStatus(), body);
        } catch (Exception e) {
            retryCount.incrementAndGet();
            log.warn("【LLM】{} 访问失败, {}", urlLog(llm), Func.getMessage(e));
        }
    }

    private <T> void checkoutTerm(String body, List<T> columns, List<Pair<T, String>> set) {
        LLMResp llmResp = JSONUtil.toBean(body, LLMResp.class);
        if (CollUtil.isEmpty(llmResp.getChoices())) {
            throw new NyxException("LLM 返回格式不正确");
        }
        String content = llmResp.getChoices().get(0).getMessage().getContent();
        if (!JSONUtil.isTypeJSONArray(content)) {
            content = "[" + StrUtil.subBetween(content, "[", "]") + "]";
        }
        List<String> list = JSONUtil.toList(content, String.class);
        if (list.size() != columns.size()) {
            throw new NyxException("LLM 返回的业务术语数量与字段数量不一致");
        }
        Iterator<T> iterator1 = columns.iterator();
        Iterator<String> iterator2 = list.iterator();
        while (iterator1.hasNext() && iterator2.hasNext()) {
            T source = iterator1.next();
            String biz = iterator2.next();
            if (biz.startsWith("_") || biz.contains("未知")) {
                continue;
            }
            set.add(Pair.of(source, biz));
        }
    }

    private <T> List<T> checkTerm(String name, List<T> candidate, Function<T, String> getText) {
        StringBuilder sb = new StringBuilder();
        sb.append("'").append(name).append("'").append("与以下业务术语中的哪个具有相同的业务含义?");
        int i = 1;
        for (T v : candidate) {
            sb.append("\n").append(i++).append(".").append(getText.apply(v));
        }
        sb.append("\nRequirements:\n" +
            "- Output only the matching term(s) id(number) in a JSON array.\n" +
            "- If no terms match, output an empty JSON array ([]).\n" +
            "- Respond strictly in JSON array format.");
        JSONArray array = new JSONArray();
        array.add(Message.ofSystem("you are a data analyst" + (null == industry ? "" : " in " + industry) + ", with expertise in regulatory and compliance terminology."));
        array.add(Message.of(sb.toString()));
        JSONObject json = new JSONObject();
        json.set("messages", array);

        JSONArray body = chat(json.toString());

        if (null != body) {
            try {
                List<Integer> list = body.toList(Integer.class);
                if (list.isEmpty()) {
                    return Collections.emptyList();
                }
                return list.stream().filter(v -> v <= candidate.size()).map(v -> candidate.get(v - 1)).collect(Collectors.toList());
            } catch (Exception e) {
                try {
                    Set<String> set = new HashSet<>(body.toList(String.class));
                    return candidate.stream().filter(v -> set.contains(getText.apply(v))).collect(Collectors.toList());
                } catch (Exception e1) {
                    log.warn("【业务术语】{} 解析异常,{}:{}", body, Func.getMessage(e), Func.getMessage(e1));
                }
            }
        }
        return null;
    }

    private boolean checkTerm(String name1, String name2) {
        String msg = "{\n  \"messages\": [\n    {\n      \"role\": \"system\",\n      \"content\": \"You are a data analyst in the healthcare industry, specializing in regulatory and compliance terminology.\"\n    },\n    {\n      \"role\": \"user\",\n      \"content\": \"Do ‘%s’ and ‘%s’ refer to the same main subject (a person)?\\nReturn the result as a JSON array with a single boolean value: true if the terms share the same main subject, false if they do not.\"\n    }\n  ]\n}";

        JSONArray body = chat(String.format(msg, name1, name2));

        if (null != body) {
            try {
                List<Boolean> list = body.toList(Boolean.class);
                if (!list.isEmpty() && Boolean.FALSE.equals(list.get(0))) {
                    return false;
                }
            } catch (Exception e) {
                log.warn("【业务术语】主体判定失败,{}", Func.getMessage(e));
            }
        }
        return true;
    }

    private void execRerank(List<Pair<MetaColumnData, List<String>>> temp) {
        List<RerankQuery> list = temp.stream().map(v -> new RerankQuery(v.getKey().getComment(), v.getValue())).collect(Collectors.toList());

        UrlBuilder urlBuilder = baseUrl(url).addPath("nlp/rerankings");
        HttpRequest request = HttpRequest.of(urlBuilder).method(Method.POST).body(JSONUtil.toJsonStr(new RerankReq(list))).setConnectionTimeout(CONNECTION_TIMEOUT).setReadTimeout(60_000);
        try (HttpResponse response = request.execute()) {
            String body = response.body();
            List<List<RerankResp>> res = JSONUtil.toBean(body, new TypeReference<List<List<RerankResp>>>() {}, true);
            if (res.size() != list.size()) {
                log.warn("【rerank】返回结果数量不一致 {}|{}", res.size(), list.size());
                return;
            }
            Iterator<Pair<MetaColumnData, List<String>>> iterator1 = temp.iterator();
            Iterator<List<RerankResp>> iterator2 = res.iterator();
            while (iterator1.hasNext() && iterator2.hasNext()) {
                Pair<MetaColumnData, List<String>> pair = iterator1.next();
                List<RerankResp> rel = iterator2.next();
                TargetResult[] results = pair.getKey().getResult().toArray(new TargetResult[0]);
                List<TargetResult> resultList = rel.stream().map(v -> results[v.getIndex()]).collect(Collectors.toList());
                pair.getKey().setResult(resultList);
            }
        } catch (Exception e) {
            log.warn("【rerank】{},{}", urlLog(urlBuilder), Func.getMessage(e));
        }
    }

    private List<ResponseItem> askClassify(CategoryReq req) {
        JSON body = askLlm("分类", baseUrl(llmUrl).addPath("classify").addPath("smart"), JSONUtil.toJsonStr(req), properties.getClassifyTimeout());
        return null == body ? null : body.toBean(new TypeReference<List<ResponseItem>>() {});
    }

    private Document askFileClassify(FileCategoryReq req) {
        JSON body = askLlm("文件分类", baseUrl(llmUrl).addPath("classify").addPath("unstructure"), JSONUtil.toJsonStr(req), properties.getFileClassifyTimeout());
        return null == body ? null : body.toBean(Document.class);
    }

    private JSONArray chat(String msg) {
        JSON body = askLlm("聊天", baseUrl(llmUrl).addPath("/v1/chat/completions"), msg, properties.getChatTimeout());
        return null == body ? null : new JSONArray(body);
    }

    private JSON askLlm(String key, UrlBuilder url, String requestBody, int timeout) {
        int count = retryCount.get();
        int tryCount = properties.getTryCount();
        if (count >= tryCount) {
            if (count - tryCount < 10) {
                log.warn("【ai】达到最大重试次数");
            }
            return null;
        }
        try (HttpResponse response = HttpRequest.of(url).method(Method.POST).setConnectionTimeout(CONNECTION_TIMEOUT).setReadTimeout(timeout).body(requestBody).execute()) {
            String body = response.body();
            log.debug("【ai】q:{} r({}):{}", requestBody, response.getStatus(), body);
            if (!response.isOk()) {
                log.error("【ai】{} {} 服务调用失败 q: {} r({}): {}", key, urlLog(url), StrUtil.truncateUtf8(requestBody, 50), response.getStatus(), body);
                return null;
            }
            retryCount.lazySet(0);
            try {
                return (JSON) new JSONObject(body, JSONConfig.create()).getObj("data");
            } catch (Exception e) {
                log.error("【ai】{} 解析失败:{} r: {}", key, e.getMessage(), body);
            }
        } catch (Exception e) {
            retryCount.incrementAndGet();
            log.error("【ai】{} {} 服务调用失败,{}", key, urlLog(url), Func.getMessage(e));
        }
        return null;
    }

    //*******************************************************************************************************************************************************************************************
    private CategoryReq getCategoryReq(DarkContext context) {
        if (null == categoryReq) {
            categoryReq = new CategoryReq(context.getJobId().toString(), industry, context.getStdId().toString(), properties.getStdUrl());
        }
        CategoryReq req = new CategoryReq(categoryReq.getTaskId(), categoryReq.getIndustry(), categoryReq.getStdId(), categoryReq.getStdUrl());
        req.setStop(Boolean.FALSE);
        return req;
    }

    private FileCategoryReq getFileCategoryReq(DarkContext context) {
        if (null == fileCategoryReq) {
            fileCategoryReq = new FileCategoryReq(context.getJobId().toString(), industry, context.getStdId().toString(), properties.getStdUrl(), callbackUrl);
        }
        FileCategoryReq req = new FileCategoryReq(fileCategoryReq.getTaskId(), fileCategoryReq.getIndustry(), fileCategoryReq.getStdId(), fileCategoryReq.getStdUrl(), fileCategoryReq.getCallbackUrl());
        req.setStop(Boolean.FALSE);
        return req;
    }

    private String urlLog(UrlBuilder url) {
        if (log.isDebugEnabled()) {
            return url.build();
        }
        return String.format("%s:%d %s", url.getHost(), url.getPort(), url.getPathStr());
    }

    private UrlBuilder baseUrl(UrlBuilder base) {
        return UrlBuilder.of(base.getScheme(), base.getHost(), base.getPort(), base.getPathStr(), null, base.getFragment(), base.getCharset());
    }

    //*******************************************************************************************************************************************************************************************

    private boolean isSimpleSameChar(String value) {
        if (value.length() > 3) {
            return false;
        }
        char ch = value.charAt(0);
        if (!CharUtil.isLetter(ch)) {
            return false;
        }
        boolean isAllSame = true;
        for (int i = 0; i < value.length(); i++) {
            if (value.charAt(i) != ch) {
                isAllSame = false;
                break;
            }
        }
        return isAllSame;
    }

    //*******************************************************************************************************************************************************************************************
    @Data
    private static class LLMReq {
        private String industry;
        private String schema;
        private String table;
        private List<String> columns;
    }

    @Data
    private static class LLMResp {
        private String id;
        private String object;
        private String model;
        private List<Choice> choices;
    }

    @Data
    private static class Choice {
        private Long index;
        private Message message;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    private static class Message {
        private String role;
        private String content;
        // private String finish_reason;

        public static Message of(String content) {
            return new Message("user", content);
        }

        public static Message ofSystem(String content) {
            return new Message("system", content);
        }
    }

    @Data
    private static class RerankReq {
        private final List<RerankQuery> rerankings;
    }

    @Data
    private static class RerankQuery {
        private final String query;
        private final List<String> documents;
    }

    @Data
    private static class RerankResp {
        private int index;
        private double relevance_score;
    }

    @Getter
    @Setter
    @RequiredArgsConstructor
    private static class BaseReq {
        private final String taskId;
        private final String industry;
        private final String stdId;
        private final String stdUrl;
        private Boolean stop;
    }

    @Getter
    @Setter
    private static class CategoryReq extends BaseReq {
        private String mode;
        private List<Column> columns;

        public CategoryReq(String taskId, String industry, String stdId, String stdUrl) {
            super(taskId, industry, stdId, stdUrl);
        }
    }

    @Getter
    @Setter
    private static class FileCategoryReq extends BaseReq {
        private final String callbackUrl;
        private LocalFile files;

        public FileCategoryReq(String taskId, String industry, String stdId, String stdUrl, String callbackUrl) {
            super(taskId, industry, stdId, stdUrl);
            this.callbackUrl = callbackUrl;
        }
    }

    @Data
    @NoArgsConstructor
    private static class Column {
        private String tableName;
        private String tableDesc;
        private String columnName;
        private String columnDesc;
        private List<Categories> categories;

        public Column(TableDTO table, ResultColumnDTO column) {
            this.tableName = table.getTableName();
            this.tableDesc = table.getTableComment();
            this.columnName = column.getMeta().getColumnName();
            this.columnDesc = StrUtil.subPre(column.getBizName(), LEN_LIMIT_LLM);
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    private static class Categories {
        private Long categoryId;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    private static class LocalFile {
        private String documentId;
        private String location;
        private Map<String, Object> meta;
    }

    @Data
    private static class ResponseItem {
        private String tableName;
        private String tableDesc;
        private String columnName;
        private String columnDesc;
        private String fieldId;
        private Long categoryId;
        private String categoryName;
        private String according;
        private Double confidence;
    }

    @Data
    private static class Document {
        private String taskId;
        private String documentId;
        @Alias("txt_file_path")
        private String txtFilePath;

        private List<Ref> refs;
        private List<Category> categories;
    }

    @Data
    private static class Ref {
        @Alias("start_line")
        private Integer startLine;
        @Alias("end_line")
        private Integer endLine;
        @Alias("start_byte")
        private Long startByte;
        @Alias("end_byte")
        private Long endByte;
    }

    @Data
    private static class Category {
        private Long categoryId;
        private String according;
        private Double confidence;
        private String content;
        @Alias("ref_index")
        private Integer refIndex;
    }
}
