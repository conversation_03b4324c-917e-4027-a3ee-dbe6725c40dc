package com.mchz.nyx.pipeline.generator;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.mchz.nyx.dark.factory.AbstractDarkActuator;
import com.mchz.nyx.dark.model.TargetInfo;
import com.mchz.nyx.dark.model.dto.ActuatorParam;
import com.mchz.nyx.dark.model.meta.AdditionalInfo;
import com.mchz.nyx.dark.model.meta.MetaColumnData;
import com.mchz.nyx.pipeline.model.api.ColumnReq;
import com.mchz.nyx.pipeline.model.api.MatchResp;
import com.mchz.nyx.pipeline.model.dto.MatchDTO;
import com.mchz.nyx.pipeline.model.dto.TableDTO;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/6/28
 */
@Slf4j
public class DarkQuickActuator extends AbstractDarkActuator<TableDTO, ColumnReq, MetaColumnData, MatchResp> {

    public DarkQuickActuator(ActuatorParam stages) {
        super(stages);
    }

    @Override
    public boolean isEmptyEngine() {
        return super.isEmptyEngine();
    }

    public MatchResp execute(AdditionalInfo addInfo, TableDTO table, List<ColumnReq> columns) {
        return execute(addInfo, table, columns, ColumnReq::getData);
    }

    @Override
    protected MetaColumnData buildColumnResultObj(ColumnReq meta, MetaColumnData columnData, List<Object> data) {
        return columnData;
    }

    @Override
    protected MatchResp buildResult(TableDTO tableMeta, List<MetaColumnData> columns) {
        MatchResp res = new MatchResp();
        List<MatchDTO> data = columns.stream().map(v -> {
            List<String> list = v.getResult().stream().map(s -> {
                TargetInfo t = s.getInfo();
                return String.format("%d【%s】 %s %s", ObjUtil.defaultIfNull(t.getId(), 0L), t.getName(), s.getScore() != null ? String.format("%.2f", s.getScore()) : "-", s.getType());
            }).collect(Collectors.toList());
            String comment = StrUtil.equals(v.getMeta().getColumnComment(), v.getComment()) ? null : v.getComment();
            return new MatchDTO(v.getMeta().getColumnName(), v.getAlias(), comment, list);
        }).collect(Collectors.toList());
        res.setColumns(data);
        return res;
    }

}
