package com.mchz.nyx.pipeline.model.other;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;

@Data
@EqualsAndHashCode
public class ColumnKey implements Serializable {
    private static final long serialVersionUID = -7833638074999584018L;
    /** 作业id */
    private Long jobId;
    /** 数据源id */
    private Integer configId;
    /** schema */
    private String schema;
    /** 表名 */
    private String tableName;
    /** 列名 */
    private String columnName;
}
