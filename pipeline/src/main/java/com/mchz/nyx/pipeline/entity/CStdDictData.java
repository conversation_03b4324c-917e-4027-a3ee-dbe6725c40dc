package com.mchz.nyx.pipeline.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 字典数据表
 */
@Data
@TableName("c_std_dict_data")
@Accessors(chain = true)
public class CStdDictData {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 字典类型表ID
     */
    private Long dictId;

    /**
     * 内容
     */
    private String content;

    /**
     * 状态：0=停用；1=正常
     */
    private Boolean status;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 备注
     */
    private String remark;

    /**
     * 数据来源：1=内置；2=自定义
     */
    private Byte dataSource;

    /**
     * 内置主键
     */
    private String sId;
}
