package com.mchz.nyx.pipeline.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mchz.nyx.pipeline.common.constants.PipelineConst;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-29
 */
@Data
@TableName(value = "artifact", schema = PipelineConst.EMBED_SCHEMA)
public class Artifact implements Serializable {
    @TableId
    private Long artifactId;

    private String name;

    private String artifactType;

    private String description;

    private Integer revision;

    private Integer level;
}
