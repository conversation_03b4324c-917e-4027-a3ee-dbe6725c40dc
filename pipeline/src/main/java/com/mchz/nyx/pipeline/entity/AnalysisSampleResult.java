package com.mchz.nyx.pipeline.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020/11/26 14:21
 */
@Data
@TableName("analysis_sample_result")
public class AnalysisSampleResult implements Serializable {
    private static final long serialVersionUID = 7729244310148174305L;
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 字段id
     */
    private Long resultId;

    /**
     * 类型，字典表中技术类型（业务类型）id
     */
    private Long bizId;

    private Long classifyId;

    private Integer level;

    /**
     * 规则类型：1=业务类型；2=NLP；3=特征；4=长文本；5=其他；
     */
    private Integer ruleType;

    /**
     * 命中率
     */
    private BigDecimal hitRate;

    /**
     * 可信度
     */
    private Integer degree;

    /**
     * 评分
     */
    private Integer score;

    /**
     * 命中数据
     */
    private String hitData;
}
