package com.mchz.nyx.pipeline.common.enums;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 数据源类型枚举
 * </p>
 *
 * <AUTHOR>
 */
@Getter
public enum DatabaseType {
    FILE_TXT(127, "TXT", 0, "txt"),
    FILE_CSV(128, "CSV", 0, "csv"),
    FILE_XLS(129, "XLS", 0, "xls"),
    FILE_XLSX(130, "XLSX", 0, "xlsx"),
    FILE_JSON(137, "JSON", 4, "json"),
    FILE_XML(138, "XML", 4, "xml"),
    FILE_HTML(139, "HTML", 4, "html"),

    FILE_METADATA(131, "元数据", 1, "xls", "xlsx"),

    WORD(185, "docx", 3, "doc", "docx"),
    PDF(186, "pdf", 3, "pdf"),
    /**
     * Other
     */
    OTHER(-1, "Other", 0);

    private final Integer code;
    private final String name;
    private final int type;
    private final List<String> suffix;

    DatabaseType(Integer code, String name, int type, String... suffix) {
        this.code = code;
        this.name = name;
        this.type = type;
        this.suffix = Arrays.asList(suffix);
    }

    // static *******************************************************************************************************

    // static *******************************************************************************************************

    public boolean isIgnoreData() {
        return (type & 1) > 0;
    }

    public boolean isUnstructured() {
        return (type & 2) > 0;
    }

    public boolean isContainDataNode() {
        return (type & 4) > 0;
    }

    public boolean isSuffix(String name) {
        if (StrUtil.isEmpty(name) || suffix.isEmpty()) {
            return false;
        }
        return suffix.stream().anyMatch(v -> {
            if (name.length() <= v.length()) {
                return false;
            }
            if (!name.endsWith(v)) {
                return false;
            }
            return StrUtil.C_DOT == name.charAt(name.length() - v.length() - 1);
        });
    }

}
