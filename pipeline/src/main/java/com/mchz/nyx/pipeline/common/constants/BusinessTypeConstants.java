package com.mchz.nyx.pipeline.common.constants;

public final class BusinessTypeConstants {

    /** 规则类型 */
    public static class RuleType {
        /** 列内容 */
        public static final byte DETAIL = 1;
        /** 列名 */
        public static final byte NAME = 2;
        /** 列注释 */
        public static final byte COMMENT = 3;
        /** 自定义代码 */
        public static final byte CUSTOM_CODE = 6;

        private RuleType() { }
    }

    /** 算法 */
    public static class Algorithm {
        public static final byte INNER = 0;
        /** 字典 */
        public static final byte DICT = 1;
        /** 正则 */
        public static final byte REGEX = 2;
        /** 函数 */
        public static final byte FUNCTION = 3;
        /** 枚举 */
        public static final byte ENUM = 4;
        /** 长文本 */
        @Deprecated
        public static final byte LONGTEXT = 5;
        /** 特征工程 */
        public static final byte FEATURE = 6;

        private Algorithm() { }
    }

    /** 匹配方式 */
    public static class MatchType {
        /** 精确 */
        public static final byte EQUAL = 1;
        /** 模糊 */
        public static final byte CONTAINS = 2;
        /** 前缀 */
        public static final byte START_WITH = 3;
        /** 后缀 */
        public static final byte END_WITH = 4;

        private MatchType() { }
    }

    /** 期望 */
    public static class Expect {
        /** 反向 */
        public static final byte NEGATIVE = 0;
        /** 正向 */
        public static final byte POSITIVE = 1;

        private Expect() { }
    }

    private BusinessTypeConstants() { }
}
