package com.mchz.nyx.pipeline.service;

import com.mchz.nyx.dark.model.TargetInfo;
import com.mchz.nyx.dark.model.dto.StdDetailDTO;

import java.util.Collection;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/10/16
 */
public interface InnerRuleService {

    Map<Long, TargetInfo> loadTerms(Long stdId);

    StdDetailDTO loadInnerRule(Collection<Long> artifactIds, boolean loadTerm, Double hit);
}
