package com.mchz.nyx.pipeline.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 * 重复表的字段类型：0=非重复表字段；1=重复表相同字段；2=重复表不同字段；
 * 重复表的字段类型：0=非重复表字段；1=主表字段；2=副表字段；
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/1
 */
@Getter
@AllArgsConstructor
public enum RepeatColumnType {
    NORMAL(0),
    MAIN(1),
    SUB(2),
    UNLINK(3),
    ;
    private final Integer code;

    public boolean equal(Integer code) {
        return this.code.equals(code);
    }
}
