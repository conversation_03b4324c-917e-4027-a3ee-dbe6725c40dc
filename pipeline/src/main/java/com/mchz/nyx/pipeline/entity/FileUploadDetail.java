package com.mchz.nyx.pipeline.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 文件数据源上传信息表
 */
@Data
@TableName("file_upload_detail")
public class FileUploadDetail {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 数据源id
     */
    private Integer configId;

    /**
     * 上传文件唯一标识
     */
    private String uuid;

    /**
     * 压缩文件uuid
     */
    private String compressUuid;

    /**
     * 文件名称
     */
    private String name;

    /**
     * 文件格式
     */
    private String format;

    /**
     * 0-未转移 1-已转移
     */
    private Byte moved;

    /**
     * 临时存储路径
     */
    private String tempPath;

    /**
     * 最终存储路径
     */
    private String path;

    public boolean isMoved() {
        return null != moved && 1 == moved;
    }

    public String getFilePath() {
        if (null == moved || 1 == moved) {
            return path;
        }
        return tempPath;
    }
}
