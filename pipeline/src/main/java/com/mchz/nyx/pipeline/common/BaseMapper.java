package com.mchz.nyx.pipeline.common;

import com.baomidou.mybatisplus.core.batch.MybatisBatch;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.override.MybatisMapperProxy;
import com.baomidou.mybatisplus.core.toolkit.MybatisBatchUtils;
import com.baomidou.mybatisplus.core.toolkit.MybatisUtils;
import com.github.yulichang.base.MPJBaseMapper;
import org.apache.ibatis.executor.BatchResult;
import org.apache.ibatis.session.SqlSessionFactory;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
public interface BaseMapper<T> extends MPJBaseMapper<T> {
    @SuppressWarnings("all")
    int insertList(List<T> entityList);

    @SuppressWarnings("all")
    int alwaysUpdateById(T entity);

    /**
     * 根据ID 批量更新
     */
    @SuppressWarnings("all")
    default List<BatchResult> alwaysUpdateById(Collection<T> entityList, int batchSize) {
        MybatisMapperProxy<?> mybatisMapperProxy = MybatisUtils.getMybatisMapperProxy(this);
        MybatisBatch.Method<T> method = new MybatisBatch.Method<>(mybatisMapperProxy.getMapperInterface());
        SqlSessionFactory sqlSessionFactory = MybatisUtils.getSqlSessionFactory(mybatisMapperProxy);
        return MybatisBatchUtils.execute(sqlSessionFactory, entityList, method.get("alwaysUpdateById", (e) -> {
            Map<String, Object> param = new HashMap();
            param.put("et", e);
            return param;
        }), batchSize);
    }

    /**
     * 根据ID 批量更新
     */
    @SuppressWarnings("all")
    default List<BatchResult> batchUpdate(Collection<T> entityList, int batchSize, Function<T, Wrapper<T>> wrapperFunction) {
        MybatisMapperProxy<?> mybatisMapperProxy = MybatisUtils.getMybatisMapperProxy(this);
        MybatisBatch.Method<T> method = new MybatisBatch.Method<>(mybatisMapperProxy.getMapperInterface());
        SqlSessionFactory sqlSessionFactory = MybatisUtils.getSqlSessionFactory(mybatisMapperProxy);
        return MybatisBatchUtils.execute(sqlSessionFactory, entityList, method.update(wrapperFunction), batchSize);
    }
}
