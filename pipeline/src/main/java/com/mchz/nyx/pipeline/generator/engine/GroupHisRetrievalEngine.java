package com.mchz.nyx.pipeline.generator.engine;

import cn.hutool.core.collection.CollUtil;
import com.mchz.nyx.dark.engine.HistoryRetrievalEngine;
import com.mchz.nyx.dark.model.TargetInfo;
import com.mchz.nyx.dark.model.meta.NyxMetaColumn;
import com.mchz.nyx.dark.model.meta.NyxMetaTable;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/7/10
 */
@AllArgsConstructor
public class GroupHisRetrievalEngine implements HistoryRetrievalEngine {
    private final List<HistoryRetrievalEngine> engines;

    @Override
    public List<TargetInfo> retrieve(NyxMetaTable table, NyxMetaColumn column) {
        for (HistoryRetrievalEngine engine : engines) {
            List<TargetInfo> res = engine.retrieve(table, column);
            if (CollUtil.isNotEmpty(res)) {
                return res;
            }
        }
        return null;
    }
}
