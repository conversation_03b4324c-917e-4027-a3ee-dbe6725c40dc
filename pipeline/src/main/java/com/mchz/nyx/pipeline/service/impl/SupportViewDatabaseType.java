package com.mchz.nyx.pipeline.service.impl;

import com.mchz.mcdatasource.core.DataBaseType;

import java.util.Objects;

public enum SupportViewDatabaseType {
    ORACLE(DataBaseType.ORACLE),
    MSSQL(DataBaseType.MSSQL),
    KINGBASE86(DataBaseType.KINGBASE86),
    OCEAN_BASE_MYSQL(DataBaseType.OCEANBASE),
    OCEAN_BASE_ORACLE(DataBaseType.OCEANBASE_ORACLE),
    MYSQL(DataBaseType.MYSQL),
    MYSQL5(DataBaseType.MYSQL_5),
    R<PERSON>_MYSQL(DataBaseType.RDS_MYSQL),
    GBASE8A(DataBaseType.GBASE8A),
    GBASE8S87(DataBaseType.GBASE8S87),
    DM(DataBaseType.DM),
    TERADATA(DataBaseType.TERADATA),
    ;

    private final DataBaseType type;

    SupportViewDatabaseType(DataBaseType type) {
        this.type = type;
    }

    public static boolean isSupport(DataBaseType type) {
        Objects.requireNonNull(type);
        for (SupportViewDatabaseType value : values()) {
            if (Objects.equals(value.type, type)) {
                return true;
            }
        }
        return false;
    }

    public static boolean isSupport(String pluginId) {
        Objects.requireNonNull(pluginId);
        for (SupportViewDatabaseType value : values()) {
            if (Objects.equals(value.type.pluginId, pluginId)) {
                return true;
            }
        }
        return false;
    }
}
