package com.mchz.nyx.pipeline.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.toolkit.JoinWrappers;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.mchz.nyx.common.util.PartitionUtil;
import com.mchz.nyx.dark.model.TargetInfo;
import com.mchz.nyx.meta.entity.DbMetaObject;
import com.mchz.nyx.pipeline.common.enums.AnalysisColumnStatus;
import com.mchz.nyx.pipeline.common.enums.JobType;
import com.mchz.nyx.pipeline.entity.AnalysisColumn;
import com.mchz.nyx.pipeline.entity.DiscoveryJobDO;
import com.mchz.nyx.pipeline.entity.DiscoveryPlanDO;
import com.mchz.nyx.pipeline.entity.PlanRelease;
import com.mchz.nyx.pipeline.mapper.AnalysisColumnMapper;
import com.mchz.nyx.pipeline.mapper.DbMetaObjectMapper;
import com.mchz.nyx.pipeline.mapper.DiscoveryPlanMapper;
import com.mchz.nyx.pipeline.mapper.PlanReleaseMapper;
import com.mchz.nyx.pipeline.service.AnalysisHistoryService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/22
 */
@Slf4j
@Service
@AllArgsConstructor
public class AnalysisHistoryServiceImpl implements AnalysisHistoryService {
    private final DiscoveryPlanMapper discoveryPlanMapper;
    private final PlanReleaseMapper planReleaseMapper;
    private final AnalysisColumnMapper analysisColumnMapper;
    private final DbMetaObjectMapper dbMetaObjectMapper;

    @Override
    public List<Long> loadPublishedPlanIds(Long stdId) {
        MPJLambdaWrapper<DiscoveryPlanDO> q = JoinWrappers.lambda(DiscoveryPlanDO.class)
            .select(DiscoveryPlanDO::getId)
            .innerJoin(DiscoveryJobDO.class, DiscoveryJobDO::getId, DiscoveryPlanDO::getJobId)
            .eq(DiscoveryPlanDO::getJobType, JobType.DISCOVERY.getCode()).eq(DiscoveryPlanDO::getStdId, stdId).eq(DiscoveryJobDO::getStatus, 8)
            .orderBy(true, false, DiscoveryPlanDO::getJobId);
        List<Map<String, Object>> res = discoveryPlanMapper.selectMaps(Page.of(1, 50, false), q);
        if (res.isEmpty()) {
            return Collections.emptyList();
        }
        String key = CollUtil.getFirst(res.get(0).keySet());
        List<Long> planIds = res.stream().map(v -> MapUtil.getLong(v, key)).collect(Collectors.toList());

        MPJLambdaWrapper<PlanRelease> s = JoinWrappers.lambda(PlanRelease.class).select(PlanRelease::getPlanId).selectMax(PlanRelease::getId, PlanRelease::getId).in(PlanRelease::getPlanId, planIds).groupBy(PlanRelease::getPlanId);
        List<PlanRelease> list = planReleaseMapper.selectList(s);
        return list.stream().sorted((o1, o2) -> o2.getId().compareTo(o1.getId())).map(PlanRelease::getPlanId).collect(Collectors.toList());
    }

    @Override
    public void loadAllHistoryResult(List<Long> planIds, BiConsumer<String, String> tcCall, BiConsumer<String, String> ccCall) {
        if (CollUtil.isEmpty(planIds) || null == tcCall && null == ccCall) {
            return;
        }
        List<Long> cidList;
        if (null != tcCall) {
            cidList = new ArrayList<>();
            for (Long planId : planIds) {
                LambdaQueryWrapper<AnalysisColumn> q = where(Wrappers.lambdaQuery(AnalysisColumn.class)
                    .select(AnalysisColumn::getColumnId, AnalysisColumn::getTableName, AnalysisColumn::getColumnName)
                    .eq(AnalysisColumn::getPlanId, planId).eq(AnalysisColumn::getFlag, 1));
                analysisColumnMapper.selectList(q, r -> {
                    cidList.add(r.getResultObject().getColumnId());
                    tcCall.accept(r.getResultObject().getTableName(), r.getResultObject().getColumnName());
                });
            }
        } else {
            LambdaQueryWrapper<AnalysisColumn> q = where(Wrappers.lambdaQuery(AnalysisColumn.class)
                .select(AnalysisColumn::getColumnId)
                .in(AnalysisColumn::getPlanId, planIds).eq(AnalysisColumn::getFlag, 1));
            cidList = analysisColumnMapper.selectObjs(q);
        }

        if (null == ccCall || CollUtil.isEmpty(cidList)) {
            return;
        }
        PartitionUtil.part(cidList, 1000).forEach(cIds -> {
            LambdaQueryWrapper<DbMetaObject> q2 = Wrappers.lambdaQuery(DbMetaObject.class).select(DbMetaObject::getName, DbMetaObject::getDescription).in(DbMetaObject::getOid, cIds);
            dbMetaObjectMapper.selectList(q2, v -> ccCall.accept(v.getResultObject().getName(), v.getResultObject().getDescription()));
        });
    }

    @Override
    public List<TargetInfo> loadHistoryResultWithCC(List<Long> planIds, String name, String comment) {
        LambdaQueryWrapper<AnalysisColumn> q = where(Wrappers.lambdaQuery(AnalysisColumn.class)
            .select(AnalysisColumn::getPlanId, AnalysisColumn::getColumnId, AnalysisColumn::getBizId, AnalysisColumn::getClassifyId, AnalysisColumn::getLevel)
            .in(AnalysisColumn::getPlanId, planIds).eq(AnalysisColumn::getFlag, 1).eq(AnalysisColumn::getColumnName, name));
        List<AnalysisColumn> columns = analysisColumnMapper.selectList(Page.of(1, 5, false), q);
        if (CollUtil.isEmpty(columns)) {
            return Collections.emptyList();
        }
        if (1 == columns.size()) {
            AnalysisColumn column = columns.get(0);
            return Collections.singletonList(new TargetInfo(column.getBizId(), column.getClassifyId(), column.getLevel()));
        }
        Map<Long, AnalysisColumn> map = columns.stream().collect(Collectors.toMap(AnalysisColumn::getColumnId, Function.identity()));
        LambdaQueryWrapper<DbMetaObject> q2 = Wrappers.lambdaQuery(DbMetaObject.class).select(DbMetaObject::getOid, DbMetaObject::getDescription).in(DbMetaObject::getOid, map.keySet());
        List<DbMetaObject> list = dbMetaObjectMapper.selectList(q2);
        if (CollUtil.isEmpty(list)) {
            return Collections.emptyList();
        }
        Stream<DbMetaObject> stream = list.stream();
        if (StrUtil.isBlank(comment)) {
            stream = stream.filter(v -> StrUtil.isBlank(v.getDescription()));
        } else {
            stream = stream.filter(v -> comment.equals(v.getDescription()));
        }
        return stream.map(v -> map.get(v.getOid()))
            .filter(Objects::nonNull).peek(v -> v.setStatus(planIds.indexOf(v.getPlanId()))).sorted(Comparator.comparing(AnalysisColumn::getStatus)).map(v -> new TargetInfo(v.getBizId(), v.getClassifyId(), v.getLevel()))
            .collect(Collectors.toList());
    }

    @Override
    public List<TargetInfo> loadHistoryResultWithTC(List<Long> planIds, String table, String column) {
        LambdaQueryWrapper<AnalysisColumn> q = where(Wrappers.lambdaQuery(AnalysisColumn.class)
            .select(AnalysisColumn::getPlanId, AnalysisColumn::getColumnId, AnalysisColumn::getBizId, AnalysisColumn::getClassifyId, AnalysisColumn::getLevel)
            .in(AnalysisColumn::getPlanId, planIds).eq(AnalysisColumn::getFlag, 1).eq(AnalysisColumn::getColumnName, column).eq(AnalysisColumn::getTableName, table)).orderBy(true, false, AnalysisColumn::getPlanId);
        List<AnalysisColumn> columns = analysisColumnMapper.selectList(Page.of(1, 5, false), q);
        if (CollUtil.isEmpty(columns)) {
            return Collections.emptyList();
        }
        return columns.stream().map(v -> new TargetInfo(v.getBizId(), v.getClassifyId(), v.getLevel())).collect(Collectors.toList());
    }

    private LambdaQueryWrapper<AnalysisColumn> where(LambdaQueryWrapper<AnalysisColumn> q) {
        return q.eq(AnalysisColumn::getStatus, AnalysisColumnStatus.S1.getCode()).isNotNull(AnalysisColumn::getBizId);
    }

}
