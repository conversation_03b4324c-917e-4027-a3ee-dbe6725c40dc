package com.mchz.nyx.pipeline.model.param;

import com.mchz.nyx.meta.support.CharsetConvert;
import com.mchz.nyx.meta.support.DataSourceConfig;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Properties;

/**
 * <p>
 * 数据源连接参数
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/19
 */
@Data
@Accessors(chain = true)
public class McDataSourceParam implements DataSourceConfig {
    private Long sourceId;
    private String type;
    private String host;
    private String port;
    private String db;
    private String user;
    private String pass;
    private Properties advanced;
    private CharsetConvert charsetConvert;
}
