package com.mchz.nyx.pipeline.config.props;

import com.mchz.nyx.pipeline.common.constants.PipelineConst;
import lombok.Data;

/**
 * <p>
 * 系统默认配置
 * </p>
 *
 * <AUTHOR>
 * @date 2020/11/3 17:15
 */
@Data
public class SysProperties {
    private String master;
    private int discoveryTaskSize = 5;
    private int storeThreadTotalSize = PipelineConst.DEFAULT_POOL_SIZE + 1;
    private int tableColumnNum = PipelineConst.TABLE_COLUMN_NUM;
    /**
     * 最大字段长度（大字段长度限制）
     */
    private long maxColumnLength = PipelineConst.MAX_COLUMN_LENGTH;
    /**
     * 开启采集视图
     */
    private boolean enableCollectView;

    /**
     * 是否启用分区筛选
     */
    private boolean enablePartitionCondition;

    private boolean overlaySampling;


}
