package com.mchz.nyx.pipeline.model.dto;

import com.mchz.nyx.pipeline.model.log.JobLogInfoDTO;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Getter;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/27
 */
@Data
public class JobLogInstantDTO {
    private final ProgressInfoDTO dto;

    @Getter(AccessLevel.PRIVATE)
    private Set<String> tables;

    public JobLogInstantDTO(String tenantId, Long jobId) {
        this.dto = new ProgressInfoDTO(tenantId, jobId, null, new ArrayList<>());
    }

    public String getTenantId() {
        return dto.getTenantId();
    }

    public Long getJobId(){
        return dto.getJobId();
    }

    public void setPercent(BigDecimal percent) {
        dto.setPercent(percent);
    }

    public void addContent(JobLogInfoDTO logInfo) {
        dto.getContent().add(logInfo);
    }

    public List<String> getTableList() {
        return new ArrayList<>(tables);
    }
}
