package com.mchz.nyx.pipeline.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 源数据发现作业表
 */
@Data
@TableName("discovery_plan")
public class DiscoveryPlanDO implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;

    private Integer jobType;

    private Long stdId;

    private Long jobId;
}
