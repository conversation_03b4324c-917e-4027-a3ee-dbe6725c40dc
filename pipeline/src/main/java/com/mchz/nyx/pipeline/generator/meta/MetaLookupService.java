package com.mchz.nyx.pipeline.generator.meta;

import com.mchz.nyx.meta.support.DataSourceConfig;
import com.mchz.nyx.pipeline.model.vo.TableVO;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/18
 */
public interface MetaLookupService {
    /**
     * 采集数据源schema列表
     */
    List<String> loadSchema(DataSourceConfig source);

    /**
     * 采集数据源table列表
     */
    List<TableVO> loadTable(DataSourceConfig source);
}
