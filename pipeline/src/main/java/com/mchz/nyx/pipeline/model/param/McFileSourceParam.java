package com.mchz.nyx.pipeline.model.param;

import com.mchz.mcdatasource.core.DataBaseType;
import com.mchz.nyx.meta.support.DataSourceConfig;
import com.mchz.nyx.pipeline.common.enums.DatabaseType;
import com.mchz.nyx.pipeline.model.api.RemoteAddressParam;
import com.mchz.nyx.pipeline.util.file.manager.FileAttach;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Properties;


/**
 * <p>
 * 数据源连接参数
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/19
 */

@Data
@Accessors(chain = true)
public class McFileSourceParam implements DataSourceConfig {
    private Long sourceId;
    private DatabaseType fileType;
    private RemoteAddressParam remote;
    private FileAttach fileAttach;

    @Override
    public String getType() {
        return DataBaseType.LOCAL_FILE.pluginId;
    }

    @Override
    public String getHost() {
        return null;
    }

    @Override
    public String getPort() {
        return null;
    }

    @Override
    public String getDb() {
        return null;
    }

    @Override
    public String getUser() {
        return null;
    }

    @Override
    public String getPass() {
        return null;
    }

    @Override
    public Properties getAdvanced() {
        return null;
    }
}
