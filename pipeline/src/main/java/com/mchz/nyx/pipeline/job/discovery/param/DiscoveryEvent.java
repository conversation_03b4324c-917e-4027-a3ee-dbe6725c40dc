package com.mchz.nyx.pipeline.job.discovery.param;

import com.mchz.nyx.pipeline.entity.AnalysisColumn;
import com.mchz.nyx.pipeline.entity.SampleColumn;
import com.mchz.nyx.pipeline.model.dto.AnalysisResultDTO;
import com.mchz.nyx.pipeline.model.dto.TableDTO;
import lombok.Data;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/9
 */
@Data
public class DiscoveryEvent {
    private TableLogParam log;

    private TableDTO table;
    private List<AnalysisColumn> columns;

    private AnalysisResultDTO analysisResult;
    private List<SampleColumn> sample;
    private Integer status;
}
