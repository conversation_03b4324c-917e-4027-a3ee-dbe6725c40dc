package com.mchz.nyx.pipeline.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <p>
 * 日志
 * </p>
 *
 * <AUTHOR>
 * @date 2022/03/25 14:24
 */
@Data
@TableName("discovery_log")
public class DiscoveryLogDO {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 作业id
     */
    private Long jobId;

    /**
     * 进度
     */
    private BigDecimal percent;

    /**
     * 内容
     */
    private String content;

}
