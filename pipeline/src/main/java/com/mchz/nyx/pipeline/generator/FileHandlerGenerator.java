package com.mchz.nyx.pipeline.generator;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.ftp.AbstractFtp;
import cn.hutool.extra.ftp.Ftp;
import cn.hutool.extra.ftp.FtpConfig;
import cn.hutool.extra.ftp.FtpMode;
import cn.hutool.extra.ssh.Sftp;
import com.mchz.nyx.pipeline.config.props.SourceDataProperties;
import com.mchz.nyx.pipeline.exception.ServiceException;
import com.mchz.nyx.pipeline.generator.file.ClusterFileHandler;
import com.mchz.nyx.pipeline.generator.file.FileHandler;
import com.mchz.nyx.pipeline.generator.file.LocalFileHandler;
import com.mchz.nyx.pipeline.generator.file.RemoteFileHandler;
import com.mchz.nyx.pipeline.model.api.RemoteAddressParam;
import com.mchz.nyx.pipeline.service.FileSyncService;
import com.mchz.nyx.pipeline.service.impl.FileSyncServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPReply;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/30
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FileHandlerGenerator {
    private final SourceDataProperties properties;
    private FileSyncService fileSyncService;

    public FileHandler generate(RemoteAddressParam param) {
        if (null == param) {
            if (properties.getFileServer().isEnabled()) {
                return new ClusterFileHandler(getFileSyncService());
            }
            return new LocalFileHandler();
        }
        return new RemoteFileHandler(getFtp(param));
    }


    private FileSyncService getFileSyncService() {
        if (null == fileSyncService && properties.getFileServer().isEnabled()) {
            synchronized (FileHandlerGenerator.class) {
                fileSyncService = new FileSyncServiceImpl(properties);
            }
        }
        return fileSyncService;
    }

    private AbstractFtp getFtp(RemoteAddressParam config) {
        int port;
        try {
            port = Integer.parseInt(config.getPort());
        } catch (Exception e) {
            throw new ServiceException("远程地址端口异常:" + config.getPort());
        }
        FtpConfig ftpConfig = new FtpConfig(config.getUrl(), port, config.getUsername(), config.getPassword(), null);
        if (config.isSftp()) {
            ftpConfig.setCharset(StrUtil.isEmpty(config.getCharset()) ? StandardCharsets.UTF_8 : Charset.forName(config.getCharset()));
            return new Sftp(ftpConfig);
        } else {
            boolean testEncoding = StrUtil.isEmpty(config.getCharset());
            ftpConfig.setCharset(testEncoding ? StandardCharsets.UTF_8 : Charset.forName(config.getCharset()));
            Ftp ftp = new Ftp(ftpConfig, FtpMode.Passive);
            if (testEncoding) {
                setEncoding(ftp, ftpConfig);
            }
            return ftp;
        }
    }

    private void setEncoding(Ftp ftp, FtpConfig ftpConfig) {
        FTPClient client = ftp.getClient();
        try {
            if (FTPReply.isPositiveCompletion(client.sendCommand("OPTS UTF8", "ON"))) {
                return;
            }
        } catch (IOException ignore) {
        }
        ftpConfig.setCharset(Charset.forName("GBK"));
        try {
            ftp.close();
        } catch (IOException ignore) {
        }
        ftp.init();
    }
}
