package com.mchz.nyx.pipeline.util.file.fs;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileNameUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.compress.extractor.Extractor;
import com.mchz.nyx.common.exception.NyxException;
import com.mchz.nyx.pipeline.common.enums.ArchiverEnum;
import com.mchz.nyx.pipeline.common.enums.DatabaseType;
import com.mchz.nyx.pipeline.entity.FileUploadDetail;
import com.mchz.nyx.pipeline.generator.file.FileHandler;
import com.mchz.nyx.pipeline.util.DecompressUtil;
import com.mchz.nyx.pipeline.util.filter.StrFilter;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/30
 */
@Slf4j
@AllArgsConstructor
public class CompressionBundle implements FilesBundle<SourceFile> {
    private final List<FileUploadDetail> files;
    private final Map<String, StrFilter> map;
    private final FileHandler handler;
    private final File baseDir;
    private final DatabaseType type;

    @NotNull
    @Override
    public Iterator<SourceFile> iterator() {
        return new InnerIterator(files.iterator());
    }

    @RequiredArgsConstructor
    private class InnerIterator implements Iterator<SourceFile> {
        private final Iterator<FileUploadDetail> source;
        private Iterator<FileMeta> tmp;

        @Override
        public boolean hasNext() {
            return null != tmp && tmp.hasNext() || source.hasNext();
        }

        @Override
        public SourceFile next() {
            if (null != tmp && tmp.hasNext()) {
                FileMeta meta = tmp.next();
                return new SourceFile(meta, FileUtil.file(baseDir, meta.getRelPath()));
            }
            FileUploadDetail detail = source.next();
            if (null == detail) {
                throw new NyxException("文件迭代失败");
            }
            File soureFile = handler.getFile(detail.getFilePath());
            List<FileMeta> decompress = decompress(detail.getName(), soureFile, ArchiverEnum.of(detail.getFormat()), FileUtil.file(baseDir, detail.getUuid()), detail.getUuid() + StrUtil.SLASH);
            decompress.forEach(v -> log.info("【解压文件】{}({})", v.getName(), v.getRelPath()));
            tmp = decompress.iterator();
            return next();
        }
    }

    private List<FileMeta> decompress(String zipName, File sourceFile, ArchiverEnum archiverEnum, File outDir, String basePath) {
        StrFilter strFilter = map.getOrDefault(zipName, StrFilter.ALL);
        List<FileMeta> res = new ArrayList<>();
        try (Extractor extractor = DecompressUtil.createExtractor(StandardCharsets.UTF_8, archiverEnum, sourceFile)) {
            extractor.extract(outDir, archiveEntry -> {
                if (archiveEntry.isDirectory()) {
                    return false;
                }
                if (type.isSuffix(archiveEntry.getName())) {
                    String fileName = FileNameUtil.getName(archiveEntry.getName());
                    String extName = FileNameUtil.extName(fileName);
                    String name = archiveEntry.getName().substring(0, fileName.length() - extName.length() - 1);
                    if (strFilter.check(name)) {
                        res.add(new FileMeta(name, extName, fileName, basePath + FileUtil.normalize(archiveEntry.getName()), zipName));
                        return true;
                    }
                }
                return false;
            });
        }
        return res;
    }
}
