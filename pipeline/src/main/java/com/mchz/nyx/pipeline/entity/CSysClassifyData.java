package com.mchz.nyx.pipeline.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 分级表
 */
@Data
@TableName("c_sys_classify_data")
@Accessors(chain = true)
public class CSysClassifyData {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /*
     * 模板id
     */
    private Long templateId;

    /**
     * 标准ID
     */
    private Long stdId;

    /**
     * 业务术语ID
     */
    private Long bizId;

    /**
     * 分级
     */
    private Long typeId;

    /**
     * 敏感等级
     */
    private Integer level;

    /**
     * 所属分类
     */
    private String belongType;

    /**
     * 是否删除：0=否；1=是
     */
    @TableLogic
    private Boolean isDelete;
}
