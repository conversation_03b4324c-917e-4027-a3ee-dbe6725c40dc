package com.mchz.nyx.pipeline.model.log;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <p>
 * 日志
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/11/5 14:16
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class JobLogDTO {
    /**
     * 源数据发现作业id
     */
    private Long jobId;

    /**
     * 开始时间
     */
    private Long startTime;

    /**
     * 百分比
     */
    private BigDecimal percent;
}
