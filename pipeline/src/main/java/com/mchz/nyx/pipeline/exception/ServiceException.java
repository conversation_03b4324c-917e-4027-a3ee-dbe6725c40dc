package com.mchz.nyx.pipeline.exception;

import cn.hutool.core.util.StrUtil;
import com.mchz.nyx.pipeline.common.api.CommonResultCode;
import com.mchz.nyx.pipeline.common.api.IResultCode;
import lombok.Getter;

/**
 * <p>
 * 通用业务异常
 * </p>
 *
 * <AUTHOR>
 */
@Getter
public class ServiceException extends RuntimeException {
    private static final long serialVersionUID = 2359767895161832954L;

    private final IResultCode resultCode;
    private final Object data;

    public ServiceException(Throwable cause) {
        super(cause);
        this.resultCode = CommonResultCode.INTERNAL_SERVER_ERROR;
        this.data = null;
    }

    public ServiceException(String message) {
        super(message);
        this.resultCode = CommonResultCode.INTERNAL_SERVER_ERROR;
        this.data = null;
    }

    public ServiceException(String message, Throwable e) {
        super(message, e);
        this.resultCode = CommonResultCode.INTERNAL_SERVER_ERROR;
        this.data = null;
    }

    public ServiceException(IResultCode resultCode) {
        super(resultCode.getMsg());
        this.resultCode = resultCode;
        this.data = null;
    }

    public ServiceException(IResultCode resultCode, Object data) {
        super(resultCode.getMsg());
        this.resultCode = resultCode;
        this.data = data;
    }

    public ServiceException(IResultCode resultCode, Throwable cause) {
        super(cause);
        this.resultCode = resultCode;
        this.data = null;
    }

    public ServiceException(IResultCode resultCode, Object data, Object... params) {
        super(StrUtil.format(resultCode.getMsg(), params));
        this.resultCode = resultCode;
        this.data = data;
    }
}
