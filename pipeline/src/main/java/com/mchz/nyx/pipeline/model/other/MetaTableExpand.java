package com.mchz.nyx.pipeline.model.other;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 元数据表扩展内容
 */
@Data
public class MetaTableExpand implements Serializable {
    private String schemaName;
    private String tableName;
    private List<MetaIndex> indices = new ArrayList<>();
    private List<MetaForeignKey> foreignKeys = new ArrayList<>();

    /**
     * 索引
     */
    @Data
    public static class MetaIndex implements Serializable {
        private String name;
        private String type;
        private List<String> columnNames = new ArrayList<>();
    }

    /**
     * 外键
     */
    @Data
    public static class MetaForeignKey implements Serializable {
        private String name;
        private String type;
        private List<MetaForeignColumn> columns = new ArrayList<>();
    }

    @Data
    public static class MetaForeignColumn implements Serializable {
        private String primaryTableName;
        private String primaryColumnName;
        private String foreignTableName;
        private String foreignColumnName;
    }
}
