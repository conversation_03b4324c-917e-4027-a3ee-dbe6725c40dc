package com.mchz.nyx.pipeline.job.discovery.param;

import lombok.Data;

import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <p>
 * 输出日志使用
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/13
 */
@Data
public class TableLogParam {
    private final String tableName;
    private final int index;
    private final int size;
    private final AtomicBoolean first;
    private final AtomicInteger count;

    public TableLogParam(String tableName, int index, int size, int batchNum) {
        this.tableName = tableName;
        this.index = index;
        this.size = size;
        this.first = new AtomicBoolean(true);
        this.count = new AtomicInteger(batchNum);
    }
}
