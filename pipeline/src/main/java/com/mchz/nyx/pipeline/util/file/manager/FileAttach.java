package com.mchz.nyx.pipeline.util.file.manager;

import com.mchz.nyx.pipeline.util.Func;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Map;
import java.util.Objects;

import static java.util.Objects.requireNonNull;

@Getter
@RequiredArgsConstructor
public class FileAttach {
    public static final String FIELD_NAME_CHARSET = Func.name(FileAttach::getCharset);
    public static final String FIELD_NAME_HEADLINE = Func.name(FileAttach::isHeadline);
    public static final String FIELD_NAME_TYPE = Func.name(FileAttach::getType);
    public static final String FIELD_NAME_REMOTE_ID = Func.name(FileAttach::getRemoteId);
    public static final String FIELD_NAME_SEPARATOR = Func.name(FileAttach::getSeparator);
    public static final String FIELD_NAME_DATANODE = Func.name(FileAttach::getDataNode);
    public static final String FIELD_NAME_COMPRESS = Func.name(FileAttach::isCompress);

    /**
     * 字符编码
     */
    private final Charset charset;
    /**
     * 有无标题行
     */
    private final boolean headline;
    /**
     * 0-本地 1-远程
     */
    private final String type;
    /**
     * 远程地址id
     */
    private final String remoteId;
    /**
     * 分隔符
     */
    private final Character separator;
    /**
     * 数据结点
     */
    private final String dataNode;
    /**
     * 是否压缩
     */
    private final boolean compress;

    public static FileAttach of(Map<String, String> attachment) {
        return new FileAttach(
            Charset.of(attachment.get(FileAttach.FIELD_NAME_CHARSET)),
            Boolean.parseBoolean(attachment.get(FileAttach.FIELD_NAME_HEADLINE)),
            requireNonNull(attachment.get(FileAttach.FIELD_NAME_TYPE), FileAttach.FIELD_NAME_TYPE),
            attachment.get(FileAttach.FIELD_NAME_REMOTE_ID),
            buildSeparator(attachment.get(FileAttach.FIELD_NAME_SEPARATOR)),
            attachment.get(FileAttach.FIELD_NAME_DATANODE),
            Boolean.parseBoolean(attachment.get(FileAttach.FIELD_NAME_COMPRESS))
        );
    }

    private static Character buildSeparator(String s) {
        if (Objects.isNull(s)) {
            return null;
        }
        if (s.length() == 1) {
            return s.charAt(0);
        }
        if (s.startsWith("/")) {
            try {
                return (char) Integer.parseInt(s.substring(1));
            } catch (Exception e) {
                throw new IllegalArgumentException(String.format("分隔符错误 %s", s));
            }
        }
        throw new IllegalArgumentException(String.format("分隔符错误 %s", s));
    }

    @Getter
    public enum Charset {
        GBK("1", "GBK"),
        UTF8("2", "UTF-8"),
        ISO88591("3", "ISO-8859-1"),
        GB2312("4", "GB2312");

        private final String value;
        private final String name;

        Charset(String value, String name) {
            this.value = value;
            this.name = name;
        }

        public static Charset of(String value) {
            for (Charset charset : Charset.values()) {
                if (charset.value.equals(value)) {
                    return charset;
                }
            }
            return null;
        }
    }

    public static class Type {
        /**
         * 本地
         */
        public static final String LOCAL = "0";
        /**
         * 远程
         */
        public static final String REMOTE = "1";
    }
}
