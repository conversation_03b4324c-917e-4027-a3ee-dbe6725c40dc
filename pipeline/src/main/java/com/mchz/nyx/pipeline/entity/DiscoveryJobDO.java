package com.mchz.nyx.pipeline.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 源数据发现作业表
 */
@Data
@TableName("discovery_job")
public class DiscoveryJobDO implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 发现作业id
     */
    private Long planId;

    /**
     * 整体运行状态
     */
    private Byte status;
    /**
     * 当前步骤，0：未开始，1：第一步，2：第二步，3：第三步，4：第四步
     */
    private Integer currentStep;
    /**
     * 当前步骤运行状态，0：进行中，1：完成，2：终止
     */
    private Byte currentStatus;
    /**
     * 任务开始时间
     */
    private Long startTime;

    private String heartbeat;
}
