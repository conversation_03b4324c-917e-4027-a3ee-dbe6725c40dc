package com.mchz.nyx.pipeline.util.file.fs;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.mchz.nyx.common.exception.NyxException;
import com.mchz.nyx.pipeline.common.constants.PipelineConst;
import com.mchz.nyx.pipeline.entity.FileUploadDetail;
import com.mchz.nyx.pipeline.generator.file.FileHandler;
import lombok.AllArgsConstructor;
import org.jetbrains.annotations.NotNull;

import java.io.File;
import java.util.Iterator;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/30
 */
@AllArgsConstructor
public class SourceFileBundle implements FilesBundle<SourceFile> {
    private final List<FileUploadDetail> files;
    private final FileHandler handler;
    private final File baseDir;

    @NotNull
    @Override
    public Iterator<SourceFile> iterator() {
        return new InnerIterator(files.iterator());
    }

    @AllArgsConstructor
    private class InnerIterator implements Iterator<SourceFile> {
        private final Iterator<FileUploadDetail> source;

        @Override
        public boolean hasNext() {
            return source.hasNext();
        }

        @Override
        public SourceFile next() {
            FileUploadDetail detail = source.next();
            if (null == detail) {
                throw new NyxException("文件迭代失败");
            }
            String fileName = detail.getUuid() + StrUtil.DOT + detail.getFormat();
            File target = FileUtil.file(baseDir, fileName);
            handler.loadFile(detail.getFilePath(), target);
            return new SourceFile(new FileMeta(detail.getName(), detail.getFormat(), fileName, fileName, PipelineConst.DEFAULT_SCHEMA), target);
        }
    }
}
