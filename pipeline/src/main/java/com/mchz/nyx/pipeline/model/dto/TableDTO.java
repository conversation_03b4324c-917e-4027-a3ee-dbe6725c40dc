package com.mchz.nyx.pipeline.model.dto;

import com.mchz.nyx.dark.model.meta.NyxMetaSchema;
import com.mchz.nyx.dark.model.meta.NyxMetaTable;
import lombok.Data;

import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2022/1/12 18:32
 */
@Data
public class TableDTO implements NyxMetaTable {
    private Long id;
    /**
     * 主键
     */
    private Long oid;

    /**
     * schema
     */
    private CatalogSchemaDTO catalogSchema;

    /**
     * 名称
     */
    private String name;

    /**
     * 描述
     */
    private String description;
    /**
     * 数据状态
     */
    private Integer status;

    private boolean rec;

    private ClassifyDTO classify;

    private Map<Long, Integer> classifyNum;

    @Override
    public NyxMetaSchema getSchema() {
        return catalogSchema;
    }

    @Override
    public String getTableName() {
        return name;
    }

    @Override
    public String getTableComment() {
        return description;
    }

    public Long getSchemaId() {
        return catalogSchema.getOid();
    }
}
