package com.mchz.nyx.pipeline.model.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ClassifyTypeVO {
    @ApiModelProperty(value = "ID", example = "1")
    private Long id;

    @ApiModelProperty(value = "全路径名称", example = "1-2-3")
    private String fullName;

    @ApiModelProperty(value = "推荐分级", example = "1")
    private Integer sensitiveLevel;

    @ApiModelProperty(value = "描述", example = "描述")
    private String description;
}
