package com.mchz.nyx.pipeline.service.impl;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.StrUtil;
import com.mchz.base.metis.FailureInfo;
import com.mchz.nyx.common.exception.NyxException;
import com.mchz.nyx.pipeline.common.constants.PipelineConst;
import com.mchz.nyx.pipeline.common.enums.JobType;
import com.mchz.nyx.pipeline.exception.ServiceException;
import com.mchz.nyx.pipeline.job.JobLogManager;
import com.mchz.nyx.pipeline.service.JobStatusService;
import com.mchz.nyx.pipeline.util.Func;
import org.springframework.context.annotation.Profile;
import org.springframework.dao.DataAccessException;
import org.springframework.stereotype.Service;

import java.util.function.BiConsumer;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/8/26
 */
@Profile("embed")
@Service
public class JobStatusServiceEmbedImpl implements JobStatusService {

    private BiConsumer<Long, FailureInfo> callback;

    public void register(BiConsumer<Long, FailureInfo> callback) {
        this.callback = callback;
    }

    @Override
    public String jobStart(JobType jobType, JobLogManager jobLog) {
        return null;
    }

    @Override
    public void jobFinish(JobType jobType, JobLogManager jobLog, boolean success, Throwable e) {
        if (null == callback) {
            return;
        }
        if (success) {
            callback.accept(jobLog.getJobId(), null);
        } else {
            FailureInfo info;
            if (e instanceof DataAccessException) {
                info = new FailureInfo("遇到管理库异常,查看服务日志", e.getMessage());
            } else if (e instanceof ServiceException || e instanceof NyxException) {
                info = new FailureInfo(Func.getMessage(e), null);
            } else {
                info = new FailureInfo(ExceptionUtil.getMessage(e), StrUtil.truncateUtf8(Func.getStackTrace(Func.getCause(e)), PipelineConst.ERROR_MSG_MAX_LEN));
            }
            callback.accept(jobLog.getJobId(), info);
        }
    }
}
