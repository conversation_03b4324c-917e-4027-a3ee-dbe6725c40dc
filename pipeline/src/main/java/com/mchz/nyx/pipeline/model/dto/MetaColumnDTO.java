package com.mchz.nyx.pipeline.model.dto;

import com.mchz.nyx.dark.model.meta.ColumnType;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/11/30 20:53
 */
@Data
public class MetaColumnDTO implements Serializable {
    private static final long serialVersionUID = -8868821445823677360L;
    /** id */
    private Long id;
    /** schema */
    private String schema;
    /** 表格名字 */
    private String tableName;
    /** 列名字 */
    private String columnName;
    /** 列类型 */
    private ColumnType columnType;
    /** 数据库原生类型 */
    private String nativeType;
    /** 列长度 */
    private Long length;
    /** 刻度值 */
    private Integer scale;
    /** 精度 */
    private Integer precision;
    /** 是否可以为空，0 - 否，1 - 是 */
    private Boolean nullable;
    /** 列注释 */
    private String columnComment;
    /** 列位置 */
    private Integer position;
    /** 是否主键列，0 - 否，1 - 是 */
    private Boolean isPk;
    /** 是否唯一列，0 - 否，1 - 是 */
    private Boolean isUnique;
    /** 是否索引列，0 - 否，1 - 是 */
    private Boolean isIndex;
    /** 是否外键，0 - 否，1 - 是 */
    private Boolean isFk;
    /** 外健引用的表格名 */
    private String fkRefTable;
    /** 外健引用的列名字 */
    private String fkRefColumn;
    /** 重复表的字段类型：0=非重复表字段；1=重复表相同字段；2=重复表不同字段 */
    private Byte repeatType;
}
