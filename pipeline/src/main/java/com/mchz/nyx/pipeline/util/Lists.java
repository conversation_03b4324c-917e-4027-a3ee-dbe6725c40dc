package com.mchz.nyx.pipeline.util;

import cn.hutool.core.collection.ListUtil;
import lombok.experimental.UtilityClass;

import java.util.*;


/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
@UtilityClass
public class Lists {
    @SafeVarargs
    @SuppressWarnings("varargs")
    public static <T> List<T> of(T... a) {
        if (1 == a.length) {
            return Collections.singletonList(a[0]);
        }
        return Arrays.asList(a);
    }

    public static <E> ArrayList<E> newArrayList() {
        return new ArrayList<>();
    }

    public static <E> ArrayList<E> newArrayList(Iterable<E> elements) {
        return ListUtil.toList(elements);
    }

    @SafeVarargs
    public static <E> List<E> newArrayList(E... elements) {
        return ListUtil.toList(elements);
    }

    public static <E> List<List<E>> partition(List<E> list, int partSize) {
        return ListUtil.partition(list, partSize);
    }
}
