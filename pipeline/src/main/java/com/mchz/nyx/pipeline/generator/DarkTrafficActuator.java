package com.mchz.nyx.pipeline.generator;

import com.mchz.nyx.dark.factory.AbstractDarkActuator;
import com.mchz.nyx.dark.model.TargetInfo;
import com.mchz.nyx.dark.model.dto.ActuatorParam;
import com.mchz.nyx.dark.model.meta.AdditionalInfo;
import com.mchz.nyx.dark.model.meta.MetaColumnData;
import com.mchz.nyx.pipeline.model.api.ColumnReq;
import com.mchz.nyx.pipeline.model.dto.MatchResDTO;
import com.mchz.nyx.pipeline.model.dto.TableDTO;
import com.mchz.nyx.pipeline.model.dto.TrafficResDTO;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/8
 */
@Slf4j
public class DarkTrafficActuator extends AbstractDarkActuator<TableDTO, ColumnReq, MetaColumnData, TrafficResDTO> {

    public DarkTrafficActuator(ActuatorParam stages) {
        super(stages);
    }

    public TrafficResDTO execute(AdditionalInfo addInfo, TableDTO table, List<ColumnReq> columns) {
        return execute(addInfo, table, columns, ColumnReq::getData);
    }

    @Override
    protected MetaColumnData buildColumnResultObj(ColumnReq meta, MetaColumnData columnData, List<Object> data) {
        return columnData;
    }

    @Override
    protected TrafficResDTO buildResult(TableDTO tableMeta, List<MetaColumnData> columns) {
        TrafficResDTO res = new TrafficResDTO();
        Map<String, List<MatchResDTO>> data = columns.stream().collect(Collectors.toMap(v -> v.getMeta().getColumnName(), v -> v.getResult().stream().map(s -> {
            TargetInfo t = s.getInfo();
            MatchResDTO dto = new MatchResDTO();
            dto.setArtifactId(t.getId());
            dto.setTerm(t.getName());
            dto.setScore(s.getScore());
            return dto;
        }).collect(Collectors.toList())));
        res.setColumns(data);
        return res;
    }

}
