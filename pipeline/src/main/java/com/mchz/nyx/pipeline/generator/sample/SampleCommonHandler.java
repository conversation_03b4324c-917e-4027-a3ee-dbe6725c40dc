package com.mchz.nyx.pipeline.generator.sample;

import cn.hutool.core.io.IoUtil;
import com.mchz.datasource.cli.DatasourceDatabaseCli;
import com.mchz.datasource.cli.RsResult;
import com.mchz.mcdatasource.api.model.SampleQuery;
import com.mchz.mcdatasource.core.DataBaseType;
import com.mchz.mcdatasource.core.DatasourceConstant;
import com.mchz.mcdatasource.model.db.exception.DatabaseException;
import com.mchz.mcdatasource.model.request.ColumnRequest;
import com.mchz.nyx.common.enums.DataTypeGroup;
import com.mchz.nyx.common.exception.NyxException;
import com.mchz.nyx.dark.model.meta.SampleResult;
import com.mchz.nyx.meta.entity.DbMetaColumn;
import com.mchz.nyx.meta.support.CharsetConvert;
import com.mchz.nyx.meta.support.DataSourceConfig;
import com.mchz.nyx.pipeline.exception.ServiceException;
import com.mchz.nyx.pipeline.generator.DarkContext;
import com.mchz.nyx.pipeline.model.dto.ColumnInfo;
import com.mchz.nyx.pipeline.model.param.TableSamplingParam;
import com.mchz.nyx.pipeline.service.MetadataService;
import com.mchz.nyx.pipeline.util.DbUtil;
import com.mchz.nyx.pipeline.util.Func;
import lombok.extern.slf4j.Slf4j;

import java.sql.SQLException;
import java.sql.Statement;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/3
 */
@Slf4j
public class SampleCommonHandler implements SampleHandler {
    private final MetadataService metadataService;
    private final DatasourceDatabaseCli cli;
    private final CharsetConvert charsetConvert;

    public SampleCommonHandler(MetadataService metadataService, DataSourceConfig source) {
        this.metadataService = metadataService;
        this.cli = buildConnect(source);
        this.charsetConvert = DbUtil.getCharsetConvert(source);
    }

    @SuppressWarnings("all")
    private static DatasourceDatabaseCli buildConnect(DataSourceConfig source) {
        try {
            final String key = "socketTimeout";
            Properties properties = source.getAdvanced();
            properties.setProperty(DatasourceConstant.NOT_SUPPORT_BIG_COLUMN_TYPE, "true");
            if (!properties.containsKey(key)) {
                properties.setProperty(key, "150000");
            }
            properties.setProperty(DatasourceConstant.NOT_SUPPORT_BIG_COLUMN_TYPE, "true");
            DatasourceDatabaseCli cli = new DatasourceDatabaseCli(source.getType(), source.getHost(), source.getDb(), source.getPort(), source.getUser(), source.getPass(), true, properties);
            cli.connect(false);
            if (DataBaseType.ODPS.pluginId.equals(source.getType())) {
                try (Statement statement = cli.getConnection().createStatement()) {
                    statement.execute("set odps.sql.type.system.odps2=true");
                    statement.execute("set odps.sql.allow.fullscan=true");
                    statement.execute("set odps.sql.decimal.odps2=true");
                } catch (SQLException e) {
                    throw new NyxException(e);
                }
            }
            return cli;
        } catch (DatabaseException e) {
            throw new ServiceException("目标数据源连接建立失败:" + e.getSuperMessage());
        } catch (Exception e) {
            throw new ServiceException("目标数据源连接建立失败:" + e.getMessage());
        }
    }

    @Override
    public SampleResult sampling(DarkContext context, TableSamplingParam param) {
        RsResult v = sampleQuery(context, param);
        if (null == v) {
            return null;
        }
        Map<String, ColumnInfo> map = param.getColumns().stream().collect(Collectors.toMap(ColumnInfo::getColumnName, Function.identity()));
        List<DbMetaColumn> list = new ArrayList<>(3);
        Set<String> colSet = null == charsetConvert ? null : new HashSet<>(v.getHeadering().length);
        String[] selectColumn = Stream.of(v.getHeadering()).map(c -> {
            ColumnInfo tmp = map.get(c.getColumnName());
            DataTypeGroup group;
            if (null != tmp && DataTypeGroup.CHARACTER == (group = tmp.getColumnType().getTypeGroup())) {
                DataTypeGroup real = DataTypeGroup.valueOf(c.getColumnType());
                if (real != group) {
                    tmp.getColumnType().setTypeGroup(real);
                    DbMetaColumn column = new DbMetaColumn();
                    column.setOid(tmp.getCid());
                    column.setTypeGroup(real.name());
                    list.add(column);
                } else if (null != colSet) {
                    colSet.add(c.getColumnName());
                }
            }
            return c.getColumnName();
        }).toArray(String[]::new);
        if (!list.isEmpty()) {
            metadataService.updateColumn(list);
        }
        SampleResult result = SampleResult.ofLine(selectColumn, v.getIterator(), context.getSampleSize());
        if (null != colSet) {
            for (Map.Entry<String, List<Object>> next : result.getSample().entrySet()) {
                if (colSet.contains(next.getKey())) {
                    next.setValue(next.getValue().stream().map(charsetConvert::decode).collect(Collectors.toList()));
                }
            }
        }
        return result;
    }

    private RsResult sampleQuery(DarkContext context, TableSamplingParam param) {
        SampleQuery sampleQuery;
        if (null != param.getCatalog()) {
            sampleQuery = new SampleQuery(param.getCatalog(), param.getSchema(), param.getTable(), context.getSampleLine());
        } else {
            sampleQuery = new SampleQuery(param.getSchema(), param.getTable(), context.getSampleLine());
        }
        List<ColumnRequest> columnRequests = param.getColumns().stream().map(column -> new ColumnRequest(column.getColumnName(), column.getColumnType().getName())).collect(Collectors.toList());
        try {
            return cli.openSampleQuery(sampleQuery, columnRequests);
        } catch (Exception e) {
            String msg = "执行采样失败: " + Func.getMessage(e);
            log.warn(msg, e);
            throw new NyxException(msg, e);
        }
    }

    @Override
    public void close() {
        IoUtil.close(cli);
    }
}
