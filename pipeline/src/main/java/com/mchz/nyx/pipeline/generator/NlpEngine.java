package com.mchz.nyx.pipeline.generator;

import cn.hutool.core.util.ReUtil;
import cn.hutool.extra.tokenizer.Result;
import cn.hutool.extra.tokenizer.TokenizerEngine;
import cn.hutool.extra.tokenizer.TokenizerUtil;
import com.mchz.nyx.dark.engine.VectorConvEngine;
import lombok.extern.slf4j.Slf4j;
import org.ehcache.Cache;

import java.util.LinkedList;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/11
 */
@Slf4j
public class NlpEngine implements VectorConvEngine {
    private final TokenizerEngine engine;
    private final Cache<String, float[]> vectorCache;

    public NlpEngine(Cache<String, float[]> cache) {
        this.engine = TokenizerUtil.createEngine();
        this.vectorCache = cache;
    }

    @Override
    public float[] getPreVector(String sequence) {
        if (ReUtil.contains("[a-zA-Z]{3}", sequence)) {
            return null;
        }
        return getVector(sequence);
    }

    @Override
    public float[] getVector(String sequence) {
        Result segments = engine.parse(sequence);
        if (!segments.hasNext()) {
            return null;
        }
        List<float[]> vectorValues = getVectorValues(segments);
        return sumVector(vectorValues);
    }

    private float[] sumVector(List<float[]> vectorValues) {
        if (vectorValues.isEmpty()) {
            return null;
        }
        int num = vectorValues.get(0).length;
        float[] res = new float[num];
        int size = vectorValues.size();
        for (float[] vectorValue : vectorValues) {
            for (int i = 0; i < num; i++) {
                res[i] += vectorValue[i];
            }
        }
        for (int i = 0; i < num; i++) {
            res[i] /= size;
        }
        return res;
    }

    private List<float[]> getVectorValues(Result segments) {
        List<float[]> vectorValues = new LinkedList<>();
        while (segments.hasNext()) {
            String segment = segments.next().getText();
            if (segment.isEmpty()) {
                continue;
            }
            float[] v = vectorCache.get(segment);
            if (null != v) {
                vectorValues.add(v);
                continue;
            }
            for (char c : segment.toCharArray()) {
                float[] v2 = vectorCache.get(String.valueOf(c));
                if (null != v2) {
                    vectorValues.add(v2);
                }
            }
        }
        return vectorValues;
    }


    @Override
    public void close() {
    }
}

