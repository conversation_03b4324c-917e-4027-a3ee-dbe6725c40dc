package com.mchz.nyx.pipeline.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mchz.nyx.common.util.PartitionUtil;
import com.mchz.nyx.dark.common.enums.AddName;
import com.mchz.nyx.dark.model.TargetInfo;
import com.mchz.nyx.dark.model.definition.RuleDetail;
import com.mchz.nyx.dark.model.dto.StdDetailDTO;
import com.mchz.nyx.dark.model.dto.TargetWithTagGroup;
import com.mchz.nyx.dark.model.rule.RuleIdInfo;
import com.mchz.nyx.dark.util.RuleLoadUtil;
import com.mchz.nyx.pipeline.common.constants.PipelineConst;
import com.mchz.nyx.pipeline.common.enums.ArtifactType;
import com.mchz.nyx.pipeline.entity.Artifact;
import com.mchz.nyx.pipeline.entity.TagRuleDetail;
import com.mchz.nyx.pipeline.entity.Term;
import com.mchz.nyx.pipeline.entity.TermTag;
import com.mchz.nyx.pipeline.mapper.ArtifactMapper;
import com.mchz.nyx.pipeline.mapper.TagRuleDetailMapper;
import com.mchz.nyx.pipeline.mapper.TermMapper;
import com.mchz.nyx.pipeline.mapper.TermTagMapper;
import com.mchz.nyx.pipeline.service.InnerRuleService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/10/16
 */
@Slf4j
@DS(PipelineConst.EMBED)
@Service
@AllArgsConstructor
public class InnerRuleServiceImpl implements InnerRuleService {

    private final ArtifactMapper artifactMapper;
    private final TermMapper termMapper;
    private final TermTagMapper termTagMapper;
    private final TagRuleDetailMapper tagRuleDetailMapper;

    @Override
    public Map<Long, TargetInfo> loadTerms(Long stdId) {
        Map<Long, TargetInfo> res = new HashMap<>();
        List<Long> terms = new ArrayList<>();
        artifactMapper.selectList(Wrappers.lambdaQuery(Artifact.class)
                .select(Artifact::getArtifactId, Artifact::getName, Artifact::getArtifactType)
                .in(Artifact::getArtifactType, Arrays.asList(ArtifactType.TERM.getName(), ArtifactType.DATA_CLASS.getName())),
            rc -> {
                Artifact artifact = rc.getResultObject();
                res.put(artifact.getArtifactId(), new TargetInfo(artifact.getArtifactId(), artifact.getName()));
                if (ArtifactType.TERM.getName().equals(artifact.getArtifactType())) {
                    terms.add(artifact.getArtifactId());
                }
            });
        PartitionUtil.part(terms, v -> {
            LambdaQueryWrapper<Term> q = Wrappers.lambdaQuery(Term.class).select(Term::getPinyin).in(Term::getArtifactId, v);
            termMapper.selectList(q, rc -> {
                List<String> sub = StrUtil.split(rc.getResultObject().getPinyin(), StrUtil.C_COMMA);
                if (sub.isEmpty()) {
                    return;
                }
                TargetInfo targetInfo = res.get(rc.getResultObject().getArtifactId());
                if (null != targetInfo) {
                    targetInfo.put(AddName.PINYIN, sub);
                }
            });
        });
        return res;
    }

    @Override
    public StdDetailDTO loadInnerRule(Collection<Long> artifactIds, boolean loadTerm, Double hit) {
        List<TermTag> termTags = new ArrayList<>(artifactIds.size());
        PartitionUtil.part(artifactIds, v -> {
            LambdaQueryWrapper<TermTag> q = Wrappers.lambdaQuery(TermTag.class).in(TermTag::getTermId, v);
            termTagMapper.selectList(q, rc -> termTags.add(rc.getResultObject()));
        });
        Set<Long> tagIds = new HashSet<>(termTags.size());

        Map<Long, Map<Long, Map<Boolean, List<Long>>>> map = termTags.stream().peek(v -> tagIds.add(v.getTagId())).collect(Collectors.groupingBy(TermTag::getTermId,
            Collectors.groupingBy(TermTag::getGroupId, Collectors.partitioningBy(TermTag::getExpect, Collectors.mapping(TermTag::getTagId, Collectors.toList())))));
        List<TargetWithTagGroup> targetWithTagGroups = map.entrySet().stream().flatMap(e -> e.getValue().values().stream().map(v -> {
            List<Long> tags = v.get(Boolean.TRUE);
            if (null == tags) {
                return null;
            }
            TargetWithTagGroup tagGroup = new TargetWithTagGroup();
            tagGroup.setId(e.getKey());
            tagGroup.setTags(tags);
            List<Long> tmp = v.get(Boolean.FALSE);
            if (!tmp.isEmpty()) {
                tagGroup.setNonTags(tmp);
            }
            return tagGroup;
        })).filter(Objects::nonNull).collect(Collectors.toList());

        List<RuleDetail> ruleDetails = loadInnerRuleDetail(tagIds, hit);

        StdDetailDTO res = new StdDetailDTO();
        res.setTagGroups(targetWithTagGroups);
        res.setRuleDetails(ruleDetails);
        return res;
    }

    private List<RuleDetail> loadInnerRuleDetail(Set<Long> ids, Double hit) {
        if (ids.isEmpty()) {
            return Collections.emptyList();
        }
        List<RuleDetail> res = new ArrayList<>(ids.size());
        PartitionUtil.part(ids, v -> tagRuleDetailMapper.selectList(Wrappers.lambdaQuery(TagRuleDetail.class).eq(TagRuleDetail::getEnabled, Boolean.TRUE).in(TagRuleDetail::getId, v),
            rc -> {
                TagRuleDetail tag = rc.getResultObject();
                RuleDetail detail = RuleLoadUtil.loadRule(tag.getVersion(), tag.getDefinition(), hit);
                if (null == detail.getLoad()) {
                    return;
                }
                detail.setInfo(new RuleIdInfo(tag.getId()));
                res.add(detail);
            }));
        return res;
    }
}
