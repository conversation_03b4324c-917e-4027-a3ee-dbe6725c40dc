package com.mchz.nyx.pipeline.job.relation.util;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.NumberUtil;
import com.mchz.nyx.common.enums.DataTypeGroup;
import com.mchz.nyx.dark.model.meta.ColumnType;
import com.mchz.nyx.pipeline.entity.ResultMetaColumn;
import com.mchz.nyx.pipeline.entity.ResultMetaTable;
import com.mchz.nyx.pipeline.model.dto.MetaColumnDTO;
import com.mchz.nyx.pipeline.model.dto.MetaColumnPlusDTO;
import com.mchz.nyx.pipeline.model.dto.MetaTableDTO;
import com.mchz.nyx.pipeline.util.Func;
import lombok.experimental.UtilityClass;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

@UtilityClass
public class MetadataUtil {

    public static MetaColumnDTO buildMetaColumn(ResultMetaColumn column) {
        MetaColumnDTO metaColumn = Func.toBean(column, MetaColumnDTO.class);
        metaColumn.setPrecision(column.getDataPrecision());
        DataTypeGroup group = DataTypeGroup.getType(column.getGroupType());
        ColumnType type = new ColumnType(column.getColumnType(), group);
        metaColumn.setColumnType(type);
        return metaColumn;
    }

    public static boolean supportQueryDataOfColumn(ResultMetaColumn column, Long maxColumnLength) {
        DataTypeGroup type = DataTypeGroup.getType(column.getGroupType());
        Long length = column.getLength();
        if (Objects.equals(type, DataTypeGroup.CHARACTER)//
            && Objects.nonNull(length) && Objects.nonNull(maxColumnLength)//
            && length.compareTo(maxColumnLength) > 0) {
            return false;
        }
        switch (type) {
            case OBJECT:
            case XML:
            case UNKNOWN:
                return false;
        }
        return true;
    }

    public static ResultMetaTable buildResultMetaTable(Long jobId, Long sourceId, MetaTableDTO metaTable) {
        ResultMetaTable table = new ResultMetaTable();
        table.setJobId(jobId);
        table.setConfigId(sourceId);
        table.setSchema(metaTable.getSchema());
        table.setTableName(metaTable.getTableName());
        table.setTableComment(metaTable.getTableComment());
        table.setBlocks(metaTable.getBlocks());
        if (null != metaTable.getRows() && metaTable.getRows() > 0) {
            table.setRows(metaTable.getRows());
        }
        table.setPkUniNum(metaTable.getIdxNum());
        table.setMaxPkColNum(metaTable.getMaxPkColNum());
        table.setFkNum(metaTable.getFkNum());
        table.setIdxNum(metaTable.getIdxNum());
        table.setBerefferd(metaTable.getBerefferd());
        table.setIsView(metaTable.getIsView());
        table.setIsCsny(null);
        table.setIsEmpty((byte) 0);
        table.setRepeatType(ResultMetaTable.RepeatType.NOT);
        return table;
    }

    public static void fillColumnStatistics(ResultMetaTable table, List<MetaColumnPlusDTO> columns) {
        table.setColNum(columns.size());
        final Integer num = table.getColNum();
        int cNum = 0, nNum = 0, tNum = 0, dNum = 0, fdNo = -1;
        for (MetaColumnPlusDTO column : columns) {
            if (null == column.getColumnType()) {
                continue;
            }
            switch (column.getColumnType().getTypeGroup()) {
                case CHARACTER:
                    cNum++;
                    break;
                case TEMPORAL:
                    dNum++;
                    if (fdNo < 0 || column.getPosition() < fdNo) {
                        fdNo = column.getPosition();
                    }
                    break;
                case INTEGER:
                case REAL:
                    nNum++;
                    break;
                case XML:
                case LARGE_OBJECT:
                    tNum++;
                default:
            }
        }

        table.setCharColNum(cNum);
        table.setNumColNum(nNum);
        table.setTextColNum(tNum);
        table.setDateColNum(dNum);
        if (fdNo > 0) {
            table.setFdDateColPos(fdNo);
            table.setDatePstn(BooleanUtil.toInteger(fdNo << 1 > num));
        }

        table.setDateRatio(calculationOf(dNum, num));
        table.setNumRatio(calculationOf(nNum, num));
        table.setCharRatio(calculationOf(cNum, num));
        table.setTextRatio(calculationOf(tNum, num));
    }

    public static ResultMetaColumn buildResultMetaColumn(ResultMetaTable tableDisplay, MetaColumnPlusDTO column) {
        ResultMetaColumn columnDisplay = new ResultMetaColumn();
        columnDisplay.setId(tableDisplay.getId());
        columnDisplay.setJobId(tableDisplay.getJobId());
        columnDisplay.setConfigId(tableDisplay.getConfigId());
        columnDisplay.setSchema(tableDisplay.getSchema());
        columnDisplay.setTableName(tableDisplay.getTableName());
        columnDisplay.setColumnName(column.getColumnName());
        columnDisplay.setColumnComment(column.getColumnComment());
        columnDisplay.setColumnType(column.getColumnType().getName());
        columnDisplay.setNativeType(column.getNativeType());
        columnDisplay.setGroupType(column.getColumnType().getTypeGroup().name());
        columnDisplay.setLength(column.getLength());
        columnDisplay.setScale(column.getScale());
        columnDisplay.setDataPrecision(column.getPrecision());
        columnDisplay.setPosition(column.getPosition());
        columnDisplay.setIsPk(column.getIsPk());
        columnDisplay.setPkName(column.getPkName());
        columnDisplay.setPkNum(column.getPkNum());
        columnDisplay.setPkPos(column.getPkPos());
        columnDisplay.setIsUnique(column.getIsUnique());
        columnDisplay.setUniName(column.getUniName());
        columnDisplay.setUniNum(column.getUniNum());
        columnDisplay.setUniPos(column.getUniPos());
        columnDisplay.setIsIndex(column.getIsIndex());
        columnDisplay.setIdxName(column.getIdxName());
        columnDisplay.setIdxPos(column.getIdxPos());
        columnDisplay.setIsFk(column.getIsFk());
        columnDisplay.setFkName(column.getFkName());
        columnDisplay.setFkPos(column.getFkPos());
        columnDisplay.setFkRefTable(column.getFkRefTable());
        columnDisplay.setFkRefColumn(column.getFkRefColumn());
        columnDisplay.setConstraintType(column.getConstraintType());
        columnDisplay.setNullable(column.getNullable());
        columnDisplay.setIsDate(DataTypeGroup.TEMPORAL.equals(column.getColumnType().getTypeGroup()));
        columnDisplay.setIsUPk(column.isUpk());
        columnDisplay.setIsCPk(column.isCpk());
        columnDisplay.setIsView(tableDisplay.getIsView());
        columnDisplay.setIsEmpty((byte) 0);
        columnDisplay.setRepeatType(ResultMetaColumn.RepeatType.NOT);
        columnDisplay.setExpandContent(column.getExpandContent());
        return columnDisplay;
    }

    private static BigDecimal calculationOf(Integer m, Integer d) {
        return Objects.equals(d, 0) ? BigDecimal.ZERO : NumberUtil.div(m, d, 4);
    }
}
