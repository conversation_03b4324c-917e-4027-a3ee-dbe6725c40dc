package com.mchz.nyx.pipeline.generator.sample;

import com.mchz.nyx.dark.model.meta.SampleResult;
import com.mchz.nyx.pipeline.generator.DarkContext;
import com.mchz.nyx.pipeline.model.dto.ColumnInfo;
import com.mchz.nyx.pipeline.model.param.TableSamplingParam;
import com.mchz.nyx.pipeline.service.MetaExtendService;
import lombok.AllArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/9
 */
@AllArgsConstructor
public class SampleResultHandler implements SampleHandler {
    private final MetaExtendService metaExtendService;

    @Override
    public SampleResult sampling(DarkContext context, TableSamplingParam param) {
        if (param.getColumns().isEmpty()) {
            return SampleResult.empty();
        }
        Map<Long, String> map = param.getColumns().stream().collect(Collectors.toMap(ColumnInfo::getCid, ColumnInfo::getColumnName));
        Map<String, List<Object>> sample = metaExtendService.getSample(map);
        return SampleResult.of(sample);
    }
}
