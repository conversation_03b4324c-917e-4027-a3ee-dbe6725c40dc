package com.mchz.nyx.pipeline.generator.file;

import cn.hutool.core.io.FileUtil;
import com.mchz.nyx.pipeline.service.FileSyncService;
import lombok.AllArgsConstructor;

import java.io.File;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/29
 */
@AllArgsConstructor
public class ClusterFileHandler implements FileHandler {
    private final FileSyncService fileSyncService;

    @Override
    public void loadFile(String source, File target) {
        File file = new File(source);
        if (target.exists()) {
            return;
        }
        if (file.exists()) {
            FileUtil.move(file, target, true);
        } else {
            fileSyncService.syncFile(source, target);
        }
    }

    @Override
    public void close() {
    }
}
