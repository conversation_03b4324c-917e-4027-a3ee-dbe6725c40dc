package com.mchz.nyx.pipeline.generator.sample;

import com.mchz.nyx.dark.model.meta.SampleResult;
import com.mchz.nyx.meta.support.DataSourceConfig;
import com.mchz.nyx.pipeline.entity.FileUploadDetail;
import com.mchz.nyx.pipeline.generator.DarkContext;
import com.mchz.nyx.pipeline.generator.meta.FileMetaAdapter;
import com.mchz.nyx.pipeline.model.param.TableSamplingParam;
import com.mchz.nyx.pipeline.util.file.reader.DatabaseFileReader;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/3
 */
@Slf4j
@RequiredArgsConstructor
public class SampleFileHandler implements SampleHandler {

    private final DataSourceConfig fileSource;
    private final FileMetaAdapter fileMetaAdapter;

    private List<FileUploadDetail> tmp;

    @Override
    public SampleResult sampling(DarkContext context, TableSamplingParam param) {
        try (DatabaseFileReader fileReader = fileMetaAdapter.getReader(fileSource, param, getFileList())) {
            List<List<String>> lists = fileReader.readData(context.getSampleLine());
            return SampleResult.ofLine(fileReader.readHead(), lists, context.getSampleSize());
        } catch (IOException e) {
            log.warn("文件关闭异常,{}", param, e);
            return SampleResult.empty();
        }
    }

    private List<FileUploadDetail> getFileList() {
        if (null == tmp) {
            tmp = fileMetaAdapter.listFileDetails(fileSource);
        }
        return tmp;
    }
}
