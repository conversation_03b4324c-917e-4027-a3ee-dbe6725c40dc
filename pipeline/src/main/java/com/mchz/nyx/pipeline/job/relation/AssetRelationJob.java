package com.mchz.nyx.pipeline.job.relation;

import com.mchz.nyx.pipeline.common.enums.JobType;
import com.mchz.nyx.pipeline.job.Job;
import com.mchz.nyx.pipeline.job.JobStartConfig;
import com.mchz.nyx.pipeline.job.relation.stage.AssetRelationJobAnalysisStage;
import com.mchz.nyx.pipeline.job.relation.stage.AssetRelationJobQueryMetaStage;
import com.mchz.nyx.pipeline.model.other.MetaTableExpand;
import com.mchz.nyx.pipeline.job.JobLogManager;
import com.mchz.nyx.pipeline.util.cache.ListCache;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class AssetRelationJob implements Job {
    private final AssetRelationJobQueryMetaStage assetRelationJobQueryMetaStage;
    private final AssetRelationJobAnalysisStage assetRelationJobAnalysisStage;

    @Override
    public JobType getJobType() {
        return JobType.ASSET_RELATION;
    }

    @Override
    public int[] getStageProportion(JobStartConfig jobConfig) {
        return new int[]{0, 50, 50};
    }

    @Override
    public void execute(JobLogManager jobLog, JobStartConfig jobConfig) {
        jobLog.setStep(1);
        try (ListCache<MetaTableExpand> metaTableExpandCache = assetRelationJobQueryMetaStage.execute(jobLog,
            jobConfig)) {
            jobLog.setStep(2);
            assetRelationJobAnalysisStage.executeTask(jobLog, jobConfig, metaTableExpandCache);
        }

    }
}
