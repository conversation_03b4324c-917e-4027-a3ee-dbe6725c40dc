package com.mchz.nyx.pipeline.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mchz.nyx.pipeline.common.constants.PipelineConst;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-29
 */
@Data
@TableName(value = "term", schema = PipelineConst.EMBED_SCHEMA)
public class Term implements Serializable {
    /**
     * 主键(引用artifacts)
     */
    @TableId(value = "artifact_id", type = IdType.INPUT)
    private Long artifactId;

    /**
     * 别名(多个以逗号分隔)
     */
    private String alias;
    /**
     * 拼音(多个以逗号分隔)
     */
    private String pinyin;
}
