package com.mchz.nyx.pipeline.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-06-27
 */
@Data
@TableName("analysis_table")
public class AnalysisTable implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId
    private Long id;

    /**
     * 配置id
     */
    private Long planId;

    /**
     * 执行作业id
     */
    private Long jobId;

    /**
     * 数据源id
     */
    private Long sourceId;

    /**
     * 元数据id
     */
    private Long schemaId;

    /**
     * 元数据id
     */
    private Long tableId;

    /**
     * schema
     */
    private String schemaName;

    /**
     * 表名
     */
    private String tableName;

    /**
     * 修订版本
     */
    private Integer version;

    private Integer repeatType;

    private Long repeatTableId;

    /**
     * 重复率
     */
    private BigDecimal repeatRate;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 分类
     */
    private String classifyId;

    /**
     * 敏感等级
     */
    private Integer level;

    /**
     * 备用
     */
    private String remark;
}
