package com.mchz.nyx.pipeline.generator.sample;

import com.mchz.nyx.common.enums.DataTypeGroup;
import com.mchz.nyx.dark.model.meta.SampleResult;
import com.mchz.nyx.pipeline.generator.DarkContext;
import com.mchz.nyx.pipeline.model.dto.CatalogSchemaDTO;
import com.mchz.nyx.pipeline.model.dto.ColumnInfo;
import com.mchz.nyx.pipeline.model.param.TableSamplingParam;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/3
 */
public interface SampleHandler extends AutoCloseable {
    SampleHandler EMPTY_HANDLER = (context, param) -> null;

    SampleResult sampling(DarkContext context, TableSamplingParam param);

    @Override
    default void close() {
    }

    static TableSamplingParam buildSamplingParam(CatalogSchemaDTO catalogSchema, String tableName, List<? extends ColumnInfo> columns, long lenLimit) {
        List<ColumnInfo> queryColumns = new ArrayList<>(columns.size());
        for (ColumnInfo column : columns) {
            if (!DataTypeGroup.isSimple(column.getColumnType().getTypeGroup())) {
                continue;
            }
            Long length = column.getLength();
            if (null != length && length > lenLimit) {
                continue;
            }
            queryColumns.add(column);
        }
        TableSamplingParam param = new TableSamplingParam();
        if (null != catalogSchema.getCatalog() || null != catalogSchema.getSchema()) {
            param.setCatalog(catalogSchema.getCatalog());
            param.setSchema(catalogSchema.getSchema());
        } else {
            param.setSchema(catalogSchema.getCatalogSchema());
        }
        param.setTable(tableName);
        param.setColumns(queryColumns);
        return param;
    }

}
