package com.mchz.nyx.pipeline.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 业务术语表
 */
@Data
@TableName("c_sys_biz_data")
@Accessors(chain = true)
public class CSysBizData {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 行业模板ID
     */
    private Integer industryId;

    /**
     * 名称
     */
    private String name;

    /**
     * 业务类型类别: 1->单一类型; 2->长文本
     */
    private Integer bizType;

    /**
     * 描述
     */
    private String description;

    /**
     * 状态：0=停用；1=正常
     */
    private Boolean status;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新者
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 创建类型：1=常规；2=规则；3=元数据注释；4=NLP；5=LLM
     */
    private Byte createType;

    /**
     * 数据来源：1=内置；2=自定义
     */
    private Byte dataSource;

    /**
     * 内置主键
     */
    private String sId;

    /**
     * 是否删除：0=否；1=是
     */
    @TableLogic
    private Boolean isDelete;
}
