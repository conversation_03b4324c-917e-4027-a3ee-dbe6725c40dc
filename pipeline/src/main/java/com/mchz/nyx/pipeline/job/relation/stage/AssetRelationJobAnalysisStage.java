package com.mchz.nyx.pipeline.job.relation.stage;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mchz.datasource.cli.RsResult;
import com.mchz.mcdatasource.api.model.CustomColumnTypeImpl;
import com.mchz.mcdatasource.core.DataBaseType;
import com.mchz.nyx.common.util.PartitionUtil;
import com.mchz.nyx.pipeline.common.api.Status;
import com.mchz.nyx.pipeline.config.props.SourceProperties;
import com.mchz.nyx.pipeline.entity.AssetRelationColumnAnalysisDTO;
import com.mchz.nyx.pipeline.entity.OverviewDO;
import com.mchz.nyx.pipeline.entity.ResultMetaColumn;
import com.mchz.nyx.pipeline.entity.ResultMetaTable;
import com.mchz.nyx.pipeline.exception.ServiceException;
import com.mchz.nyx.pipeline.job.JobStartConfig;
import com.mchz.nyx.pipeline.mapper.AssetRelationColumnAnalysisMapper;
import com.mchz.nyx.pipeline.mapper.OverviewMapper;
import com.mchz.nyx.pipeline.mapper.ResultMetaColumnMapper;
import com.mchz.nyx.pipeline.mapper.ResultMetaTableMapper;
import com.mchz.nyx.pipeline.model.api.DataSourceLimitParam;
import com.mchz.nyx.pipeline.model.api.TableDataSetParam;
import com.mchz.nyx.pipeline.model.other.MetaTableExpand;
import com.mchz.nyx.pipeline.model.vo.ResultSetVO;
import com.mchz.nyx.pipeline.service.DatabaseFactory;
import com.mchz.nyx.pipeline.job.JobLogManager;
import com.mchz.nyx.pipeline.util.CollectionSearcher;
import com.mchz.nyx.pipeline.util.Lists;
import com.mchz.nyx.pipeline.util.Func;
import com.mchz.nyx.pipeline.util.cache.ListCache;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;


@Slf4j
@Component
@RequiredArgsConstructor
public class AssetRelationJobAnalysisStage {
    private final SourceProperties properties;
    private final DatabaseFactory databaseFactory;

    private final ResultMetaTableMapper metaTableMapper;
    private final ResultMetaColumnMapper metaColumnMapper;
    private final AssetRelationColumnAnalysisMapper assetRelationColumnAnalysisMapper;
    private final OverviewMapper overviewMapper;

    @SneakyThrows
    public void executeTask(JobLogManager jobLog, JobStartConfig param,
        ListCache<MetaTableExpand> metaTableExpandCache) {
        try {
            Map<String, List<MetaTableExpand>> metaTableExpandMap = getMetaTableExpandMap(metaTableExpandCache);
            jobLog.processed(metaTableExpandMap.size());

            List<AssetRelationColumnAnalysisDTO> columnAnalysisDTOS = new ArrayList<>();

            for (Map.Entry<String, List<MetaTableExpand>> entry : metaTableExpandMap.entrySet()) {
                Func.isInterrupted();
                // 资产关系仅在schema范围内
                columnAnalysisDTOS.addAll(analysis(param, jobLog, entry.getKey(), entry.getValue()));
            }
            Func.isInterrupted();
            jobLog.info("保存资产关系");
            PartitionUtil.part(columnAnalysisDTOS, assetRelationColumnAnalysisMapper::insertList);
        } finally {
            databaseFactory.closeConnection(param.getSource().getId());
        }
    }

    private List<AssetRelationColumnAnalysisDTO> analysis(JobStartConfig param, JobLogManager jobLog,
                                                          String schema, List<MetaTableExpand> tableExpands) {
        jobLog.info(String.format("分析资产关系 %s", schema));

        // 获取元数据
        Map<String, Map<String, Set<String>>> schemaMap = getSchemaMap(tableExpands);
        Long jobId = param.getJobId();
        CollectionSearcher<String, ResultMetaTable> metaTableSearcher = getMetaTableSearcher(jobId, schemaMap);
        CollectionSearcher<Column, ResultMetaColumn> metaColumnSearcher = getMetaColumnSearcher(jobId, schemaMap);

        // 已经存在关联关系的字段
        List<Set<Column>> linkedColumns = new ArrayList<>();
        List<ColumnRelation> columnRelations = new ArrayList<>();

        // 分析主外键
        columnRelations.addAll(getColumnRelationsByForeignKey(tableExpands, linkedColumns));

        List<Index> indices = getIndices(tableExpands, metaColumnSearcher);
        if (CollectionUtils.isNotEmpty(indices)) {
            // 分析字段名
            columnRelations.addAll(getColumnRelationsByColumnName(param, metaColumnSearcher, linkedColumns, indices));
            // 分析业务类型
            columnRelations.addAll(getColumnRelationsByBizName(param, metaColumnSearcher, linkedColumns, indices));
        }

        return ofAssetRelationColumnAnalysisDTO(jobId, metaTableSearcher, metaColumnSearcher, columnRelations);
    }

    private List<AssetRelationColumnAnalysisDTO> ofAssetRelationColumnAnalysisDTO(Long jobId,
        CollectionSearcher<String, ResultMetaTable> metaTableSearcher,
        CollectionSearcher<Column, ResultMetaColumn> metaColumnSearcher, List<ColumnRelation> columnRelations) {
        List<AssetRelationColumnAnalysisDTO> dtos = new ArrayList<>();
        for (ColumnRelation columnRelation : columnRelations) {
            String tableName1 = columnRelation.getTableName1();
            String columnName1 = columnRelation.getColumnName1();
            ResultMetaTable metaTable1 = metaTableSearcher.get(tableName1);
            if (Objects.isNull(metaTable1)) {
                log.warn(String.format("表元数据未找到 %s", tableName1));
                continue;
            }
            ResultMetaColumn metaColumn1 = metaColumnSearcher.get(new Column(tableName1, columnName1));
            if (Objects.isNull(metaColumn1)) {
                log.warn(String.format("列元数据未找到 %s.%s", tableName1, columnName1));
                continue;
            }

            String tableName2 = columnRelation.getTableName2();
            String columnName2 = columnRelation.getColumnName2();
            ResultMetaTable metaTable2 = metaTableSearcher.get(tableName2);
            if (Objects.isNull(metaTable2)) {
                log.warn(String.format("表元数据未找到 %s", tableName2));
                continue;
            }
            ResultMetaColumn metaColumn2 = metaColumnSearcher.get(new Column(tableName2, columnName2));
            if (Objects.isNull(metaColumn2)) {
                log.warn(String.format("列元数据未找到 %s.%s", tableName2, metaColumn2));
                continue;
            }

            AssetRelationColumnAnalysisDTO dto = new AssetRelationColumnAnalysisDTO();
            dto.setJobId(jobId);
            dto.setMetaTableId(metaTable1.getId());
            dto.setMetaColumnId(metaColumn1.getId());
            dto.setRelationMetaTableId(metaTable2.getId());
            dto.setRelationMetaColumnId(metaColumn2.getId());
            dto.setIdentifyMode(columnRelation.getIdentifyMode());
            dtos.add(dto);
        }
        return dtos;
    }

    private CollectionSearcher<String, ResultMetaTable> getMetaTableSearcher(Long jobId,
        Map<String, Map<String, Set<String>>> schemaMap) {
        List<ResultMetaTable> metaTables = getMetaTables(jobId, schemaMap);
        return CollectionSearcher.newInstance(metaTables, ResultMetaTable::getTableName);
    }

    private CollectionSearcher<Column, ResultMetaColumn> getMetaColumnSearcher(Long jobId,
        Map<String, Map<String, Set<String>>> schemaMap) {
        List<ResultMetaColumn> metaColumns = getMetaColumns(jobId, schemaMap);
        return CollectionSearcher.newInstance(metaColumns,
            metaColumn -> new Column(metaColumn.getTableName(), metaColumn.getColumnName()));
    }

    private List<ResultMetaTable> getMetaTables(Long jobId, Map<String, Map<String, Set<String>>> schemaMap) {
        List<ResultMetaTable> metaTables = new ArrayList<>();
        schemaMap.forEach((schemaName, tableMap) -> {
            LambdaQueryWrapper<ResultMetaTable> q = Wrappers.lambdaQuery(ResultMetaTable.class)
                .eq(ResultMetaTable::getJobId, jobId)
                .eq(ResultMetaTable::getSchema, schemaName)
                .in(ResultMetaTable::getTableName, tableMap.keySet());
            metaTables.addAll(metaTableMapper.selectList(q));
        });
        return metaTables;
    }

    private List<ResultMetaColumn> getMetaColumns(Long jobId, Map<String, Map<String, Set<String>>> schemaMap) {
        List<ResultMetaColumn> metaColumns = new ArrayList<>();
        schemaMap.forEach((schemaName, tableMap) -> {
            tableMap.forEach((tableName, columnNames) -> {
                LambdaQueryWrapper<ResultMetaColumn> q = Wrappers.lambdaQuery(ResultMetaColumn.class)
                    .eq(ResultMetaColumn::getJobId, jobId)
                    .eq(ResultMetaColumn::getSchema, schemaName)
                    .eq(ResultMetaColumn::getTableName, tableName)
                    .in(ResultMetaColumn::getColumnName,columnNames);
                metaColumns.addAll(metaColumnMapper.selectList(q));
            });
        });
        return metaColumns;
    }

    /**
     * {@code Map<SchemaName, Map<TableName, Set<ColumnName>>>}
     */
    private Map<String, Map<String, Set<String>>> getSchemaMap(List<MetaTableExpand> tableExpands) {
        Map<String, Map<String, Set<String>>> schemaMap = new HashMap<>();
        for (MetaTableExpand tableExpand : tableExpands) {
            String schemaName = tableExpand.getSchemaName();
            String tableName = tableExpand.getTableName();

            for (MetaTableExpand.MetaIndex index : tableExpand.getIndices()) {
                index.getColumnNames().forEach(columnName -> setSchema(schemaMap, schemaName, tableName, columnName));
            }

            for (MetaTableExpand.MetaForeignKey foreignKey : tableExpand.getForeignKeys()) {
                foreignKey.getColumns().forEach(metaForeignColumn -> {
                    setSchema(schemaMap, schemaName, metaForeignColumn.getForeignTableName(),
                        metaForeignColumn.getForeignColumnName());
                    setSchema(schemaMap, schemaName, metaForeignColumn.getPrimaryTableName(),
                        metaForeignColumn.getPrimaryColumnName());
                });
            }
        }
        return schemaMap;
    }

    private void setSchema(Map<String, Map<String, Set<String>>> schemaMap, String schemaName, String tableName,
        String columnName) {
        Map<String, Set<String>> tableMap = schemaMap.get(schemaName);
        if (Objects.isNull(tableMap)) {
            tableMap = new HashMap<>();
            schemaMap.put(schemaName, tableMap);
        }

        Set<String> columnNames = tableMap.get(tableName);
        if (Objects.isNull(columnNames)) {
            columnNames = new HashSet<>();
            tableMap.put(tableName, columnNames);
        }

        columnNames.add(columnName);
    }

    private List<ColumnRelation> getColumnRelationsByColumnName(JobStartConfig param,
                                                                CollectionSearcher<Column, ResultMetaColumn> metaColumnSearcher, List<Set<Column>> linkedColumns,
                                                                List<Index> indices) {
        List<ColumnRelation> columnRelations = new ArrayList<>();

        Map<String, List<Index>> columnNameMap = indices.stream().collect(Collectors.groupingBy(Index::getColumnName));
        for (Map.Entry<String, List<Index>> entry : columnNameMap.entrySet()) {
            List<Index> value = entry.getValue();
            if (value.size() <= 1) {
                // 没有相同列名
                continue;
            }
            for (Index indexI : value) {
                for (Index indexJ : value) {
                    if (indexI == indexJ || isLinked(linkedColumns, indexI, indexJ)) {
                        continue;
                    }
                    if (checkLink(param, metaColumnSearcher, indexI, indexJ)) {
                        ColumnRelation relation = new ColumnRelation();
                        relation.setTableName1(indexI.getTableName());
                        relation.setColumnName1(indexI.getColumnName());
                        relation.setTableName2(indexJ.getTableName());
                        relation.setColumnName2(indexJ.getColumnName());
                        relation.setIdentifyMode(AssetRelationColumnAnalysisDTO.IdentifyMode.COLUMN_NAME);
                        columnRelations.add(relation);
                        setLinkedColumns(linkedColumns, relation);
                    }
                }
            }
        }
        return columnRelations;
    }

    private boolean checkLink(JobStartConfig param,
                              CollectionSearcher<Column, ResultMetaColumn> metaColumnSearcher, Index indexI, Index indexJ) {
        try {
            Set<String> columnDataI = getColumnData(param, metaColumnSearcher, indexI);
            if (CollectionUtils.isEmpty(columnDataI)) {
                return false;
            }
            Set<String> columnDataJ = getAssociatedColumnData(param, indexJ, columnDataI);

            // 比较
            boolean equals = Objects.equals(columnDataI.size(), columnDataJ.size());
            if (log.isDebugEnabled() && !equals) {
                columnDataI.removeAll(columnDataJ);
                log.debug(
                    String.format("字段[%s]的[%s]在字段[%s]中不存在", JSONUtil.toJsonStr(indexI), JSONUtil.toJsonStr(columnDataI),
                        JSONUtil.toJsonStr(indexJ)));
            }
            return equals;
        } catch (Exception e) {
            log.error("查询样本数据失败", e);
            return false;
        }
    }

    private Set<String> getAssociatedColumnData(JobStartConfig param, Index indexJ, Set<String> columnDataI)
        throws Exception {
        DataSourceLimitParam source = param.getSource();
        DataBaseType dataBaseType = source.getDataBaseType();
        InSqlGenerate inSqlGenerate = InSqlGenerate.of(dataBaseType);
        if (Objects.isNull(inSqlGenerate)) {
            throw new ServiceException(Status.UN_SUPPORTED_OPERATION, dataBaseType);
        }
        String sql = inSqlGenerate.buildInSql(indexJ.getSchemaName(), indexJ.getTableName(), indexJ.getColumnName(),
            Lists.newArrayList(columnDataI));
        RsResult rsResult = databaseFactory.openQuery(source, sql);
        Set<String> columnDataJ = new HashSet<>();
        rsResult.getIterator().forEachRemaining(objects -> {
            Object o = objects[0];
            if (Objects.nonNull(o)) {
                columnDataJ.add(o.toString());
            }
        });
        return columnDataJ;
    }

    private Set<String> getColumnData(JobStartConfig param,
                                      CollectionSearcher<Column, ResultMetaColumn> metaColumnSearcher, Index index) throws Exception {
        TableDataSetParam tableParam = new TableDataSetParam();
        tableParam.setJobId(param.getJobId());
        tableParam.setSource(param.getSource());
        tableParam.setCatalogSchema(index.getSchemaName());
        tableParam.setTable(index.getTableName());
        ResultMetaColumn metaColumn = metaColumnSearcher.getNonNull(
            new Column(index.getTableName(), index.getColumnName()));
        tableParam.setColumns(Lists.newArrayList(metaColumn));
        // 固定1000
        tableParam.setSize(1000);
        // 固定100%
        tableParam.setSampleRate(100);

        ResultSetVO resultSetVO = databaseFactory.getTableDataSet(tableParam);

        // 行专列
        return resultSetVO.getData().stream()//
            .map(objects -> objects.get(0))// 仅有一列
            .filter(Objects::nonNull)//
            .map(String::valueOf)//
            .filter(StrUtil::isNotBlank)// 数据库对空格支持不一致，所以忽略掉
            .collect(Collectors.toSet());
    }

    private boolean isLinked(List<Set<Column>> linkedColumns, Index indexI, Index indexJ) {
        for (Set<Column> linkedColumn : linkedColumns) {
            if (linkedColumn.contains(new Column(indexI.getTableName(), indexI.getColumnName()))//
                && linkedColumn.contains(new Column(indexJ.getTableName(), indexJ.getColumnName()))) {
                return true;
            }
        }

        return false;
    }

    private List<ColumnRelation> getColumnRelationsByBizName(JobStartConfig param,
                                                             CollectionSearcher<Column, ResultMetaColumn> metaColumnSearcher, List<Set<Column>> linkedColumns,
                                                             List<Index> indices) {
        List<ColumnRelation> columnRelations = new ArrayList<>();

        List<OverviewDO> overviewDTOS = queryOverviewDTO(param.getSource().getId(), indices);
        CollectionSearcher<Column, OverviewDO> overviewSearcher = CollectionSearcher.newInstance(overviewDTOS,
            overviewDTO -> new Column(overviewDTO.getRmcdTableName(), overviewDTO.getRmcdColumnName()));
        Map<Long, List<Index>> bizIdMap = indices.stream()//
            .collect(Collectors.groupingBy(index -> {
                OverviewDO dto = overviewSearcher.get(new Column(index.getTableName(), index.getColumnName()));
                return Objects.nonNull(dto) && Objects.nonNull(dto.getAsBizId())//
                    ? dto.getAsBizId() : -1L;
            }));

        for (Map.Entry<Long, List<Index>> entry : bizIdMap.entrySet()) {
            List<Index> value = entry.getValue();
            if (entry.getKey() < 0L || value.size() <= 1) {
                // 字段没有业务类型 || 没有相同业务类型
                continue;
            }
            for (Index indexI : value) {
                for (Index indexJ : value) {
                    if (indexI == indexJ || isLinked(linkedColumns, indexI, indexJ)) {
                        continue;
                    }
                    if (checkLink(param, metaColumnSearcher, indexI, indexJ)) {
                        ColumnRelation relation = new ColumnRelation();
                        relation.setTableName1(indexI.getTableName());
                        relation.setColumnName1(indexI.getColumnName());
                        relation.setTableName2(indexJ.getTableName());
                        relation.setColumnName2(indexJ.getColumnName());
                        relation.setIdentifyMode(AssetRelationColumnAnalysisDTO.IdentifyMode.BIZ_TYPE);
                        columnRelations.add(relation);
                        setLinkedColumns(linkedColumns, relation);
                    }
                }
            }
        }
        return columnRelations;
    }

    private List<OverviewDO> queryOverviewDTO(Long sourceId, List<Index> indices) {
        return indices.stream().flatMap(index -> {
            LambdaQueryWrapper<OverviewDO> q = Wrappers.lambdaQuery(OverviewDO.class).eq(OverviewDO::getScdId, sourceId)
                .eq(OverviewDO::getRmcdSchema, index.getSchemaName())
                .eq(OverviewDO::getRmcdTableName, index.getTableName())
                .eq(OverviewDO::getRmcdColumnName, index.getColumnName());
            List<OverviewDO> dtos = overviewMapper.selectList(q);
            if (dtos.size() > 1) {
                log.warn(String.format("schema[%s] table[%s] column[%s] 存在重复数据忽略 [%s]", index.getSchemaName(),
                    index.getTableName(), index.getColumnName(), JSONUtil.toJsonStr(dtos)));
                return Stream.empty();
            }
            return dtos.stream();
        }).collect(Collectors.toList());
    }

    private List<ColumnRelation> getColumnRelationsByForeignKey(List<MetaTableExpand> tableExpands,
        List<Set<Column>> linkedColumns) {
        List<ForeignKey> foreignKeys = getForeignKey(tableExpands);
        if (CollectionUtils.isEmpty(foreignKeys)) {
            return new ArrayList<>();
        }

        return foreignKeys.stream().flatMap(foreignKey ->//
            foreignKey.getColumns().stream().map(foreignColumn -> {
                ColumnRelation relation = new ColumnRelation();
                relation.setTableName1(foreignColumn.getPrimaryTableName());
                relation.setColumnName1(foreignColumn.getPrimaryColumnName());
                relation.setTableName2(foreignColumn.getForeignTableName());
                relation.setColumnName2(foreignColumn.getForeignColumnName());
                relation.setIdentifyMode(AssetRelationColumnAnalysisDTO.IdentifyMode.FOREIGN_KEY);
                setLinkedColumns(linkedColumns, relation);
                return relation;
            })).collect(Collectors.toList());
    }

    private void setLinkedColumns(List<Set<Column>> linkedColumns, ColumnRelation columnRelation) {
        linkedColumns.add(Func.newHashSet(new Column(columnRelation.tableName1, columnRelation.columnName1),
            new Column(columnRelation.tableName2, columnRelation.columnName2)));
    }

    private List<ForeignKey> getForeignKey(List<MetaTableExpand> tableExpands) {
        return tableExpands.stream().flatMap(tableExpand -> //
            tableExpand.getForeignKeys().stream().map(metaForeignKey -> {//
                ForeignKey foreignKey = new ForeignKey();
                foreignKey.setColumns(metaForeignKey.getColumns().stream()//
                    .map(metaForeignColumn -> {//
                        ForeignColumn foreignColumn = new ForeignColumn();
                        foreignColumn.setPrimaryTableName(metaForeignColumn.getPrimaryTableName());
                        foreignColumn.setPrimaryColumnName(metaForeignColumn.getPrimaryColumnName());
                        foreignColumn.setForeignTableName(metaForeignColumn.getForeignTableName());
                        foreignColumn.setForeignColumnName(metaForeignColumn.getForeignColumnName());
                        return foreignColumn;
                    }).collect(Collectors.toList())//
                );
                return foreignKey;
            }).filter(foreignKey -> CollectionUtils.isNotEmpty(foreignKey.getColumns()))//
        ).collect(Collectors.toList());
    }

    private List<Index> getIndices(List<MetaTableExpand> tableExpands,
        CollectionSearcher<Column, ResultMetaColumn> metaColumnSearcher) {
        return tableExpands.stream().flatMap(tableExpand ->//
                tableExpand.getIndices().stream().map(metaIndex -> {//
                    Index index = new Index();
                    index.setSchemaName(tableExpand.getSchemaName());
                    index.setTableName(tableExpand.getTableName());
                    // 联合索引仅简单处理第一个字段
                    index.setColumnName(metaIndex.getColumnNames().get(0));
                    return index;
                })//
            )//
            .filter(index -> {
                ResultMetaColumn metaColumn = metaColumnSearcher.getNonNull(
                    new Column(index.getTableName(), index.getColumnName()));
                Long length = metaColumn.getLength();
                Long maxColumnLength = properties.getSys().getMaxColumnLength();
                return CustomColumnTypeImpl.valueOf(metaColumn.getColumnType()).isLiteral() // 字符串类型
                    && (//
                    Objects.nonNull(length) && Objects.nonNull(maxColumnLength)//
                        && length.compareTo(maxColumnLength) < 0//
                ); // 非大字段
            })//
            .distinct().collect(Collectors.toList());
    }

    private Map<String, List<MetaTableExpand>> getMetaTableExpandMap(ListCache<MetaTableExpand> metaTableExpandCache) {
        List<MetaTableExpand> tableExpands = new ArrayList<>(metaTableExpandCache.size());
        metaTableExpandCache.consume(tableExpands::addAll, 1000);
        return tableExpands.stream().collect(Collectors.groupingBy(MetaTableExpand::getSchemaName));
    }

    @RequiredArgsConstructor
    enum InSqlGenerate {
        MYSQL(DataBaseType.MYSQL, "`", "'"),
        RDS_MYSQL(DataBaseType.RDS_MYSQL, "`", "'"),
        ORACLE(DataBaseType.ORACLE, "\"", "'"),
        MSSQL(DataBaseType.MSSQL, "\"", "'"),
        DB2(DataBaseType.DB2, "\"", "'");

        private static final String IN_FORMAR = "SELECT DISTINCT %s FROM %s WHERE %s IN ( %s )";

        /** 数据库类型 */
        private final DataBaseType dataBaseType;
        /** 关键字转义符号 */
        private final String keywordsEscapeCharacter;
        /** 字符串引用符号 */
        private final String stringQuoteCharacter;

        public String buildInSql(String schemaName, String tableName, String columnName, List<String> columnData) {
            String columnNameWrap = Func.wrap(columnName, this.keywordsEscapeCharacter);
            String[] catalogAndSchema = Func.splitCatalogAndSchema(this.dataBaseType, schemaName);
            String fromWrap = Func.wrap(catalogAndSchema[1], this.keywordsEscapeCharacter) + "." +
                Func.wrap(tableName, this.keywordsEscapeCharacter);
            String catalog = catalogAndSchema[0];
            if (Objects.nonNull(catalog)) {
                fromWrap = Func.wrap(catalog, this.keywordsEscapeCharacter) + "." + fromWrap;
            }
            StringBuilder columnDataWrap = new StringBuilder();
            for (int i = 0; i < columnData.size(); i++) {
                columnDataWrap.append(Func.wrap(columnData.get(i), this.stringQuoteCharacter, "\\"));
                if (i < columnData.size() - 1) {
                    columnDataWrap.append(", ");
                }
            }

            return String.format(IN_FORMAR, columnNameWrap, fromWrap, columnNameWrap, columnDataWrap);
        }

        public static InSqlGenerate of(DataBaseType dataBaseType) {
            for (InSqlGenerate inSqlGenerate : values()) {
                if (Objects.equals(inSqlGenerate.dataBaseType, dataBaseType)) {
                    return inSqlGenerate;
                }
            }
            return null;
        }
    }

    @Data
    @RequiredArgsConstructor
    @EqualsAndHashCode
    private static class Column {
        private final String tableName;
        private final String columnName;
    }

    @Data
    private static class ColumnRelation {
        private String tableName1;
        private String columnName1;
        private String tableName2;
        private String columnName2;
        private int identifyMode;
    }

    @Data
    @EqualsAndHashCode
    private static class Index {
        private String schemaName;
        private String tableName;
        private String columnName;
    }

    @Data
    @EqualsAndHashCode
    private static class ForeignKey {
        private List<ForeignColumn> columns = new ArrayList<>();
    }

    @Data
    @EqualsAndHashCode
    private static class ForeignColumn {
        private String primaryTableName;
        private String primaryColumnName;
        private String foreignTableName;
        private String foreignColumnName;
    }
}
