package com.mchz.nyx.pipeline.util.file.reader;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

import java.io.File;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.util.*;
import java.util.stream.Collectors;

public class JsonDatabaseFileReader extends AbstractDatabaseFileReader {
    private final Charset charset;
    private final String dataNode;
    private final File file;
    private Map<String, List<String>> map = new HashMap<>();

    public JsonDatabaseFileReader(InputStream inputStream, File file, boolean hasHead, Charset charset, String dataNode) {
        super(inputStream, hasHead);
        this.file = file;
        this.charset = charset;
        this.dataNode = dataNode;
        init();
    }

    private void init() {
        List<String> lists = StrUtil.split(dataNode, StrUtil.DOT);
        JSONObject jsonObject = JSONUtil.readJSONObject(file, charset);
        for (int i = 0; i < lists.size(); i++) {
            if (i == lists.size() - 1) {
                JSONArray jsonArray = jsonObject.getJSONArray(lists.get(i));
                for (Object o : jsonArray) {
                    JSONObject jsonObj = (JSONObject) o;
                    jsonObj.forEach((key, value) -> map.computeIfAbsent(key, k -> new ArrayList<>()).add(value.toString()));
                }
                break;
            }
            jsonObject = jsonObject.getJSONObject(lists.get(i));
        }
    }

    @Override
    public List<String> readHead() {
        return new ArrayList<>(map.keySet());
    }

    @Override
    public List<List<String>> readData(int num) {
        List<List<String>> res = new ArrayList<>();
        List<Integer> collect = map.keySet().stream().map(k -> map.get(k).size()).limit(1).collect(Collectors.toList());
        int size = CollUtil.getFirst(collect);
        for (int i = 0; i < size; i++) {
            List<String> obj = new ArrayList<>();
            for (List<String> str : map.values()) {
                if (i < str.size()) {
                    obj.add(str.get(i));
                }
            }
            res.add(obj);
        }
        return res.stream().limit(num).collect(Collectors.toList());
    }

    @Override
    public int count() {
        return map.size();
    }
}
