package com.mchz.nyx.pipeline.config;

import com.mchz.mcdatasource.DbCoreInit;
import com.mchz.mcdatasource.core.DatasourceConstant;
import com.mchz.nyx.pipeline.config.props.HomeProperties;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.annotation.Configuration;

import java.io.File;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/16
 */
@Slf4j
@Configuration
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class McDatasourceConfig implements ApplicationRunner {
    private final HomeProperties properties;

    @Override
    public void run(ApplicationArguments args) {
        String path = System.getProperty(DatasourceConstant.MCDATASOURCE_HOME);
        if (path == null) {
            path = new File(properties.getHome() + "/base/mcdatasource").getAbsolutePath();
            System.setProperty(DatasourceConstant.MCDATASOURCE_HOME, path);
        }
        String version = System.getProperty(DatasourceConstant.MCDATASOURCE_VERSION);
        if (version == null) {
            version = "1.6.8.1";
            System.setProperty(DatasourceConstant.MCDATASOURCE_VERSION, version);
        }
        DbCoreInit.getInstance();
        log.info("设置统一数据源版本:{};路径:{}", version, path);
    }
}
