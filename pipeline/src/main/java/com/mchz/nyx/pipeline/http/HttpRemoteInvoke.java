package com.mchz.nyx.pipeline.http;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.mchz.nyx.pipeline.config.props.SourceProperties;
import com.mchz.nyx.pipeline.model.api.TaskHeartbeatBeatReq;
import com.mchz.nyx.pipeline.model.dto.ProgressInfoDTO;
import com.mchz.nyx.pipeline.model.log.JobLogVO;
import com.mchz.nyx.pipeline.model.log.ProcessingTable;
import com.mchz.nyx.pipeline.util.HttpUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 * 单机模式Http调用
 * </p>
 *
 * <AUTHOR>
 * @date 2022/3/23 10:07
 * @since 1.6.0
 */
@Slf4j
@AllArgsConstructor
public class HttpRemoteInvoke implements RemoteInvokeHandler {
    private final SourceProperties properties;

    @Override
    public void finished(Long jobId, boolean success, JobLogVO jobLog) {
        String body = HttpUtil.post(buildUrl(
                String.format("/discovery/job/%d/finished?result=%d", jobId, BooleanUtil.toInt(success))),
            JSONUtil.toJsonStr(jobLog), 2000);
        log.debug(body);
    }

    @Override
    public void sendProcessingTable(ProcessingTable table) {
        HttpUtil.post(buildUrl("/discovery/job/processingTable"), JSONUtil.toJsonStr(table), 2000);
    }

    @Override
    public void asyncSendMessage(ProgressInfoDTO logDTO) {
        HttpUtil.post(buildUrl("/discovery/job/log"), JSONUtil.toJsonStr(logDTO), 2000);
    }

    @Override
    public void heart(TaskHeartbeatBeatReq value) {
        HttpUtil.post(buildUrl("/api/v1/taskHeartbeat/beat"), JSONUtil.toJsonStr(value), 2000);
    }

    private String getBaseUrl() {
        String baseUrl = properties.getSys().getMaster();
        if (StrUtil.isBlank(baseUrl)) {
            baseUrl = "https://127.0.0.1:8080";
            log.warn("【地址获取】默认使用localhost");
        }
        String contextPath = "/source";
        if (baseUrl.endsWith(contextPath)) {
            return baseUrl;
        }
        baseUrl = baseUrl + contextPath;
        properties.getSys().setMaster(baseUrl);
        return baseUrl;
    }

    private String buildUrl(String value) {
        if (StrUtil.isEmpty(value)) {
            return getBaseUrl();
        }
        if (value.startsWith(StrUtil.SLASH)) {
            return getBaseUrl() + value;
        }
        return getBaseUrl() + StrUtil.SLASH + value;
    }

}
