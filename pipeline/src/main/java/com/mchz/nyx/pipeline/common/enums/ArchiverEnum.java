package com.mchz.nyx.pipeline.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @createTime 2022年11月11日 16:36:00
 */
@Getter
@AllArgsConstructor
public enum ArchiverEnum {

    ZIP("zip"),
    SEVEN_Z("7z"),
    TAR("tar"),
    TAR_GZ("tar.gz"),
    RAR("rar");

    private final String name;


    public static boolean compressSuffix(String name) {
        return Arrays.stream(ArchiverEnum.values()).anyMatch(s -> s.getName().equals(name));
    }

    public static ArchiverEnum of(String name) {
        for (ArchiverEnum archiverEnum : ArchiverEnum.values()) {
            if (Objects.equals(archiverEnum.getName(), name)) {
                return archiverEnum;
            }
        }
        return ArchiverEnum.ZIP;
    }
}
