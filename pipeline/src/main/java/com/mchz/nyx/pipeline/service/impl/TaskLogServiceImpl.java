package com.mchz.nyx.pipeline.service.impl;

import cn.hutool.json.JSONArray;
import com.mchz.nyx.common.util.PartitionUtil;
import com.mchz.nyx.pipeline.entity.DiscoveryLogDO;
import com.mchz.nyx.pipeline.mapper.DiscoveryLogMapper;
import com.mchz.nyx.pipeline.model.log.JobLogInfoDTO;
import com.mchz.nyx.pipeline.model.log.JobLogItem;
import com.mchz.nyx.pipeline.service.TaskLogService;
import com.mchz.starter.tenant.util.TenantUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/11
 */
@Slf4j
@Service
@AllArgsConstructor
public class TaskLogServiceImpl implements TaskLogService {
    private final DiscoveryLogMapper discoveryLogMapper;

    @Override
    public void persistentLog(List<JobLogItem> list) {
        list.stream().collect(Collectors.groupingBy(JobLogItem::getTenantId))
            .forEach((k, v) -> TenantUtil.apply(k, () -> persistent(v)));
    }

    private void persistent(List<JobLogItem> list) {
        List<DiscoveryLogDO> doList = list.stream()
            .sorted(Comparator.comparing(JobLogItem::getTime))
            .map(this::buildDiscoveryLog).collect(Collectors.toList());
        PartitionUtil.part(doList, discoveryLogMapper::insertList);
    }

    private DiscoveryLogDO buildDiscoveryLog(JobLogItem item) {
        DiscoveryLogDO logDO = new DiscoveryLogDO();
        logDO.setJobId(item.getJobId());
        logDO.setPercent(item.getPercent());
        logDO.setContent(formatLogContent(item));
        return logDO;
    }

    private String formatLogContent(JobLogItem item) {
        JSONArray array = new JSONArray();
        array.add(new JobLogInfoDTO(item));
        return array.toString();
    }
}
