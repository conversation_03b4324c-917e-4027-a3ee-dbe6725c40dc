package com.mchz.nyx.pipeline.thread.listener;

import com.lmax.disruptor.IgnoreExceptionHandler;
import com.lmax.disruptor.PhasedBackoffWaitStrategy;
import com.lmax.disruptor.RingBuffer;
import com.lmax.disruptor.SleepingWaitStrategy;
import com.lmax.disruptor.dsl.Disruptor;
import com.lmax.disruptor.dsl.ProducerType;
import com.lmax.disruptor.util.DaemonThreadFactory;
import com.mchz.nyx.pipeline.common.constants.PipelineConst;
import com.mchz.nyx.pipeline.model.log.JobLogItem;
import com.mchz.nyx.pipeline.service.TaskCallbackService;
import com.mchz.nyx.pipeline.service.TaskLogService;
import com.mchz.nyx.pipeline.thread.event.JobLogEvent;
import com.mchz.nyx.pipeline.thread.event.LogItemEvent;
import com.mchz.nyx.pipeline.thread.handler.JobLogPersistentHandler;
import com.mchz.nyx.pipeline.thread.handler.JobLogSendHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.annotation.Profile;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/9
 */
@Profile("!embed")
@Slf4j
@Component
@RequiredArgsConstructor
public class JobLogListener implements InitializingBean {
    private final TaskCallbackService taskCallbackService;
    private final TaskLogService taskLogService;

    private RingBuffer<LogItemEvent> ringBuffer;

    private static void publishJobLog(LogItemEvent event, long sequence, JobLogEvent logEvent) {
        event.setProcessingTables(logEvent.getProcessingTables());
        event.setLogItem(logEvent.getLog());
    }

    @Override
    public void afterPropertiesSet() {
        Disruptor<LogItemEvent> disruptor = new Disruptor<>(LogItemEvent::new, PipelineConst.JOB_LOG_BUFFER_SIZE, DaemonThreadFactory.INSTANCE, ProducerType.SINGLE, new PhasedBackoffWaitStrategy(0, 1, TimeUnit.SECONDS, new SleepingWaitStrategy(100,500000000)));
        disruptor.handleEventsWith(new JobLogSendHandler(taskCallbackService), new JobLogPersistentHandler(taskLogService));
        disruptor.setDefaultExceptionHandler(new IgnoreExceptionHandler());
        // 启动 Disruptor
        ringBuffer = disruptor.start();
    }

    @EventListener(JobLogEvent.class)
    public void jobLog(JobLogEvent event) {
        if (!ringBuffer.tryPublishEvent(JobLogListener::publishJobLog, event)) {
            JobLogItem item = event.getLog();
            log.warn("【作业日志】队列阻塞，丢弃日志: jobId({}) {} {} {} {}", item.getJobId(), item.getTime(), item.getLevel(), item.getMessage(), item.getRemark());
        }
    }
}
