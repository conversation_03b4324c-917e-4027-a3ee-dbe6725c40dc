package com.mchz.nyx.pipeline.exception;

import com.mchz.nyx.common.exception.NyxException;
import lombok.AccessLevel;
import lombok.NoArgsConstructor;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/10/15
 */
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ChainEndInterruptException extends NyxException {
    public static final ChainEndInterruptException INSTANCE = new ChainEndInterruptException();
}
