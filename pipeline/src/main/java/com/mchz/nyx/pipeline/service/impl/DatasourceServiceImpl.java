package com.mchz.nyx.pipeline.service.impl;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.util.ObjUtil;
import com.mchz.datasource.cli.DatasourceMetabaseCli;
import com.mchz.mcdatasource.model.db.DatasourceDatabase;
import com.mchz.mcdatasource.model.db.DatasourceDatabaseMeta;
import com.mchz.nyx.meta.support.DataSourceConfig;
import com.mchz.nyx.pipeline.common.enums.DatabaseType;
import com.mchz.nyx.pipeline.exception.ApiException;
import com.mchz.nyx.pipeline.generator.meta.FileMetaAdapter;
import com.mchz.nyx.pipeline.generator.meta.MetaFileMetaAdapter;
import com.mchz.nyx.pipeline.generator.meta.UnFileMetaAdapter;
import com.mchz.nyx.pipeline.model.api.DataSourceParam;
import com.mchz.nyx.pipeline.model.dto.TestConnInfo;
import com.mchz.nyx.pipeline.service.DatasourceService;
import com.mchz.nyx.pipeline.service.FileManagerService;
import com.mchz.nyx.pipeline.util.DbUtil;
import com.mchz.nyx.pipeline.util.Func;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.sql.DatabaseMetaData;
import java.sql.SQLException;
import java.util.List;
import java.util.Properties;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/12/26
 */
@Slf4j
@Service
@AllArgsConstructor
public class DatasourceServiceImpl implements DatasourceService {
    private final FileManagerService fileManagerService;

    @Override
    public TestConnInfo testConnection(DataSourceParam config) {
        TestConnInfo res = new TestConnInfo();
        DataSourceConfig p = DbUtil.newDatasourceParam(config);
        DatasourceDatabaseMeta datasourceDatabaseMeta = new DatasourceDatabaseMeta(p.getType(), p.getHost(), p.getDb(), p.getPort(), p.getUser(), p.getPass(), p.getAdvanced());
        DatasourceDatabase datasourceDatabase = new DatasourceDatabase(datasourceDatabaseMeta);
        try {
            datasourceDatabase.connect();
            res.setSuccess(true);
            try {
                DatabaseMetaData metaData = datasourceDatabase.getConnection().getMetaData();
                res.setMessage(metaData.getDatabaseMajorVersion() > 0 ? String.format("连接成功 %s.%s", metaData.getDatabaseMajorVersion(), metaData.getDatabaseMinorVersion()) : "连接成功");
                log.info("测试连接成功 {}.{} ({})", metaData.getDatabaseMajorVersion(), metaData.getDatabaseMinorVersion(), metaData.getDatabaseProductVersion());
            } catch (Exception e) {
                res.setMessage("连接成功");
            }
        } catch (Exception e) {
            Throwable unwrap = ExceptionUtil.unwrap(e);
            log.error("测试连接失败", unwrap);
            res.setSuccess(false);
            res.setMessage(getMessage(unwrap));
        } finally {
            try {
                datasourceDatabase.close();
            } catch (IOException ignore) {
            }
        }
        return res;
    }

    @Override
    public List<String> loadSchemas(DataSourceParam config) {
        DataSourceConfig source = DbUtil.newDatasourceParam(config);
        DatabaseType type = DbUtil.fileType(source);
        switch (type) {
            case PDF:
            case WORD:
                return new UnFileMetaAdapter(fileManagerService).loadSchema(source);
            case FILE_METADATA:
                return new MetaFileMetaAdapter(fileManagerService).loadSchema(source);
            case OTHER:
                if (null != config.getDataBaseType()) {
                    try {
                        DatasourceMetabaseCli metadataCli = new DatasourceMetabaseCli(source.getType(), source.getHost(), source.getDb(), source.getPort(), source.getUser(), source.getPass(), true, ObjUtil.defaultIfNull(source.getAdvanced(), () -> new Properties()));
                        return metadataCli.fetchSchemas();
                    } catch (Exception e) {
                        Throwable unwrap = ExceptionUtil.unwrap(e);
                        log.error("获取schema异常", unwrap);
                        throw new ApiException(getMessage(unwrap));
                    }
                }
                break;
            default:
                return new FileMetaAdapter(fileManagerService).loadSchema(source);
        }
        throw new ApiException("不支持的数据源类型");
    }

    private String getMessage(Throwable e) {
        List<Throwable> list = ExceptionUtil.getThrowableList(e);
        Throwable cause = null;
        for (int i = list.size() - 1; i >= 0; i--) {
            Throwable throwable = list.get(i);
            if (throwable instanceof SQLException) {
                cause = throwable;
                break;
            }
        }
        if (cause == null) {
            cause = list.get(list.size() - 1);
        }
        String msg = cause.getMessage();
        if (msg == null) {
            msg = Func.getStackTrace(e);
        }
        return msg;
    }
}
