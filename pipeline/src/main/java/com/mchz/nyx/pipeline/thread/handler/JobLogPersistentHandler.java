package com.mchz.nyx.pipeline.thread.handler;

import com.lmax.disruptor.EventHandler;
import com.mchz.nyx.pipeline.model.log.JobLogItem;
import com.mchz.nyx.pipeline.service.TaskLogService;
import com.mchz.nyx.pipeline.thread.event.LogItemEvent;
import lombok.RequiredArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/11
 */
@RequiredArgsConstructor
public class JobLogPersistentHandler implements EventHandler<LogItemEvent> {
    private final TaskLogService taskLogService;
    private List<JobLogItem> logItems = new ArrayList<>();

    @Override
    public void onEvent(LogItemEvent event, long sequence, boolean endOfBatch) {
        logItems.add(event.getLogItem());
        event.clear1();
        if (endOfBatch) {
            taskLogService.persistentLog(logItems);
            logItems = new ArrayList<>();
        }
    }
}
