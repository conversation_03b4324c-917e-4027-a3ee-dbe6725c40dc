package com.mchz.nyx.pipeline.http;

import com.mchz.nyx.pipeline.model.api.TaskHeartbeatBeatReq;
import com.mchz.nyx.pipeline.model.dto.ProgressInfoDTO;
import com.mchz.nyx.pipeline.model.log.JobLogVO;
import com.mchz.nyx.pipeline.model.log.ProcessingTable;

/**
 * <p>
 * 远程调用处理
 * </p>
 *
 * <AUTHOR>
 * @date 2022/3/23 10:03
 * @since 1.6.0
 */
public interface RemoteInvokeHandler {

    void finished(Long jobId, boolean success, JobLogVO jobLog);

    void sendProcessingTable(ProcessingTable table);

    void asyncSendMessage(ProgressInfoDTO logDTO);

    void heart(TaskHeartbeatBeatReq value);
}
