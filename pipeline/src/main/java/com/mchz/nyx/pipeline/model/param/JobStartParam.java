package com.mchz.nyx.pipeline.model.param;

import com.mchz.nyx.pipeline.model.api.DataSourceLimitParam;
import lombok.Data;

import java.io.Serializable;

@Data
public class JobStartParam implements Serializable {
    private Integer industryId;
    private String industry;

    private Long planId;
    private Long jobId;
    private Integer jobType;
    /**
     * 数据源信息
     */
    private DataSourceLimitParam source;

    /**
     * 发现方式
     */
    private Integer discoveryMode;

    private Long stdId;

    private Long templateId;

    private Integer sampleLine;

    /**
     * 采样比例%
     */
    private Integer sampleRate;

    private Integer hitRate;

    /**
     * 是否启动可配置特征，1表示启动，0表示未启动
     */
    private Integer autoFeature;

    /**
     * 是否启动自动注释配置，1表示启动，0表示未启动
     */
    private Integer autoComment;

    /**
     * 表格合并，1表示启动，0表示未启动
     */
    private Integer formMerge;

    /**
     * 字段注释匹配基线：%
     */
    private Integer commentBaseline;

    private Integer aiPinyin;

    /**
     * 是否开启自动列名发现，1表示启动，0表示未启动
     */
    private Integer autoColumn;

    /**
     * 自动列名匹配基线：%
     */
    private Integer columnBaseline;

    /**
     * 是否开启自动规则发现，1表示启动，0表示未启动
     */
    private Integer autoRule;

    /**
     * LLM大模型
     */
    private Integer llm;

    private Long lastJobId;

    private Integer mainSimilarity;
    private Integer subSimilarity;
    private Integer num;

    private Boolean nlp;
    private Boolean ignoreMeta;
    private Boolean ignoreData;
    private Boolean ignoreSample;
    private Boolean ignoreCandidate;
    private Boolean full;
    private Boolean embedding;

    private String llmUrl;
    private String embeddingUrl;

    /**
     * 发现作业planId
     */
    private Long discoveryPlanId;
    /**
     * 发现作业jobId
     */
    private Long discoveryJobId;
    /**
     * 采样行数
     */
    private Integer sampleRows;
    /**
     * 合并策略
     */
    private Byte mergeStrategy;

    /**
     * 表格分类分级策略, 0->按表含义进行分类分级; 1->按字段分类分级结果计算表格分类分级
     */
    private Integer tableClassify;
    /**
     * 按表含义进行分类分级基准线
     */
    private Integer tableClassifyBaseline;
    /**
     * 样本数据策略 0->使用数据清单样本数据 1->重连业务库采集样本数据
     */
    private Integer contentStrategy;
    /**
     * 策略 0.元数据+样本数据 1.元数据
     * 0->不保留样本数据, 1->保留X条样本数据
     */
    private Integer saveStrategy;

    /**
     * 默认分类
     */
    private Long defaultClassifyId;

    /**
     * 默认分级
     */
    private Integer defaultLevel;
}
