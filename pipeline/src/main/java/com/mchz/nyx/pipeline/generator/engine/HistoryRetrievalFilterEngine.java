package com.mchz.nyx.pipeline.generator.engine;

import cn.hutool.cache.Cache;
import cn.hutool.cache.CacheUtil;
import cn.hutool.core.util.HashUtil;
import cn.hutool.core.util.StrUtil;
import com.mchz.nyx.dark.engine.HistoryRetrievalEngine;
import com.mchz.nyx.dark.model.TargetInfo;
import com.mchz.nyx.dark.model.meta.NyxMetaColumn;
import com.mchz.nyx.dark.model.meta.NyxMetaTable;
import com.mchz.nyx.dark.util.bloomfilter.CustomBloomFilter;
import com.mchz.nyx.pipeline.service.AnalysisHistoryService;
import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/22
 */
public class HistoryRetrievalFilterEngine implements HistoryRetrievalEngine {
    @Getter
    private final List<Long> planIds;
    private final AnalysisHistoryService analysisHistoryService;

    private final Cache<List<String>, List<TargetInfo>> cache;
    private final CustomBloomFilter filter1;
    private final CustomBloomFilter filter2;


    public HistoryRetrievalFilterEngine(long expectedElements, List<Long> planIds, AnalysisHistoryService analysisHistoryService) {
        this.planIds = planIds;
        this.analysisHistoryService = analysisHistoryService;
        this.cache = CacheUtil.newLFUCache(2000);
        this.filter1 = new CustomBloomFilter(expectedElements, 0.01);
        this.filter2 = new CustomBloomFilter(expectedElements, 0.01);
        init();
    }

    private void init() {
        analysisHistoryService.loadAllHistoryResult(planIds, (t, c) -> filter1.add(getTcKey(t, c)), (c, comment) -> filter2.add(getCcKey(c, comment)));
    }

    @Override
    public List<TargetInfo> retrieve(NyxMetaTable table, NyxMetaColumn column) {
        String tableName = table.getTableName();
        String columnName = column.getColumnName();
        if (filter1.contains(getTcKey(tableName, columnName))) {
            return analysisHistoryService.loadHistoryResultWithTC(planIds, tableName, columnName);
        }
        List<String> key = getListKey(columnName, column.getColumnComment());
        return cache.get(key, () -> {
            if (filter2.contains(key.toString())) {
                return analysisHistoryService.loadHistoryResultWithCC(planIds, columnName, column.getColumnComment());
            } else {
                return Collections.emptyList();
            }
        });
    }

    private String getTcKey(String t, String c) {
        return Arrays.asList(t, c).toString();
    }

    private String getCcKey(String name, String comment) {
        return getListKey(name, comment).toString();
    }

    private List<String> getListKey(String name, String comment) {
        if (StrUtil.isBlank(comment)) {
            return Collections.singletonList(name);
        } else if (comment.length() < 50) {
            return Arrays.asList(name, comment);
        } else {
            return Arrays.asList(name, StrUtil.subPre(comment, 50), Integer.toString(HashUtil.fnvHash(comment), 36));
        }
    }
}
