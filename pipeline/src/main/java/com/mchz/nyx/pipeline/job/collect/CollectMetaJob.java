package com.mchz.nyx.pipeline.job.collect;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.mchz.nyx.common.util.NyxStopWatch;
import com.mchz.nyx.meta.NyxInstance;
import com.mchz.nyx.meta.model.meta.CatalogSchema;
import com.mchz.nyx.meta.support.DataSourceConfig;
import com.mchz.nyx.meta.support.SchemaTables;
import com.mchz.nyx.pipeline.common.constants.PipelineConst;
import com.mchz.nyx.pipeline.common.enums.JobType;
import com.mchz.nyx.pipeline.config.props.SourceProperties;
import com.mchz.nyx.pipeline.generator.DarkContext;
import com.mchz.nyx.pipeline.generator.MetaInstanceGenerator;
import com.mchz.nyx.pipeline.job.Job;
import com.mchz.nyx.pipeline.job.JobLogManager;
import com.mchz.nyx.pipeline.job.JobStartConfig;
import com.mchz.nyx.pipeline.job.collect.stage.SamplingStage;
import com.mchz.nyx.pipeline.model.api.DataSourceLimitParam;
import com.mchz.nyx.pipeline.service.FileManagerService;
import com.mchz.nyx.pipeline.service.MetaExtendService;
import com.mchz.nyx.pipeline.service.MetadataService;
import com.mchz.nyx.pipeline.util.DbUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Component
@RequiredArgsConstructor
public class CollectMetaJob implements Job {
    private final MetaInstanceGenerator metaInstanceGenerator;
    private final SourceProperties properties;
    private final MetadataService metadataService;
    private final MetaExtendService metaExtendService;
    private final FileManagerService fileManagerService;

    @Override
    public JobType getJobType() {
        return JobType.COLLECT_META;
    }

    @Override
    public int[] getStageProportion(JobStartConfig jobConfig) {
        if (Boolean.TRUE.equals(jobConfig.getIgnoreMeta())) {
            return new int[]{0, 0, 90};
        }
        if (Boolean.TRUE.equals(jobConfig.getIgnoreData())) {
            return new int[]{0, 80, 10};
        }
        return new int[]{0, 50, 40};
    }

    @Override
    public void execute(JobLogManager jobLog, JobStartConfig jobConfig) throws Exception {
        DataSourceLimitParam sourceParam = jobConfig.getSource();
        DataSourceConfig source = DbUtil.newDatasourceParam(sourceParam);
        NyxStopWatch watch = jobLog.getStopWatch();

        if (jobLog.setStep(1) && !Boolean.TRUE.equals(jobConfig.getIgnoreMeta())) {
            String schema = jobLog.getHeartbeatDetails(String.class);
            log.info("【分类分级】采集元数据{}", StrUtil.nullToEmpty(schema));
            NyxInstance instance = metaInstanceGenerator.generate(source, jobLog, jobScope(sourceParam, schema), schema, jobLog::heartbeat);
            jobLog.info(null == schema ? "开始采集元数据信息" : "继续采集元数据信息");
            long[] key = watch.startChild("stage1");
            instance.run();
            watch.stopChild(key);
            jobLog.info("元数据采集完成");
        }
        if (jobLog.setStep(2)) {
            if (DbUtil.fileType(source).isIgnoreData()) {
                metaExtendService.fillNewTable(source.getSourceId());
            } else if (Boolean.TRUE.equals(jobConfig.getIgnoreData())) {
                metaExtendService.fillTable(source.getSourceId());
            } else {
                DarkContext context = buildDarkContext(source, jobConfig, jobLog);
                long[] key = watch.startChild("stage2");
                SamplingStage stage = new SamplingStage(metadataService, metaExtendService, fileManagerService);
                stage.execute(context, source, sourceParam.getSchemaSet());
                watch.stopChild(key);
            }
        }
        jobLog.setStep(3);
    }

    private DarkContext buildDarkContext(DataSourceConfig source, JobStartConfig param, JobLogManager jobLog) {
        DarkContext context = new DarkContext(param.getPlanId(), param.getJobId(), param.getStdId(), ObjUtil.defaultIfNull(param.getLastJobId(), 0L), source.getSourceId());
        context.setSampleLine(ObjUtil.defaultIfNull(param.getSampleLine(), 50))
            .setSampleRate(ObjUtil.defaultIfNull(param.getSampleRate(), 100))
            .setSampleSize(Math.min(context.getSampleLine() * context.getSampleRate() / 100, PipelineConst.MAX_SAMPLE_DATA_SIZE))
            .setColumnNum(properties.getSys().getTableColumnNum())
            .setMaxColumnLength(properties.getSys().getMaxColumnLength())
            .setOverlaySample(properties.getSys().isOverlaySampling())
            .setJobLogManager(jobLog)
            .setStopWatch(jobLog.getStopWatch());
        return context;
    }

    private List<SchemaTables> jobScope(DataSourceLimitParam source, String schema) {
        if (CollUtil.isEmpty(source.getSchemaSet())) {
            return new ArrayList<>();
        }
        Stream<String> stream = source.getSchemaSet().stream();
        if (null != schema) {
            stream = stream.filter(v -> v.compareTo(schema) >= 0);
        }
        return stream.map(v -> SchemaTables.of(CatalogSchema.of(v)))
            .sorted(Comparator.comparing(o -> o.getSchema().getName())).collect(Collectors.toList());
    }

}
