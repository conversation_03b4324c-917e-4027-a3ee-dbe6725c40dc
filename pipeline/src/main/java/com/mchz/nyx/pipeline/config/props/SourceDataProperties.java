package com.mchz.nyx.pipeline.config.props;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.context.annotation.Configuration;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2022/4/22 17:21
 * @since 1.6.0
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "sourcedata")
public class SourceDataProperties {

    @NestedConfigurationProperty
    private FileServerProperties fileServer = new FileServerProperties();

    @NestedConfigurationProperty
    private FileTempProperties file = new FileTempProperties();
}
