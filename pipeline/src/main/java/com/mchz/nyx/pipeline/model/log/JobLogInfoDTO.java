package com.mchz.nyx.pipeline.model.log;

import cn.hutool.core.date.DateUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2021/1/7 13:32
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class JobLogInfoDTO {
    private String msg;
    private String status;
    private String time;
    private String remark;

    public JobLogInfoDTO(JobLogItem item) {
        this.msg = item.getMessage();
        this.status = item.getLevel().name();
        this.time = DateUtil.formatDateTime(new Date(item.getTime()));
        this.remark = item.getRemark();
    }
}
