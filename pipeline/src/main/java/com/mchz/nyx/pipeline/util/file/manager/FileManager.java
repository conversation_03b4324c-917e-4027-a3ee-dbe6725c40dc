package com.mchz.nyx.pipeline.util.file.manager;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.compress.extractor.Extractor;
import cn.hutool.json.JSONUtil;
import com.jcraft.jsch.Channel;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;
import com.mchz.nyx.pipeline.common.enums.ArchiverEnum;
import com.mchz.nyx.pipeline.common.enums.DatabaseType;
import com.mchz.nyx.pipeline.entity.FileUploadDetail;
import com.mchz.nyx.pipeline.exception.ServiceException;
import com.mchz.nyx.pipeline.model.api.RemoteAddressParam;
import com.mchz.nyx.pipeline.model.vo.CompressVO;
import com.mchz.nyx.pipeline.util.DecompressUtil;
import com.mchz.nyx.pipeline.util.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.ftp.FTP;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPReply;
import org.apache.poi.util.IOUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Properties;

import static cn.hutool.core.text.CharSequenceUtil.subAfter;
import static cn.hutool.core.text.CharSequenceUtil.subBefore;
import static cn.hutool.core.text.StrPool.DOT;

@Slf4j
@Component
@RequiredArgsConstructor
public class FileManager {

    public List<SourceFile> getFiles(List<FileUploadDetail> fileUploadDetails,
                                     RemoteAddressParam remote, String downLoadPath, DatabaseType suffix) throws Exception {
        if (null != remote) {
            return getRemoteFiles(fileUploadDetails, remote, downLoadPath, suffix);
        }
        return getLocalFiles(fileUploadDetails, downLoadPath, suffix);
    }

    public List<FileUploadDetail> getFileUploadDetailDTOS(List<FileUploadDetail> fileUploadDetails, FileAttach fileAttach,
                                                          RemoteAddressParam remote, String downLoadPath, DatabaseType suffix) throws Exception {
        String type = fileAttach.getType();
        switch (type) {
            case FileAttach.Type.LOCAL:
                return buildLocalFileUploadDetailDTOS(fileUploadDetails, downLoadPath, suffix);
            case FileAttach.Type.REMOTE:
                return buildRemoteFileUploadDetailDTOS(fileUploadDetails, remote, downLoadPath, suffix);
            default:
                throw new IllegalArgumentException(JSONUtil.toJsonStr(fileAttach));
        }
    }

    private List<SourceFile> getRemoteFiles(List<FileUploadDetail> fileUploadDetails,
                                            RemoteAddressParam remote, String downLoadPath,
                                            DatabaseType suffix) throws Exception {
        switch (remote.getType()) {
            case RemoteAddressParam.Type.FTP:
                return getRemoteFilesByFTP(fileUploadDetails, remote, downLoadPath, suffix);
            case RemoteAddressParam.Type.SFTP:
                return getRemoteFilesBySFTP(fileUploadDetails, remote, downLoadPath, suffix);
            default:
                throw new IllegalArgumentException(JSONUtil.toJsonStr(remote));
        }
    }

    private List<SourceFile> getRemoteFilesByFTP(List<FileUploadDetail> fileUploadDetails,
                                                 RemoteAddressParam remoteAddressParam, String downLoadPath, DatabaseType suffix) throws Exception {

        FTPClient ftpClient = null;
        try {
            ftpClient = getFTPClient(remoteAddressParam, "GBK");
            List<SourceFile> files = new ArrayList<>();
            List<FileUploadDetail> dtos = getBaseRemoteFileUploadDetail(fileUploadDetails, downLoadPath, suffix, ftpClient, null);
            buildRemoteFilesByFTP(dtos, downLoadPath, files, ftpClient);
            return files;
        } finally {
            if (Objects.nonNull(ftpClient)) {
                ftpClient.disconnect();
            }
        }
    }

    private File downloadRemoteFileByFTP(FileUploadDetail detailDTO,
                                         String downLoadPath, FTPClient ftpClient) throws Exception {
        String path = detailDTO.getFilePath();
        Objects.requireNonNull(path);
        int index = path.lastIndexOf("/");
        if (index == -1) {
            throw new ServiceException(String.format("远程文件路径错误 %s", path));
        }
        String fileName = path.substring(index + 1);
        File file = new File(downLoadPath + File.separator + fileName);
        // 可能在获取元数据时已经下载文件
        if (!file.exists()) {
            File parentFile = file.getParentFile();
            if (!parentFile.exists()) {
                if (!parentFile.mkdirs()) {
                    log.error(String.format("临时文件夹创建失败 %s", parentFile.getAbsolutePath()));
                }
            }
            InputStream inputStream = null;
            try {
                log.info("ftp path: {}", path);
                inputStream = ftpClient.retrieveFileStream(new String(path.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1));//
                FileOutputStream outputStream = new FileOutputStream(file);
                IOUtils.copy(inputStream, outputStream);
                log.info("远程下载文件成功 {}", file.getAbsolutePath());
            } catch (Exception e) {
                log.error("远程下载文件失败", e);
            } finally {
                if (Objects.nonNull(inputStream)) {
                    inputStream.close();
                    ftpClient.completePendingCommand();
                }
            }
        }
        return file;
    }

    private void buildRemoteFilesByFTP(List<FileUploadDetail> fileUploadDetails, String downLoadPath,
                                       List<SourceFile> files, FTPClient ftpClient) throws Exception {
        for (FileUploadDetail detailDTO : fileUploadDetails) {
            File file = Objects.nonNull(detailDTO.getCompressUuid())
                ? new File(detailDTO.getFilePath())
                : downloadRemoteFileByFTP(detailDTO, downLoadPath, ftpClient);
            files.add(new SourceFile(detailDTO, file));
        }
    }

    private FTPClient getFTPClient(RemoteAddressParam remoteAddressParam, String charset) throws IOException {
        String host = remoteAddressParam.getUrl();
        String port = remoteAddressParam.getPort();
        String userName = remoteAddressParam.getUsername();
        String password = remoteAddressParam.getPassword();

        FTPClient ftp = new FTPClient();
        ftp.setControlEncoding(charset);
        ftp.connect(host, Integer.parseInt(port));
        ftp.enterLocalPassiveMode();
        ftp.login(userName, password);
        ftp.setFileType(FTP.BINARY_FILE_TYPE);
        if (!FTPReply.isPositiveCompletion(ftp.getReplyCode())) {
            ftp.disconnect();
            throw new ServiceException("无法连接到FTP服务器，用户名或密码错误！");
        }
        if (!Objects.equals(charset, "UTF-8")//
            && FTPReply.isPositiveCompletion(ftp.sendCommand("OPTS UTF8", "ON"))) {
            ftp.disconnect();
            return getFTPClient(remoteAddressParam, "UTF-8");
        }
        return ftp;
    }

    private Channel getSFTPChannel(RemoteAddressParam remoteAddressParam) throws Exception {
        JSch jsch = new JSch();
        String host = remoteAddressParam.getUrl();
        int port = Integer.parseInt(remoteAddressParam.getPort());
        String userName = remoteAddressParam.getUsername();
        String password = remoteAddressParam.getPassword();
        Session sshSession = jsch.getSession(userName, host, port);
        sshSession.setPassword(password);
        Properties sshConfig = new Properties();
        sshConfig.put("StrictHostKeyChecking", "no");
        sshSession.setConfig(sshConfig);
        sshSession.connect();
        Channel channel = sshSession.openChannel("sftp");
        channel.connect();
        return channel;
    }

    private List<SourceFile> getRemoteFilesBySFTP(List<FileUploadDetail> fileUploadDetails,
                                                  RemoteAddressParam remoteAddressParam, String downLoadPath, DatabaseType suffix) throws Exception {
        Channel channel = getSFTPChannel(remoteAddressParam);
        ChannelSftp sftp = null;
        try {
            sftp = (ChannelSftp) channel;

            Field field = ChannelSftp.class.getDeclaredField("server_version");
            field.setAccessible(true);
            field.set(sftp, 2);

            int version = sftp.getServerVersion();
            if (version < 3 || version > 5) {
                sftp.setFilenameEncoding("GBK");
            }

            List<SourceFile> files = new ArrayList<>();
            // 将压缩文件解压
            List<FileUploadDetail> dtos = getBaseRemoteFileUploadDetail(fileUploadDetails, downLoadPath, suffix, null, sftp);
            buildRemoteFilesBySFTP(dtos, downLoadPath, files, sftp);
            return files;
        } finally {
            if (sftp.isConnected()) {
                sftp.disconnect();
            }
        }
    }

    private File downloadRemoteFileBySFTP(FileUploadDetail detailDTO,
                                          String downLoadPath, ChannelSftp sftp) throws Exception {
        String path = detailDTO.getFilePath();
        Objects.requireNonNull(path);
        int index = path.lastIndexOf("/");
        if (index == -1) {
            throw new ServiceException(String.format("远程文件路径错误 %s", path));
        }
        sftp.cd(path.substring(0, index));
        String fileName = path.substring(index + 1);
        File file = new File(downLoadPath + File.separator + fileName);
        // 可能在获取元数据时已经下载文件
        if (!file.exists()) {
            File parentFile = file.getParentFile();
            if (!parentFile.exists()) {
                if (!parentFile.mkdirs()) {
                    log.error(String.format("临时文件夹创建失败 %s", parentFile.getAbsolutePath()));
                }
            }
            try (FileOutputStream stream = new FileOutputStream(file)) {
                sftp.get(fileName, stream);
                log.info("远程下载文件成功 {}", file.getAbsolutePath());
            }
        }
        return file;
    }

    private void buildRemoteFilesBySFTP(List<FileUploadDetail> fileUploadDetails, String downLoadPath,
                                        List<SourceFile> files, ChannelSftp sftp) throws Exception {
        for (FileUploadDetail detailDTO : fileUploadDetails) {
            File file = Objects.nonNull(detailDTO.getCompressUuid())
                ? new File(detailDTO.getFilePath())
                : downloadRemoteFileBySFTP(detailDTO, downLoadPath, sftp);
            files.add(new SourceFile(detailDTO, file));
        }
    }

    private List<FileUploadDetail> buildLocalFileUploadDetailDTOS(List<FileUploadDetail> dtos,
                                                                  String path, DatabaseType suffix) throws IOException {
        if (CollUtil.isEmpty(dtos)) {
            return Lists.newArrayList();
        }
        boolean nonExistCompress = dtos.stream().noneMatch(s -> ArchiverEnum.compressSuffix(s.getFormat()));
        if (nonExistCompress) {
            // 1. fileUploadDetailDTOS不包含压缩文件
            return dtos;
        }
        // 2. fileUploadDetailDTOS只包含压缩文件; 此时需要解压文件
        List<FileUploadDetail> collect = Lists.newArrayList();
        for (FileUploadDetail s : dtos) {
            List<CompressVO> decompress = decompress(s.getFilePath(), ArchiverEnum.of(s.getFormat()), path, suffix);
            if (CollUtil.isEmpty(decompress)) {
                continue;
            }
            for (CompressVO u : decompress) {
                FileUploadDetail innerDto = new FileUploadDetail();
                BeanUtils.copyProperties(s, innerDto);
                // 1. 新增时id为null;
                innerDto.setId(null);
                String format = subAfter(u.getFileName(), DOT, true);
                innerDto.setFormat(format);
                String name = subBefore(u.getFileName(), DOT + format, false);
                innerDto.setName(name);
                // 2. 设置path
                innerDto.setPath(u.getPath());
                // 3. 设置Uuid, compressUuid
                String uuid = cn.hutool.core.lang.UUID.randomUUID().toString();
                innerDto.setUuid(uuid);
                innerDto.setCompressUuid(s.getUuid());
                log.info("新增解压缩文件: {}", u.getFileName());
                collect.add(collect.size(), innerDto);
            }
        }
        return collect;
    }

    private List<FileUploadDetail> getBaseRemoteFileUploadDetail(List<FileUploadDetail> dtos, String path,
                                                                 DatabaseType suffix,
                                                                 FTPClient ftpClient, ChannelSftp sftp) throws Exception {
        if (CollUtil.isEmpty(dtos)) {
            return Lists.newArrayList();
        }
        boolean nonExistCompress = dtos.stream().noneMatch(s -> ArchiverEnum.compressSuffix(s.getFormat()));
        if (nonExistCompress) {
            // 1. fileUploadDetailDTOS不包含压缩文件
            return dtos;
        }
        // 2. fileUploadDetailDTOS只包含压缩文件; 此时需要解压文件
        List<FileUploadDetail> collect = Lists.newArrayList();
        for (FileUploadDetail s : dtos) {
            File file;
            if (Objects.nonNull(ftpClient)) {
                file = downloadRemoteFileByFTP(s, path, ftpClient);
            } else {
                file = downloadRemoteFileBySFTP(s, path, sftp);
            }
            log.info("远程压缩文件下载路径: {}", file.getAbsolutePath());
            List<CompressVO> decompress = decompress(s.getFilePath(), ArchiverEnum.of(s.getFormat()), path, suffix);
            if (CollUtil.isEmpty(decompress)) {
                continue;
            }
            for (CompressVO u : decompress) {
                FileUploadDetail innerDto = new FileUploadDetail();
                BeanUtils.copyProperties(s, innerDto);
                // 1. 新增时id为null;
                innerDto.setId(null);
                String format = subAfter(u.getFileName(), DOT, true);
                innerDto.setFormat(format);
                String name = subBefore(u.getFileName(), DOT + format, false);
                innerDto.setName(name);
                // 2. 设置path
                innerDto.setPath(u.getPath());
                // 3. 设置Uuid, compressUuid
                String uuid = cn.hutool.core.lang.UUID.randomUUID().toString();
                innerDto.setUuid(uuid);
                innerDto.setCompressUuid(s.getUuid());
                log.info("新增解压缩文件: {}", u.getFileName());
                collect.add(collect.size(), innerDto);
            }
        }
        return collect;
    }

    private List<FileUploadDetail> buildRemoteFileUploadDetailDTOS(List<FileUploadDetail> fileUploadDetails, RemoteAddressParam remote, String downLoadPath, DatabaseType suffix) throws IOException {
        switch (remote.getType()) {
            case RemoteAddressParam.Type.FTP:
                return getRemoteFileUploadDetailByFTP(fileUploadDetails, remote, downLoadPath, suffix);
            case RemoteAddressParam.Type.SFTP:
                return getRemoteFileUploadDetailBySFTP(fileUploadDetails, remote, downLoadPath, suffix);
            default:
                throw new IllegalArgumentException(JSONUtil.toJsonStr(remote));
        }
    }

    private List<FileUploadDetail> getRemoteFileUploadDetailByFTP(List<FileUploadDetail> dtos,
                                                                  RemoteAddressParam remote,
                                                                  String downLoadPath, DatabaseType suffix) throws IOException {
        FTPClient ftpClient = null;
        try {
            ftpClient = getFTPClient(remote, "GBK");
            dtos = getBaseRemoteFileUploadDetail(dtos, downLoadPath, suffix, ftpClient, null);
            for (FileUploadDetail u : dtos) {
                if (Objects.isNull(u.getCompressUuid())) {
                    downloadRemoteFileByFTP(u, downLoadPath, ftpClient);
                }
            }
        } catch (Exception e) {
            log.error("ftp获取文件失败: ", e);
        } finally {
            if (Objects.nonNull(ftpClient)) {
                ftpClient.disconnect();
            }
        }
        return dtos;
    }

    private List<FileUploadDetail> getRemoteFileUploadDetailBySFTP(List<FileUploadDetail> dtos,
                                                                   RemoteAddressParam remote, String downLoadPath,
                                                                   DatabaseType suffix) {
        ChannelSftp sftp = null;
        try {
            Channel channel = getSFTPChannel(remote);
            sftp = (ChannelSftp) channel;
            Field field = ChannelSftp.class.getDeclaredField("server_version");
            field.setAccessible(true);
            field.set(sftp, 2);

            int version = sftp.getServerVersion();
            if (version < 3 || version > 5) {
                sftp.setFilenameEncoding("GBK");
            }

            // 将压缩文件解压
            dtos = getBaseRemoteFileUploadDetail(dtos, downLoadPath, suffix, null, sftp);
            for (FileUploadDetail s : dtos) {
                if (Objects.isNull(s.getCompressUuid())) {
                    downloadRemoteFileBySFTP(s, downLoadPath, sftp);
                }
            }
        } catch (Exception e) {
            log.error("sftp获取文件失败: ", e);
        } finally {
            if (sftp.isConnected()) {
                sftp.disconnect();
            }
        }
        return dtos;
    }

    private List<SourceFile> getLocalFiles(List<FileUploadDetail> fileUploadDetails,
                                           String path, DatabaseType suffix) {
        List<SourceFile> files = new ArrayList<>();
        List<FileUploadDetail> dtos;
        try {
            dtos = buildLocalFileUploadDetailDTOS(fileUploadDetails, path, suffix);
            buildDataFiles(files, dtos);
        } catch (IOException e) {
            log.info("本地构建DatabaseFile失败: ", e);
        }
        return files;
    }

    private void buildDataFiles(List<SourceFile> files, List<FileUploadDetail> fileUploadDetails) {
        for (FileUploadDetail dto : fileUploadDetails) {
            // 集群使用远程服务器情况
            if (!FileUtil.exist(dto.getFilePath())) {
            }
            files.add(new SourceFile(dto, new File(dto.getFilePath())));
        }
    }

    private List<CompressVO> decompress(String path, ArchiverEnum archiverEnum, String outDir, DatabaseType databaseType) {
        List<CompressVO> res = new ArrayList<>();
        File targetDir = new File(outDir);
        String parent = targetDir.getAbsolutePath() + StrUtil.SLASH;
        try (Extractor extractor = DecompressUtil.createExtractor(StandardCharsets.UTF_8, archiverEnum, new File(path))) {
            extractor.extract(targetDir, archiveEntry -> {
                if (archiveEntry.isDirectory()) {
                    return false;
                }
                if (databaseType.isSuffix(archiveEntry.getName())) {
                    res.add(CompressVO.builder().fileName(archiveEntry.getName()).path(FileUtil.normalize(parent + archiveEntry.getName())).build());
                    return true;
                }
                return false;
            });
        }
        return res;
    }
}
