package com.mchz.nyx.pipeline.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 * 作业类型
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2019/12/25 14:25
 */
@Getter
@AllArgsConstructor
public enum JobType {

    NONE(-1),
    /**
     * 资产盘点
     */
    DISCOVERY(0),
    /** 资产关系 */
    ASSET_RELATION(4),
    /** 特征工程 */
    FEATURE_GENERATE(5),
    /** 元数据采集 */
    COLLECT_META(7),
    ;

    private final Integer code;

    public static JobType getType(Integer code) {
        if (null == code) {
            return DISCOVERY;
        }
        for (JobType type : JobType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return DISCOVERY;
    }
}
