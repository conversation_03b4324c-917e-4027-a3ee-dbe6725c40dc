package com.mchz.nyx.pipeline.config;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties;
import com.baomidou.mybatisplus.core.config.GlobalConfig;
import com.baomidou.mybatisplus.core.handlers.PostInitTableInfoHandler;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.toolkit.JdbcUtils;
import com.mchz.nyx.common.util.DbDialectFactory;
import com.mchz.nyx.pipeline.common.CustomInjector;
import com.mchz.nyx.pipeline.common.CustomPostInitTableInfoHandler;
import com.mchz.nyx.pipeline.config.props.JdbcProperties;
import com.mchz.nyx.pipeline.util.Func;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import java.util.Properties;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
@Configuration
@MapperScan("com.mchz.nyx.pipeline.mapper")
@EnableTransactionManagement
public class MybatisPlusConfig {

    @Bean
    @Primary
    public CustomInjector sqlInjector() {
        return new CustomInjector();
    }

    /**
     * 添加分页插件
     */
    @Bean
    public MybatisPlusInterceptor mybatisPlusInterceptor(JdbcProperties properties, MybatisPlusProperties mybatisPlusProperties) {
        DbType dbType = JdbcUtils.getDbType(properties.getUrl());
        if (DbType.OTHER.equals(dbType)) {
            throw new IllegalArgumentException(properties.getUrl());
        }
        if (null == mybatisPlusProperties.getConfiguration().getDatabaseId()) {
            mybatisPlusProperties.getConfiguration().setDatabaseId(dbType.getDb());
        }
        mybatisPlusProperties.getGlobalConfig().setPostInitTableInfoHandler(buildPostInitTableInfoHandler(mybatisPlusProperties, properties.getDriverClassName()));
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(dbType));
        return interceptor;
    }

    private PostInitTableInfoHandler buildPostInitTableInfoHandler(MybatisPlusProperties properties, String driverName) {
        GlobalConfig.DbConfig dbConfig = properties.getGlobalConfig().getDbConfig();
        Properties variables = properties.getConfiguration().getVariables();
        String dcf, dtf, ecf, etf;
        if (null == dbConfig.getColumnFormat()) {
            dcf = DbDialectFactory.newDialect(driverName).getWrapper().wrap("%s");
        } else {
            dcf = dbConfig.getColumnFormat();
        }
        dtf = dbConfig.getTableFormat();
        ecf = variables.getProperty(Func.name(GlobalConfig.DbConfig::getColumnFormat), "`%s`");
        etf = variables.getProperty(Func.name(GlobalConfig.DbConfig::getTableFormat));
        return new CustomPostInitTableInfoHandler(dbConfig, format(dtf), format(dcf), format(etf), format(ecf));
    }

    private String format(String format) {
        return StrUtil.isBlank(format) || "%s".equals(format) ? null : format;
    }
}
