package com.mchz.nyx.pipeline.model.api;

import cn.hutool.json.JSONUtil;
import com.mchz.mcdatasource.model.core.StreamDataType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <p>
 * 源数据发现数据源配置
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/1/4 11:29
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DataSourceLimitParam extends DataSourceParam {

    /**
     * schemas
     */
    private String schemas;

    /**
     * schema set
     */
    private Set<String> schemaSet;

    /**
     * tables
     */
    private String tables;

    /**
     * 选择schema时需要排除的tables
     */
    private String excludeTables;

    private List<StreamDataType> streamDataTypes = new ArrayList<>();

    public Set<String> getSchemaSet() {
        if (null == schemaSet && null != schemas) {
            schemaSet = new HashSet<>(JSONUtil.toList(getSchemas(), String.class));
        }
        return schemaSet;
    }
}
