package com.mchz.nyx.pipeline.generator;

import com.mchz.nyx.meta.common.enums.MetaObjType;
import com.mchz.nyx.meta.instance.sink.NyxEventSink;
import com.mchz.nyx.meta.model.event.NyxEvent;
import com.mchz.nyx.meta.model.meta.CatalogSchema;
import com.mchz.nyx.pipeline.job.JobLogManager;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.function.Consumer;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/9/10
 */
@Slf4j
@AllArgsConstructor
public class NyxEventSinkPoxy implements NyxEventSink {
    private final NyxEventSink sink;
    private final JobLogManager jobLogManager;
    private final Consumer<String> callback;

    @Override
    public void start() {
        sink.start();
    }

    @Override
    public void sink(Consumer<NyxEvent> func) {
        jobLogManager.interrupted();
        sink.sink(func);
    }

    @Override
    public void shutdown() {
        sink.shutdown();
    }

    @Override
    public void schema(List<CatalogSchema> schemas, List<String> delSchema) {
        jobLogManager.processed(schemas.size());
        sink.schema(schemas, delSchema);
    }

    @Override
    public void stageStart(CatalogSchema catalogSchema, List<String> tableNames, MetaObjType first, MetaObjType... include) {
        callback.accept(catalogSchema.getName());
        jobLogManager.info("采集 " + catalogSchema.getName());
        sink.stageStart(catalogSchema, tableNames, first, include);
    }

    @Override
    public void stageFinish(CatalogSchema catalogSchema) {
        sink.stageFinish(catalogSchema);
        jobLogManager.complete();
    }
}
