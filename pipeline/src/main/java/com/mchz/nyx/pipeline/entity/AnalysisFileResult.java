package com.mchz.nyx.pipeline.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/21
 */
@Data
@TableName("analysis_file_result")
public class AnalysisFileResult {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 配置id
     */
    private Long planId;

    /**
     * 执行作业id
     */
    private Long jobId;

    private Long resultId;

    /**
     * 分类type id
     */
    private Long classifyId;

    /**
     * 分级级别
     */
    private Integer level;

    private String according;

    private String content;

    private Integer version;

    private String ref;
}
