package com.mchz.nyx.pipeline.job.discovery.stage;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.lmax.disruptor.WorkHandler;
import com.mchz.nyx.common.enums.DataTypeGroup;
import com.mchz.nyx.dark.common.enums.HitType;
import com.mchz.nyx.dark.model.TargetInfo;
import com.mchz.nyx.dark.model.TargetResult;
import com.mchz.nyx.dark.model.meta.ColumnType;
import com.mchz.nyx.dark.model.meta.SampleResult;
import com.mchz.nyx.pipeline.common.constants.PipelineConst;
import com.mchz.nyx.pipeline.common.enums.AnalysisColumnStatus;
import com.mchz.nyx.pipeline.common.enums.RepeatColumnType;
import com.mchz.nyx.pipeline.common.enums.SampleStatus;
import com.mchz.nyx.pipeline.entity.*;
import com.mchz.nyx.pipeline.generator.DarkClassifyActuator;
import com.mchz.nyx.pipeline.generator.DarkContext;
import com.mchz.nyx.pipeline.generator.sample.SampleHandler;
import com.mchz.nyx.pipeline.job.discovery.param.DiscoveryEvent;
import com.mchz.nyx.pipeline.job.discovery.param.TableLogParam;
import com.mchz.nyx.pipeline.model.dto.*;
import com.mchz.nyx.pipeline.model.param.TableSamplingParam;
import com.mchz.nyx.pipeline.service.MetadataService;
import com.mchz.nyx.pipeline.util.Func;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/9
 */
@Slf4j
@AllArgsConstructor
public class DarkHandler implements WorkHandler<DiscoveryEvent> {
    private final DarkContext context;
    private final DarkClassifyActuator actuator;
    private final MetadataService metadataService;
    private final SampleHandler sampleHandler;

    @Override
    public void onEvent(DiscoveryEvent event) {
        if (context.isAlerted()) {
            return;
        }
        //开启日志
        String tableName = startLog(event);
        execute(event, tableName);
        //结束日志
        endLog(event);
    }

    private void execute(DiscoveryEvent event, String tableName) {
        TableDTO table = event.getTable();
        List<AnalysisColumn> c1 = event.getColumns();
        event.setColumns(null);
        long[] key = context.startChild("meta column");
        Map<Long, AnalysisColumn> map = c1.stream().collect(Collectors.toMap(AnalysisColumn::getColumnId, Function.identity()));
        List<ColumnDTO> c2 = metadataService.listMetaColumn(context.getSourceId(), map.keySet());
        context.stopChild(key);
        if (c2.isEmpty()) {
            return;
        }
        List<NyxMetaColumnDTO> c3 = c2.stream().map(v -> toMetaColumn(v, map.get(v.getOid()))).collect(Collectors.toList());

        DateTime now = DateUtil.date();
        int type;
        Map<String, List<Object>> finalSample;
        if (SampleHandler.EMPTY_HANDLER != sampleHandler) {
            context.info(context.isIgnoreData(), "采样开始 {}", tableName);
            TableSamplingParam param = SampleHandler.buildSamplingParam(table.getCatalogSchema(), table.getTableName(), c3, context.getMaxColumnLength());

            key = context.startChild("select sample");
            Map<String, List<Object>> sample = null;
            try {
                SampleResult result = sampleHandler.sampling(context, param);
                context.info(context.isIgnoreData(), "采样完成 {}", tableName);
                if (null == result) {
                    type = 0;
                } else {
                    if (null == result.getTotal()) {
                        type = 0;
                    } else if (0 == result.getTotal()) {
                        type = 1;
                    } else {
                        type = 2;
                    }
                    sample = result.getSample();
                }
            } catch (Exception e) {
                type = 0;
                if (log.isDebugEnabled()) {
                    log.warn("【获取样本】{} 失败", tableName, e);
                } else {
                    log.warn("【获取样本】{} 失败,{}", tableName, Func.getMessageWithStack(e));
                }
                context.warn("获取样本数据失败 {}", tableName);
            } finally {
                context.stopChild(key);
            }
            finalSample = null == sample ? Collections.emptyMap() : sample;
        } else {
            type = 0;
            finalSample = Collections.emptyMap();
        }
        key = context.startChild("execute");
        ResultTableColumnDTO result = actuator.execute(context.getAddInfo(), table, c3, v -> finalSample.get(v.getColumnName()));
        context.stopChild(key);
        event.setAnalysisResult(resultProcessing(result));
        if (context.isSaveSample() && type > 0) {
            event.setSample(buildSampleData(result, now, 1 == type));
        }
        event.setStatus(type);
    }

    private String startLog(DiscoveryEvent event) {
        TableLogParam log = event.getLog();
        if (log.getFirst().compareAndSet(true, false)) {
            context.table(log.getTableName());
            context.info("正在分析 {} ({}/{})", log.getTableName(), log.getIndex(), log.getSize());
        }
        return log.getTableName();
    }

    private void endLog(DiscoveryEvent event) {
        TableLogParam logParam = event.getLog();
        int i = logParam.getCount().decrementAndGet();
        if (0 == i) {
            context.complete(logParam.getTableName());
            context.info("分析完成 {}", logParam.getTableName());
        } else if (i < 0) {
            log.warn("【发现引擎】Counter breakdown");
        }
        event.setLog(null);
    }

    private AnalysisResultDTO resultProcessing(ResultTableColumnDTO result) {
        TableDTO table = result.getTable();
        AnalysisResultDTO payload = new AnalysisResultDTO();
        saveTableClassify(payload, table);
        long[] key = context.startChild("classify");
        actuator.recommendColumnClassify(context, result);
        context.stopChild(key);
        result.getColumns().forEach(v -> procColumn(payload, v));
        return payload;
    }

    private void saveTableClassify(AnalysisResultDTO payload, TableDTO table) {
        if (table.isRec()) {
            ClassifyDTO classify = table.getClassify();
            AnalysisTable temp = new AnalysisTable();
            temp.setId(table.getId());
            temp.setClassifyId(String.format(",%d,", classify.getTypeId()));
            temp.setLevel(classify.getLevel());
            payload.getUpdateTable().add(temp);
        }
    }

    private void buildCandidates(List<AnalysisSampleResult> candidate, NyxMetaColumnDTO column, List<TargetResult> list) {
        int score = 10000;
        for (TargetResult hit : list) {
            if (null == hit.getInfo().getId()) {
                continue;
            }
            AnalysisSampleResult bizResult = new AnalysisSampleResult();
            TargetInfo info = hit.getInfo();
            bizResult.setResultId(column.getId());
            bizResult.setBizId(info.getId());
            bizResult.setClassifyId(info.getClassifyId());
            bizResult.setLevel(info.getLevel());
            bizResult.setRuleType(getHitType(hit));
            if (null != hit.getScore()) {
                bizResult.setHitRate(BigDecimal.valueOf(hit.getScore()));
            }
            if (null != hit.getDegree()) {
                bizResult.setDegree(hit.getDegree());
            }
            score = resetRecRate(hit.getType(), hit.getScore(), score);
            bizResult.setScore(score);
            bizResult.setHitData(JSONUtil.toJsonStr(hit.getHitData()));
            candidate.add(bizResult);
        }
    }

    private List<SampleColumn> buildSampleData(ResultTableColumnDTO table, DateTime now, boolean empty) {
        return table.getColumns().stream().map(
            column -> {
                SampleColumn sample = new SampleColumn();
                sample.setOid(column.getMeta().getCid());
                sample.setSourceId(context.getSourceId());
                sample.setTableId(table.getTable().getOid());
                sample.setUpdatedAt(now);
                if (null == column.getData()) {
                    sample.setStatus(empty ? SampleStatus.CRAWL_0ROWS : SampleStatus.CRAWL_BIG);
                } else if (!column.getData().isEmpty()) {
                    sample.setStatus(SampleStatus.CRAWL);
                    sample.setSampleData(Func.sample(column.getData()));
                } else {
                    if (empty) {
                        sample.setStatus(SampleStatus.CRAWL_0ROWS);
                    } else {
                        sample.setStatus(SampleStatus.CRAWL_NULL);
                        sample.setSampleData(PipelineConst.SAMPLE_EMPTY);
                    }
                }
                return sample;
            }
        ).collect(Collectors.toList());
    }

    private void procColumn(AnalysisResultDTO payload, ResultColumnDTO dto) {
        NyxMetaColumnDTO column = dto.getMeta();
        AnalysisColumn ac = new AnalysisColumn();
        ac.setId(column.getId());
        if (column.isMain() && null == payload.getMainSubSync()) {
            payload.setMainSubSync(new HashMap<>());
        }
        boolean flag = true;
        TargetResult first = dto.getFirst();
        if (null != first) {
            ac.setBizId(first.getInfo().getId());
            ac.setStatus(AnalysisColumnStatus.S3.getCode());
            flag = false;
            payload.getUpdateAnalysis().add(ac);
            AnalysisColumnTemp temp;
            if (null == ac.getBizId() && null != first.getInfo().getName()) {
                temp = new AnalysisColumnTemp();
                temp.setPlanId(context.getPlanId());
                temp.setJobId(context.getJobId());
                temp.setAcId(ac.getId());
                temp.setName(first.getInfo().getName());
                temp.setType(BooleanUtil.toInt(StrUtil.contains(column.getColumnComment(), temp.getName().charAt(0))));
                payload.getTempResult().add(temp);
            } else {
                temp = null;
            }
            if (column.isMain()) {
                payload.getMainSubSync().put(column.getCid(), Pair.of(ac, temp));
            }
            Integer ruleType = getRuleType(first.getType());
            if (null != ruleType) {
                addAnalysisColumnHit(payload, ac, 1, ruleType);
            }
        }
        ClassifyDTO classify = dto.getClassify();
        if (null != classify && null != classify.getTypeId()) {
            ac.setClassifyId(classify.getTypeId());
            ac.setLevel(maxLevel(ac.getLevel(), classify.getLevel()));
            if (null != dto.getRuleType()) {
                addAnalysisColumnHit(payload, ac, 2, dto.getRuleType().getCode());
            }
            if (flag) {
                flag = false;
                ac.setStatus(AnalysisColumnStatus.S3.getCode());
                payload.getUpdateAnalysis().add(ac);
                if (column.isMain()) {
                    payload.getMainSubSync().put(column.getCid(), Pair.of(ac, null));
                }
            }
        }
        if (flag) {
            ac.setStatus(AnalysisColumnStatus.S4.getCode());
            payload.getUpdateNotFound().add(ac.getId());
            if (column.isMain()) {
                payload.getMainSubSync().put(column.getCid(), null);
            }
        }
        if (context.isIgnoreCandidate()) {
            return;
        }
        if (CollUtil.isNotEmpty(dto.getResult())) {
            buildCandidates(payload.getInsertCandidate(), column, dto.getResult());
        }
        if (null != classify && null != classify.getAccording()) {
            AnalysisSampleResult asr = new AnalysisSampleResult();
            asr.setResultId(ac.getId());
            asr.setHitData(classify.getAccording());
            if (null != classify.getConfidence()) {
                asr.setHitRate(BigDecimal.valueOf(classify.getConfidence()));
            }
            asr.setRuleType(7);
            payload.getInsertCandidate().add(asr);
        }
        payload.getDeleteAnalysisResultIds().add(ac.getId());
    }

    private void addAnalysisColumnHit(AnalysisResultDTO payload, AnalysisColumn ac, int type, Integer ruleType) {
        if (context.isIgnoreCandidate()) {
            return;
        }
        AnalysisColumnHit hit = new AnalysisColumnHit();
        hit.setPlanId(context.getPlanId());
        hit.setJobId(context.getJobId());
        hit.setAcId(ac.getId());
        hit.setType(type);
        hit.setRuleType(ruleType);
        payload.getInsertHit().add(hit);
    }

    private Integer getHitType(TargetResult hit) {
        switch (hit.getType()) {
            case RULE:
                return 1;
            case COMMENT:
            case COMMENT_SIMILAR:
                return 9;
            case MAPPING:
                return 10;
            case LLM:
            case NAME_SIMILAR:
                return 2;
            default:
                return 5;
        }
    }

    private Integer getRuleType(HitType type) {
        switch (type) {
            case RULE:
                return 1;
            case COMMENT_SIMILAR:
                return 2;
            case MAPPING:
                return 3;
            case NAME_SIMILAR:
                return 4;
            case COMMENT:
                if (!context.isUseLLM()) {
                    break;
                }
            case LLM:
                return 5;
            default:
        }
        return null;
    }

    private int resetRecRate(HitType hit, Double hitRate, int maxScore) {
        int score;
        switch (hit) {
            case RULE:
            case LLM:
                if (null == hitRate) {
                    score = 9000;
                } else {
                    score = (int) (5000 + 5000 * hitRate * hitRate);
                }
                break;
            case COMMENT_SIMILAR:
            case NAME_SIMILAR:
                if (null == hitRate) {
                    score = 8500;
                } else {
                    score = 6000 + (int) (3500 * hitRate * hitRate);
                }
                break;
            case MAPPING:
                if (null == hitRate) {
                    score = 8000;
                } else {
                    score = 6000 + (int) (3000 * Math.sqrt(hitRate));
                }
                break;
            default:
                score = maxScore;
        }
        if (score > maxScore) {
            return maxScore / 500 * 500;
        }
        return score;
    }

    private NyxMetaColumnDTO toMetaColumn(ColumnDTO column, AnalysisColumn analysisColumn) {
        NyxMetaColumnDTO dto = new NyxMetaColumnDTO();
        dto.setId(analysisColumn.getId());
        dto.setCid(analysisColumn.getColumnId());
        dto.setColumnType(buildColumnType(column));
        dto.setColumnName(column.getName());
        dto.setColumnComment(StrUtil.trim(column.getDescription()));
        dto.setLength(column.getLength());
        dto.setPrecision(column.getPrecision());
        dto.setScale(column.getScale());
        dto.setNullable(column.getNullable());
        dto.setPosition(column.getPosition());
        dto.setIsPk(column.getIsPk());
        dto.setIsUnique(column.getIsUnique());
        dto.setIsIndex(column.getIsIndex());
        dto.setIsFk(column.getIsFk());
        dto.setMain(RepeatColumnType.MAIN.equal(analysisColumn.getRepeatType()));
        return dto;
    }

    private ColumnType buildColumnType(ColumnDTO type) {
        DataTypeGroup typeGroup;
        if (null == type.getTypeGroup()) {
            typeGroup = DataTypeGroup.CHARACTER;
        } else {
            typeGroup = DataTypeGroup.getType(type.getTypeGroup());
        }
        return new ColumnType(type.getDataType(), typeGroup);
    }

    private Integer maxLevel(Integer level1, Integer level2) {
        if (null == level1) {
            return level2;
        }
        if (null == level2) {
            return level1;
        }
        return level1 > level2 ? level1 : level2;
    }
}
