package com.mchz.nyx.pipeline.service;


import com.mchz.nyx.pipeline.entity.AnalysisColumn;
import com.mchz.nyx.pipeline.entity.AnalysisFileResult;
import com.mchz.nyx.pipeline.entity.AnalysisTable;
import com.mchz.nyx.pipeline.entity.AnalysisTableClassify;
import com.mchz.nyx.pipeline.model.dto.AnalysisResultDTO;

import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/6/28
 */
public interface AnalysisService {
    /**
     * 查询历史作业关联的schemaIds
     * 增量差异比对
     */
    Set<Long> listJobSchemas(Long planId);

    /**
     * 查询历史作业关联的表格
     * 增量差异比对
     */
    List<AnalysisTable> listJobTable(Long planId, Long schemaId);

    /**
     * 查询历史作业关联的表格
     * 增量差异比对
     */
    List<AnalysisTable> listJobTable(Long planId, Long schemaId, Long tId, boolean increment);

    /**
     * 查询历史作业关联的表格
     * 增量差异比对
     */
    List<AnalysisTable> listJobFile(Long planId, Long schemaId, Long tId, boolean increment);

    /**
     * 查询历史分析列确认情况
     * 增量差异比对
     */
    List<AnalysisColumn> listJobColumnAll(Long planId, Long tableId);

    /**
     * 查询历史分析列确认情况
     * 执行引擎相关
     */
    List<AnalysisColumn> listJobColumn(Long planId, Long tableId);

    /**
     * 插入
     */
    void saveAnalysisTableColumn(List<AnalysisTable> tables, List<AnalysisColumn> columns, List<AnalysisTable> updateTables);

    /**
     * 增量执行
     */
    void saveAnalysisTableColumnIncremental(Long planId, List<AnalysisTable> tables, List<AnalysisTable> updateTable, List<Long> delTableIds, Collection<Long> delColByTableIds, List<AnalysisColumn> columns, List<Long> deleteColumnIds, boolean ignoreCandidate);

    /**
     * 副表相关字段关联主表
     */
    void updateSubColumn(Long planId, Long mid, List<Long> tableIds, String columnName);

    /**
     * 副表相关字段断开关联
     */
    void unlinkSubTableColumn(Long planId, Collection<Long> mid);

    void updateToNormalTable(Long planId, List<Long> tableId);

    /**
     * 移除已不存在的列
     */
    void deleteJobColumnByIds(List<Long> ids, boolean ignoreCandidate);

    /**
     * 根据schemaId删除表与列
     */
    void deleteJobWithSchemaId(Long planId, Collection<Long> schemaIds);

    /**
     * 清除表分类推荐
     */
    void deleteTableClassify(Long planId, Collection<Long> list);

    /**
     * 表分类推荐
     */
    void saveTableClassify(List<AnalysisTableClassify> list);

    /**
     * 文件分类
     */
    void saveFileClassify(AnalysisTable at, List<AnalysisFileResult> list);

    /**
     * 录入分析结果数据
     */
    CompletableFuture<Boolean> saveAnalysisResult(Long planId, Long jobId, AnalysisResultDTO analysisResult, Long defaultClassifyId, Integer defaultLevel);
}
