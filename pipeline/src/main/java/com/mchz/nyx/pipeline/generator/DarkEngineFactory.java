package com.mchz.nyx.pipeline.generator;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.mchz.nyx.dark.engine.HistoryRetrievalEngine;
import com.mchz.nyx.dark.engine.VectorConvEngine;
import com.mchz.nyx.dark.engine.YunCeEngine;
import com.mchz.nyx.dark.engine.algorithm.TrieMatch;
import com.mchz.nyx.dark.factory.AbstractEngineFactory;
import com.mchz.nyx.dark.factory.RangeFunction;
import com.mchz.nyx.dark.factory.TransColumnFun;
import com.mchz.nyx.dark.model.ClassifyInfo;
import com.mchz.nyx.dark.model.TargetInfo;
import com.mchz.nyx.dark.model.config.DarkConfig;
import com.mchz.nyx.dark.model.dto.StdDetailDTO;
import com.mchz.nyx.dark.util.bloomfilter.CustomBloomFilter;
import com.mchz.nyx.pipeline.config.props.EngineProperties;
import com.mchz.nyx.pipeline.entity.EntityMapping;
import com.mchz.nyx.pipeline.generator.engine.GroupHisRetrievalEngine;
import com.mchz.nyx.pipeline.generator.engine.HistoryRetrievalFilterEngine;
import com.mchz.nyx.pipeline.generator.engine.InnerRetrievalFilterEngine;
import com.mchz.nyx.pipeline.service.AnalysisHistoryService;
import com.mchz.nyx.pipeline.service.EntityMappingService;
import com.mchz.nyx.pipeline.service.InnerHistoryService;
import com.mchz.nyx.pipeline.util.cache.EhCacheManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.map.HashedMap;
import org.ehcache.Cache;
import org.ehcache.config.ResourcePools;
import org.ehcache.config.builders.CacheConfigurationBuilder;
import org.ehcache.config.builders.ResourcePoolsBuilder;
import org.ehcache.config.units.MemoryUnit;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.LineNumberReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/11
 */
@Slf4j
public class DarkEngineFactory extends AbstractEngineFactory {
    private static final String CACHE_NAME = "NLP_VECTOR";

    private final RangeFunction rangeFunction;

    private final EntityMappingService entityMappingService;
    private final AnalysisHistoryService analysisHistoryService;
    private final InnerHistoryService innerHistoryService;

    private final EhCacheManager cacheManager;
    private Cache<String, float[]> cache;
    private volatile boolean reload;
    private final AtomicBoolean empty;

    public DarkEngineFactory(EngineProperties config, RangeFunction rangeFunction, EntityMappingService entityMappingService, AnalysisHistoryService analysisHistoryService, InnerHistoryService innerHistoryService, EhCacheManager cacheManager) {
        super(config);
        this.rangeFunction = rangeFunction;
        this.entityMappingService = entityMappingService;
        this.analysisHistoryService = analysisHistoryService;
        this.innerHistoryService = innerHistoryService;
        this.cacheManager = cacheManager;
        this.reload = true;
        this.empty = new AtomicBoolean(true);
    }

    @Override
    public RangeFunction createRangeFunction(DarkConfig config) {
        return rangeFunction;
    }

    @Override
    public VectorConvEngine createSimilarityEngine() {
        Cache<String, float[]> vectorCache = getVectorCache();
        if (null == vectorCache) {
            return null;
        }
        return new NlpEngine(vectorCache);
    }

    @Override
    public YunCeEngine createLLMEngine(DarkConfig darkConfig) {
        EngineProperties properties = getProperties();
        return YunCeClient.of(properties, darkConfig.getUrl(), darkConfig.getLlmUrl(), darkConfig.getIndustryId(), darkConfig.getIndustry());
    }

    @Override
    protected TransColumnFun translateColumnFun(DarkConfig config) {
        if (null == config.getIndustryId() || null == entityMappingService) {
            return null;
        }
        List<EntityMapping> list = entityMappingService.loadAllWithIndex(config.getIndustryId(), 1);
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        TrieMatch<Integer> match = new TrieMatch<>(list);
        return new DefaultTransColumnFun(match, entityMappingService);
    }

    @Override
    public HistoryRetrievalEngine createHistoryRetrievalEngine(DarkConfig config, StdDetailDTO rule) {
        List<HistoryRetrievalEngine> engines = new ArrayList<>(2);
        List<Long> planIds = analysisHistoryService.loadPublishedPlanIds(config.getStdId());
        if (CollUtil.isNotEmpty(planIds)) {
            engines.add(new HistoryRetrievalFilterEngine(getProperties().getExpectNum(), planIds, analysisHistoryService));
        }
        long count = innerHistoryService.loadAllHistoryCount(config.getIndustryId());
        if (count > 0) {
            Map<String, Long> bizMap = rule.getTargets().entrySet().stream().filter(v -> null != v.getValue().getName()).collect(Collectors.toMap(v -> v.getValue().getName(), Map.Entry::getKey, (o1, o2) -> o1));
            Map<String, Long> clsMap = rule.getClassify().stream().filter(v -> null != v.getFullName()).collect(Collectors.toMap(ClassifyInfo::getFullName, ClassifyInfo::getId));
            Map<Integer, TargetInfo> resMap = new HashMap<>();
            CustomBloomFilter filter = new CustomBloomFilter(count, 0.005);
            innerHistoryService.loadAllHistoryResult(config.getIndustryId(), v -> {
                Long bizId = bizMap.get(v.getBizName());
                Long clsId = clsMap.get(v.getClassify());
                if (null != bizId && null != clsId) {
                    resMap.put(v.getId(), new TargetInfo(bizId, null, clsId, v.getLevel()));
                    filter.add(InnerRetrievalFilterEngine.getTcKey(v.getTableName(), v.getColumnName()));
                }
            });
            if (!resMap.isEmpty()) {
                engines.add(new InnerRetrievalFilterEngine(innerHistoryService, filter, resMap));
            }
        }
        if (engines.isEmpty()) {
            return null;
        }
        if (1 == engines.size()) {
            return engines.get(0);
        }
        return new GroupHisRetrievalEngine(engines);
    }

    @Override
    protected List<Map<String, ?>> loadTransDictList() {
        List<Map<String, ?>> res = new ArrayList<>();
        EngineProperties engine = (EngineProperties) config;

        if (StrUtil.isNotEmpty(engine.getChinesePath())) {
            res.add(getDictionary(engine.getChinesePath()));
        }
        if (CollUtil.isNotEmpty(engine.getCustomDictPaths())) {
            engine.getCustomDictPaths().forEach(v -> res.add(getDictionary(v)));
        }
        return res;
    }

    private Cache<String, float[]> getVectorCache() {
        if (reload) {
            synchronized (this) {
                if (reload) {
                    if (null == cache) {
                        ResourcePools resourcePools = ResourcePoolsBuilder.newResourcePoolsBuilder().heap(10, MemoryUnit.MB).disk(2, MemoryUnit.GB).build();
                        cache = cacheManager.getCacheManager().createCache(CACHE_NAME, CacheConfigurationBuilder.newCacheConfigurationBuilder(String.class, float[].class, resourcePools));
                    }
                    reload();
                    reload = false;
                }
            }
        }
        if (empty.get()) {
            return null;
        }
        return cache;
    }

    private void reload() {
        String uri = config.getVectorPath();
        log.info("加载向量文件 {}", uri);
        HttpResponse response;
        InputStream inputStream;
        final String http = "http";
        if (StrUtil.startWith(uri, http, true)) {
            try {
                response = HttpRequest.get(uri).execute(true);
                inputStream = response.bodyStream();
            } catch (Exception e) {
                log.warn("向量文件不存在 {},{}", uri, e.getMessage());
                return;
            }
        } else {
            File file = new File(uri);
            if (!FileUtil.exist(file) || file.isDirectory()) {
                log.warn("向量文件不存在 {}", file.getAbsolutePath());
                return;
            }
            response = null;
            inputStream = FileUtil.getInputStream(file);
        }
        try (LineNumberReader reader = new LineNumberReader(IoUtil.getReader(inputStream, StandardCharsets.UTF_8))) {
            int size = -1;
            while (true) {
                String s = reader.readLine();
                if (null == s) {
                    return;
                }
                String[] split = s.split(StrUtil.SPACE);
                if (size < 0) {
                    empty.compareAndSet(true, Long.parseLong(split[0]) <= 0);
                    size = Integer.parseInt(split[1]) + 1;
                    continue;
                }
                if (split.length != size) {
                    throw new IOException("内容格式错误 " + reader.getLineNumber());
                }
                float[] res = new float[size - 1];
                for (int i = 1; i < size; i++) {
                    res[i - 1] = Float.parseFloat(split[i]);
                }
                cache.put(split[0], res);
            }
        } catch (NumberFormatException | IOException e) {
            log.warn("向量载入失败 {}", e.getMessage());
        } finally {
            IoUtil.close(response);
            IoUtil.close(inputStream);
            log.info("向量文件载入完成");
        }
    }

    private Map<String, String> getDictionary(String filePath) {
        try {
            JSONObject json = JSONUtil.readJSONObject(new File(filePath), StandardCharsets.UTF_8);
            Map<String, String> map = new HashedMap<>();
            json.forEach((s, o) -> map.put(s.toLowerCase(), String.valueOf(o)));
            return map;
        } catch (Exception e) {
            log.error("加载词典失败 {}", filePath, e);
            return new HashMap<>();
        }
    }

    private EngineProperties getProperties() {
        return (EngineProperties) config;
    }
}
