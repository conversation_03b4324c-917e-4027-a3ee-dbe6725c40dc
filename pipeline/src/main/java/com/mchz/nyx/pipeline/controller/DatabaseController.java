package com.mchz.nyx.pipeline.controller;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.mchz.nyx.pipeline.common.api.R;
import com.mchz.nyx.pipeline.model.api.DataSourceParam;
import com.mchz.nyx.pipeline.model.dto.TestConnInfo;
import com.mchz.nyx.pipeline.model.vo.TableVO;
import com.mchz.nyx.pipeline.service.DatasourceService;
import com.mchz.nyx.pipeline.util.Func;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/12/30 21:11
 */
@Slf4j
@RestController
@RequestMapping("/db")
@AllArgsConstructor
public class DatabaseController {

    private final DatasourceService datasourceService;

    /**
     * 數據庫連接测试
     */
    @PostMapping("/connect")
    public R<TestConnInfo> testConnection(@RequestBody DataSourceParam param) {
        if (log.isDebugEnabled()) {
            if (log.isTraceEnabled()) {
                log.debug("{}", JSONUtil.toJsonStr(param));
            } else {
                JSONObject json = JSONUtil.parseObj(param);
                json.set(Func.name(DataSourceParam::getPassword), null);
                log.debug("{}", json);
            }
        }
        return R.success(datasourceService.testConnection(param));
    }

    /**
     * 获取schemas
     */
    @PostMapping("/schemas")
    public R<List<String>> getSchemas(@RequestBody DataSourceParam param) {
        return R.success(datasourceService.loadSchemas(param));
    }

    /**
     * 获取tables
     */
    @PostMapping("/tables")
    public R<List<TableVO>> getTables(@RequestBody DataSourceParam param) {
        return R.success();
    }
}
