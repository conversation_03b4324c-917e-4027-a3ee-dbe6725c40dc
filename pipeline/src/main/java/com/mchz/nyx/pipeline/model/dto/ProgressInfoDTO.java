package com.mchz.nyx.pipeline.model.dto;

import com.mchz.nyx.pipeline.model.log.JobLogInfoDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 作业日志表
 * </p>
 *
 * <AUTHOR>
 * @date 2020/11/5 14:07
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProgressInfoDTO {
    /**
     * 租户Id -> 集群->多租户
     */
    private String tenantId;

    /**
     * 源数据发现作业id
     */
    private Long jobId;

    /**
     * 百分比
     */
    private BigDecimal percent;

    /**
     * 日志内容
     */
    private List<JobLogInfoDTO> content;
}
