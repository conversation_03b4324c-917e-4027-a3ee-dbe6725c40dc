package com.mchz.nyx.pipeline.common.api;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.io.Serializable;
import java.util.Optional;

/**
 * <p>
 * 统一API响应结果封装
 * </p>
 *
 * @param <T> 泛型参数
 * <AUTHOR>
 * Created in 2019-07-12 16:23
 */
@Getter
@Setter
@ToString
@NoArgsConstructor
@ApiModel(description = "返回信息")
public final class R<T> implements Serializable {

    private static final long serialVersionUID = -7618165659758377287L;

    /**
     * 状态码
     */
    @ApiModelProperty(value = "状态码", required = true)
    private int code;
    /**
     * 返回消息
     */
    @ApiModelProperty(value = "返回消息", required = true)
    private String msg;
    /**
     * 承载数据
     */
    @ApiModelProperty(value = "承载数据")
    private T data;

    private R(IResultCode resultCode) {
        this(resultCode, resultCode.getMsg(), null);
    }

    private R(IResultCode resultCode, String msg) {
        this(resultCode, msg, null);
    }

    private R(IResultCode resultCode, T data) {
        this(resultCode, resultCode.getMsg(), data);
    }

    private R(IResultCode resultCode, String msg, T data) {
        this.code = resultCode.getCode();
        this.msg = msg;
        this.data = data;
    }

    private R(int code, T data, String msg) {
        this.code = code;
        this.data = data;
        this.msg = msg;
    }

    /**
     * 判断返回是否为成功
     *
     * @param result Result
     * @return 是否成功
     */
    public static boolean isSuccess(R<?> result) {
        return result.isSuccess();
    }

    /**
     * 判断返回是否为成功
     *
     * @param result Result
     * @return 是否成功
     */
    public static boolean isNotSuccess(R<?> result) {
        return !R.isSuccess(result);
    }

    /**
     * 获取data
     *
     * @param result Result
     * @param <T>    泛型标记
     * @return 泛型对象
     */
    public static <T> T getData(R<T> result) {
        return Optional.ofNullable(result).filter(r -> r.isSuccess()).map(x -> x.getData()).orElse(null);
    }

    /**
     * 返回成功
     *
     * @param <T> 泛型标记
     * @return Result
     */
    public static <T> R<T> success() {
        return new R<>(CommonResultCode.SUCCESS);
    }

    /**
     * 返回成功
     *
     * @param <T> 泛型参数
     * @param msg 成功消息
     * @return Result
     */
    public static <T> R<T> message(String msg) {
        return new R<>(CommonResultCode.SUCCESS.getCode(), null, msg);
    }

    /**
     * 成功-携带数据
     *
     * @param data 数据
     * @param <T>  泛型标记
     * @return Result
     */
    public static <T> R<T> success(T data) {
        return new R<>(CommonResultCode.SUCCESS, data);
    }

    /**
     * 根据状态返回成功或者失败
     *
     * @param status 状态
     * @param msg    异常msg
     * @param <T>    泛型标记
     * @return Result
     */
    public static <T> R<T> status(boolean status, String msg) {
        return status ? R.success() : R.fail(msg);
    }

    /**
     * 根据状态返回成功或者失败
     *
     * @param status 状态
     * @param sCode  异常code码
     * @param <T>    泛型标记
     * @return Result
     */
    public static <T> R<T> status(boolean status, IResultCode sCode) {
        return status ? R.success() : R.fail(sCode);
    }

    /**
     * 返回失败
     *
     * @param <T> 泛型标记
     * @return Result
     */
    public static <T> R<T> fail() {
        return new R<>(CommonResultCode.FAILURE);
    }

    /**
     * 返回失败信息，用于 web
     *
     * @param msg 失败信息
     * @param <T> 泛型标记
     * @return {Result}
     */
    public static <T> R<T> fail(String msg) {
        return new R<>(CommonResultCode.FAILURE, msg);
    }

    /**
     * 返回失败信息
     *
     * @param code 状态码
     * @param msg  失败信息
     * @param <T>  泛型标记
     * @return {Result}
     */
    public static <T> R<T> fail(int code, String msg) {
        return new R<>(code, null, msg);
    }

    /**
     * 返回失败信息
     *
     * @param code 状态码
     * @param msg  失败信息
     * @param data 数据
     * @param <T>  泛型标记
     * @return {Result}
     */
    public static <T> R<T> fail(int code, String msg, T data) {
        return new R<>(code, data, msg);
    }

    /**
     * 返回失败信息
     *
     * @param rCode 异常枚举
     * @param <T>   泛型标记
     * @return {Result}
     */
    public static <T> R<T> fail(IResultCode rCode) {
        return new R<>(rCode, rCode.getMsg());
    }

    /**
     * 返回失败信息
     *
     * @param rCode 异常枚举
     * @param msg   失败信息
     * @param <T>   泛型标记
     * @return {Result}
     */
    public static <T> R<T> fail(IResultCode rCode, String msg) {
        return new R<>(rCode, msg);
    }

    /**
     * 返回失败信息
     *
     * @param rCode 异常枚举
     * @param data  数据
     * @param <T>   泛型标记
     * @return {Result}
     */
    public static <T> R<T> fail(IResultCode rCode, T data) {
        return new R<>(rCode, data);
    }

    /**
     * 返回失败信息
     *
     * @param rCode 异常枚举
     * @param msg   失败信息
     * @param data  数据
     * @param <T>   泛型标记
     * @return {Result}
     */
    public static <T> R<T> fail(IResultCode rCode, String msg, T data) {
        return new R<>(rCode, msg, data);
    }

    public boolean isSuccess() {
        return CommonResultCode.SUCCESS.getCode() == code;
    }
}
