package com.mchz.nyx.pipeline.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mchz.nyx.pipeline.common.constants.PipelineConst;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024-07-29
 */
@Data
@TableName(value = "ref_data_item" , schema = PipelineConst.EMBED_SCHEMA)
public class RefDataItem implements Serializable {
    @TableId("id")
    private Long id;

    private Long rdsId;

    private String code;

    private String name;
}
