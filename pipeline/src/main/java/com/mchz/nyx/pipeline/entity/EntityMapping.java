package com.mchz.nyx.pipeline.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mchz.nyx.dark.engine.algorithm.TrieData;
import com.mchz.nyx.pipeline.common.constants.PipelineConst;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/18
 */
@Data
@TableName(value = "entity_mapping", schema = PipelineConst.EMBED_SCHEMA)
public class EntityMapping implements TrieData<Integer>, Serializable {

    /**
     * 主键ID
     */
    @TableId("id")
    private Integer id;

    /**
     * 行业ID
     */
    private Integer industryId;

    /**
     * 类型
     */
    private Integer cate;

    /**
     * 名称
     */
    private String name;

    /**
     * 中文名称
     */
    private String cname;

    @Override
    public Integer getResult() {
        return id;
    }

    @Override
    public String getValue() {
        return name;
    }
}
