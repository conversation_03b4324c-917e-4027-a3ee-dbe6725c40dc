package com.mchz.nyx.pipeline.common;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.config.GlobalConfig;
import com.baomidou.mybatisplus.core.handlers.PostInitTableInfoHandler;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.mchz.nyx.pipeline.common.constants.PipelineConst;
import lombok.AllArgsConstructor;
import org.apache.ibatis.session.Configuration;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/29
 */
@AllArgsConstructor
public class CustomPostInitTableInfoHandler implements PostInitTableInfoHandler {
    private GlobalConfig.DbConfig dbConfig;
    private String dtf;
    private String dcf;
    private String etf;
    private String ecf;

    @Override
    public TableInfo creteTableInfo(Configuration configuration, Class<?> entityType) {
        TableName tableName = entityType.getAnnotation(TableName.class);
        if (null != tableName && PipelineConst.EMBED_SCHEMA.equals(tableName.schema())) {
            dbConfig.setTableFormat(etf);
            dbConfig.setColumnFormat(ecf);
        } else {
            dbConfig.setTableFormat(dtf);
            dbConfig.setColumnFormat(dcf);
        }
        return PostInitTableInfoHandler.super.creteTableInfo(configuration, entityType);
    }
}
