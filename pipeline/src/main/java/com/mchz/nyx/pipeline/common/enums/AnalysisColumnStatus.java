package com.mchz.nyx.pipeline.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/6/28
 */
@Getter
@AllArgsConstructor
public enum AnalysisColumnStatus {
    /**
     * 人工确认
     */
    S1(1),
    /**
     * 自动确认
     */
    S2(2),
    /**
     * 发现有结果
     */
    S3(3),
    /**
     * 发现无结果
     */
    S4(4),
    /**
     * 未进行发现
     */
    S5(5);
    private final Integer code;
}
