package com.mchz.nyx.pipeline.generator.meta;

import com.mchz.nyx.meta.common.enums.MetaObjType;
import com.mchz.nyx.meta.exception.CrawlerException;
import com.mchz.nyx.meta.instance.crawl.DsMetaAdapter;
import com.mchz.nyx.meta.instance.crawl.NyxCrawlAgent;
import com.mchz.nyx.meta.model.meta.CatalogSchema;
import com.mchz.nyx.meta.support.DataSourceConfig;
import com.mchz.nyx.meta.support.SchemaTables;
import com.mchz.nyx.pipeline.common.constants.PipelineConst;
import com.mchz.nyx.pipeline.entity.FileUploadDetail;
import com.mchz.nyx.pipeline.model.vo.TableVO;
import com.mchz.nyx.pipeline.service.FileManagerService;
import lombok.AllArgsConstructor;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 非结构化文件
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/11
 */
@AllArgsConstructor
public class UnFileMetaAdapter implements MetaLookupService, DsMetaAdapter {
    private final FileManagerService fileManagerService;

    @Override
    public List<String> loadSchema(DataSourceConfig source) {
        return Collections.singletonList(PipelineConst.DEFAULT_SCHEMA);
    }

    @Override
    public List<TableVO> loadTable(DataSourceConfig source) {
        List<FileUploadDetail> list = fileManagerService.listFileDetail(source.getSourceId());
        return list.stream().map(v -> new TableVO(PipelineConst.DEFAULT_SCHEMA, fileName(v))).collect(Collectors.toList());
    }

    @Override
    public void execute(NyxCrawlAgent agent, DataSourceConfig source, int type, List<SchemaTables> schemaTables) {
        CatalogSchema catalogSchema = defaultSchema();
        agent.schema(Collections.singletonList(catalogSchema), Collections.emptyList());
        List<FileUploadDetail> list = fileManagerService.listFileDetail(source.getSourceId());
        agent.crawlTableName(catalogSchema, list.stream().map(this::fileName).collect(Collectors.toList()), MetaObjType.TABLE);
    }

    private String fileName(FileUploadDetail file) {
        if (file.getName().length() > PipelineConst.MAX_META_NAME_LEN) {
            throw new CrawlerException("元数据名称超长" + file.getName().length());
        }
        return file.getName();
    }

    private CatalogSchema defaultSchema() {
        return CatalogSchema.of(PipelineConst.DEFAULT_SCHEMA);
    }
}
