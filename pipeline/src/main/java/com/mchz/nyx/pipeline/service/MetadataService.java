package com.mchz.nyx.pipeline.service;


import com.mchz.nyx.meta.entity.DbMetaColumn;
import com.mchz.nyx.meta.entity.DbMetaObject;
import com.mchz.nyx.meta.support.SchemaTables;
import com.mchz.nyx.pipeline.model.dto.*;

import java.util.Collection;
import java.util.List;
import java.util.function.BiConsumer;
import java.util.function.Function;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/6/28
 */
public interface MetadataService {
    /**
     * 范围
     */
    List<SchemaScopeDTO> getSchemaTableScope(Long sourceId, List<SchemaTables> scope, boolean ignoreTable);

    List<DbMetaObject> listMetaTableObj(Long sourceId, Long schemaId);

    List<DbMetaObject> listMetaTableDes(IdsQueryDTO idsQuery);

    List<DbMetaObject> listMetaColumnObj(Long sourceId, Long tableId);

    default void listMetaColumnObj(Long sourceId, Collection<DbMetaObject> tableObjs, BiConsumer<DbMetaObject, List<DbMetaObject>> fun) {
        listMetaColumnObj(sourceId, tableObjs, DbMetaObject::getOid, fun);
    }

    <T> void listMetaColumnObj(Long sourceId, Collection<T> tableObjs, Function<T, Long> getIdFun, BiConsumer<T, List<DbMetaObject>> fun);

    /**
     * 查询指定schema详情信息（分析作业 需要较为详细）
     */
    List<CatalogSchemaDTO> listMetaSchema(List<Long> schemaIds);

    /**
     * 查询指定schema详情信息（分析作业 需要较为详细）
     */
    List<CatalogSchemaDTO> listMetaSchema(Long sourceId, Long minSchemaId);

    List<SimpleTableDTO> listMetaTable(List<Long> tableIds);

    List<SimpleTableDTO> listMetaTable(Long sourceId, Long schemaId, Long minTableId);

    List<SimpleColumnInfo> listSimpleMetaColumn(Long sourceId, Collection<Long> tableIds);

    /**
     * 根据列查询详情
     */
    List<ColumnDTO> listMetaColumn(Long sourceId, Collection<Long> columnId);

    /**
     * 更新元数据 列类型
     */
    void updateColumn(List<DbMetaColumn> list);
}
