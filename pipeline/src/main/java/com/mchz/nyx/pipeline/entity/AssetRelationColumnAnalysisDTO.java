package com.mchz.nyx.pipeline.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 资产关系作业的列分析
 */
@Data
@TableName("asset_relation_column_analysis")
public class AssetRelationColumnAnalysisDTO implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 作业id
     */
    private Long jobId;

    /**
     * 元数据表id
     */
    private Long metaTableId;

    /**
     * 元数据列id
     */
    private Long metaColumnId;

    /**
     * 关联元数据表id
     */
    private Long relationMetaTableId;

    /**
     * 关联元数据列id
     */
    private Long relationMetaColumnId;

    /**
     * 识别方式：1=主外键；2=业务类型匹配；3=字段名匹配
     */
    private Integer identifyMode;

    public interface IdentifyMode {
        /** 主外键 */
        int FOREIGN_KEY = 1;
        /** 业务类型匹配 */
        int BIZ_TYPE = 2;
        /** 字段名匹配 */
        int COLUMN_NAME = 3;
    }
}
