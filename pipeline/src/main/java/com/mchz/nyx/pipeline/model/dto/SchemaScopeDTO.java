package com.mchz.nyx.pipeline.model.dto;

import com.mchz.nyx.meta.entity.DbMetaObject;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/6/27
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class SchemaScopeDTO {
    private DbMetaObject schema;
    private List<DbMetaObject> tables;

    public SchemaScopeDTO(DbMetaObject schema) {
        this.schema = schema;
    }
}
