package com.mchz.nyx.pipeline.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.mchz.nyx.pipeline.common.api.R;
import com.mchz.nyx.pipeline.common.enums.JobType;
import com.mchz.nyx.pipeline.common.enums.MergeStrategy;
import com.mchz.nyx.pipeline.job.JobStartConfig;
import com.mchz.nyx.pipeline.job.TaskSchedulingManager;
import com.mchz.nyx.pipeline.model.api.DataSourceParam;
import com.mchz.nyx.pipeline.model.log.ProcessingTable;
import com.mchz.nyx.pipeline.model.param.JobStartParam;
import com.mchz.nyx.pipeline.util.Func;
import com.mchz.starter.tenant.util.TenantUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Objects;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/10/20 16:18
 */
@Slf4j
@RestController
@RequestMapping("/discovery/job")
@AllArgsConstructor
public class TaskController {

    private final TaskSchedulingManager manager;

    /**
     * 发布任务
     *
     * @param param 参数
     */
    @PostMapping(params = "start")
    public R<Boolean> releaseTask(@RequestBody JobStartParam param) {
        if (log.isDebugEnabled()) {
            if (log.isTraceEnabled()) {
                log.debug("{}", JSONUtil.toJsonStr(param));
            } else {
                JSONObject json = JSONUtil.parseObj(param);
                JSONObject source = json.getJSONObject(Func.name(JobStartParam::getSource));
                if (null != source) {
                    source.set(Func.name(DataSourceParam::getPassword), null);
                }
                log.debug("{}", json);
            }
        }
        manager.submit(convertFromJobStartParam(param));
        return R.success();
    }

    /**
     * 暂停任务
     *
     * @param jobId 任务唯一标识
     */
    @PostMapping(params = "pause")
    public R<Boolean> pauseTask(@RequestParam Long jobId) {
        manager.terminated(jobId);
        return R.success();
    }

    /**
     * 终止任务
     *
     * @param jobId 任务唯一标识
     */
    @PostMapping(params = "stop")
    public R<Boolean> stopTask(@RequestParam Long jobId) {
        manager.terminated(jobId);
        return R.success();
    }

    /**
     * 获取处理中表格
     *
     * @param jobId 任务唯一标识
     */
    @GetMapping("/processingTable")
    public R<ProcessingTable> getProcessingTable(@RequestParam Long jobId) {
        return R.success(manager.getProcessingTable(jobId));
    }

    private static JobStartConfig convertFromJobStartParam(JobStartParam jobStartParam) {
        JobStartConfig jobConfig = new JobStartConfig();
        BeanUtil.copyProperties(jobStartParam, jobConfig, CopyOptions.create().ignoreNullValue().setIgnoreProperties(Func.name(JobStartConfig::getMergeStrategy)));
        jobConfig.setTenantId(TenantUtil.tenant());
        if (null == jobConfig.getIgnoreMeta() && JobType.DISCOVERY.equals(jobConfig.getJobType())) {
            jobConfig.setIgnoreMeta(Boolean.TRUE);
        }

        if (null == jobConfig.getNlp()) {
            jobConfig.setNlp(null != jobStartParam.getColumnBaseline() || null != jobStartParam.getCommentBaseline());
        }

        jobConfig.setClassify(jobConfig.getNlp() && Objects.equals(0, jobStartParam.getTableClassify()) && null != jobConfig.getTableClassifyBaseline());

        if (null != jobStartParam.getContentStrategy()) {
            jobConfig.setIgnoreData(0 == jobStartParam.getContentStrategy());
        }

        if (null != jobStartParam.getSaveStrategy()) {
            if (JobType.DISCOVERY.equals(jobConfig.getJobType())) {
                jobConfig.setIgnoreSample(0 == jobStartParam.getSaveStrategy());
            } else if (JobType.COLLECT_META.equals(jobConfig.getJobType())) {
                jobConfig.setIgnoreData(1 == jobStartParam.getSaveStrategy());
            }
        }

        if (null != jobStartParam.getMergeStrategy()) {
            if (2 == jobStartParam.getMergeStrategy()) {
                jobConfig.setMergeStrategy(MergeStrategy.COVER);
            } else {
                jobConfig.setMergeStrategy(MergeStrategy.MERGE);
            }
        }
        return jobConfig;
    }
}
