package com.mchz.nyx.pipeline.model.api;

import com.mchz.nyx.pipeline.entity.ResultMetaColumn;
import com.mchz.nyx.pipeline.util.Func;
import lombok.Data;
import lombok.NonNull;

import java.util.List;

/**
 * <p>
 * 获取数据源指定表格数据
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/10/27 14:55
 */
@Data
public class TableDataSetParam {
    private Long jobId;
    private DataSourceParam source;
    private String catalog;
    private String schema;
    private String table;
    private List<String> pkColumns;
    private List<ResultMetaColumn> columns;
    private Integer size;
    private Integer sampleRate;


    /**
     * 默认3层为schema（比如mysql为catalog依旧将值放入schema）
     *
     * @param catalogSchema catalog.schema
     */
    public void setCatalogSchema(@NonNull String catalogSchema) {
        String[] catalogAndSchema = Func.splitCatalogAndSchema(source.getDataBaseType(), catalogSchema);
        this.catalog = catalogAndSchema[0];
        this.schema = catalogAndSchema[1];
    }

    public String getFullName() {
        if (null != catalog) {
            return String.format("%s.%s.%s", catalog, schema, table);
        }
        if (null != schema) {
            return String.format("%s.%s", schema, table);
        }
        return table;
    }
}
