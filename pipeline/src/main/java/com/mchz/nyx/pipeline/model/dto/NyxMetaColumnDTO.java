package com.mchz.nyx.pipeline.model.dto;

import com.mchz.nyx.dark.model.meta.ColumnType;
import com.mchz.nyx.dark.model.meta.NyxMetaColumn;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/1
 */
@Data
public class NyxMetaColumnDTO implements NyxMetaColumn, ColumnInfo {
    /**
     * id
     */
    private Long id;

    /**
     * 列id
     */
    private Long cid;

    /**
     * 列名字
     */
    private String columnName;

    /**
     * 列类型
     */
    private ColumnType columnType;

    /**
     * 列长度
     */
    private Long length;

    /**
     * 刻度值
     */
    private Integer scale;

    /**
     * 精度
     */
    private Integer precision;

    /**
     * 是否可以为空，0 - 否，1 - 是
     */
    private Boolean nullable;

    /**
     * 列注释
     */
    private String columnComment;

    /**
     * 列位置
     */
    private Integer position;

    /**
     * 是否主键列，0 - 否，1 - 是
     */
    private Boolean isPk;

    /**
     * 是否唯一列，0 - 否，1 - 是
     */
    private Boolean isUnique;

    /**
     * 是否索引列，0 - 否，1 - 是
     */
    private Boolean isIndex;

    /**
     * 是否外键，0 - 否，1 - 是
     */
    private Boolean isFk;

    private boolean main;

    @Override
    public String toString() {
        return String.format("%d(%d)-%s(%s)", getId(), getCid(), getColumnName(), getColumnComment());
    }
}
