package com.mchz.nyx.pipeline.util.cache;

import cn.hutool.core.collection.ListUtil;
import com.mchz.nyx.pipeline.util.Lists;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.ehcache.Cache;
import org.jetbrains.annotations.NotNull;

import java.io.Closeable;
import java.io.Serializable;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Consumer;

@Slf4j
@NoArgsConstructor
public class ListCache<V extends Serializable> implements Iterable<V>, Closeable {
    public static final ListCache EMPTY = new EmptyListCache();
    private final AtomicInteger partIndex = new AtomicInteger(0);
    private int size = 0;
    private int modCount = 0;
    private boolean closed = false;

    private EhCacheManager cacheManager;
    private String cacheName;
    private Cache<ListKey, ArrayList> cache;
    @Getter
    private int partSize;

    @Setter
    private ClassLoader classLoader;

    public ListCache(EhCacheManager cacheManager, String cacheName, int partSize, Class<V> valueType) {
        Objects.requireNonNull(valueType);
        this.cacheManager = Objects.requireNonNull(cacheManager);
        this.cacheName = Objects.requireNonNull(cacheName);
        this.cache = cacheManager.createCache(cacheName, ListKey.class, ArrayList.class);
        this.partSize = partSize;
    }

    public synchronized void consume(Consumer<List<V>> action, int size) {
        ClassLoader currentClassLoader = Thread.currentThread().getContextClassLoader();
        try {
            if (Objects.nonNull(classLoader)) {
                Thread.currentThread().setContextClassLoader(classLoader);
            }
            checkClosed();
            List<V> temp = new ArrayList<>();
            for (int i = 1; i <= partIndex.get(); i++) {
                ArrayList<V> value = cache.get(key(i));
                if (Objects.isNull(value)) {
                    log.warn(String.format("%s(%s) 分片数据为空", i, partSize));
                    continue;
                }
                temp.addAll(value);
                List<List<V>> partition = ListUtil.partition(temp, size);
                if (temp.size() % size != 0) {
                    temp = partition.get(partition.size() - 1);
                    partition = partition.subList(0, partition.size() - 1);
                }
                partition.forEach(action);
                if (temp.size() % size == 0) {
                    temp.clear();
                }
                cache.remove(key(i));
            }
            if (CollectionUtils.isNotEmpty(temp)) {
                action.accept(temp);
            }
            close();
        } finally {
            Thread.currentThread().setContextClassLoader(currentClassLoader);
        }
    }

    public synchronized void add(V v) {
        checkClosed();
        addAll(Lists.newArrayList(v));
    }

    public synchronized void addAll(List<V> list) {
        checkClosed();
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        modCount++;
        if (!partFull()) {
            int i = makeUpPart(list);
            size += i;
            list = list.subList(i, list.size());
        }
        add(list);
        size += list.size();
    }

    @Override
    public synchronized void close() {
        try {
            cacheManager.removeCache(cacheName);
            closed = true;
        } catch (Exception e) {
            log.warn(String.format("%s %s %s", "ListCache", cacheName, "close"), e.getMessage());
        }
    }

    private int makeUpPart(List<V> list) {
        int lackNum = Math.min(partSize - (size % partSize), list.size());
        list = list.subList(0, lackNum);

        int partNum = partIndex.get();
        ListKey key = key(partNum);
        ArrayList<V> value = cache.get(key);
        value.addAll(list);
        cache.replace(key, value);
        return lackNum;
    }

    private void add(List<V> list) {
        Lists.partition(list, partSize).forEach(part -> {
            int partNum = partIndex.incrementAndGet();
            cache.put(key(partNum), Lists.newArrayList(part));
        });
    }

    private boolean partFull() {
        return size % partSize == 0;
    }

    private ListKey key(int partNum) {
        return new ListKey(partNum);
    }

    private void checkClosed() {
        if (closed) {
            throw new IllegalStateException("closed");
        }
    }

    @NotNull
    @Override
    public Iterator<V> iterator() {
        return new Iterator<V>() {
            final int expectedModCount = modCount;
            int index = 0;

            @Override
            public boolean hasNext() {
                return index != size;
            }

            @Override
            public V next() {
                checkMod();
                if (!hasNext()) {
                    throw new NoSuchElementException();
                }
                ArrayList<V> list = cache.get(key(index / partSize + 1));
                V v = list.get(index % partSize);
                index++;
                return v;
            }

            void checkMod() {
                if (modCount != expectedModCount) {
                    throw new ConcurrentModificationException();
                }
            }
        };
    }

    public int size() {
        return size;
    }

    @Data
    @RequiredArgsConstructor
    private static class ListKey implements Serializable {
        private static final long serialVersionUID = -953717823393724300L;
        private final int partNum;
    }

    private static class EmptyListCache extends ListCache {
        @Override
        public synchronized void consume(Consumer action, int size) {
        }
    }
}
