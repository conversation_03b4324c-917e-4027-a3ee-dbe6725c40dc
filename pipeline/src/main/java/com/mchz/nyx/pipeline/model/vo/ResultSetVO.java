package com.mchz.nyx.pipeline.model.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 结果集
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/10/26 15:05
 */
@Data
@NoArgsConstructor
public class ResultSetVO implements Serializable {
    private static final long serialVersionUID = 2674206255829837883L;

    private ClassLoader classLoader;
    private List<String> metadata;
    private List<List<Object>> data;

    public ResultSetVO(List<String> metadata, List<List<Object>> data) {
        this.metadata = metadata;
        this.data = data;
    }

    public ResultSetVO(ClassLoader classLoader, List<String> metadata, List<List<Object>> data) {
        this.classLoader = classLoader;
        this.metadata = metadata;
        this.data = data;
    }

    public void addMetadata(String name) {
        this.metadata.add(name);
    }

    public void addData(List<Object> data) {
        this.data.add(data);
    }

    public boolean isEmpty() {
        return null == data || data.isEmpty();
    }
}
