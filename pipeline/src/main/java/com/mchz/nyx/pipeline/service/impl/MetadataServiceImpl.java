package com.mchz.nyx.pipeline.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.toolkit.JoinWrappers;
import com.github.yulichang.toolkit.MPJWrappers;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.mchz.nyx.common.util.CommUtil;
import com.mchz.nyx.common.util.PartitionUtil;
import com.mchz.nyx.meta.common.enums.MetaObjType;
import com.mchz.nyx.meta.entity.DbMetaColumn;
import com.mchz.nyx.meta.entity.DbMetaObject;
import com.mchz.nyx.meta.entity.DbMetaSchema;
import com.mchz.nyx.meta.entity.DbMetaTable;
import com.mchz.nyx.meta.support.SchemaTables;
import com.mchz.nyx.pipeline.common.constants.PipelineConst;
import com.mchz.nyx.pipeline.config.props.SourceProperties;
import com.mchz.nyx.pipeline.mapper.DbMetaColumnMapper;
import com.mchz.nyx.pipeline.mapper.DbMetaObjectMapper;
import com.mchz.nyx.pipeline.mapper.DbMetaSchemaMapper;
import com.mchz.nyx.pipeline.mapper.DbMetaTableMapper;
import com.mchz.nyx.pipeline.model.dto.*;
import com.mchz.nyx.pipeline.service.MetadataService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2022/1/7 11:46
 */
@Slf4j
@Service
@AllArgsConstructor
public class MetadataServiceImpl implements MetadataService {
    private final SourceProperties properties;
    private final DbMetaObjectMapper dbMetaObjectMapper;
    private final DbMetaColumnMapper dbMetaColumnMapper;
    private final DbMetaSchemaMapper dbMetaSchemaMapper;
    private final DbMetaTableMapper dbMetaTableMapper;

    @Override
    public List<SchemaScopeDTO> getSchemaTableScope(Long sourceId, List<SchemaTables> scope, boolean ignoreTable) {
        if (CollUtil.isEmpty(scope)) {
            Wrapper<DbMetaObject> q = Wrappers.lambdaQuery(DbMetaObject.class)
                .select(DbMetaObject::getOid, DbMetaObject::getName)
                .eq(DbMetaObject::getSourceId, sourceId)
                .eq(DbMetaObject::getPid, PipelineConst.SCHEMA_PID)
                .eq(DbMetaObject::getType, MetaObjType.SCHEMA.name())
                .eq(DbMetaObject::getDeleted, Boolean.FALSE);
            List<DbMetaObject> list = dbMetaObjectMapper.selectList(q);
            return list.stream().map(SchemaScopeDTO::new).collect(Collectors.toList());
        }
        boolean view = properties.getSys().isEnableCollectView();
        return PartitionUtil.reduce(scope, v -> {
            Wrapper<DbMetaObject> q = Wrappers.lambdaQuery(DbMetaObject.class)
                .select(DbMetaObject::getOid, DbMetaObject::getName)
                .eq(DbMetaObject::getSourceId, sourceId)
                .eq(DbMetaObject::getType, MetaObjType.SCHEMA.name())
                .in(DbMetaObject::getName, v.stream().map(schema -> schema.getSchema().getName()).collect(Collectors.toList()))
                .eq(DbMetaObject::getDeleted, Boolean.FALSE);
            List<DbMetaObject> list = dbMetaObjectMapper.selectList(q);
            if (ignoreTable) {
                return list.stream().map(SchemaScopeDTO::new).collect(Collectors.toList());
            }
            Map<String, List<String>> map = v.stream().filter(t -> CollUtil.isNotEmpty(t.getTables())).collect(Collectors.toMap(t -> t.getSchema().getName(), SchemaTables::getTables));
            List<SchemaScopeDTO> reduce = new ArrayList<>(list.size());
            for (DbMetaObject schema : list) {
                List<String> tables = map.get(schema.getName());
                if (null == tables) {
                    reduce.add(new SchemaScopeDTO(schema));
                } else {
                    List<DbMetaObject> tableObjs = PartitionUtil.reduce(tables, t -> getTableObjs(sourceId, schema.getOid(), t, view));
                    reduce.add(new SchemaScopeDTO(schema, tableObjs));
                }
            }
            return reduce;
        });
    }

    @Override
    public List<DbMetaObject> listMetaTableObj(Long sourceId, Long schemaId) {
        boolean view = properties.getSys().isEnableCollectView();
        return getTableObjs(sourceId, schemaId, null, view);
    }

    @Override
    public List<DbMetaObject> listMetaTableDes(IdsQueryDTO idsQuery) {
        LambdaQueryWrapper<DbMetaObject> q = Wrappers.lambdaQuery(DbMetaObject.class)
            .select(DbMetaObject::getOid, DbMetaObject::getName, DbMetaObject::getDescription)
            .in(null != idsQuery.getIds(), DbMetaObject::getOid, idsQuery.getIds())
            .ge(null != idsQuery.getStart(), DbMetaObject::getOid, idsQuery.getStart())
            .le(null != idsQuery.getEnd(), DbMetaObject::getOid, idsQuery.getEnd());
        return dbMetaObjectMapper.selectList(q);
    }

    @Override
    public List<DbMetaObject> listMetaColumnObj(Long sourceId, Long tableId) {
        LambdaQueryWrapper<DbMetaObject> q = Wrappers.lambdaQuery(DbMetaObject.class)
            .select(DbMetaObject::getOid, DbMetaObject::getName)
            .eq(DbMetaObject::getSourceId, sourceId)
            .eq(DbMetaObject::getPid, tableId)
            .eq(DbMetaObject::getType, MetaObjType.COLUMN.name())
            .eq(DbMetaObject::getDeleted, Boolean.FALSE);
        return dbMetaObjectMapper.selectList(q);
    }

    @Override
    public <T> void listMetaColumnObj(Long sourceId, Collection<T> tableObjs, Function<T, Long> getIdFun, BiConsumer<T, List<DbMetaObject>> fun) {
        PartitionUtil.part(tableObjs, t -> {
            LambdaQueryWrapper<DbMetaObject> q = Wrappers.lambdaQuery(DbMetaObject.class)
                .select(DbMetaObject::getOid, DbMetaObject::getName, DbMetaObject::getPid)
                .eq(DbMetaObject::getSourceId, sourceId)
                .in(DbMetaObject::getPid, t.stream().map(getIdFun).collect(Collectors.toList()))
                .eq(DbMetaObject::getType, MetaObjType.COLUMN.name())
                .eq(DbMetaObject::getDeleted, Boolean.FALSE);
            List<DbMetaObject> list = dbMetaObjectMapper.selectList(q);
            Map<Long, List<DbMetaObject>> map = CommUtil.groupByPid(list, DbMetaObject::getPid);
            t.forEach(v -> fun.accept(v, map.getOrDefault(getIdFun.apply(v), Collections.emptyList())));
        });
    }

    @Override
    public List<CatalogSchemaDTO> listMetaSchema(List<Long> schemaIds) {
        if (CollUtil.isEmpty(schemaIds)) {
            return new ArrayList<>(0);
        }
        MPJLambdaWrapper<DbMetaSchema> q = MPJWrappers.lambdaJoin(DbMetaSchema.class)
            .select(DbMetaObject::getOid, DbMetaObject::getName)
            .select(DbMetaSchema::getCatalog, DbMetaSchema::getSchema)
            .innerJoin(DbMetaObject.class, DbMetaObject::getOid, DbMetaSchema::getOid)
            .in(DbMetaSchema::getOid, schemaIds)
            .eq(DbMetaObject::getDeleted, Boolean.FALSE);
        return dbMetaSchemaMapper.selectJoinList(CatalogSchemaDTO.class, q);
    }

    @Override
    public List<CatalogSchemaDTO> listMetaSchema(Long sourceId, Long minSchemaId) {
        if (null == sourceId) {
            return new ArrayList<>(0);
        }
        MPJLambdaWrapper<DbMetaSchema> q = MPJWrappers.lambdaJoin(DbMetaSchema.class)
            .select(DbMetaObject::getOid, DbMetaObject::getName)
            .select(DbMetaSchema::getCatalog, DbMetaSchema::getSchema)
            .innerJoin(DbMetaObject.class, DbMetaObject::getOid, DbMetaSchema::getOid)
            .eq(DbMetaSchema::getSourceId, sourceId)
            .ge(null != minSchemaId, DbMetaSchema::getOid, minSchemaId)
            .eq(DbMetaObject::getDeleted, Boolean.FALSE);
        return dbMetaSchemaMapper.selectJoinList(CatalogSchemaDTO.class, q);
    }

    @Override
    public List<SimpleTableDTO> listMetaTable(List<Long> tableIds) {
        if (CollUtil.isEmpty(tableIds)) {
            return new ArrayList<>(0);
        }
        MPJLambdaWrapper<DbMetaTable> q = MPJWrappers.lambdaJoin(DbMetaTable.class)
            .select(DbMetaObject::getOid, DbMetaObject::getName)
            .select(DbMetaTable::getColumnNum)
            .innerJoin(DbMetaObject.class, DbMetaObject::getOid, DbMetaTable::getOid)
            .in(DbMetaTable::getOid, tableIds)
            .eq(DbMetaObject::getDeleted, Boolean.FALSE);
        return dbMetaTableMapper.selectJoinList(SimpleTableDTO.class, q);
    }

    @Override
    public List<SimpleTableDTO> listMetaTable(Long sourceId, Long schemaId, Long minTableId) {
        MPJLambdaWrapper<DbMetaTable> q = MPJWrappers.lambdaJoin(DbMetaTable.class)
            .select(DbMetaObject::getOid, DbMetaObject::getName)
            .select(DbMetaTable::getColumnNum)
            .innerJoin(DbMetaObject.class, DbMetaObject::getOid, DbMetaTable::getOid)
            .eq(DbMetaTable::getSchemaId, schemaId)
            .ge(null != minTableId, DbMetaTable::getOid, minTableId)
            .eq(DbMetaObject::getDeleted, Boolean.FALSE);
        return dbMetaTableMapper.selectJoinList(SimpleTableDTO.class, q);
    }

    @Override
    public List<SimpleColumnInfo> listSimpleMetaColumn(Long sourceId, Collection<Long> tableIds) {
        if (CollUtil.isEmpty(tableIds)) {
            return new ArrayList<>(0);
        }
        MPJLambdaWrapper<DbMetaColumn> q = JoinWrappers.lambda(DbMetaColumn.class)
            .selectAsClass(DbMetaColumn.class, SimpleColumnInfo.class)
            .select(DbMetaObject::getName)
            .innerJoin(DbMetaObject.class, DbMetaObject::getOid, DbMetaColumn::getOid)
            .in(DbMetaColumn::getTableId, tableIds)
            .eq(DbMetaObject::getDeleted, Boolean.FALSE);
        return dbMetaColumnMapper.selectJoinList(SimpleColumnInfo.class, q);
    }

    @Override
    public List<ColumnDTO> listMetaColumn(Long sourceId, Collection<Long> columnId) {
        if (CollUtil.isEmpty(columnId)) {
            return new ArrayList<>(0);
        }
        MPJLambdaWrapper<DbMetaColumn> q = JoinWrappers.lambda(DbMetaColumn.class)
            .selectAsClass(DbMetaColumn.class, ColumnDTO.class)
            .select(DbMetaObject::getName, DbMetaObject::getDescription)
            .innerJoin(DbMetaObject.class, DbMetaObject::getOid, DbMetaColumn::getOid)
            .in(DbMetaColumn::getOid, columnId)
            .eq(DbMetaObject::getDeleted, Boolean.FALSE);
        return dbMetaColumnMapper.selectJoinList(ColumnDTO.class, q);
    }

    @Override
    public void updateColumn(List<DbMetaColumn> list) {
        dbMetaColumnMapper.updateById(list, PartitionUtil.DEFAULT_PART_SIZE);
    }

    private List<DbMetaObject> getTableObjs(Long sourceId, Long schemaId, List<String> v, boolean view) {
        //唯一索引
        LambdaQueryWrapper<DbMetaObject> q = Wrappers.lambdaQuery(DbMetaObject.class)
            .select(DbMetaObject::getOid, DbMetaObject::getName, DbMetaObject::getRevision)
            .eq(DbMetaObject::getSourceId, sourceId)
            .eq(DbMetaObject::getPid, schemaId)
            .eq(!view, DbMetaObject::getType, MetaObjType.TABLE.name())
            .in(view, DbMetaObject::getType, Arrays.asList(MetaObjType.TABLE.name(), MetaObjType.VIEW.name()))
            .in(null != v, DbMetaObject::getName, v)
            .eq(DbMetaObject::getDeleted, Boolean.FALSE);
        return dbMetaObjectMapper.selectList(q);
    }
}
