package com.mchz.nyx.pipeline.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.toolkit.JoinWrappers;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.mchz.nyx.common.util.PartitionUtil;
import com.mchz.nyx.meta.entity.DbMetaTable;
import com.mchz.nyx.pipeline.common.constants.PipelineConst;
import com.mchz.nyx.pipeline.common.enums.SampleStatus;
import com.mchz.nyx.pipeline.entity.SampleColumn;
import com.mchz.nyx.pipeline.entity.SampleTable;
import com.mchz.nyx.pipeline.mapper.DbMetaTableMapper;
import com.mchz.nyx.pipeline.mapper.SampleColumnMapper;
import com.mchz.nyx.pipeline.mapper.SampleTableMapper;
import com.mchz.nyx.pipeline.model.dto.IdsQueryDTO;
import com.mchz.nyx.pipeline.model.dto.UpdateTableStatus;
import com.mchz.nyx.pipeline.service.MetaExtendService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.binding.BindingException;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Isolation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/1/16
 */
@Slf4j
@Service
@AllArgsConstructor
public class MetaExtendServiceImpl implements MetaExtendService {
    private final DbMetaTableMapper dbMetaTableMapper;
    private final SampleColumnMapper sampleColumnMapper;
    private final SampleTableMapper sampleTableMapper;

    @Override
    @Async(PipelineConst.STORE_THREAD)
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public CompletableFuture<Boolean> asyncSaveSample(List<SampleColumn> list, boolean coverSample) {
        saveSample(list, coverSample);
        return CompletableFuture.completedFuture(Boolean.TRUE);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, isolation = Isolation.READ_COMMITTED)
    public void saveSample(List<SampleColumn> list, boolean coverSample) {
        PartitionUtil.part(list, columns -> {
            LambdaQueryWrapper<SampleColumn> q = Wrappers.lambdaQuery(SampleColumn.class)
                .select(SampleColumn::getOid, SampleColumn::getStatus)
                .in(SampleColumn::getOid, columns.stream().map(SampleColumn::getOid).collect(Collectors.toList()));
            List<SampleColumn> insert = new ArrayList<>(0);
            List<SampleColumn> update = new ArrayList<>(0);
            List<Long> empty = new ArrayList<>(0);
            List<SampleColumn> samples = sampleColumnMapper.selectList(q);
            Map<Long, SampleStatus> map = samples.stream().collect(Collectors.toMap(SampleColumn::getOid, SampleColumn::getStatus));
            for (SampleColumn data : columns) {
                SampleStatus status = map.get(data.getOid());
                if (null == status) {
                    insert.add(data);
                    continue;
                }
                switch (data.getStatus()) {
                    case CRAWL:
                        update.add(data);
                        break;
                    case CRAWL_0ROWS:
                        if (!SampleStatus.CRAWL_0ROWS.equals(status)) {
                            empty.add(data.getOid());
                        }
                        break;
                    default:
                        if (coverSample) {
                            update.add(data);
                        }
                }
            }
            if (!insert.isEmpty()) {
                sampleColumnMapper.insertList(insert);
            }
            if (!update.isEmpty()) {
                update.forEach(v -> {
                    v.setSourceId(null);
                    v.setTableId(null);
                });
                sampleColumnMapper.updateById(update);
            }
            if (!empty.isEmpty()) {
                sampleColumnMapper.update(Wrappers.lambdaUpdate(SampleColumn.class).set(SampleColumn::getStatus, SampleStatus.CRAWL_0ROWS).set(SampleColumn::getSampleData, PipelineConst.SAMPLE_EMPTY).in(SampleColumn::getOid, empty));
            }
        });
    }

    @Override
    public Map<String, List<Object>> getSample(Map<Long, String> id2ColumnName) {
        LambdaQueryWrapper<SampleColumn> q = Wrappers.lambdaQuery(SampleColumn.class).select(SampleColumn::getOid, SampleColumn::getSampleData).in(SampleColumn::getOid, id2ColumnName.keySet());
        Map<String, List<Object>> map = new HashMap<>(id2ColumnName.size());
        sampleColumnMapper.selectList(q, rc -> {
            String name = id2ColumnName.get(rc.getResultObject().getOid());
            if (null == name) {
                return;
            }
            map.put(name, JSONUtil.parseArray(rc.getResultObject().getSampleData()));
        });
        return map;
    }

    @Override
    public void updateTableStatus(UpdateTableStatus data) {
        if (CollUtil.isNotEmpty(data.getInsert())) {
            sampleTableMapper.insertList(data.getInsert());
        }
        PartitionUtil.part(data.getIsEmpty(), v -> sampleTableMapper.update(Wrappers.lambdaUpdate(SampleTable.class).set(SampleTable::getDataEmpty, 1).in(SampleTable::getOid, v)));
        PartitionUtil.part(data.getNotEmpty(), v -> sampleTableMapper.update(Wrappers.lambdaUpdate(SampleTable.class).set(SampleTable::getDataEmpty, 2).in(SampleTable::getOid, v)));
    }

    @Override
    public void fillNewTable(Long sourceId) {
        String id = "id";
        Map<String, Object> map = sampleTableMapper.selectMaps(JoinWrappers.lambda(SampleTable.class).selectMax(SampleTable::getOid, id).eq(SampleTable::getSourceId, sourceId)).get(0);
        Long minOid = MapUtil.getLong(map, id);
        MPJLambdaWrapper<DbMetaTable> q = JoinWrappers.lambda(DbMetaTable.class).select(DbMetaTable::getOid, DbMetaTable::getSourceId, DbMetaTable::getSchemaId)
            .eq(DbMetaTable::getSourceId, sourceId)
            .gt(null != minOid, DbMetaTable::getOid, minOid);
        insertSampleTableFromMeta(q);
    }

    @Override
    public void fillTable(Long sourceId) {
        Long l = dbMetaTableMapper.selectCount(Wrappers.lambdaQuery(DbMetaTable.class).eq(DbMetaTable::getSourceId, sourceId));
        Long r = sampleTableMapper.selectCount(Wrappers.lambdaQuery(SampleTable.class).eq(SampleTable::getSourceId, sourceId));
        if (ObjUtil.equals(l, r)) {
            return;
        }
        MPJLambdaWrapper<DbMetaTable> q = JoinWrappers.lambda(DbMetaTable.class).select(DbMetaTable::getOid, DbMetaTable::getSourceId, DbMetaTable::getSchemaId).eq(DbMetaTable::getSourceId, sourceId);
        if (r > 0) {
            q.notExists(SampleTable.class, w -> w.select("1").eq(SampleTable::getOid, DbMetaTable::getOid));
        }
        insertSampleTableFromMeta(q);
    }


    @Override
    public Map<Long, Integer> getTableStatus(Long sourceId, IdsQueryDTO query) {
        Map<Long, Integer> res = new HashMap<>();
        if (null != query.getIds()) {
            PartitionUtil.part(query.getIds(), v -> {
                LambdaQueryWrapper<SampleTable> q = Wrappers.lambdaQuery(SampleTable.class).select(SampleTable::getOid, SampleTable::getDataEmpty).in(SampleTable::getOid, v);
                sampleTableMapper.selectList(q, rc -> res.put(rc.getResultObject().getOid(), rc.getResultObject().getDataEmpty()));
            });
        } else {
            LambdaQueryWrapper<SampleTable> q = Wrappers.lambdaQuery(SampleTable.class).select(SampleTable::getOid, SampleTable::getDataEmpty).ge(SampleTable::getOid, query.getStart()).le(SampleTable::getOid, query.getEnd());
            sampleTableMapper.selectList(q, rc -> res.put(rc.getResultObject().getOid(), rc.getResultObject().getDataEmpty()));
        }
        return res;
    }

    private void insertSampleTableFromMeta(MPJLambdaWrapper<DbMetaTable> q) {
        List<DbMetaTable> list = dbMetaTableMapper.selectList(q);
        if (list.isEmpty()) {
            return;
        }
        List<SampleTable> res = list.stream().map(v -> {
            SampleTable table = new SampleTable();
            table.setOid(v.getOid());
            table.setSourceId(v.getSourceId());
            table.setSchemaId(v.getSchemaId());
            table.setDataEmpty(0);
            return table;
        }).collect(Collectors.toList());
        sampleTableMapper.insertList(res);
    }
}
