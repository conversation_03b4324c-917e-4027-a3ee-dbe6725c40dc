package com.mchz.nyx.pipeline.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mchz.nyx.pipeline.common.constants.PipelineConst;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/29
 */
@Data
@TableName(value = "tag_rule_detail", schema = PipelineConst.EMBED_SCHEMA)
public class TagRuleDetail implements Serializable {
    /**
     * 主键(引用artifacts)
     */
    @TableId("id")
    private Long id;

    /**
     * 是否启用
     */
    private Boolean enabled;

    /**
     * 版本
     */
    private Integer version;

    /**
     * 发现规则定义
     */
    private String definition;
}
