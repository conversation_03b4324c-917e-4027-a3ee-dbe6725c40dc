package com.mchz.nyx.pipeline.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.toolkit.JoinWrappers;
import com.mchz.nyx.pipeline.entity.EntityMapping;
import com.mchz.nyx.pipeline.mapper.EntityMappingMapper;
import com.mchz.nyx.pipeline.service.EntityMappingService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/18
 */
@Slf4j
@Service
@AllArgsConstructor
public class EntityMappingServiceImpl implements EntityMappingService {
    private final EntityMappingMapper entityMappingMapper;

    @Override
    public List<EntityMapping> loadAllWithIndex(Integer industry, Integer type) {
        LambdaQueryWrapper<EntityMapping> q = Wrappers.lambdaQuery(EntityMapping.class).select(EntityMapping::getId, EntityMapping::getName)
            .eq(EntityMapping::getIndustryId, industry).eq(EntityMapping::getCate, type);
        try {
            return entityMappingMapper.selectList(q);
        } catch (Exception e) {
            log.warn("entity 数据加载异常,{}", e.getMessage());
            return null;
        }
    }

    @Override
    public String getChineseName(Integer id) {
        return entityMappingMapper.selectJoinOne(String.class, JoinWrappers.lambda(EntityMapping.class).select(EntityMapping::getCname).eq(EntityMapping::getId, id));
    }
}
