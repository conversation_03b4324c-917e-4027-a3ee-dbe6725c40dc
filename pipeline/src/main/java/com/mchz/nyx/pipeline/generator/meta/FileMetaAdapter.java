package com.mchz.nyx.pipeline.generator.meta;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.CharUtil;
import cn.hutool.core.util.ObjUtil;
import com.mchz.nyx.common.enums.DataTypeGroup;
import com.mchz.nyx.meta.common.enums.MetaObjType;
import com.mchz.nyx.meta.entity.DbMetaColumn;
import com.mchz.nyx.meta.entity.DbMetaObject;
import com.mchz.nyx.meta.entity.DbMetaTable;
import com.mchz.nyx.meta.instance.crawl.DsMetaAdapter;
import com.mchz.nyx.meta.instance.crawl.NyxCrawlAgent;
import com.mchz.nyx.meta.model.meta.CatalogSchema;
import com.mchz.nyx.meta.model.meta.WrapTable;
import com.mchz.nyx.meta.support.DataSourceConfig;
import com.mchz.nyx.meta.support.SchemaTables;
import com.mchz.nyx.pipeline.common.constants.PipelineConst;
import com.mchz.nyx.pipeline.common.enums.ArchiverEnum;
import com.mchz.nyx.pipeline.common.enums.DatabaseType;
import com.mchz.nyx.pipeline.entity.FileUploadDetail;
import com.mchz.nyx.pipeline.exception.ServiceException;
import com.mchz.nyx.pipeline.model.param.McFileSourceParam;
import com.mchz.nyx.pipeline.model.param.TableSamplingParam;
import com.mchz.nyx.pipeline.model.vo.TableVO;
import com.mchz.nyx.pipeline.service.FileManagerService;
import com.mchz.nyx.pipeline.util.DbUtil;
import com.mchz.nyx.pipeline.util.Func;
import com.mchz.nyx.pipeline.util.file.fs.FileMeta;
import com.mchz.nyx.pipeline.util.file.fs.FilesBundle;
import com.mchz.nyx.pipeline.util.file.fs.SourceFile;
import com.mchz.nyx.pipeline.util.file.manager.FileAttach;
import com.mchz.nyx.pipeline.util.file.reader.*;
import com.mchz.nyx.pipeline.util.filter.StrFilter;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/19
 */
@Slf4j
@AllArgsConstructor
public class FileMetaAdapter implements MetaLookupService, DsMetaAdapter {
    private final FileManagerService fileManagerService;

    @Override
    public List<String> loadSchema(DataSourceConfig source) {
        McFileSourceParam fileSource = DbUtil.fileSource(source);
        FileAttach fileAttach = fileSource.getFileAttach();
        if (fileAttach.isCompress()) {
            List<FileUploadDetail> list = fileManagerService.listFileDetail(fileSource.getSourceId());
            return list.stream().filter(s -> ArchiverEnum.compressSuffix(s.getFormat())).map(FileUploadDetail::getName).collect(Collectors.toList());
        }
        return Collections.singletonList(PipelineConst.DEFAULT_SCHEMA);
    }

    @Override
    public List<TableVO> loadTable(DataSourceConfig source) {
        throw new UnsupportedOperationException();
    }

    @Override
    public void execute(NyxCrawlAgent agent, DataSourceConfig source, int type, List<SchemaTables> schemaTables) {
        Long sourceId = source.getSourceId();
        McFileSourceParam fileSource = DbUtil.fileSource(source);
        FileAttach fileAttach = fileSource.getFileAttach();
        List<FileUploadDetail> tmp = fileManagerService.listFileDetail(sourceId);
        List<FileUploadDetail> list;
        Map<String, StrFilter> filterMap;
        Map<String, List<String>> tablesMap = new HashMap<>();
        if (CollUtil.isEmpty(schemaTables)) {
            list = tmp;
            filterMap = null;
            List<CatalogSchema> schemas;
            if (fileAttach.isCompress()) {
                schemas = list.stream().filter(s -> ArchiverEnum.compressSuffix(s.getFormat())).map(v -> CatalogSchema.of(v.getName())).collect(Collectors.toList());
            } else {
                schemas = Collections.singletonList(CatalogSchema.of(PipelineConst.DEFAULT_SCHEMA));
            }
            agent.schema(schemas, Collections.emptyList());
        } else {
            if (fileAttach.isCompress()) {
                filterMap = new HashMap<>(schemaTables.size());
                Func.init(schemaTables, filterMap, tablesMap);
                list = tmp.stream().filter(v -> filterMap.containsKey(v.getName())).collect(Collectors.toList());
                Set<String> set = list.stream().map(FileUploadDetail::getName).collect(Collectors.toSet());
                List<String> delSchema = schemaTables.stream().filter(v -> !set.contains(v.getSchema().getName())).map(v -> v.getSchema().getName()).collect(Collectors.toList());
                agent.schema(list.stream().map(FileUploadDetail::getName).map(CatalogSchema::of).collect(Collectors.toList()), delSchema);
            } else {
                filterMap = null;
                SchemaTables t = schemaTables.get(0);
                StrFilter strFilter = StrFilter.ofBlackWhite(t.getExcludeTables(), t.getTables());
                tablesMap.put(t.getSchema().getName(), t.getTables());
                list = tmp.stream().filter(v -> strFilter.check(v.getName())).collect(Collectors.toList());
                agent.schema(Collections.singletonList(t.getSchema()), null);
            }
        }
        FilesBundle<SourceFile> sourceFiles = fileManagerService.loadFile(list, fileSource.getRemote(), filterMap, fileSource.getFileType());
        CatalogSchema schema = null;
        for (SourceFile sourceFile : sourceFiles) {
            FileMeta meta = sourceFile.getMeta();
            String zipName = meta.getZipName();
            if (null == zipName) {
                throw new ServiceException("压缩文件加载异常");
            }
            if (null == schema || !schema.getName().equals(zipName)) {
                if (null != schema) {
                    agent.stageFinish(schema);
                }
                schema = CatalogSchema.of(zipName);
                agent.stageStart(schema, tablesMap.get(zipName), MetaObjType.TABLE);
            }
            List<String> columnNames;
            try (DatabaseFileReader fileReader = getReader(fileSource.getFileType(), fileAttach, sourceFile.getFile())) {
                columnNames = getColumnNames(fileSource.getFileType(), fileAttach, fileReader);
            } catch (Exception e) {
                log.error("读取文件失败：{}[{}]({})", meta.getName(), meta.getFileName(), meta.getRelPath(), e);
                throw new ServiceException(String.format("文件(%s)读取失败:%s", meta.getName(), e.getMessage()));
            }
            WrapTable wrapTable = new WrapTable(buildTableObject(meta.getName()), buildTable(columnNames.size()));
            agent.parseColumn(columnNames, wrapTable, this::buildColumnObject, this::buildColumn);
            agent.consume(wrapTable);
        }
        agent.stageFinish(schema);
    }

    public List<FileUploadDetail> listFileDetails(DataSourceConfig source) {
        return fileManagerService.listFileDetail(source.getSourceId());
    }

    public DatabaseFileReader getReader(DataSourceConfig source, TableSamplingParam param, List<FileUploadDetail> tmp) {
        McFileSourceParam fileSource = DbUtil.fileSource(source);
        FileAttach fileAttach = fileSource.getFileAttach();
        FilesBundle<SourceFile> sourceFiles;
        if (fileAttach.isCompress()) {
            if (null == param.getSchema()) {
                log.warn("压缩文件名为空,{}", param.getTable());
                return null;
            }
            Optional<FileUploadDetail> first = tmp.stream().filter(v -> param.getSchema().equals(v.getName())).findFirst();
            if (!first.isPresent()) {
                log.warn("未找到对应文件,{}/{}", param.getSchema(), param.getTable());
                return null;
            }
            Map<String, StrFilter> filterMap = Func.newMap(param.getSchema(), StrFilter.of(param.getTable()));
            sourceFiles = fileManagerService.loadFile(Collections.singletonList(first.get()), fileSource.getRemote(), filterMap, fileSource.getFileType());

        } else {
            Optional<FileUploadDetail> first = tmp.stream().filter(v -> param.getTable().equals(v.getName())).findFirst();
            if (!first.isPresent()) {
                log.warn("未找到对应文件,{}", param.getTable());
                return null;
            }
            sourceFiles = fileManagerService.loadFile(Collections.singletonList(first.get()), fileSource.getRemote());
        }
        SourceFile file = CollUtil.getFirst(sourceFiles);
        try {
            return getReader(fileSource.getFileType(), fileAttach, file.getFile());
        } catch (IOException e) {
            FileMeta meta = file.getMeta();
            log.error("加载文件失败：{}[{}]({})", meta.getName(), meta.getFileName(), meta.getRelPath(), e);
            return null;
        }
    }

    private DatabaseFileReader getReader(DatabaseType fileType, FileAttach fileAttach, File file) throws IOException {
        FileInputStream stream = new FileInputStream(file);
        boolean headline = fileAttach.isHeadline();
        switch (fileType) {
            case FILE_XLSX:
            case FILE_XLS:
                return new ExcelDatabaseFileReader(stream, headline);
            case FILE_CSV:
            case FILE_TXT:
                return new CsvDatabaseFileReader(stream, headline, getCharset(fileAttach), ObjUtil.defaultIfNull(fileAttach.getSeparator(), CharUtil.COMMA));
            case FILE_XML:
                return new XmlDatabaseFileReader(stream, headline, fileAttach.getDataNode());
            case FILE_HTML:
                return new HtmlDatabaseFileReader(stream, file, headline);
            case FILE_JSON:
                return new JsonDatabaseFileReader(stream, file, headline, getCharset(fileAttach), fileAttach.getDataNode());
            default:
                throw new IllegalArgumentException(fileType.getName());
        }
    }

    private List<String> getColumnNames(DatabaseType fileType, FileAttach fileAttach, DatabaseFileReader fileReader) {
        if (fileAttach.isHeadline() || fileType.isContainDataNode()) {
            return fileReader.readHead();
        }

        int size = fileReader.readData(1).get(0).size();
        List<String> fieldNames = new ArrayList<>(size);
        for (
            int i = 1;
            i <= size; i++) {
            fieldNames.add(PipelineConst.DEFAULT_COLUMN_PREFIX + i);
        }
        return fieldNames;
    }

    private Charset getCharset(FileAttach fileAttach) {
        if (null != fileAttach.getCharset()) {
            return Charset.forName(fileAttach.getCharset().getName());
        }
        return StandardCharsets.UTF_8;
    }

    private DbMetaObject buildTableObject(String name) {
        DbMetaObject obj = new DbMetaObject();
        obj.setName(name);
        obj.setType(MetaObjType.TABLE.name());
        return obj;
    }

    private DbMetaObject buildColumnObject(String name) {
        Objects.requireNonNull(name, "列名不能为空");
        DbMetaObject obj = new DbMetaObject();
        obj.setName(Func.compressStr(name));
        obj.setType(MetaObjType.COLUMN.name());
        return obj;
    }

    private DbMetaTable buildTable(Integer columnNum) {
        DbMetaTable meta = new DbMetaTable();
        meta.setType(MetaObjType.TABLE.name());
        meta.setColumnNum(columnNum);
        return meta;
    }

    private DbMetaColumn buildColumn(String name, int index) {
        DbMetaColumn meta = new DbMetaColumn();
        meta.setTypeGroup(DataTypeGroup.CHARACTER.name());
        meta.setDataType(meta.getTypeGroup());
        meta.setPosition(index);
        return meta;
    }
}
