package com.mchz.nyx.pipeline.model.dto;

import com.mchz.nyx.meta.entity.DbMetaObject;
import lombok.Data;
import org.jetbrains.annotations.NotNull;
import org.roaringbitmap.RoaringBitmap;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
@Data
public class TableMSInfo implements Comparable<TableMSInfo> {
    private DbMetaObject table;
    private int size;
    private RoaringBitmap bitmap;

    public TableMSInfo(DbMetaObject table) {
        this.table = table;
        this.size = 0;
        this.bitmap = new RoaringBitmap();
    }

    public void addColumnIndex(int index) {
        size++;
        bitmap.add(index);
    }

    @Override
    public int compareTo(@NotNull TableMSInfo o) {
        if (size != o.size) {
            return size - o.size;
        }
        int diff = table.getName().length() - o.table.getName().length();
        if (0 != diff) {
            return diff;
        }
        return table.getName().compareTo(o.table.getName());
    }
}
