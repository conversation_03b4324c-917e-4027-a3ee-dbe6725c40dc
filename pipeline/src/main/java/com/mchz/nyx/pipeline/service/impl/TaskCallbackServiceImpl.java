package com.mchz.nyx.pipeline.service.impl;

import com.mchz.nyx.pipeline.http.RemoteInvokeHandler;
import com.mchz.nyx.pipeline.model.api.TaskHeartbeatBeatReq;
import com.mchz.nyx.pipeline.model.dto.JobLogInstantDTO;
import com.mchz.nyx.pipeline.model.log.JobLogVO;
import com.mchz.nyx.pipeline.model.log.ProcessingTable;
import com.mchz.nyx.pipeline.service.TaskCallbackService;
import com.mchz.starter.tenant.util.TenantUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/10/23 13:16
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TaskCallbackServiceImpl implements TaskCallbackService {

    private final RemoteInvokeHandler remoteInvokeHandler;

    @Override
    public void finished(Long jobId, boolean success, JobLogVO jobLog) {
        try {
            remoteInvokeHandler.finished(jobId, success, jobLog);
        } catch (Exception e) {
            log.warn("【作业结束】", e);
        }
    }

    @Override
    public void sendProgress(JobLogInstantDTO dto) {
        TenantUtil.apply(dto.getTenantId(), () -> {
            try {
                remoteInvokeHandler.asyncSendMessage(dto.getDto());
            } catch (Exception e) {
                log.warn("【发送日志】", e);
            }
            try {
                ProcessingTable table = new ProcessingTable();
                table.setTenantId(dto.getTenantId());
                table.setJobId(dto.getJobId());
                table.setTables(dto.getTableList());
                remoteInvokeHandler.sendProcessingTable(table);
            } catch (Exception e) {
                log.warn("【发送处理中表格】", e);
            }
        });
    }

    @Override
    public void heart(TaskHeartbeatBeatReq value) {
        try {
            log.debug("【心跳】{}", value);
            remoteInvokeHandler.heart(value);
        } catch (Exception e) {
            log.warn("【心跳】{}", e.getMessage());
        }
    }
}
