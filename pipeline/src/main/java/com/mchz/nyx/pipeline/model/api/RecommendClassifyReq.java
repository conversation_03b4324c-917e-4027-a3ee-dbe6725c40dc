package com.mchz.nyx.pipeline.model.api;

import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class RecommendClassifyReq {

    /**
     * 基线%
     */
    @NotNull(message = "基线%不能为NULL")
    @Range(message = "基线%范围为[{min},{max}]", min = 1, max = 100)
    private Integer baseline;
    /**
     * 推荐数量
     */
    @NotNull
    @Range(message = "推荐数量最小为{min}", min = 1)
    private Integer recommendNum = 1;

    /**
     * 标准id
     **/
    @NotNull
    private Long stdId;
    /**
     * 表信息
     */
    @NotNull
    private List<TableInfo> tableInfos;

    @Data
    public static class TableInfo {
        /**
         * 数据库名称
         */
        private String schema;
        /**
         * 表名称
         */
        private String tableName;
        /**
         * 表注释
         */
        private String tableComment;
    }

}
