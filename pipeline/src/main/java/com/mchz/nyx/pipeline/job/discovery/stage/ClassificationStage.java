package com.mchz.nyx.pipeline.job.discovery.stage;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.lmax.disruptor.*;
import com.lmax.disruptor.dsl.Disruptor;
import com.lmax.disruptor.dsl.ProducerType;
import com.mchz.nyx.common.exception.NyxException;
import com.mchz.nyx.common.util.PartitionUtil;
import com.mchz.nyx.common.util.WaitUtil;
import com.mchz.nyx.dark.util.NlpUtil;
import com.mchz.nyx.meta.entity.DbMetaObject;
import com.mchz.nyx.meta.support.DataSourceConfig;
import com.mchz.nyx.pipeline.common.enums.AnalysisColumnStatus;
import com.mchz.nyx.pipeline.common.enums.DatabaseType;
import com.mchz.nyx.pipeline.entity.AnalysisColumn;
import com.mchz.nyx.pipeline.entity.AnalysisTable;
import com.mchz.nyx.pipeline.entity.AnalysisTableClassify;
import com.mchz.nyx.pipeline.exception.AlertException;
import com.mchz.nyx.pipeline.exception.ChainEndInterruptException;
import com.mchz.nyx.pipeline.generator.DarkClassifyActuator;
import com.mchz.nyx.pipeline.generator.DarkContext;
import com.mchz.nyx.pipeline.generator.meta.FileMetaAdapter;
import com.mchz.nyx.pipeline.generator.sample.SampleCommonHandler;
import com.mchz.nyx.pipeline.generator.sample.SampleFileHandler;
import com.mchz.nyx.pipeline.generator.sample.SampleHandler;
import com.mchz.nyx.pipeline.generator.sample.SampleResultHandler;
import com.mchz.nyx.pipeline.job.discovery.param.DiscoveryEvent;
import com.mchz.nyx.pipeline.job.discovery.param.DiscoveryJobState;
import com.mchz.nyx.pipeline.job.discovery.param.TableLogParam;
import com.mchz.nyx.pipeline.model.dto.*;
import com.mchz.nyx.pipeline.service.AnalysisService;
import com.mchz.nyx.pipeline.service.FileManagerService;
import com.mchz.nyx.pipeline.service.MetaExtendService;
import com.mchz.nyx.pipeline.service.MetadataService;
import com.mchz.nyx.pipeline.util.DbUtil;
import com.mchz.nyx.pipeline.util.Func;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/9
 */
@Slf4j
@RequiredArgsConstructor
public class ClassificationStage {
    private final MetadataService metadataService;
    private final AnalysisService analysisService;
    private final MetaExtendService metaExtendService;
    private final FileManagerService fileManagerService;

    public void execute(DarkContext context, DarkClassifyActuator actuator, DataSourceConfig source, List<Long> schemaIds) {
        ThreadFactory factory = ThreadUtil.newNamedThreadFactory("Step3-" + StrUtil.subSufByLength(context.getJobId().toString(), 3) + "-", null, true, (t, e) -> {
            if (!ChainEndInterruptException.INSTANCE.equals(e)) {
                log.warn("【Step3】UncaughtExceptionHandler {}", e.getMessage());
            }
        });
        int bufferSize = 32;
        Disruptor<DiscoveryEvent> disruptor = new Disruptor<>(DiscoveryEvent::new, bufferSize, factory, ProducerType.SINGLE, new PhasedBackoffWaitStrategy(100, 30000, TimeUnit.MILLISECONDS, new SleepingWaitStrategy()));
        ClExceptionHandler handler = new ClExceptionHandler(context);
        disruptor.setDefaultExceptionHandler(handler);

        WorkHandler<DiscoveryEvent>[] discovery = new DarkHandler[2];
        List<AutoCloseable> closeableList = new ArrayList<>(discovery.length);
        for (int i = 0; i < discovery.length; i++) {
            SampleHandler sampleHandler = getSampleHandler(context, source);
            closeableList.add(sampleHandler);
            discovery[i] = new DarkHandler(context, actuator, metadataService, sampleHandler);
        }
        StoreHandler storeHandler = new StoreHandler(context, analysisService, metaExtendService);
        disruptor.handleEventsWithWorkerPool(discovery).then(storeHandler);
        RingBuffer<DiscoveryEvent> ringBuffer = disruptor.start();
        log.info("【发现引擎】执行任务");
        try {
            DiscoveryJobState state = context.getState();
            if (null != state && null != state.getSId()) {
                schemaIds = schemaIds.stream().filter(v -> v >= state.getSId()).collect(Collectors.toList());
            }
            List<CatalogSchemaDTO> list = metadataService.listMetaSchema(schemaIds);
            //迁移历史数据兼容
            if (schemaIds.size() != list.size()) {
                throw new NyxException("schema 中存在异常数据");
            }
            list.sort(Comparator.comparing(CatalogSchemaDTO::getOid));
            Stream<CatalogSchemaDTO> stream = list.stream();
            if (null != state && null != state.getSId()) {
                CatalogSchemaDTO schema = list.get(0);
                if (state.getSId().equals(schema.getOid())) {
                    processTable(context, actuator, ringBuffer, schema, state.getTId());
                    stream = stream.skip(1);
                }
            }
            stream.forEach(v -> processTable(context, actuator, ringBuffer, v, null));
        } catch (AlertException ignore) {
            log.debug("【发现引擎】异常中断");
        } finally {
            if (disruptor.getCursor() < 2) {
                WaitUtil.applyWait(10);
            }
            disruptor.shutdown();
            storeHandler.join();
            closeSourceConnect(context, closeableList);
        }
        if (null != handler.getCause()) {
            throw ExceptionUtil.wrap(handler.getCause(), NyxException.class);
        }
    }

    private SampleHandler getSampleHandler(DarkContext context, DataSourceConfig source) {
        if (context.isIgnoreData()) {
            return new SampleResultHandler(metaExtendService);
        }
        DatabaseType type = DbUtil.fileType(source);
        if (!DatabaseType.OTHER.equals(type)) {
            if (type.isIgnoreData()) {
                return SampleHandler.EMPTY_HANDLER;
            }
            return new SampleFileHandler(source, new FileMetaAdapter(fileManagerService));
        }
        return new SampleCommonHandler(metadataService, source);
    }

    private void closeSourceConnect(DarkContext context, List<AutoCloseable> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        log.info("【发现引擎】{} close connection", context.getSourceId());
        for (AutoCloseable closeable : list) {
            try {
                closeable.close();
            } catch (Exception e) {
                log.error("【发现引擎】Failed to close ({}):{}", context.getSourceId(), e.getMessage());
            }
        }
    }

    private void processTable(DarkContext context, DarkClassifyActuator actuator, RingBuffer<DiscoveryEvent> ringBuffer, CatalogSchemaDTO schema, Long tId) {
        List<AnalysisTable> list = analysisService.listJobTable(context.getPlanId(), schema.getOid(), tId, context.isIncrement());
        list.sort(Comparator.comparing(AnalysisTable::getTableId));
        context.tables(list.size());
        AtomicInteger i = new AtomicInteger();
        PartitionUtil.part(list).forEach(v -> {
            IdsQueryDTO query = IdsQueryDTO.ofOrderly(v, AnalysisTable::getTableId);
            Map<Long, Integer> tableStatus = context.isTableStatus() ? metaExtendService.getTableStatus(context.getSourceId(), query) : Collections.emptyMap();
            List<DbMetaObject> tableDes = metadataService.listMetaTableDes(query);
            Map<Long, DbMetaObject> tableMap = tableDes.stream().collect(Collectors.toMap(DbMetaObject::getOid, Function.identity()));
            if (!context.isIgnoreCandidate()) {
                analysisService.deleteTableClassify(context.getPlanId(), tableMap.keySet());
            }
            for (AnalysisTable table : v) {
                TableDTO tableDTO = new TableDTO();
                DbMetaObject metaTable = tableMap.get(table.getTableId());
                if (null == metaTable) {
                    log.error("【发现引擎】{}({}) no db meta ", table.getTableId(), context.getSourceId());
                    throw new NyxException("异常数据");
                }
                tableDTO.setId(table.getId());
                tableDTO.setOid(table.getTableId());
                tableDTO.setName(metaTable.getName());
                tableDTO.setCatalogSchema(schema);
                tableDTO.setDescription(NlpUtil.dealComment(metaTable.getDescription()));
                tableDTO.setStatus(tableStatus.get(table.getTableId()));
                executeTableClassify(context, actuator, table, tableDTO);
                executeSingleTable(context, ringBuffer, tableDTO, i.incrementAndGet(), list.size());
            }
        });
    }

    private void executeTableClassify(DarkContext context, DarkClassifyActuator actuator, AnalysisTable table, TableDTO tableDTO) {
        if (table.getStatus() > 1) {
            if (null != table.getClassifyId()) {
                List<String> ids = StrUtil.split(table.getClassifyId(), StrUtil.COMMA, false, true);
                if (1 == ids.size()) {
                    tableDTO.setClassify(new ClassifyDTO(Long.valueOf(ids.get(0)), table.getLevel()));
                }
            }
            return;
        }
        long[] key = context.startChild("table classify");
        List<ClassifyTargetDTO> classify = actuator.recommendTableClassify(tableDTO.getTableName(), tableDTO.getTableComment());
        context.stopChild(key);
        if (classify.isEmpty()) {
            return;
        }
        ClassifyTargetDTO dto = classify.get(0);
        if (null == dto.getTarget().getTypeId()) {
            return;
        }
        tableDTO.setRec(true);
        tableDTO.setClassify(dto.getTarget());
        if (context.isIgnoreCandidate()) {
            return;
        }
        List<AnalysisTableClassify> list = classify.stream().map(v -> {
            AnalysisTableClassify tmp = new AnalysisTableClassify();
            tmp.setPlanId(context.getPlanId());
            tmp.setSourceId(context.getSourceId());
            tmp.setTableId(tableDTO.getOid());
            tmp.setClassifyId(v.getTarget().getTypeId());
            tmp.setLevel(v.getTarget().getLevel());
            tmp.setScore(null == v.getScore() ? 99 : v.getScore() * 100);
            return tmp;
        }).collect(Collectors.toList());
        analysisService.saveTableClassify(list);
    }

    private void executeSingleTable(DarkContext context, RingBuffer<DiscoveryEvent> ringBuffer, TableDTO table, int i, int size) {
        context.interrupted();
        long[] key = context.startChild("job column");
        List<AnalysisColumn> temp = analysisService.listJobColumn(context.getPlanId(), table.getOid());
        List<AnalysisColumn> columns = new ArrayList<>(temp.size() >> 1);
        Map<Long, Integer> map = new ConcurrentHashMap<>();
        for (AnalysisColumn column : temp) {
            if (pending(context, column)) {
                columns.add(column);
            } else if (null != column.getClassifyId()) {
                map.compute(column.getClassifyId(), (k, v) -> v == null ? 1 : v + 1);
            }
        }
        table.setClassifyNum(map);
        context.stopChild(key);
        List<List<AnalysisColumn>> lists = ListUtil.partition(columns, context.getColumnNum());
        if (lists.isEmpty()) {
            context.complete();
            return;
        }
        String tableName = tableName(table);
        TableLogParam logParam = new TableLogParam(tableName, i, size, lists.size());
        key = context.startChild("send");
        lists.forEach(v -> send(context, ringBuffer, table, v, logParam));
        context.stopChild(key);
    }

    private boolean pending(DarkContext context, AnalysisColumn column) {
        return AnalysisColumnStatus.S5.getCode().equals(column.getStatus()) || column.getJobId() < context.getJobId() && (!context.isIncrement() || column.getStatus() > AnalysisColumnStatus.S2.getCode());
    }

    /**
     * @see DarkHandler#onEvent
     * @see StoreHandler#onEvent
     */
    private void send(DarkContext context, RingBuffer<DiscoveryEvent> ringBuffer, TableDTO table, List<AnalysisColumn> columns, TableLogParam logParam) {
        int fullTimes = 0;
        while (!context.isAlerted()) {
            try {
                long next = ringBuffer.tryNext();
                DiscoveryEvent event = ringBuffer.get(next);
                event.setLog(logParam);
                event.setTable(table);
                event.setColumns(columns);
                ringBuffer.publish(next);
                break;
            } catch (InsufficientCapacityException e) {
                WaitUtil.applyWait(++fullTimes);
            }
        }
    }

    private String tableName(TableDTO tableDTO) {
        return tableDTO.getCatalogSchema().getName() + "." + tableDTO.getName();
    }

    @Slf4j
    @RequiredArgsConstructor
    private static class ClExceptionHandler implements ExceptionHandler<DiscoveryEvent> {
        private final DarkContext context;
        @Getter
        private volatile Throwable cause;

        @Override
        public void handleEventException(Throwable ex, long sequence, DiscoveryEvent event) {
            if (ChainEndInterruptException.INSTANCE.equals(ex)) {
                throw ChainEndInterruptException.INSTANCE;
            }
            log.debug("【分类分级】执行异常", ex);
            if (!AlertException.INSTANCE.equals(ex)) {
                if (null == cause) {
                    cause = ex;
                    log.error("【分类分级】执行异常", ex);
                } else {
                    log.warn("【分类分级】执行异常,{}", Func.getMessageWithStack(ex));
                }
            }
            context.alert();
        }

        @Override
        public void handleOnStartException(final Throwable ex) {
            log.error("【sink】Exception during onStart()", ex);
        }

        @Override
        public void handleOnShutdownException(final Throwable ex) {
            log.error("【sink】Exception during onShutdown()", ex);
        }
    }
}
