package com.mchz.nyx.pipeline.config.props;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/7/10
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "nyx")
public class HomeProperties {
    private String home;
    private String data;
}
