package com.mchz.nyx.pipeline.http;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.cloud.nacos.discovery.NacosServiceDiscovery;
import com.alibaba.nacos.api.exception.NacosException;
import com.mchz.nyx.pipeline.model.api.TaskHeartbeatBeatReq;
import com.mchz.nyx.pipeline.model.dto.ProgressInfoDTO;
import com.mchz.nyx.pipeline.model.log.JobLogVO;
import com.mchz.nyx.pipeline.model.log.ProcessingTable;
import com.mchz.nyx.pipeline.util.HttpUtil;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;

@Slf4j
@AllArgsConstructor
public class ClusterRemoteInvoke implements RemoteInvokeHandler {
    private final NacosServiceDiscovery nacosServiceDiscovery;
    private final Executor executor;

    @Override
    public void finished(Long jobId, boolean success, JobLogVO jobLog) {
        try {
            CompletableFuture<?>[] futures = nacosServiceDiscovery.getInstances("sourcedata").stream().map(instance -> CompletableFuture.runAsync(() -> {
                String protocol = instance.isSecure() ? "https" : "http";
                try {
                    HttpUtil.post(protocol + "://" + instance.getHost() + ":" + instance.getPort() + "/discovery/job/" + jobId + "%d/finished?result=" + BooleanUtil.toInt(success), JSONUtil.toJsonStr(jobLog), 2000);
                } catch (Exception e) {
                    log.warn("【发送日志】", e);
                }
            }, executor)).toArray(CompletableFuture[]::new);
            CompletableFuture.allOf(futures).join();
        } catch (NacosException e) {
            log.error("通过nacos获取服务实例失败", e);
        }
    }

    @Override
    public void sendProcessingTable(ProcessingTable table) {
        try {
            CompletableFuture<?>[] futures = nacosServiceDiscovery.getInstances("sourcedata").stream().map(instance -> CompletableFuture.runAsync(() -> {
                String protocol = instance.isSecure() ? "https" : "http";
                try {
                    HttpUtil.post(protocol + "://" + instance.getHost() + ":" + instance.getPort() + "/discovery/job/processingTable", JSONUtil.toJsonStr(table), 2000);
                } catch (Exception e) {
                    log.warn("【发送日志】", e);
                }
            }, executor)).toArray(CompletableFuture[]::new);
            CompletableFuture.allOf(futures).join();
        } catch (NacosException e) {
            log.error("通过nacos获取服务实例失败", e);
        }
    }

    @Override
    public void asyncSendMessage(ProgressInfoDTO dto) {
        try {
            CompletableFuture<?>[] futures = nacosServiceDiscovery.getInstances("sourcedata").stream().map(instance -> CompletableFuture.runAsync(() -> {
                String protocol = instance.isSecure() ? "https" : "http";
                try {
                    HttpUtil.post(protocol + "://" + instance.getHost() + ":" + instance.getPort() + "/discovery/job/log", JSONUtil.toJsonStr(dto), 2000);
                } catch (Exception e) {
                    log.warn("【发送日志】", e);
                }
            }, executor)).toArray(CompletableFuture[]::new);
            CompletableFuture.allOf(futures).join();
        } catch (NacosException e) {
            log.error("通过nacos获取服务实例失败", e);
        }
    }

    @Override
    public void heart(TaskHeartbeatBeatReq value) {
        //TODO  集群功能合并
    }
}
