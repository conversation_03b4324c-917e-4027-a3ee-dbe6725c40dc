package com.mchz.nyx.pipeline.exception;

import cn.hutool.core.io.IORuntimeException;
import com.mchz.nyx.common.exception.NyxException;
import com.mchz.nyx.pipeline.common.api.CommonResultCode;
import com.mchz.nyx.pipeline.common.api.R;
import com.mchz.nyx.pipeline.util.Func;
import com.mchz.starter.tool.BsFunc;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.context.annotation.Configuration;
import org.springframework.dao.DataAccessException;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpMediaTypeNotSupportedException;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import javax.servlet.Servlet;

/**
 * <p>
 * 全局异常处理，处理可预见的异常
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2019-07-12 17:26
 */
@Slf4j
@Configuration
@ConditionalOnClass(Servlet.class)
@ConditionalOnWebApplication(type = ConditionalOnWebApplication.Type.SERVLET)
@RestControllerAdvice
public class GlobalRestExceptionHandler {

    /**
     * 处理 MissingServletRequestParameterException
     *
     * @param e MissingServletRequestParameterException
     * @return R<String> 标准异常
     */
    @ExceptionHandler(MissingServletRequestParameterException.class)
    public R<String> handleError(MissingServletRequestParameterException e) {
        log.warn("缺少请求参数，{}", e.getMessage());
        String message = String.format("缺少必要的请求参数: %s", e.getParameterName());
        return R.fail(CommonResultCode.PARAM_MISS, message);
    }

    /**
     * 处理 MethodArgumentTypeMismatchException
     *
     * @param e MethodArgumentTypeMismatchException
     * @return R<String> 标准异常
     */
    @ExceptionHandler(MethodArgumentTypeMismatchException.class)
    public R<String> handleError(MethodArgumentTypeMismatchException e) {
        log.warn("请求参数格式错误，{}", e.getMessage());
        String message = String.format("请求参数格式错误: %s", e.getName());
        return R.fail(CommonResultCode.PARAM_TYPE_ERROR, message);
    }

    /**
     * 处理 MethodArgumentNotValidException
     *
     * @param e MethodArgumentNotValidException
     * @return R<String> 标准异常
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public R<String> handleError(MethodArgumentNotValidException e) {
        log.warn("参数验证失败，{}", e.getMessage());
        return handleError(e.getBindingResult());
    }

    /**
     * 处理 BindException
     *
     * @param e BindException
     * @return R<String> 标准异常
     */
    @ExceptionHandler(BindException.class)
    public R<String> handleError(BindException e) {
        log.warn("参数绑定失败，{}", e.getMessage());
        return handleError(e.getBindingResult());
    }

    /**
     * 内部方法
     *
     * @param result BindingResult
     * @return R<String> 标准异常
     */
    private R<String> handleError(BindingResult result) {
        FieldError error = result.getFieldError();
        String message;
        if (null == error) {
            return R.fail(CommonResultCode.PARAM_BIND_ERROR);
        } else {
            message = String.format("%s:%s", error.getField(), error.getDefaultMessage());
            return R.fail(CommonResultCode.PARAM_BIND_ERROR, error.getDefaultMessage(), message);
        }
    }

    /**
     * 处理 HttpMessageNotReadableException
     *
     * @param e HttpMessageNotReadableException
     * @return R<String> 标准异常
     */
    @ExceptionHandler(HttpMessageNotReadableException.class)
    public R<String> handleError(HttpMessageNotReadableException e) {
        log.error("消息不能读取,{}", e.getMessage(), e);
        return R.fail(CommonResultCode.MSG_NOT_READABLE, e.getMessage());
    }

    /**
     * 处理 HttpRequestMethodNotSupportedException
     *
     * @param e HttpRequestMethodNotSupportedException
     * @return R<String> 标准异常
     */
    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public R<String> handleError(HttpRequestMethodNotSupportedException e) {
        log.error("不支持当前请求方法,{}", e.getMessage(), e);
        return R.fail(CommonResultCode.METHOD_NOT_SUPPORTED, e.getMessage());
    }

    /**
     * 处理 HttpMediaTypeNotSupportedException
     *
     * @param e HttpMediaTypeNotSupportedException
     * @return R<String> 标准异常
     */
    @ExceptionHandler(HttpMediaTypeNotSupportedException.class)
    public R<String> handleError(HttpMediaTypeNotSupportedException e) {
        log.error("不支持当前媒体类型,{}", e.getMessage(), e);
        return R.fail(CommonResultCode.MEDIA_TYPE_NOT_SUPPORTED, e.getMessage());
    }

    /**
     * 处理 BadSqlGrammarException
     *
     * @param e BadSqlGrammarException
     * @return R<String> 标准异常
     */
    @ExceptionHandler(DataAccessException.class)
    public R<String> handleError(DataAccessException e) {
        log.error("数据库语句执行异常", e);
        return R.fail(CommonResultCode.INTERNAL_SERVER_ERROR);
    }

    /**
     * 处理 IORuntimeException
     *
     * @param e IORuntimeException
     * @return R<Object> 标准异常
     */
    @ExceptionHandler(IORuntimeException.class)
    public R<Object> handleError(IORuntimeException e) {
        log.error("IO异常", e);
        return R.fail(CommonResultCode.INTERNAL_SERVER_ERROR, "读写异常");
    }

    /**
     * 处理 ServiceException
     *
     * @param e ServiceException
     * @return R<String> 标准异常
     */
    @ExceptionHandler(ServiceException.class)
    public R<Object> handleError(ServiceException e) {
        log.error("业务异常({}){}", e.getResultCode(), Func.getMessageWithStack(e));
        return R.fail(e.getResultCode(), e.getMessage(), e.getData());
    }

    /**
     * 处理 ApiException
     *
     * @param e ApiException
     * @return R<String> 标准异常
     */
    @ExceptionHandler(ApiException.class)
    public R<Object> handleError(ApiException e) {
        log.error("Api异常调用({}):{}", e.getCode(), e.getMessage());
        return R.fail(e.getCode(), e.getMessage(), null);
    }


    /**
     * 处理 NyxException
     *
     * @param e NyxException
     * @return R<String> 标准异常
     */
    @ExceptionHandler(NyxException.class)
    public R<Object> handleError(NyxException e) {
        Throwable cause = e.getCause();
        if (null == cause) {
            log.error("运行异常,{}", e.getMessage());
        } else {
            log.error("运行异常,{},{}", e.getMessage(), Func.getMessageWithStack(cause));
        }
        return R.fail(CommonResultCode.INTERNAL_SERVER_ERROR, e.getMessage(), null);
    }

    /**
     * 处理 Throwable
     *
     * @param e Throwable
     * @return R<String> 标准异常
     */
    @ExceptionHandler(Throwable.class)
    public R<String> handleError(Throwable e) {
        log.error("服务异常", e);
        return R.fail(CommonResultCode.INTERNAL_SERVER_ERROR, e.getMessage());
    }

}
