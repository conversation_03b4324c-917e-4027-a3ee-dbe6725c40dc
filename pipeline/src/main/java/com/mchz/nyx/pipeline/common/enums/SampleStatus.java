package com.mchz.nyx.pipeline.common.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/1/14
 */
@Getter
@AllArgsConstructor
public enum SampleStatus implements IEnum<Integer> {

    OTHER(0),
    IMPORT(1),
    CRAWL(2),
    CRAWL_NULL(3),
    IMPORT_EMPTY(4),
    CRAWL_0ROWS(5),
    CRAWL_BIG(6),
    CRAWL_ERROR(7);
    private final Integer code;

    @Override
    public Integer getValue() {
        return code;
    }
}
