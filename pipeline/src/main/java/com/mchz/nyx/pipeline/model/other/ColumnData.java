package com.mchz.nyx.pipeline.model.other;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <p></p>
 *
 * <AUTHOR>
 * @date 2022/10/25 16:44
 * @since 1.7.0
 */
@Data
public class ColumnData implements Serializable {
    private static final long serialVersionUID = -8680341671499655320L;

    private List<Object> data;

    public ColumnData(List<Object> data) {
        this.data = data;
    }
}
