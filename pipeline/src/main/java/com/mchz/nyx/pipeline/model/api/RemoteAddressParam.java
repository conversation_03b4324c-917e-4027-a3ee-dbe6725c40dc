package com.mchz.nyx.pipeline.model.api;

import lombok.Data;

@Data
public class RemoteAddressParam {
    private Long id;

    private String name;

    private Integer type;

    private String url;

    private String port;

    private String username;

    private String password;

    private String charset;

    public boolean isSftp() {
        return null != type && 2 == type;
    }

    public boolean isFtp() {
        return null != type && 1 == type;
    }
    public static class Type {
        public static final int FTP = 1;
        public static final int SFTP = 2;
    }
}
