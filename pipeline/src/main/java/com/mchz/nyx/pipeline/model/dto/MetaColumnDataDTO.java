package com.mchz.nyx.pipeline.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/12/1 15:28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MetaColumnDataDTO implements Serializable {
    private static final long serialVersionUID = -5926265505409834284L;
    private MetaColumnDTO meta;
    private List<Object> data;

    public MetaColumnDataDTO(MetaColumnDTO meta) {
        this.meta = meta;
    }
}
