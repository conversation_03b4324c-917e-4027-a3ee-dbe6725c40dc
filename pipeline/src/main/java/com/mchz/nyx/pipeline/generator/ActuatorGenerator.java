package com.mchz.nyx.pipeline.generator;

import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.CharUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.mchz.nyx.common.util.CommUtil;
import com.mchz.nyx.dark.common.constants.RuleConst;
import com.mchz.nyx.dark.common.enums.AddName;
import com.mchz.nyx.dark.engine.VectorConvEngine;
import com.mchz.nyx.dark.exception.RuleException;
import com.mchz.nyx.dark.factory.AbstractDarkActuatorGenerator;
import com.mchz.nyx.dark.factory.EngineFactory;
import com.mchz.nyx.dark.factory.RangeFunction;
import com.mchz.nyx.dark.model.ClassifyInfo;
import com.mchz.nyx.dark.model.TargetInfo;
import com.mchz.nyx.dark.model.config.DarkConfig;
import com.mchz.nyx.dark.model.dto.ActuatorParam;
import com.mchz.nyx.dark.model.dto.StandardLoadContext;
import com.mchz.nyx.dark.model.dto.StdDetailDTO;
import com.mchz.nyx.pipeline.common.constants.PipelineConst;
import com.mchz.nyx.pipeline.config.props.EngineProperties;
import com.mchz.nyx.pipeline.config.props.SourceProperties;
import com.mchz.nyx.pipeline.job.JobStartConfig;
import com.mchz.nyx.pipeline.model.dto.*;
import com.mchz.nyx.pipeline.service.AnalysisHistoryService;
import com.mchz.nyx.pipeline.service.EntityMappingService;
import com.mchz.nyx.pipeline.service.InnerHistoryService;
import com.mchz.nyx.pipeline.service.RuleDetailService;
import com.mchz.nyx.pipeline.util.cache.EhCacheManager;
import lombok.extern.slf4j.Slf4j;
import net.sourceforge.pinyin4j.PinyinHelper;
import org.springframework.stereotype.Component;

import java.lang.ref.WeakReference;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class ActuatorGenerator extends AbstractDarkActuatorGenerator<JobStartConfig, DarkClassifyActuator> {
    private final EngineFactory engineFactory;
    private final RuleDetailService ruleDetailService;
    private final SourceProperties properties;
    private WeakReference<VectorConvEngine> refVectorConvEngine;

    public ActuatorGenerator(SourceProperties properties, RangeFunction rangeFunction, RuleDetailService ruleDetailService, EntityMappingService entityMappingService, AnalysisHistoryService analysisHistoryService, InnerHistoryService innerHistoryService, EhCacheManager ehCacheManager) {
        this.engineFactory = getEngineFactory(properties, rangeFunction, entityMappingService, analysisHistoryService, innerHistoryService, ehCacheManager);
        this.ruleDetailService = ruleDetailService;
        this.properties = properties;
    }

    private static DarkEngineFactory getEngineFactory(SourceProperties properties, RangeFunction rangeFunction, EntityMappingService entityMappingService, AnalysisHistoryService analysisHistoryService, InnerHistoryService innerHistoryService, EhCacheManager ehCacheManager) {
        EngineProperties engine = properties.getEngine();
        return new DarkEngineFactory(engine, rangeFunction, entityMappingService, analysisHistoryService, innerHistoryService, ehCacheManager);
    }

    @Override
    public DarkClassifyActuator generate(JobStartConfig param) {
        StandardLoadContext context = getContext(param);
        StandardDetailDTO detail = getStandardDetail(param, context);
        return generate(param, context, detail, (v) -> buildActuator(param, detail, v));
    }

    public DarkQuickActuator generateQuick(JobStartConfig param) {
        StandardLoadContext context = getContext(param);
        StandardDetailDTO detail = getStandardDetail(param, context);
        return generate(param, context, detail, DarkQuickActuator::new);
    }

    public DarkTrafficActuator generateTraffic(JobStartConfig param) {
        StandardLoadContext context = getContext(param);
        StandardDetailDTO detail = getStandardDetail(param, context);
        return generate(param, context, detail, DarkTrafficActuator::new);
    }

    public ClassifyRecommender generateClassifyRecommender(JobStartConfig config) {
        List<ClassifyTypeDTO> list = ruleDetailService.loadClassifyType(config.getStdId());
        return getClassifyRecommender(config, null, list);
    }

    public VectorConvEngine generateSimilarityEngine() {
        if (null != refVectorConvEngine) {
            VectorConvEngine engine = refVectorConvEngine.get();
            if (null != engine) {
                return engine;
            }
        }
        VectorConvEngine engine = engineFactory.createSimilarityEngine();
        refVectorConvEngine = new WeakReference<>(engine);
        return engine;
    }

    private DarkClassifyActuator buildActuator(JobStartConfig config, StandardDetailDTO stdDetail, ActuatorParam param) {
        VectorConvEngine similarityEngine;
        if (Boolean.TRUE.equals(config.getClassify())) {
            similarityEngine = engineFactory.createSimilarityEngine();
        } else {
            similarityEngine = null;
        }
        List<ClassifyVectorDTO> vectorList;
        float baseline;
        List<? extends ClassifyInfo> list = stdDetail.getRule().getClassify();
        if (null != similarityEngine) {
            vectorList = loadClassifyType(list, similarityEngine);
            baseline = null == config.getTableClassifyBaseline() ? PipelineConst.DEFAULT_TABLE_CLASSIFY_BASELINE : config.getTableClassifyBaseline() / 100f;
        } else {
            vectorList = null;
            baseline = 0;
        }
        ClassifyRecommender classifyRecommender = getClassifyRecommender(config, stdDetail.getInfoMap(), list);
        return new DarkClassifyActuator(param, similarityEngine, vectorList, baseline, ObjUtil.defaultIfNull(config.getNum(), PipelineConst.CANDIDATE_COUNT), classifyRecommender);
    }

    private ClassifyRecommender getClassifyRecommender(JobStartConfig config, Map<Long, ExtendInfoDTO> extendMap, List<? extends ClassifyInfo> list) {
        EngineProperties engine = properties.getEngine();
        YunCeClient client;
        int type = engine.isSkipDefault() ? 0 : 1;
        if (Boolean.TRUE.equals(config.getEmbedding()) && !engine.isOnlyLlm() && StrUtil.isNotBlank(config.getEmbeddingUrl())) {
            String industry = config.getIndustry();
            if (EngineProperties.RecommendType.LLM.equals(engine.getType())) {
                type += 2;
                industry = null;
            }
            client = YunCeClient.of(engine, config.getEmbeddingUrl(), StrUtil.emptyToDefault(config.getLlmUrl(), config.getEmbeddingUrl()), getIndustry(config.getIndustry(), config.getIndustryId()), industry);
        } else if (EngineProperties.RecommendType.LLM.equals(engine.getType()) && Boolean.TRUE.equals(config.getLlm()) && StrUtil.isNotBlank(config.getLlmUrl())) {
            client = YunCeClient.of(engine, null, config.getLlmUrl(), getIndustry(config.getIndustry(), config.getIndustryId()), null);
            type += 2;
        } else {
            client = null;
        }
        Map<String, ClassifyDTO> map = (type & 2) == 0 ? new HashMap<>(list.size()) : null;
        Map<Long, Integer> classifyLevel = new HashMap<>(list.size());
        list.forEach(v -> {
            if (null != map) {
                map.put(v.getFullName(), new ClassifyDTO(v.getId(), v.getSensitiveLevel()));
            }
            classifyLevel.put(v.getId(), v.getSensitiveLevel());
        });
        return new ClassifyRecommender(extendMap, map, classifyLevel, client, type);
    }

    private List<ClassifyVectorDTO> loadClassifyType(List<? extends ClassifyInfo> list, VectorConvEngine similarityEngine) {
        List<ClassifyVectorDTO> vectorList = new ArrayList<>(list.size());
        for (ClassifyInfo classify : list) {
            ClassifyDTO dto = new ClassifyDTO(classify.getId(), classify.getSensitiveLevel());
            if (StrUtil.isNotEmpty(classify.getDescription())) {
                float[] vector = similarityEngine.getPreVector(classify.getDescription());
                if (null != vector) {
                    vectorList.add(new ClassifyVectorDTO(vector, dto));
                    continue;
                }
            }
            float[] vector = similarityEngine.getPreVector(classify.getTypeName());
            if (null == vector) {
                continue;
            }
            vectorList.add(new ClassifyVectorDTO(vector, dto));
        }
        return vectorList;
    }

    private StandardLoadContext getContext(JobStartConfig param) {
        return StandardLoadContext.builder().tenant(param.getTenantId()).withTime(System.currentTimeMillis()).build();
    }

    private StandardDetailDTO getStandardDetail(JobStartConfig param, StandardLoadContext context) {
        boolean autoRule = Boolean.TRUE.equals(param.getAutoRule());
        Double hit;
        if (autoRule && null != param.getHitRate()) {
            hit = param.getHitRate() / 100d;
        } else {
            hit = null;
        }
        param.setIndustryId(ruleDetailService.getIndustryId(param.getStdId(), param.getIndustryId()));
        StandardDetailDTO ruleDetail = ruleDetailService.loadRuleDetail(context, param.getStdId(), param.getIndustryId(), autoRule, null == param.getLoadBase() || param.getLoadBase(), hit);
        if (null == ruleDetail) {
            throw new RuleException("无效规则集");
        }
        return ruleDetail;
    }

    private <T> T generate(JobStartConfig param, StandardLoadContext context, StandardDetailDTO stdDetail, Function<ActuatorParam, T> build) {
        StdDetailDTO detail = stdDetail.getRule();
        if (null == detail) {
            return build.apply(ActuatorParam.empty());
        }
        if (Boolean.TRUE.equals(param.getAiPinyin()) && null != detail.getTargets()) {
            //业务类型首字母 / 业务类型全拼
            loadAutoColumn(detail);
        }

        DarkConfig.DarkConfigBuilder builder = DarkConfig.builder()
            .context(context)
            .stdId(param.getStdId())
            .industryId(param.getIndustryId())
            .industry(getIndustry(param.getIndustry(), param.getIndustryId()))
            .fast(Boolean.TRUE.equals(param.getTraffic()))
            .autoColName(Boolean.TRUE.equals(param.getAutoColumn()))
            .autoColComment(Boolean.TRUE.equals(param.getAutoComment()))
            .recallHis(!Boolean.TRUE.equals(param.getIgnoreHis()))
            .nlp(Boolean.TRUE.equals(param.getNlp()))
            .llm(Boolean.TRUE.equals(param.getLlm()) && StrUtil.isNotEmpty(param.getLlmUrl()))
            .semantic(Boolean.TRUE.equals(param.getEmbedding()) && !properties.getEngine().isOnlyLlm() && StrUtil.isNotBlank(param.getEmbeddingUrl()))
            .url(param.getEmbeddingUrl())
            .llmUrl(param.getLlmUrl())
            .commentOverlay(!Boolean.TRUE.equals(param.getIgnoreCandidate()))
            .candidateCount(ObjUtil.defaultIfNull(param.getNum(), PipelineConst.CANDIDATE_COUNT));
        if (null != param.getSampleRate() && null != param.getSampleLine()) {
            int size = (param.getSampleLine() * param.getSampleRate()) / 100;
            builder.cacheSize(Math.min(Math.max(size, 100), RuleConst.DEFAULT_CACHE_SIZE << 1));
        }
        if (null != param.getCommentBaseline()) {
            builder.colCommentBaseline(param.getCommentBaseline());
        }
        if (null != param.getColumnBaseline()) {
            builder.colNameBaseline(param.getColumnBaseline());
        }
        DarkConfig config = builder.build();
        ActuatorParam stages = generatorStage(detail, config, engineFactory);
        return build.apply(stages);
    }

    private void loadAutoColumn(StdDetailDTO detail) {
        detail.getTargets().values().stream().filter(TargetInfo::isEntity).forEach(v -> {
            List<String> pinyin = getPinyin(v.getName().trim());
            if (null != pinyin) {
                v.put(AddName.PINYIN, pinyin);
            }
        });
    }

    private static List<String> getPinyin(String s) {
        if (!s.codePoints().allMatch(CommUtil::isChineseChar)) {
            return null;
        }
        StringBuilder first = new StringBuilder();
        StringBuilder full = new StringBuilder();
        for (int i = 0; i < s.length(); i++) {
            char ch = s.charAt(i);
            String[] pinyin = PinyinHelper.toHanyuPinyinStringArray(ch);
            if (ArrayUtil.isEmpty(pinyin)) {
                return null;
            }
            String py = pinyin[0];
            first.append(CommUtil.letterUpper(py.charAt(0)));
            for (char c : py.toCharArray()) {
                if (CharUtil.isLetterUpper(c)) {
                    full.append(c);
                } else if (CharUtil.isLetterLower(c)) {
                    full.append(CommUtil.letterUpper(c));
                } else {
                    break;
                }
            }
        }
        if (first.length() <= 2) {
            return Collections.singletonList(full.toString());
        }
        return Arrays.asList(first.toString(), full.toString());
    }

    private static String getIndustry(String industry, Integer industryId) {
        if (null == industryId) {
            return industry;
        }
        String jsonStr = ResourceUtil.readStr("classpath:data/industry.json", StandardCharsets.UTF_8);
        JSONObject json = JSONUtil.parseObj(jsonStr);
        return StrUtil.nullToDefault(json.getStr(industryId.toString()), industry);
    }
}
