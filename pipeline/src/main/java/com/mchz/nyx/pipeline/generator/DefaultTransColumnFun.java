package com.mchz.nyx.pipeline.generator;

import cn.hutool.core.collection.CollUtil;
import com.mchz.nyx.dark.engine.algorithm.TrieMatch;
import com.mchz.nyx.dark.factory.TransColumnFun;
import com.mchz.nyx.pipeline.service.EntityMappingService;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/18
 */
@AllArgsConstructor
public class DefaultTransColumnFun implements TransColumnFun {
    private final TrieMatch<Integer> match;
    private final EntityMappingService service;

    @Override
    public String toChineseName(String name) {
        List<Integer> list = match.match(name, true);
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        return service.getChineseName(list.get(0));
    }
}
