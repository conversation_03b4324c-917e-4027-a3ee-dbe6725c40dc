package com.mchz.nyx.pipeline.job.relation.stage;

import cn.hutool.core.collection.CollUtil;
import com.mchz.mcdatasource.model.core.StreamDataType;
import com.mchz.nyx.pipeline.config.props.SourceProperties;
import com.mchz.nyx.pipeline.entity.ResultMetaColumn;
import com.mchz.nyx.pipeline.entity.ResultMetaTable;
import com.mchz.nyx.pipeline.exception.ServiceException;
import com.mchz.nyx.pipeline.job.JobLogManager;
import com.mchz.nyx.pipeline.job.JobStartConfig;
import com.mchz.nyx.pipeline.job.relation.util.JobUtil;
import com.mchz.nyx.pipeline.job.relation.util.MetadataUtil;
import com.mchz.nyx.pipeline.mapper.ResultMetaColumnMapper;
import com.mchz.nyx.pipeline.mapper.ResultMetaTableMapper;
import com.mchz.nyx.pipeline.model.api.DataSourceLimitParam;
import com.mchz.nyx.pipeline.model.other.MetaTableExpand;
import com.mchz.nyx.pipeline.service.DatabaseFactory;
import com.mchz.nyx.pipeline.service.impl.SupportAllTablesAndViewsDatabaseType;
import com.mchz.nyx.pipeline.service.impl.SupportViewDatabaseType;
import com.mchz.nyx.pipeline.util.Lists;
import com.mchz.nyx.pipeline.util.Timer;
import com.mchz.nyx.pipeline.util.cache.EhCacheManager;
import com.mchz.nyx.pipeline.util.cache.ListCache;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

import static com.mchz.nyx.common.util.PartitionUtil.DEFAULT_PART_SIZE;

@Slf4j
@Component
@AllArgsConstructor
public class AssetRelationJobQueryMetaStage {
    private static final String CACHE_KEY_META_TABLE_SUFFIX = "ASSET_RELATION_JOB_META_TABLE";
    private static final String CACHE_KEY_META_COLUMN_SUFFIX = "ASSET_RELATION_JOB_META_COLUMN";
    private static final String CACHE_KEY_META_TABLE_EXPAND = "ASSET_RELATION_JOB_META_TABLE_EXPAND";

    private final EhCacheManager ehCacheManager;
    private final SourceProperties properties;
    private final DatabaseFactory metadataFactory;

    private final ResultMetaColumnMapper columnDisplayMapper;
    private final ResultMetaTableMapper tableDisplayMapper;

    public ListCache<MetaTableExpand> execute(JobLogManager jobLog, JobStartConfig param) {
        try (ListCache<ResultMetaTable> metaTableCache = getMetaTableCache(param.getJobId());//
             ListCache<ResultMetaColumn> metaColumnCache = getMetaColumnCache(param.getJobId())) {
            ListCache<MetaTableExpand> metaTableExpandCache = getMetaTableExpandCache(param.getJobId());
            execute(jobLog, param, metaTableCache, metaColumnCache, metaTableExpandCache);
            return metaTableExpandCache;
        }
    }

    private void execute(JobLogManager jobLog, JobStartConfig param,
                         ListCache<ResultMetaTable> metaTableCache, ListCache<ResultMetaColumn> metaColumnCache,
                         ListCache<MetaTableExpand> metaTableExpandCache) {
        DataSourceLimitParam source = param.getSource();
        source.setStreamDataTypes(Lists.newArrayList(StreamDataType.TABLE_COLUMN, StreamDataType.COLUMN_COMMENT,
            StreamDataType.TABLE_COMMENTS, StreamDataType.TABLE_COUNT, StreamDataType.PRIMARY_KEY,
            StreamDataType.FOREIGN_KEY, StreamDataType.INDEX));

        boolean complete;
        try {
            complete = metadataFactory.crawlMetadata(source, jobLog, (table, columns, tableExpand) -> {
                if (Thread.currentThread().isInterrupted()) {
                    return;
                }
                ResultMetaTable resultMetaTable = MetadataUtil.buildResultMetaTable(param.getJobId(),
                    param.getSource().getId(), table);

                if (CollUtil.isNotEmpty(columns)) {
                    MetadataUtil.fillColumnStatistics(resultMetaTable, columns);
                    List<ResultMetaColumn> resultMetaColumns = columns.stream()//
                        .map(column -> MetadataUtil.buildResultMetaColumn(resultMetaTable, column))
                        .collect(Collectors.toList());
                    metaColumnCache.addAll(resultMetaColumns);
                }
                metaTableCache.add(resultMetaTable);
                metaTableExpandCache.add(tableExpand);
            });
            if (!complete) {
                throw new ServiceException(
                    String.format("【执行任务】%s数据源(%s)元数据采集失败", source.getConfigName(), source.getDataBaseType().name));
            }
        } catch (Exception e) {
            throw new ServiceException(
                String.format("【执行任务】%s数据源(%s)元数据采集失败", source.getConfigName(), source.getDataBaseType().name), e);
        }

        try {
            if (properties.getSys().isEnableCollectView() //
                && (SupportViewDatabaseType.isSupport(source.getDataBaseType()) || SupportAllTablesAndViewsDatabaseType.isSupport(source.getDataBaseType()))) {
                complete = metadataFactory.crawlViewMetadata(source, jobLog,
                    (table, columns, tableExpand) -> {
                        if (Thread.currentThread().isInterrupted()) {
                            return;
                        }
                        ResultMetaTable resultMetaTable = MetadataUtil.buildResultMetaTable(param.getJobId(),
                            param.getSource().getId(), table);
                        resultMetaTable.setIsView(Boolean.TRUE);
                        if (CollUtil.isNotEmpty(columns)) {
                            MetadataUtil.fillColumnStatistics(resultMetaTable, columns);
                            List<ResultMetaColumn> resultMetaColumns = columns.stream()//
                                .map(column -> MetadataUtil.buildResultMetaColumn(resultMetaTable, column))
                                .collect(Collectors.toList());
                            metaColumnCache.addAll(resultMetaColumns);
                        }
                        metaTableCache.add(resultMetaTable);
                        metaTableExpandCache.add(tableExpand);
                    });
                if (!complete) {
                    throw new ServiceException(
                        String.format("【执行任务】%s数据源(%s)视图元数据采集失败", source.getConfigName(),
                            source.getDataBaseType().name));
                }
            }
        } catch (Exception e) {
            throw new ServiceException(String.format("【执行任务】%s数据源(%s)视图元数据采集失败", source.getConfigName(),
                source.getDataBaseType().name), e);
        }

        Timer timer = Timer.start("保存元数据");
        timer.startChild("表数据");
        metaTableCache.consume(tableDisplayMapper::insertList, DEFAULT_PART_SIZE);
        timer.endChild();
        timer.startChild("列数据");
        metaColumnCache.consume(columnDisplayMapper::insertList, DEFAULT_PART_SIZE);
        timer.endChild();
        jobLog.info("元数据采集完成");
    }

    private ListCache<ResultMetaTable> getMetaTableCache(Long jobId) {
        return new ListCache<>(ehCacheManager,
            String.join("_", JobUtil.buildTaskId(jobId), CACHE_KEY_META_TABLE_SUFFIX),
            properties.getEhcache().getTypeListPartSize(), ResultMetaTable.class);
    }

    private ListCache<ResultMetaColumn> getMetaColumnCache(Long jobId) {
        return new ListCache<>(ehCacheManager,
            String.join("_", JobUtil.buildTaskId(jobId), CACHE_KEY_META_COLUMN_SUFFIX),
            properties.getEhcache().getTypeListPartSize(), ResultMetaColumn.class);
    }

    private ListCache<MetaTableExpand> getMetaTableExpandCache(Long jobId) {
        return new ListCache<>(ehCacheManager,
            String.join("_", JobUtil.buildTaskId(jobId), CACHE_KEY_META_TABLE_EXPAND),
            properties.getEhcache().getTypeListPartSize(), MetaTableExpand.class);
    }
}
