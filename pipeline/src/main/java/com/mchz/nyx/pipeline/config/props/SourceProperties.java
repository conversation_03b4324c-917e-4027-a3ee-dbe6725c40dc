package com.mchz.nyx.pipeline.config.props;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.context.annotation.Configuration;

/**
 * <p>
 * 项目配置信息
 * </p>
 *
 * <AUTHOR>
 * @date 2020/11/3 17:11
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "source")
public class SourceProperties {
    /**
     * 系统默认属性配置
     */
    @NestedConfigurationProperty
    private SysProperties sys = new SysProperties();
    /**
     * 缓存配置
     */
    @NestedConfigurationProperty
    private EhCacheProperties ehcache = new EhCacheProperties();
    /**
     * 引擎配置
     */
    @NestedConfigurationProperty
    private EngineProperties engine = new EngineProperties();
}
