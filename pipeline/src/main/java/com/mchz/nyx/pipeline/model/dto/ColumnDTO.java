package com.mchz.nyx.pipeline.model.dto;

import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2022/1/12 18:37
 */
@Data
public class ColumnDTO {
    /**
     * 主键
     */
    private Long oid;

    /**
     * 表id
     */
    private Long tableId;

    /**
     * 列名字
     */
    private String name;

    /**
     * 列注释
     */
    private String description;

    /**
     * 原始类型
     */
    private String dataType;

    /**
     * 类型分组
     */
    private String typeGroup;

    /**
     * 列长度
     */
    private Long length;

    /**
     * 精度
     */
    private Integer precision;

    /**
     * 刻度值
     */
    private Integer scale;

    /**
     * 是否可以为空，0 - 否，1 - 是
     */
    private Boolean nullable;

    /**
     * 位置
     */
    private Integer position;

    /**
     * 是否自增
     */
    private Boolean isAutoIncremented;

    /**
     * 是否生成
     */
    private Boolean isGenerated;

    /**
     * 是否主键
     */
    private Boolean isPk;

    /**
     * 是否唯一键
     */
    private Boolean isUnique;

    /**
     * 是否索引
     */
    private Boolean isIndex;

    /**
     * 是否外键
     */
    private Boolean isFk;
}
