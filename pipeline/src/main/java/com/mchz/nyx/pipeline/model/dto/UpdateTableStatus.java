package com.mchz.nyx.pipeline.model.dto;

import com.mchz.nyx.pipeline.entity.SampleTable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/1/16
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class UpdateTableStatus {
    private List<SampleTable> insert;
    private List<Long> isEmpty;
    private List<Long> notEmpty;

    public static UpdateTableStatus of() {
        return new UpdateTableStatus(new ArrayList<>(0), new ArrayList<>(0), new ArrayList<>(0));
    }

    public void clear() {
        if (null != insert) {
            insert.clear();
        }
        if (null != isEmpty) {
            isEmpty.clear();
        }
        if (null != notEmpty) {
            notEmpty.clear();
        }
    }
}
