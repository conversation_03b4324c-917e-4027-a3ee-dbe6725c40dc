package com.mchz.nyx.pipeline.generator;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.StrUtil;
import com.mchz.nyx.dark.model.TargetInfo;
import com.mchz.nyx.dark.model.TargetResult;
import com.mchz.nyx.pipeline.common.enums.ClassifyHitType;
import com.mchz.nyx.pipeline.model.dto.*;
import lombok.AllArgsConstructor;

import java.util.*;
import java.util.stream.Stream;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/1
 */
@AllArgsConstructor
public class ClassifyRecommender implements AutoCloseable {
    private final Map<Long, ExtendInfoDTO> extendMap;

    private final Map<String, ClassifyDTO> map;
    private final Map<Long, Integer> classifyLevel;

    private final YunCeClient client;
    private final int type;

    public boolean nonClient() {
        return null == client;
    }

    public void recommendColumnClassify(DarkContext context, ResultTableColumnDTO result) {
        TableDTO table = result.getTable();

        List<ResultColumnDTO> multiCls;
        List<ResultColumnDTO> noCls;
        if (CollUtil.isEmpty(extendMap) || (type & 1) == 0 && null != client) {
            multiCls = Collections.emptyList();
            noCls = result.getColumns();
        } else {
            List<ResultColumnDTO> columns = result.getColumns();
            multiCls = new ArrayList<>(columns.size() >> 2);
            noCls = new ArrayList<>(columns.size() >> 1);
            classifyRecPre(table, columns, extendMap, multiCls, noCls);
        }
        if (null == client) {
            classifyRecMulti(table, multiCls);
            classifyRecDefault(table, noCls);
        } else {
            if ((type & 2) == 0) {
                classifyRecMulti(table, multiCls);
                classifyRecEmbedding(context, noCls);
            } else {
                classifyRecLLM(context, table, multiCls, true);
                classifyRecMulti(table, multiCls.stream().filter(v -> null == v.getClassify()));
                classifyRecLLM(context, table, noCls, false);
            }
        }
    }

    public List<ClassifyFileDTO> recommendFileClassify(DarkContext context, String docId, String path) {
        if (null == client) {
            return Collections.emptyList();
        }
        List<ClassifyFileDTO> list = client.askCategoryLLM(context, docId, path);
        if (null == list) {
            return Collections.emptyList();
        }
        list.forEach(v -> v.setLevel(classifyLevel.get(v.getTypeId())));
        return list;
    }

    private void classifyRecPre(TableDTO table, List<ResultColumnDTO> columns, Map<Long, ExtendInfoDTO> extendMap, List<ResultColumnDTO> multiCls, List<ResultColumnDTO> noCls) {
        Map<Long, Integer> classifyNum = table.getClassifyNum();
        for (ResultColumnDTO column : columns) {
            TargetResult first = column.getFirst();
            if (null == first) {
                noCls.add(column);
                continue;
            }
            if (null != first.getInfo().getClassifyId()) {
                column.setClassify(new ClassifyDTO(first.getInfo().getClassifyId(), first.getInfo().getLevel()), ClassifyHitType.RULE);
                continue;
            }
            ExtendInfoDTO info;
            if (null == (info = extendMap.get(first.getInfo().getId())) || info.getClassify().isEmpty()) {
                noCls.add(column);
                continue;
            }
            if (1 == info.getClassify().size()) {
                ClassifyDTO classify = info.getClassify().get(0);
                column.setClassify(classify, ClassifyHitType.RULE);
                classifyNum.compute(classify.getTypeId(), (k, v) -> v == null ? 1 : v + 1);
                continue;
            }
            column.setClassifyList(info.getClassify());
            multiCls.add(column);
        }
        for (ResultColumnDTO column : columns) {
            if (null == column.getResult()) {
                continue;
            }
            column.getResult().stream().skip(1)
                .filter(v -> v.getInfo() != null && v.getInfo().getClassifyId() == null)
                .forEach(v -> {
                    TargetInfo info = v.getInfo();
                    ExtendInfoDTO dto = extendMap.get(info.getId());
                    if (null == dto || CollUtil.isEmpty(dto.getClassify())) {
                        return;
                    }
                    dto.getClassify().stream()
                        .filter(classify -> classifyNum.get(classify.getTypeId()) != null)
                        .max(Comparator.comparing(classify -> classifyNum.get(classify.getTypeId())))
                        .ifPresent(s -> v.updateTargetInfo(s.getTypeId(), s.getLevel()));
                });
        }
    }

    private void classifyRecMulti(TableDTO table, List<ResultColumnDTO> multiCls) {
        classifyRecMulti(table, multiCls.stream());
    }

    private void classifyRecMulti(TableDTO table, Stream<ResultColumnDTO> multiCls) {
        Map<Long, Integer> classifyNum = table.getClassifyNum();
        multiCls.forEach(column -> {
            Optional<ClassifyDTO> optional = column.getClassifyList().stream().map(v -> Pair.of(v, classifyNum.get(v.getTypeId()))).filter(v -> null != v.getValue()).max(Comparator.comparing(Pair::getValue)).map(Pair::getKey);
            column.setClassify(optional.orElse(column.getClassifyList().get(0)), ClassifyHitType.RULE);
        });
    }

    private void classifyRecDefault(TableDTO table, List<ResultColumnDTO> temp) {
        if (null == table.getClassify()) {
            return;
        }
        ClassifyDTO classify = table.getClassify();
        temp.forEach(v -> v.setClassify(classify, ClassifyHitType.RULE));
    }

    private void classifyRecEmbedding(DarkContext context, List<ResultColumnDTO> columns) {
        for (ResultColumnDTO column : columns) {
            context.interrupted();
            String des = column.getBizName();
            if (StrUtil.isEmpty(des)) {
                continue;
            }
            List<EmbeddingDTO> list = client.askCategory(des);
            Optional<ClassifyDTO> first = list.stream().filter(v -> v.getScore() > 0.7)
                .map(v -> StrUtil.subAfter(v.getId(), StrUtil.C_COLON, true))
                .map(v -> StrUtil.replace(v, ".", "-"))
                .map(map::get).filter(Objects::nonNull).findFirst();
            first.ifPresent(v -> column.setClassify(v, ClassifyHitType.EBM));
        }
    }

    private void classifyRecLLM(DarkContext context, TableDTO table, List<ResultColumnDTO> columns, boolean limit) {
        long[] key = context.startChild(limit ? "cr LLM reranker" : "cr LLM");
        Map<String, ClassifyDTO> res;
        try {
            res = client.askCategoryLLM(context, table, columns, limit);
        } finally {
            context.stopChild(key);
        }
        columns.forEach(v -> updateClassify(v, res));
    }

    private void updateClassify(ResultColumnDTO column, Map<String, ClassifyDTO> map) {
        ClassifyDTO classify = map.get(column.getMeta().getColumnName());
        if (null == classify || null == classify.getTypeId()) {
            return;
        }
        if (null != column.getClassifyList()) {
            column.getClassifyList().stream().filter(v -> classify.getTypeId().equals(v.getTypeId())).findFirst().ifPresent(v -> classify.setLevel(v.getLevel()));
        }
        if (null == classify.getLevel()) {
            classify.setLevel(classifyLevel.get(classify.getTypeId()));
        }
        column.setClassify(classify, ClassifyHitType.LLM);
    }

    @Override
    public void close() {
        if (null != client) {
            client.taskFinish();
        }
    }
}
