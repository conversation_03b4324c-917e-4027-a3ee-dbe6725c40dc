package com.mchz.nyx.pipeline.generator.engine;

import cn.hutool.core.collection.CollUtil;
import com.mchz.nyx.dark.engine.HistoryRetrievalEngine;
import com.mchz.nyx.dark.model.TargetInfo;
import com.mchz.nyx.dark.model.meta.NyxMetaColumn;
import com.mchz.nyx.dark.model.meta.NyxMetaTable;
import com.mchz.nyx.dark.util.bloomfilter.CustomBloomFilter;
import com.mchz.nyx.pipeline.service.InnerHistoryService;

import java.util.Arrays;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/22
 */
public class InnerRetrievalFilterEngine implements HistoryRetrievalEngine {
    private final InnerHistoryService innerHistoryService;

    private final CustomBloomFilter filter;
    private final Map<Integer, TargetInfo> resMap;

    public static String getTcKey(String t, String c) {
        return Arrays.asList(t, c).toString();
    }

    public InnerRetrievalFilterEngine(InnerHistoryService innerHistoryService, CustomBloomFilter filter, Map<Integer, TargetInfo> resMap) {
        this.innerHistoryService = innerHistoryService;
        this.filter = filter;
        this.resMap = resMap;
    }

    @Override
    public List<TargetInfo> retrieve(NyxMetaTable table, NyxMetaColumn column) {
        String tableName = table.getTableName().toUpperCase(Locale.ROOT);
        String columnName = column.getColumnName().toUpperCase(Locale.ROOT);
        if (filter.contains(getTcKey(tableName, columnName))) {
            List<Integer> list = innerHistoryService.loadHistoryResult(tableName, columnName);
            if (CollUtil.isEmpty(list)) {
                return null;
            }
            return list.stream().map(resMap::get).collect(Collectors.toList());
        }
        return null;
    }
}
