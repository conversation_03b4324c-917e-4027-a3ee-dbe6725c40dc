package com.mchz.nyx.pipeline.model.excel;

import cn.hutool.core.annotation.Alias;
import cn.idev.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/15
 */
@Data
public class MetaColumn {
    @Alias("Schema")
    @ExcelProperty(value = "Schema", index = 0)
    private String schema;

    @Alias("表名")
    @ExcelProperty(value = "表名", index = 1)
    private String tableName;

    @Alias("字段名")
    @ExcelProperty(value = "字段名", index = 2)
    private String columnName;

    @Alias("字段注释")
    @ExcelProperty(value = "字段注释", index = 3)
    private String columnComment;

    private String expandContent;
}
