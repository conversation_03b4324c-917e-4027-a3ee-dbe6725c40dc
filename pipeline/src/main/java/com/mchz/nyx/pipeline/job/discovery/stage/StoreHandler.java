package com.mchz.nyx.pipeline.job.discovery.stage;

import com.lmax.disruptor.EventHandler;
import com.mchz.nyx.pipeline.entity.SampleColumn;
import com.mchz.nyx.pipeline.entity.SampleTable;
import com.mchz.nyx.pipeline.exception.ChainEndInterruptException;
import com.mchz.nyx.pipeline.generator.DarkContext;
import com.mchz.nyx.pipeline.job.discovery.param.DiscoveryEvent;
import com.mchz.nyx.pipeline.model.dto.AnalysisResultDTO;
import com.mchz.nyx.pipeline.model.dto.TableDTO;
import com.mchz.nyx.pipeline.model.dto.UpdateTableStatus;
import com.mchz.nyx.pipeline.service.AnalysisService;
import com.mchz.nyx.pipeline.service.MetaExtendService;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/9
 */
@Slf4j
public class StoreHandler implements EventHandler<DiscoveryEvent> {
    private final DarkContext context;
    private final AnalysisService analysisService;
    private final MetaExtendService metaExtendService;

    private final UpdateTableStatus updateTableStatus;

    private List<SampleColumn> sampleList;
    private AnalysisResultDTO analysisResult;
    private TableDTO table;

    private CompletableFuture<?> future;
    private CompletableFuture<?> sampleFuture;

    public StoreHandler(DarkContext context, AnalysisService analysisService, MetaExtendService metaExtendService) {
        this.context = context;
        this.analysisService = analysisService;
        this.metaExtendService = metaExtendService;
        this.updateTableStatus = context.isTableStatus() ? UpdateTableStatus.of() : null;

        this.future = CompletableFuture.completedFuture(null);
        this.sampleFuture = CompletableFuture.completedFuture(null);
        this.sampleList = new ArrayList<>(0);
        this.analysisResult = null;
    }

    public void join() {
        long[] key = context.startChild("store");
        future.join();
        context.stopChild(key);
        key = context.startChild("save sample");
        sampleFuture.join();
        context.stopChild(key);
    }

    @Override
    public void onEvent(DiscoveryEvent event, long sequence, boolean endOfBatch) {
        if (context.isAlerted()) {
            throw ChainEndInterruptException.INSTANCE;
        }
        mergeData(event);
        if (!endOfBatch) {
            return;
        }
        saveData();
    }

    protected void mergeData(DiscoveryEvent event) {
        AnalysisResultDTO result = event.getAnalysisResult();
        if (null != result) {
            table = event.getTable();
            event.setTable(null);
            event.setAnalysisResult(null);
            if (null == analysisResult) {
                analysisResult = result;
            } else {
                analysisResult.combine(result);
            }
            List<SampleColumn> sample = event.getSample();
            if (null != sample) {
                event.setSample(null);
                sampleList.addAll(sample);
            }
        } else {
            event.setSample(null);
        }
        if (context.isTableStatus() && null != event.getStatus()) {
            if (null == table.getStatus()) {
                SampleTable sampleTable = new SampleTable();
                sampleTable.setOid(table.getOid());
                sampleTable.setSourceId(context.getSourceId());
                sampleTable.setSchemaId(table.getSchemaId());
                sampleTable.setDataEmpty(event.getStatus());
                updateTableStatus.getInsert().add(sampleTable);
            } else if (!table.getStatus().equals(event.getStatus())) {
                if (1 == event.getStatus()) {
                    updateTableStatus.getIsEmpty().add(table.getOid());
                } else if (2 == event.getStatus()) {
                    updateTableStatus.getNotEmpty().add(table.getOid());
                }
            }
            table.setStatus(event.getStatus());
            event.setStatus(null);
        }
    }

    protected void saveData() {
        long[] key;
        if (null != analysisResult) {
            key = context.startChild("store");
            CompletableFuture<Boolean> saved = analysisService.saveAnalysisResult(context.getPlanId(), context.getJobId(), analysisResult, context.getDefaultClassifyId(), context.getDefaultLevel());
            Long schemaId = table.getSchemaId();
            Long tableId = table.getOid();
            future = CompletableFuture.allOf(future, saved.thenRun(() -> context.heartbeat(schemaId, tableId)));
            context.stopChild(key);
            analysisResult = null;
        }
        if (!sampleList.isEmpty()) {
            key = context.startChild("save sample");
            sampleFuture = CompletableFuture.allOf(sampleFuture, metaExtendService.asyncSaveSample(sampleList, context.isOverlaySample()));
            context.stopChild(key);
            sampleList = new ArrayList<>(0);
        }
        if (context.isTableStatus()) {
            metaExtendService.updateTableStatus(updateTableStatus);
            updateTableStatus.clear();
        }
    }
}
