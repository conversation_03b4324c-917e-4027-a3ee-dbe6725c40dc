package com.mchz.nyx.pipeline.config.props;

import com.mchz.nyx.dark.model.config.EngineConfig;
import lombok.Data;

import java.util.List;

@Data
public class EngineProperties implements EngineConfig {
    /**
     * 缓存路径
     */
    private String cachePath;
    private String onnx;
    private String vocab;
    private Integer maxSeqLen;
    /**
     * 向量文件路径
     */
    private String vectorPath;
    /**
     * 基础中文字典文件路径
     */
    private String transDictPath;
    /**
     * 中文音节文件路径
     */
    private String chinesePath;
    /**
     * 自定义中文字典文件路径
     */
    private List<String> customDictPaths;

    private int tryCount = 5;
    private int partitionSize = 8;
    private int expectNum = 50000;

    private boolean ignoreCheck = false;

    /**
     * 推荐类型
     */
    private RecommendType type = RecommendType.LLM;

    /**
     * 是否跳过默认推荐
     */
    private boolean skipDefault = false;

    private boolean onlyLlm = false;

    /**
     * 批量推荐分类大小
     */
    private int batchClassifySize = 10;

    private int termTimeout = 60_000;
    /**
     * 分类超时时间(毫秒)
     */
    private int classifyTimeout = 180_000;

    /**
     * 文件分类超时时间(毫秒)
     */
    private int fileClassifyTimeout = 600_000;

    /**
     * 聊天超时时间(毫秒)
     */
    private int chatTimeout = 180_000;

    private String stdUrl;

    public enum RecommendType {
        /**
         * 语义向量推荐
         */
        EMBEDDING,
        /**
         * LLM推荐
         */
        LLM
    }
}
