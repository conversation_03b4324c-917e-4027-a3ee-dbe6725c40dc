package com.mchz.nyx.pipeline.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/1/16
 */
@Data
@TableName("sample_table")
public class SampleTable {
    @TableId(type = IdType.INPUT)
    private Long oid;

    private Long sourceId;

    private Long schemaId;
    /**
     * UNKNOWN(0, "未知", 2),
     * YES(1, "是", 0),
     * NO(2, "否", 1),
     */
    private Integer dataEmpty;
}
