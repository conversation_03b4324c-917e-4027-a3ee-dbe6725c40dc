package com.mchz.nyx.pipeline.embed;

import cn.hutool.cache.Cache;
import cn.hutool.cache.CacheUtil;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.autoconfigure.MybatisPlusProperties;
import com.mchz.base.metis.*;
import com.mchz.base.metis.model.MetaInfo;
import com.mchz.base.metis.model.TrafficParam;
import com.mchz.base.metis.model.TrafficResult;
import com.mchz.mcdatasource.core.DataBaseType;
import com.mchz.nyx.common.exception.NyxException;
import com.mchz.nyx.dark.model.meta.AdditionalInfo;
import com.mchz.nyx.pipeline.common.constants.PipelineConst;
import com.mchz.nyx.pipeline.common.enums.JobType;
import com.mchz.nyx.pipeline.generator.ActuatorGenerator;
import com.mchz.nyx.pipeline.generator.DarkTrafficActuator;
import com.mchz.nyx.pipeline.job.JobStartConfig;
import com.mchz.nyx.pipeline.job.TaskSchedulingManager;
import com.mchz.nyx.pipeline.model.api.ColumnReq;
import com.mchz.nyx.pipeline.model.api.DataSourceLimitParam;
import com.mchz.nyx.pipeline.model.dto.MatchResDTO;
import com.mchz.nyx.pipeline.model.dto.TableDTO;
import com.mchz.nyx.pipeline.model.dto.TrafficResDTO;
import com.mchz.nyx.pipeline.service.impl.JobStatusServiceEmbedImpl;
import com.mchz.nyx.pipeline.service.impl.RangeFunctionImpl;
import com.mchz.nyx.pipeline.service.impl.RuleDetailServiceEmbedImpl;
import com.mchz.starter.dynamic.support.DataSourceHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

import javax.sql.DataSource;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/8/16
 */
@Profile("embed")
@Slf4j
@RequiredArgsConstructor
@Component
public class MetisEngineImpl implements MetisEngine, InitializingBean {
    private final TaskSchedulingManager manager;
    private final JobStatusServiceEmbedImpl embedHandler;
    private final DataSourceHandler dataSourceHandler;
    private final RangeFunctionImpl rangeFunction;
    private final RuleDetailServiceEmbedImpl detailServiceEmbed;

    private final ActuatorGenerator actuatorGenerator;
    private final Cache<String, DarkTrafficActuator> cache = CacheUtil.newLRUCache(10, AthenaConst.CACHE_TIMEOUT);

    @Override
    public void afterPropertiesSet() {
        cache.setListener((k, v) -> v.close());
    }

    @Override
    public void register(BiConsumer<Long, FailureInfo> callback) {
        embedHandler.register(callback);
    }

    @Override
    public void register(RangeService rangeService) {
        rangeFunction.setRangeService(rangeService);
    }

    @Override
    public void register(StandardService standardService) {
        detailServiceEmbed.setStandardService(standardService);
    }

    @Override
    public void submit(Long sourceId, DatasourceMeta source, Long planId, Long jobId, JobConfig config) {
        DataSourceLimitParam sourceParam = new DataSourceLimitParam();
        sourceParam.setId(sourceId);
        sourceParam.setConfigName(source.getDsUid());
        DataBaseType type = DataBaseType.findByPluginId(source.getPluginId());
        if (null == type) {
            throw new NyxException("Unsupported " + source.getPluginId());
        }
        sourceParam.setDataBaseType(type);
        sourceParam.setHost(source.getHost());
        sourceParam.setPort(source.getPort());
        sourceParam.setUsername(source.getUser());
        sourceParam.setPassword(source.getPass());
        sourceParam.setDbName(source.getDbName());
        if (null != source.getProperties()) {
            sourceParam.setProperties(source.getProperties().entrySet().stream().filter(v -> Objects.nonNull(v.getValue())).collect(Collectors.toMap(v -> v.getKey().toString(), v -> v.getValue().toString())));
        }
        if (CollUtil.isNotEmpty(config.getSchemas())) {
            sourceParam.setSchemaSet(new HashSet<>(config.getSchemas()));
        }

        JobStartConfig param = buildConfig(config);
        param.setPlanId(planId);
        param.setJobId(jobId);
        param.setJobType(JobType.DISCOVERY);
        param.setSource(sourceParam);

        param.setIgnoreSample(Boolean.TRUE);
        param.setIgnoreCandidate(Boolean.TRUE);
        param.setSaveTableStatus(Boolean.FALSE);

        manager.submit(param);
    }

    @Override
    public void interrupt(Long jobId) {
        manager.terminated(jobId);
    }

    @Override
    public void release(JobConfig config) {
        String key = getKey(config);
        if (cache.containsKey(key)) {
            //服务守护
            ThreadUtil.sleep(RandomUtil.randomInt(1000, 3000));
            cache.remove(key);
        } else {
            log.warn("【Metis】释放资源 {} 租户下 {} 不存在", config.getTenantId(), config.getStdId());
        }
    }

    @Override
    public List<TrafficResult> trafficClassify(List<TrafficParam> req, JobConfig config) {
        if (CollUtil.isEmpty(req)) {
            return Collections.emptyList();
        }
        List<ColumnReq> column = req.stream().filter(v -> null != v.getMeta() && null != v.getMeta().getName()).map(v -> {
            ColumnReq columnReq = new ColumnReq();
            columnReq.setName(v.getMeta().getName());
            columnReq.setComment(v.getMeta().getComment());
            columnReq.setData(v.getData());
            return columnReq;
        }).collect(Collectors.toList());

        DarkTrafficActuator actuator = getDarkSimpleActuator(false, config);
        TableDTO table = new TableDTO();
        table.setName(StrUtil.EMPTY);
        TrafficResDTO match = actuator.execute(new AdditionalInfo(), table, column);
        if (null == match) {
            return new ArrayList<>(0);
        }
        Map<String, List<MatchResDTO>> columns = match.getColumns();
        return req.stream().map(v -> {
            MetaInfo meta = v.getMeta();
            MatchResDTO first = CollUtil.getFirst(columns.get(meta.getName()));
            return new TrafficResultDTO(meta, null != first ? first.getArtifactId() : null);
        }).collect(Collectors.toList());

    }

    @Override
    public DataSource getDatasource() {
        return dataSourceHandler.primaryDataSource();
    }

    @Override
    public DataSource getContentDatasource() {
        return dataSourceHandler.current().get(PipelineConst.EMBED);
    }

    @Override
    public String getSchema() {
        MybatisPlusProperties properties = SpringUtil.getBean(MybatisPlusProperties.class);
        return properties.getGlobalConfig().getDbConfig().getSchema();
    }

    private DarkTrafficActuator getDarkSimpleActuator(boolean force, JobConfig config) {
        String key = getKey(config);
        if (force) {
            DarkTrafficActuator actuator = actuatorGenerator.generateTraffic(buildTrafficConfig(config));
            cache.put(key, actuator);
            return actuator;
        } else {
            return cache.get(key, () -> actuatorGenerator.generateTraffic(buildTrafficConfig(config)));
        }
    }

    private String getKey(JobConfig config) {
        return String.format("%s %d", config.getTenantId(), config.getStdId());
    }

    private JobStartConfig buildTrafficConfig(JobConfig config) {
        JobStartConfig param = buildConfig(config);
        param.setTraffic(Boolean.TRUE);
        return param;
    }

    private JobStartConfig buildConfig(JobConfig config) {
        JobStartConfig param = new JobStartConfig();
        BeanUtil.copyProperties(config, param, new CopyOptions());
        if (null == param.getAutoRule()) {
            param.setAutoRule(Boolean.TRUE);
        }
        if (null == param.getTableColumnNum()) {
            param.setTableColumnNum(200);
        }
        param.setIgnoreHis(Boolean.TRUE);
        return param;
    }
}
