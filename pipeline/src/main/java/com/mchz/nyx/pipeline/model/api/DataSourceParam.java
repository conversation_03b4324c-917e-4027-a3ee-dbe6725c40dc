package com.mchz.nyx.pipeline.model.api;

import com.mchz.mcdatasource.core.DataBaseType;
import lombok.Data;

import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/10/27 14:58
 */
@Data
public class DataSourceParam {

    /**
     * 主键
     */
    private Long id;

    /**
     * 数据源名称
     */
    private String configName;

    /**
     * 数据源类型
     */
    private Integer configType;

    /**
     * 统一数据源的数据源类型
     */
    private DataBaseType dataBaseType;

    /**
     * IP地址
     */
    private String host;

    /**
     * 端口号
     */
    private String port;

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 数据库名称
     */
    private String dbName;

    /**
     * 附加信息
     */
    private Map<String, String> attachment;
    /**
     * 高级参数
     */
    private Map<String, String> advancedConfig;

    /**
     * 统一数据源高级参数
     */
    private Map<String, String> properties;

    /**
     * 远程连接信息
     */
    private RemoteAddressParam remote;
}
