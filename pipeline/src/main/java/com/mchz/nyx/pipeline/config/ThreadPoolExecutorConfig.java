package com.mchz.nyx.pipeline.config;

import cn.hutool.core.thread.ExecutorBuilder;
import cn.hutool.core.thread.RejectPolicy;
import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.ttl.threadpool.TtlExecutors;
import com.mchz.nyx.pipeline.common.constants.PipelineConst;
import com.mchz.nyx.pipeline.config.props.SourceProperties;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;

import java.util.concurrent.Executor;
import java.util.concurrent.SynchronousQueue;

/**
 * <p>
 * 线程池配置
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2019/11/28 15:05
 */
@Slf4j
@Configuration
@EnableAsync
public class ThreadPoolExecutorConfig {

    @Bean(PipelineConst.THIRD_PARTY)
    public Executor taskExecutor() {
        return TtlExecutors.getTtlExecutorService(ExecutorBuilder.create().setCorePoolSize(0)
            .setMaxPoolSize(Math.max(PipelineConst.DEFAULT_POOL_SIZE - PipelineConst.PARALLEL_THREAD_SIZE, 1))
            .setThreadFactory(ThreadUtil.newNamedThreadFactory("call-", true))
            .setHandler(RejectPolicy.CALLER_RUNS.getValue())
            .build());
    }

    @Bean(PipelineConst.STORE_THREAD)
    public Executor storeExecutor(SourceProperties properties) {
        return TtlExecutors.getTtlExecutorService(ExecutorBuilder.create().setCorePoolSize(Math.min(properties.getSys().getStoreThreadTotalSize(), 3))
            .setMaxPoolSize(properties.getSys().getStoreThreadTotalSize())
            .setWorkQueue(new SynchronousQueue<>())
            .setThreadFactory(ThreadUtil.newNamedThreadFactory("store-", true))
            .setHandler(RejectPolicy.CALLER_RUNS.getValue())
            .build());
    }
}
