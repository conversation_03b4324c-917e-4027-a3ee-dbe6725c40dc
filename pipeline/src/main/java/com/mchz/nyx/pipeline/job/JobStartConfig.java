package com.mchz.nyx.pipeline.job;

import com.mchz.nyx.pipeline.common.enums.JobType;
import com.mchz.nyx.pipeline.common.enums.MergeStrategy;
import com.mchz.nyx.pipeline.model.api.DataSourceLimitParam;
import lombok.Data;

import java.io.Serializable;

@Data
public class JobStartConfig implements Serializable {
    /**
     * 租户Id -> 集群->多租户情况下
     */
    private String tenantId;
    private Integer industryId;
    private String industry;

    private Long planId;
    private Long jobId;
    private JobType jobType;
    /**
     * 数据源信息
     */
    private DataSourceLimitParam source;

    private Long stdId;

    private Integer sampleLine;

    /**
     * 采样比例%
     */
    private Integer sampleRate;

    private Integer hitRate;

    private Boolean autoFeature;

    private Boolean autoComment;

    /**
     * 字段注释匹配基线：%
     */
    private Integer commentBaseline;

    private Boolean formMerge;

    private Boolean aiPinyin;

    private Boolean autoColumn;

    /**
     * 自动列名匹配基线：%
     */
    private Integer columnBaseline;

    private Boolean autoRule;

    private Long lastJobId;

    private Integer mainSimilarity;
    private Integer subSimilarity;
    private Integer num;

    private Boolean traffic;
    private Boolean ignoreMeta;
    private Boolean ignoreData;
    private Boolean ignoreSample;
    private Boolean ignoreCandidate;
    private Boolean ignoreHis;
    private Boolean loadBase;
    private Boolean saveTableStatus;
    private Boolean full;
    private Boolean nlp;
    private Boolean llm;
    private Boolean embedding;

    private String llmUrl;
    private String embeddingUrl;

    private Integer tableColumnNum;

    /**
     * 表格分类分级策略, 0->按表含义进行分类分级; 1->按字段分类分级结果计算表格分类分级
     */
    private Boolean classify;
    /**
     * 按表含义进行分类分级基准线
     */
    private Integer tableClassifyBaseline;

    /**
     * 默认分类
     */
    private Long defaultClassifyId;

    /**
     * 默认分级
     */
    private Integer defaultLevel;

    /**
     * 发现作业planId
     */
    private Long discoveryPlanId;
    /**
     * 发现作业jobId
     */
    private Long discoveryJobId;
    /**
     * 采样行数
     */
    private Integer sampleRows;
    /**
     * 合并策略
     */
    private MergeStrategy mergeStrategy;
}
