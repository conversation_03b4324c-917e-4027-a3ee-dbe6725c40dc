package com.mchz.nyx.pipeline.config;

import org.apache.catalina.Context;
import org.apache.catalina.webresources.ExtractingRoot;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingClass;
import org.springframework.boot.web.embedded.tomcat.TomcatContextCustomizer;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConditionalOnMissingClass("com.bes.enterprise.springboot.autoconfigure.BesServletWebServerFactoryConfiguration")
public class EmbeddedTomcatConfiguration {

    @Bean
    public TomcatServletWebServerFactory tomcatFactory() {
        return new TomcatServletWebServerFactory() {

            @Override
            protected void postProcessContext(Context context) {
                // It tells <PERSON><PERSON> to extract packages on startup (which requires a bit more disk space) instead of scanning through un-extracted packages.
                context.setResources(new ExtractingRoot());
            }
        };
    }

    @Bean
    public WebServerFactoryCustomizer<TomcatServletWebServerFactory> servletContainerCustomizer() {
        return new WebServerFactoryCustomizer<TomcatServletWebServerFactory>() {

            @Override
            public void customize(TomcatServletWebServerFactory container) {
                container.addContextCustomizers(
                        new TomcatContextCustomizer() {
                            @Override
                            public void customize(Context cntxt) {
                                // It asks Tomcat not to throw away the loaded classes and do a refresh.
                                cntxt.setReloadable(false);
                            }
                        });
            }
        };
    }
}
