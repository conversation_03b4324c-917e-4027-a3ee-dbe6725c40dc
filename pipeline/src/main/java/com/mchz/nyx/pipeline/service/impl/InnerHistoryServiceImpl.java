package com.mchz.nyx.pipeline.service.impl;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mchz.nyx.pipeline.common.constants.PipelineConst;
import com.mchz.nyx.pipeline.entity.MemoryMapping;
import com.mchz.nyx.pipeline.mapper.MemoryMappingMapper;
import com.mchz.nyx.pipeline.service.InnerHistoryService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.function.Consumer;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/7/9
 */
@Slf4j
@DS(PipelineConst.EMBED)
@Service
@AllArgsConstructor
public class InnerHistoryServiceImpl implements InnerHistoryService {
    private MemoryMappingMapper memoryMappingMapper;

    @Override
    public long loadAllHistoryCount(Integer industryId) {
        try {
            return memoryMappingMapper.selectCount(Wrappers.lambdaQuery(MemoryMapping.class).eq(MemoryMapping::getIndustryId, industryId));
        } catch (Exception e) {
            return 0;
        }
    }

    @Override
    public void loadAllHistoryResult(Integer industryId, Consumer<MemoryMapping> resCall) {
        LambdaQueryWrapper<MemoryMapping> q = Wrappers.lambdaQuery(MemoryMapping.class).select(MemoryMapping::getId, MemoryMapping::getBizName, MemoryMapping::getClassify, MemoryMapping::getLevel)
            .eq(MemoryMapping::getIndustryId, industryId);
        memoryMappingMapper.selectList(q, v -> resCall.accept(v.getResultObject()));
    }

    @Override
    public List<Integer> loadHistoryResult(String table, String column) {
        LambdaQueryWrapper<MemoryMapping> q = Wrappers.lambdaQuery(MemoryMapping.class).select(MemoryMapping::getId).eq(MemoryMapping::getTableName, table).eq(MemoryMapping::getColumnName, column);
        return memoryMappingMapper.selectObjs(q);
    }
}
