package com.mchz.nyx.pipeline.config;

import com.alibaba.cloud.nacos.ConditionalOnNacosDiscoveryEnabled;
import com.alibaba.cloud.nacos.discovery.NacosServiceDiscovery;
import com.mchz.nyx.pipeline.common.constants.PipelineConst;
import com.mchz.nyx.pipeline.config.props.SourceProperties;
import com.mchz.nyx.pipeline.http.ClusterRemoteInvoke;
import com.mchz.nyx.pipeline.http.HttpRemoteInvoke;
import com.mchz.nyx.pipeline.http.RemoteInvokeHandler;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.Executor;

/**
 * <p>
 * 远程调用配置类
 * </p>
 *
 * <AUTHOR>
 * @date 2022/3/23 10:15
 * @since 1.6.0
 */
@Configuration
public class RemoteInvokeConfig {

    @Bean
    @ConditionalOnNacosDiscoveryEnabled
    public RemoteInvokeHandler feignHandler(NacosServiceDiscovery nacosServiceDiscovery, @Qualifier(PipelineConst.THIRD_PARTY) Executor executor) {
        return new ClusterRemoteInvoke(nacosServiceDiscovery, executor);
    }

    @Bean
    @ConditionalOnMissingBean
    public RemoteInvokeHandler httpHandler(SourceProperties properties) {
        return new HttpRemoteInvoke(properties);
    }

}
