package com.mchz.nyx.pipeline.exception;

import cn.hutool.core.text.StrFormatter;
import com.mchz.nyx.common.exception.NyxException;
import com.mchz.nyx.pipeline.common.api.CommonResultCode;
import com.mchz.nyx.pipeline.common.api.IResultCode;
import lombok.Getter;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
@Getter
public class ApiException extends NyxException {
    private final int code;

    public ApiException(String message, Object... args) {
        super(StrFormatter.format(message, args));
        code = CommonResultCode.INTERNAL_SERVER_ERROR.getCode();
    }

    public ApiException(int code, String message, Object... args) {
        super(StrFormatter.format(message, args));
        this.code = code;
    }

    public ApiException(IResultCode code, Object... args) {
        super(StrFormatter.format(code.getMsg(), args));
        this.code = code.getCode();
    }

    public ApiException(IResultCode code) {
        super(code.getMsg());
        this.code = code.getCode();
    }
}
