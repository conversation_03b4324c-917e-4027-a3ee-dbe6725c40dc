package com.mchz.nyx.pipeline.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.mchz.nyx.pipeline.common.BaseEmbedMapper;
import com.mchz.nyx.pipeline.common.constants.PipelineConst;
import com.mchz.nyx.pipeline.entity.EntityMapping;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/18
 */
@DS(PipelineConst.EMBED)
public interface EntityMappingMapper extends BaseEmbedMapper<EntityMapping> {
}
