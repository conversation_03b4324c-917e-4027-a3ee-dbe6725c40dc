package com.mchz.nyx.pipeline.util;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.logging.LogLevel;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Supplier;

@Slf4j
public class Timer {
    private static final ThreadLocal<Timer> LOCAL = new ThreadLocal<>();
    private final AtomicInteger index = new AtomicInteger(1);
    private final String name;
    private final LogLevel logLevel;
    private LocalDateTime start;

    private Timer(String name, LocalDateTime start, LogLevel logLevel) {
        this.name = name;
        this.start = start;
        this.logLevel = logLevel;
        log1(name + "\t开始");
    }

    public static Timer start(String name) {
        return new Timer(name, LocalDateTime.now(), LogLevel.INFO);
    }

    public static <V> V run(String name, LogLevel logLevel, Supplier<V> supplier) {
        Timer timer = new Timer(name, LocalDateTime.now(), logLevel);
        V v = supplier.get();
        timer.end();
        return v;
    }

    public static <V> V run(String name, Supplier<V> supplier) {
        Timer timer = start(name);
        V v = supplier.get();
        timer.end();
        return v;
    }

    public static Timer start(String name, LogLevel logLevel) {
        return new Timer(name, LocalDateTime.now(), logLevel);
    }

    public Timer createChild(String name) {
        return start(this.name + "\t" + index.getAndIncrement() + "." + name, this.logLevel);
    }

    public synchronized void startChild(String name) {
        Timer timer = LOCAL.get();
        Timer newTimer = start(this.name + "\t" + index.getAndIncrement() + "." + name, this.logLevel);
        if (Objects.nonNull(timer)) {
            log.warn("{} 替换已存在计时器 {}", JSONUtil.toJsonStr(newTimer), JSONUtil.toJsonStr(timer));
        }
        LOCAL.set(newTimer);
    }

    public synchronized void endChild() {
        Timer timer = LOCAL.get();
        if (Objects.isNull(timer)) {
            log.warn("不存在计时器");
            return;
        }
        timer.end();
        LOCAL.remove();
    }

    public void log(String message) {
        log1(this.name + "\t" + message);
    }

    public void end() {
        end("");
    }

    public void end(String msg) {
        LocalDateTime end = LocalDateTime.now();
        Duration duration = Duration.between(start, end);
        start = end;
        log1(this.name + "\t结束\t" + duration.toNanos() / (1000 * 1000) + "ms" + "\t" + msg);
    }

    private void log1(String message) {
        switch (logLevel) {
            case INFO:
                log.info(message);
                break;
            case WARN:
                log.warn(message);
                break;
            case DEBUG:
                log.debug(message);
                break;
            case ERROR:
                log.error(message);
                break;
            case TRACE:
                log.trace(message);
                break;
            case OFF:
            case FATAL:
            default:
                throw new IllegalArgumentException(String.valueOf(logLevel));
        }
    }
}
