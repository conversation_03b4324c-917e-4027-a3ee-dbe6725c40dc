package com.mchz.nyx.pipeline.generator.file;

import cn.hutool.core.io.IORuntimeException;
import cn.hutool.extra.ftp.AbstractFtp;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/29
 */
@Slf4j
@AllArgsConstructor
public class RemoteFileHandler implements FileHandler {
    private final AbstractFtp ftp;

    @Override
    public void loadFile(String source, File target) {
        try {
            ftp.download(source, target);
            log.info("【下载文件】{}->{}", source, target.getPath());
        } catch (IORuntimeException e) {
            ftp.reconnectIfTimeout();
            ftp.download(source, target);
            log.info("【下载文件】r {}->{}", source, target.getPath());
        }
    }

    @Override
    public void close() throws IOException {
        ftp.close();
    }
}
