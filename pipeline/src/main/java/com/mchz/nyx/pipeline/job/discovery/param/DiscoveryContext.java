package com.mchz.nyx.pipeline.job.discovery.param;

import com.mchz.nyx.pipeline.job.JobLogManager;
import lombok.Builder;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/2
 */
@Data
@Builder
public class DiscoveryContext {
    private final Long planId;
    private final Long jobId;
    private final Long sourceId;

    private final boolean mainSub;
    private final int mainSimilarity;
    private final int subSimilarity;

    private boolean ignoreCandidate;

    private final JobLogManager jobLogManager;

    public void heartbeat(Long schemaId) {
        jobLogManager.heartbeat(DiscoveryJobState.of().setSId(schemaId));
    }

    public void interrupted() {
        jobLogManager.interrupted();
    }

}
