package com.mchz.nyx.pipeline.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.lang.func.Func1;
import cn.hutool.core.lang.func.LambdaUtil;
import cn.hutool.core.lang.func.VoidFunc0;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONUtil;
import com.mchz.mcdatasource.core.DataBaseType;
import com.mchz.nyx.common.util.PartitionUtil;
import com.mchz.nyx.meta.support.SchemaTables;
import com.mchz.nyx.pipeline.common.constants.PipelineConst;
import com.mchz.nyx.pipeline.job.JobLogManager;
import com.mchz.nyx.pipeline.util.filter.StrFilter;
import com.mchz.starter.tool.BsFunc;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DeadlockLoserDataAccessException;

import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.*;
import java.util.function.Consumer;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/10/21 10:56
 */
@Slf4j
@UtilityClass
public class Func {

    private final ThreadLocal<JobLogManager> JOB_LOG_THREAD_LOCAL = new ThreadLocal<>();
    private final JSONConfig JSON_CONFIG = JSONConfig.create().setIgnoreNullValue(false);

    /**
     * 拷贝属性
     *
     * @param src   原对象
     * @param clazz 拷贝后的类
     * @param <T>   泛型
     * @return 拷贝后的对象
     */
    public <T> T toBean(Object src, Class<T> clazz) {
        return BeanUtil.toBean(src, clazz, CopyOptions.create().ignoreCase().ignoreError().ignoreNullValue());
    }

    public <T> String name(Func1<T, ?> fun) {
        return LambdaUtil.getFieldName(fun);
    }

    @SafeVarargs
    public <E> HashSet<E> newHashSet(E... elements) {
        HashSet<E> set = new HashSet<>();
        Collections.addAll(set, elements);
        return set;
    }

    public <K, V> Map<K, V> newMap(K k, V v) {
        Map<K, V> res = new HashMap<>();
        res.put(k, v);
        return res;
    }

    public void init(List<SchemaTables> schemaTables, Map<String, StrFilter> filterMap, Map<String, List<String>> tablesMap) {
        for (SchemaTables v : schemaTables) {
            String name = v.getSchema().getName();
            filterMap.put(name, StrFilter.ofBlackWhite(v.getExcludeTables(), v.getTables()));
            tablesMap.put(name, v.getTables());
        }
    }

    public void pushJobLog(JobLogManager log) {
        JOB_LOG_THREAD_LOCAL.set(log);
    }

    public void popJobLog() {
        JOB_LOG_THREAD_LOCAL.remove();
    }

    public JobLogManager getJobLog() {
        return JOB_LOG_THREAD_LOCAL.get();
    }

    public void isInterrupted() throws InterruptedException {
        if (Thread.currentThread().isInterrupted()) {
            throw new InterruptedException();
        }
    }

    public static void isInterrupted(Thread supperThread) {
        if (Objects.nonNull(supperThread) && supperThread.isInterrupted()) {
            throw new RuntimeException(new InterruptedException());
        }
    }

    public void retry(VoidFunc0 fun, int retry, String msgTemplate) {
        for (int i = 0; i < retry; i++) {
            try {
                fun.call();
                break;
            } catch (DeadlockLoserDataAccessException e) {
                log.warn(msgTemplate, e.getMessage());
            } catch (Exception e) {
                ExceptionUtil.wrapAndThrow(e);
            }
        }
    }

    public <E> void processBatches(List<E> collection, Consumer<List<E>> consumer, String msgTemplate) {
        PartitionUtil.part(collection).forEach(v -> Func.retry(() -> consumer.accept(v), 3, msgTemplate));
    }

    public <E> void processBatches(List<E> collection, Consumer<List<E>> consumer, int size, String msgTemplate) {
        PartitionUtil.part(collection, size).forEach( v -> Func.retry(() -> consumer.accept(v), 3, msgTemplate));
    }

    public static String getMessage(Throwable throwable) {
        return BsFunc.getMessage(throwable);
    }

    public static String getMessageWithStack(Throwable throwable) {
        return BsFunc.getMessageWithStack(throwable);
    }

    public static String getStackTrace(Throwable e) {
        Objects.requireNonNull(e);
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        e.printStackTrace(pw);
        return sw.toString();
    }

    public static Throwable getCause(Throwable throwable) {
        return ExceptionUtil.getRootCause(throwable);
    }

    /**
     * 拆分Catalog和Schema
     *
     * @param dataBaseType  数据库类型
     * @param catalogSchema Catalog和Schema
     * @return String[Catalog, Schema]
     */
    public static String[] splitCatalogAndSchema(DataBaseType dataBaseType, String catalogSchema) {
        String[] s = new String[2];
        String leftPatch = "";
        String rightPatch = "";
        if (StrUtil.isEmpty(catalogSchema)) {
            return s;
        }
        String splitStr = null;
        if ((Objects.equals(DataBaseType.MSSQL, dataBaseType) || Objects.equals(DataBaseType.RDS_MSSQL, dataBaseType) || Objects.equals(DataBaseType.SYBASE, dataBaseType))//
            && catalogSchema.contains(StrUtil.DOT)) {
            if (catalogSchema.startsWith("[")) {
                splitStr = "].";
                leftPatch = "]";
            } else {
                splitStr = StrUtil.DOT;
            }
        } else if (catalogSchema.contains(StrUtil.COLON)) {
            splitStr = StrUtil.COLON;
        }
        if (Objects.isNull(splitStr)) {
            s[1] = catalogSchema;
        } else {
            String[] split = StrUtil.splitToArray(catalogSchema, splitStr);
            s[0] = split[0] + leftPatch;
            s[1] = split[1] + rightPatch;
        }
        return s;
    }

    public static String wrap(String s, String wrapCharacter) {
        return wrap(s, wrapCharacter, null);
    }

    public static String wrap(String s, String wrapCharacter, String escapeCharacter) {
        if (StrUtil.isEmpty(s)) {
            return null;
        }
        if (null != escapeCharacter) {
            s = s.replace(wrapCharacter, escapeCharacter + wrapCharacter);
        }
        return wrapCharacter + s + wrapCharacter;
    }

    public static boolean containDataNode(String format) {
        String suffix = StrUtil.DOT + format;
        return suffix.endsWith(".json") || suffix.endsWith(".xml") || suffix.endsWith(".html");
    }

    public List<String> parseEnums(String content) {
        List<String> list = StrUtil.split(content, StrUtil.C_LF);
        if (null == list) {
            return new ArrayList<>(0);
        }
        return list;
    }

    public String clearClassify(String input) {
        StringBuilder result = new StringBuilder();
        int depth = 0;

        for (char c : input.toCharArray()) {
            if (c == '(') {
                depth++;
            } else if (c == ')') {
                if (depth > 0) depth--;
            } else if (depth == 0) {
                result.append(c);
            }
        }

        return result.toString();
    }

    public String sample(List<?> value) {
        try {
            return JSONUtil.parseArray(value, JSON_CONFIG).toString();
        } catch (Exception e) {
            log.warn("无法转换样本,{}", value);
            return PipelineConst.SAMPLE_EMPTY;
        }
    }

    public String compressStr(String input) {
        return compressStr(input, 255);
    }

    public String compressStr(String input, int maxLen) {
        if (null == input || input.length() < maxLen) {
            return input;
        }

        String hash = SecureUtil.md5(input);
        String shortHash = hash.length() > 8 ? hash.substring(0, 8) : hash;
        return input.substring(0, maxLen - 8) + shortHash;
    }
}
