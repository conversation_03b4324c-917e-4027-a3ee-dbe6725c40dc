package com.mchz.nyx.pipeline.service;

import com.mchz.datasource.cli.RsResult;
import com.mchz.nyx.pipeline.model.api.DataSourceLimitParam;
import com.mchz.nyx.pipeline.model.api.DataSourceParam;
import com.mchz.nyx.pipeline.model.api.TableDataSetParam;
import com.mchz.nyx.pipeline.model.dto.MetaColumnPlusDTO;
import com.mchz.nyx.pipeline.model.dto.MetaTableDTO;
import com.mchz.nyx.pipeline.model.other.ColumnData;
import com.mchz.nyx.pipeline.model.other.MetaTableExpand;
import com.mchz.nyx.pipeline.model.other.TriConsumer;
import com.mchz.nyx.pipeline.model.vo.ResultSetVO;
import com.mchz.nyx.pipeline.model.vo.TableVO;
import com.mchz.nyx.pipeline.job.JobLogManager;
import com.mchz.nyx.pipeline.util.Func;
import com.mchz.nyx.pipeline.util.cache.ListCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/11/2 9:20
 */
@Slf4j
@Service
public class DatabaseFactory {

    private final List<DatabaseService> metadataServiceList;

    public DatabaseFactory(List<DatabaseService> metadataServiceList) {
        metadataServiceList.sort(Comparator.comparingInt(DatabaseService::priority));
        this.metadataServiceList = metadataServiceList;
    }

    public boolean crawlMetadata(DataSourceLimitParam source, JobLogManager jobLog,
        TriConsumer<MetaTableDTO, List<MetaColumnPlusDTO>, MetaTableExpand> consumer) throws Exception {
        for (DatabaseService service : metadataServiceList) {
            if (service.crawlMetadata(source, jobLog, consumer)) {
                return true;
            }
            Func.isInterrupted();
        }
        return false;
    }

    public boolean crawlViewMetadata(DataSourceLimitParam source, JobLogManager jobLog,
        TriConsumer<MetaTableDTO, List<MetaColumnPlusDTO>, MetaTableExpand> consumer) throws Exception {
        for (DatabaseService service : metadataServiceList) {
            if (service.crawlViewMetadata(source, jobLog, consumer)) {
                return true;
            }
            Func.isInterrupted();
        }
        return false;
    }

    public List<String> crawlSchema(DataSourceParam config) {
        for (DatabaseService service : metadataServiceList) {
            List<String> schemas = service.crawlSchema(config);
            if (null != schemas) {
                return schemas;
            }
        }
        return null;
    }

    public List<TableVO> getTables(DataSourceParam config) {
        for (DatabaseService service : metadataServiceList) {
            List<TableVO> tables = service.getTables(config);
            if (null != tables) {
                return tables;
            }
        }
        return null;
    }


    public List<String> testQuery(DataSourceParam config) {
        throw new UnsupportedOperationException();
    }

    public void testConnection(DataSourceParam config) {
        try {
            for (DatabaseService service : metadataServiceList) {
                boolean support = service.testConnection(config);
                if (support) {
                    return;
                }
            }
        } catch (Exception e) {
            log.error("测试数据源失败", e);
            throw e;
        }
    }

    public ResultSetVO getTableDataSet(TableDataSetParam param) throws Exception {
        for (DatabaseService service : metadataServiceList) {
            ResultSetVO tableDataSet = service.getTableDataSet(param, false);
            if (null != tableDataSet) {
                return tableDataSet;
            }
            Func.isInterrupted();
        }
        return null;
    }

    public ClassLoader getTableDataSet2Cache(TableDataSetParam param,
                                      ListCache<ColumnData> resultDataCache) throws Exception {
        for (DatabaseService service : metadataServiceList) {
            ClassLoader classLoader = service.getTableDataSet2Cache(param, resultDataCache, false);
            if (null != classLoader)
                return classLoader;
            Func.isInterrupted();
        }
        return null;
    }

    public RsResult openQuery(DataSourceParam param, String sql) throws Exception {
        for (DatabaseService service : metadataServiceList) {
            RsResult rsResult = service.openQuery(param, sql);
            if (Objects.nonNull(rsResult)) {
                return rsResult;
            }
            Func.isInterrupted();
        }
        return null;
    }

    public void closeConnection(Long key) {
        for (DatabaseService service : metadataServiceList) {
            service.closeConnection(key);
        }
    }
}
