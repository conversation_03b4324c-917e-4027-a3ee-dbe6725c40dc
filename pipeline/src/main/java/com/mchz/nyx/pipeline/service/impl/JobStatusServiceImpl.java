package com.mchz.nyx.pipeline.service.impl;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.crypto.SecureUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mchz.nyx.pipeline.common.enums.JobType;
import com.mchz.nyx.pipeline.entity.DiscoveryJobDO;
import com.mchz.nyx.pipeline.job.JobLogManager;
import com.mchz.nyx.pipeline.mapper.DiscoveryJobMapper;
import com.mchz.nyx.pipeline.model.api.TaskHeartbeatBeatReq;
import com.mchz.nyx.pipeline.service.JobStatusService;
import com.mchz.nyx.pipeline.service.TaskCallbackService;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.TimeUnit;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/8/26
 */
@Service
@RequiredArgsConstructor
@ConditionalOnMissingBean(value = JobStatusService.class, ignored = JobStatusServiceImpl.class)
public class JobStatusServiceImpl implements JobStatusService {
    private final DiscoveryJobMapper jobMapper;
    private final TaskCallbackService callbackService;

    private ScheduledExecutorService executor;
    private Map<String, ScheduledFuture<?>> map;

    @PostConstruct
    public void init() {
        executor = ThreadUtil.createScheduledExecutor(1);
        map = new ConcurrentHashMap<>();
    }

    @Override
    public String jobStart(JobType jobType, JobLogManager jobLog) {
        Long jobId = jobLog.getJobId();
        TaskHeartbeatBeatReq req = new TaskHeartbeatBeatReq();
        req.setTaskGroup(jobType.name());
        req.setTaskName(jobId.toString());
        req.setStatus(1);
        callbackService.heart(req);

        req.setStatus(2);
        Heartbeat heartbeat = new Heartbeat(req, jobLog);
        ScheduledFuture<?> old = map.put(jobLog.getId(), executor.scheduleWithFixedDelay(() -> heart(heartbeat), 10, 29, TimeUnit.SECONDS));
        if (null != old) {
            old.cancel(true);
        }

        DiscoveryJobDO one = jobMapper.selectOne(Wrappers.lambdaQuery(DiscoveryJobDO.class).select(DiscoveryJobDO::getHeartbeat).eq(DiscoveryJobDO::getId, jobId));
        return null == one ? null : one.getHeartbeat();
    }

    @Override
    public void jobFinish(JobType jobType, JobLogManager jobLog, boolean success, Throwable error) {
        Long jobId = jobLog.getJobId();
        callbackService.finished(jobId, success, jobLog.getHistoryLog());
        ScheduledFuture<?> scheduledFuture = map.get(jobLog.getId());
        if (null != scheduledFuture) {
            scheduledFuture.cancel(true);
        }
        saveState(jobLog.getJobId(), jobLog.getSnapshot());
        TaskHeartbeatBeatReq req = new TaskHeartbeatBeatReq();
        req.setTaskGroup(jobType.name());
        req.setTaskName(jobId.toString());
        req.setStatus(3);
        callbackService.heart(req);
    }

    private void heart(Heartbeat heartbeat) {
        String value = heartbeat.getHeartbeat();
        if (null != value) {
            saveState(heartbeat.getJobLog().getJobId(), value);
        }
        callbackService.heart(heartbeat.getReq());
    }

    private void saveState(Long jobId, String heartbeat) {
        DiscoveryJobDO updateJob = new DiscoveryJobDO();
        updateJob.setId(jobId);
        updateJob.setHeartbeat(heartbeat);
        jobMapper.updateById(updateJob);
    }

    @Data
    private static class Heartbeat {
        private final TaskHeartbeatBeatReq req;
        private final JobLogManager jobLog;
        private String hash;

        public String getHeartbeat() {
            String snapshot = jobLog.getSnapshot();
            String md5 = SecureUtil.md5(snapshot);
            if (md5.equals(hash)) {
                return null;
            }
            hash = md5;
            return snapshot;
        }
    }
}
