package com.mchz.nyx.pipeline.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 分类表
 */
@Data
@TableName("c_sys_classify_type")
@Accessors(chain = true)
public class CSysClassifyType {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 模板ID
     */
    private Long templateId;

    /**
     * 标准ID
     */
    private Long stdId;

    /**
     * 字典名称
     */
    private String typeName;

    /**
     * 定义说明
     */
    private String description;

    /**
     * 父级id
     */
    private Long parentId;

    /**
     * 父全路径 .分隔
     */
    private String parentPath;

    /**
     * 全路径名 分隔符号"-"
     */
    private String fullName;

    /**
     * 层级
     */
    private Integer level;

    /**
     * 状态，0 - 停用，1 - 正常
     */
    private Boolean status;

    /**
     * 敏感等级
     */
    private Integer sensitiveLevel;

    /**
     * 是否删除：0=否；1=是
     */
    @TableLogic
    private Boolean isDelete;
}
