package com.mchz.nyx.pipeline.generator.meta;

import cn.hutool.core.bean.BeanDesc;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.PropDesc;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import cn.idev.excel.ExcelReader;
import cn.idev.excel.FastExcel;
import cn.idev.excel.context.AnalysisContext;
import cn.idev.excel.event.AnalysisEventListener;
import cn.idev.excel.read.builder.ExcelReaderBuilder;
import cn.idev.excel.read.listener.ReadListener;
import cn.idev.excel.read.metadata.ReadSheet;
import com.mchz.nyx.common.enums.DataTypeGroup;
import com.mchz.nyx.meta.common.enums.MetaObjType;
import com.mchz.nyx.meta.entity.DbMetaColumn;
import com.mchz.nyx.meta.entity.DbMetaObject;
import com.mchz.nyx.meta.entity.DbMetaTable;
import com.mchz.nyx.meta.instance.crawl.DsMetaAdapter;
import com.mchz.nyx.meta.instance.crawl.NyxCrawlAgent;
import com.mchz.nyx.meta.model.meta.CatalogSchema;
import com.mchz.nyx.meta.model.meta.WrapTable;
import com.mchz.nyx.meta.support.DataSourceConfig;
import com.mchz.nyx.meta.support.SchemaTables;
import com.mchz.nyx.pipeline.common.enums.DatabaseType;
import com.mchz.nyx.pipeline.entity.FileUploadDetail;
import com.mchz.nyx.pipeline.exception.ServiceException;
import com.mchz.nyx.pipeline.model.api.RemoteAddressParam;
import com.mchz.nyx.pipeline.model.excel.MetaColumn;
import com.mchz.nyx.pipeline.model.excel.MetaTable;
import com.mchz.nyx.pipeline.model.param.McFileSourceParam;
import com.mchz.nyx.pipeline.model.vo.TableVO;
import com.mchz.nyx.pipeline.service.FileManagerService;
import com.mchz.nyx.pipeline.util.DbUtil;
import com.mchz.nyx.pipeline.util.Func;
import com.mchz.nyx.pipeline.util.file.fs.FilesBundle;
import com.mchz.nyx.pipeline.util.file.fs.SourceFile;
import com.mchz.nyx.pipeline.util.filter.StrFilter;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/15
 */
@Slf4j
public class MetaFileMetaAdapter implements MetaLookupService, DsMetaAdapter {
    private final FileManagerService fileManagerService;

    public MetaFileMetaAdapter(FileManagerService fileManagerService) {
        this.fileManagerService = fileManagerService;
    }

    @Override
    public List<String> loadSchema(DataSourceConfig source) {
        Set<String> schemas = new TreeSet<>();
        readTable(source, new SchemaAnalysisEventListener(schemas));
        return new ArrayList<>(schemas);
    }

    @Override
    public List<TableVO> loadTable(DataSourceConfig source) {
        Map<String, List<String>> map = new TreeMap<>();
        readTable(source, new TableAnalysisEventListener(map));
        return map.entrySet().stream().flatMap(e -> e.getValue().stream().map(v -> new TableVO(e.getKey(), v))).collect(Collectors.toList());
    }

    @Override
    public void execute(NyxCrawlAgent agent, DataSourceConfig source, int type, List<SchemaTables> schemaTables) {
        McFileSourceParam fileSource = DbUtil.fileSource(source);
        Map<String, StrFilter> filterMap;
        Map<String, List<String>> tablesMap;
        if (CollUtil.isEmpty(schemaTables)) {
            filterMap = null;
            tablesMap = Collections.emptyMap();
        } else {
            filterMap = new HashMap<>(schemaTables.size());
            tablesMap = new HashMap<>(schemaTables.size());
            Func.init(schemaTables, filterMap, tablesMap);
        }
        Map<String, Map<String, String>> commentMap = new HashMap<>();
        List<MetaColumn> list = new ArrayList<>();
        ReadSheet sheet1 = FastExcel.readSheet("表元数据").head(MetaTable.class).registerReadListener(new SchemaTableAnalysisEventListener(filterMap, commentMap)).build();
        ReadSheet sheet2 = FastExcel.readSheet("列元数据").registerReadListener(new ColumnAnalysisEventListener(filterMap, list)).build();

        try (ExcelReader excelReader = getExcelReaderBuilder(fileSource.getSourceId(), fileSource.getRemote()).build()) {
            excelReader.read(sheet1, sheet2);
        }
        List<String> delSchema = null == schemaTables ? null : schemaTables.stream().filter(v -> !commentMap.containsKey(v.getSchema().getName())).map(v -> v.getSchema().getName()).collect(Collectors.toList());
        agent.schema(commentMap.keySet().stream().map(CatalogSchema::of).collect(Collectors.toList()), delSchema);

        Map<String, Map<String, List<MetaColumn>>> columns = list.stream().collect(Collectors.groupingBy(MetaColumn::getSchema, Collectors.groupingBy(MetaColumn::getTableName, Collectors.toList())));
        columns.forEach((s, v) -> {
            CatalogSchema schema = CatalogSchema.of(s);
            agent.stageStart(schema, tablesMap.get(s), MetaObjType.TABLE);
            v.forEach((t, c) -> {
                Map<String, String> tc = commentMap.get(s);
                String comment;
                if (null != tc) {
                    comment = tc.get(t);
                } else {
                    comment = null;
                }
                WrapTable wrapTable = new WrapTable(buildTableObject(t), buildTable(comment));
                agent.parseColumn(c, wrapTable, this::buildColumnObject, this::buildColumn);
                agent.consume(wrapTable);
            });
            agent.stageFinish(schema);
        });
    }

    private ExcelReaderBuilder getExcelReaderBuilder(Long sourceId, RemoteAddressParam remote) {
        List<FileUploadDetail> list = fileManagerService.listFileDetail(sourceId);
        if (CollUtil.isEmpty(list)) {
            throw new ServiceException("不存在元数据文件 sourceId " + sourceId);
        }
        FilesBundle<SourceFile> files = fileManagerService.loadFile(list, remote, null, DatabaseType.FILE_METADATA);
        Iterator<SourceFile> iterator = files.iterator();
        if (!iterator.hasNext()) {
            throw new ServiceException("不存在元数据文件 sourceId " + sourceId);
        }
        SourceFile file = iterator.next();
        if (!file.getFile().exists()) {
            log.error("【元数据文件】{}文件不存在,{}", sourceId, file.getMeta().getFileName());
            throw new ServiceException(String.format("不存在元数据文件%d(%s)", sourceId, file.getMeta().getName()));
        }
        return FastExcel.read(file.getFile());
    }

    private <T> void readTable(DataSourceConfig source, ReadListener<T> readListener) {
        McFileSourceParam fileSource = DbUtil.fileSource(source);
        getExcelReaderBuilder(fileSource.getSourceId(), fileSource.getRemote()).head(MetaTable.class).registerReadListener(readListener).sheet("表元数据").doRead();
    }

    private DbMetaObject buildTableObject(String name) {
        DbMetaObject obj = new DbMetaObject();
        obj.setName(name);
        obj.setType(MetaObjType.TABLE.name());
        return obj;
    }

    private DbMetaObject buildColumnObject(MetaColumn column) {
        DbMetaObject obj = new DbMetaObject();
        obj.setName(column.getColumnName());
        obj.setType(MetaObjType.COLUMN.name());
        return obj;
    }

    private DbMetaTable buildTable(String comment) {
        DbMetaTable meta = new DbMetaTable();
        meta.setType(MetaObjType.TABLE.name());
        meta.setComment(comment);
        return meta;
    }

    private DbMetaColumn buildColumn(MetaColumn column, int index) {
        DbMetaColumn meta = new DbMetaColumn();
        meta.setComment(column.getColumnComment());
        meta.setTypeGroup(DataTypeGroup.CHARACTER.name());
        meta.setDataType(meta.getTypeGroup());
        meta.setPosition(index);
        return meta;
    }

    @AllArgsConstructor
    private static class SchemaAnalysisEventListener extends AnalysisEventListener<MetaTable> {
        private final Set<String> schemas;

        @Override
        public void invoke(MetaTable metaTable, AnalysisContext analysisContext) {
            schemas.add(metaTable.getSchema());
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
        }
    }

    @AllArgsConstructor
    private static class TableAnalysisEventListener extends AnalysisEventListener<MetaTable> {
        private final Map<String, List<String>> schemas;

        @Override
        public void invoke(MetaTable metaTable, AnalysisContext analysisContext) {
            schemas.computeIfAbsent(metaTable.getSchema(), k -> new ArrayList<>()).add(metaTable.getTableName());
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
        }
    }

    @AllArgsConstructor
    private static class SchemaTableAnalysisEventListener extends AnalysisEventListener<MetaTable> {
        private final Map<String, StrFilter> map;
        private final Map<String, Map<String, String>> commentMap;

        @Override
        public void invoke(MetaTable metaTable, AnalysisContext analysisContext) {
            if (check(map, metaTable.getSchema(), metaTable.getTableName())) {
                commentMap.computeIfAbsent(metaTable.getSchema(), k -> new HashMap<>()).put(metaTable.getTableName(), metaTable.getTableComment());
            }
        }

        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
            if (null != map) {
                map.entrySet().removeIf(stringPairEntry -> !commentMap.containsKey(stringPairEntry.getKey()));
            }
        }
    }

    @Getter
    @RequiredArgsConstructor
    private static class ColumnAnalysisEventListener extends AnalysisEventListener<Map<Integer, String>> {
        private final Map<String, StrFilter> map;
        private final List<MetaColumn> list;
        private Map<Integer, PropDesc> index2Field;
        private Map<Integer, String> index2Name;

        @Override
        public void invokeHeadMap(Map<Integer, String> map, AnalysisContext context) {
            BeanDesc desc = BeanUtil.getBeanDesc(MetaColumn.class);
            index2Field = new HashMap<>();
            index2Name = new HashMap<>();

            map.forEach((index, value) -> {
                PropDesc prop = desc.getProp(value);
                if (null == prop) {
                    index2Name.put(index, value);
                } else {
                    index2Field.put(index, prop);
                }
            });
        }

        @Override
        public void invoke(Map<Integer, String> data, AnalysisContext context) {
            MetaColumn column = new MetaColumn();
            Map<String, String> expandMap = new HashMap<>();
            data.forEach((index, value) -> {
                PropDesc propDesc = index2Field.get(index);
                if (null != propDesc) {
                    propDesc.setValue(column, value);
                } else {
                    String name = index2Name.get(index);
                    if (null != name) {
                        expandMap.put(name, value);
                    }
                }
            });
            if (null == column.getColumnName() || !check(map, column.getSchema(), column.getTableName())) {
                return;
            }
            if (!expandMap.isEmpty()) {
                column.setExpandContent(JSONUtil.toJsonStr(expandMap));
            }
            list.add(column);
        }


        @Override
        public void doAfterAllAnalysed(AnalysisContext context) {
        }
    }

    private static boolean check(Map<String, StrFilter> map, String schema, String tableName) {
        if (null == map) {
            return true;
        }
        StrFilter filter = map.get(schema);
        if (null == filter) {
            return false;
        }
        return filter.check(tableName);
    }
}
