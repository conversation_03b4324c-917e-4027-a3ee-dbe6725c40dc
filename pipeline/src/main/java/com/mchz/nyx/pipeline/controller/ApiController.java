package com.mchz.nyx.pipeline.controller;

import com.mchz.nyx.dark.engine.impl.JavaScriptEngine;
import com.mchz.nyx.pipeline.common.api.R;
import com.mchz.nyx.pipeline.model.api.RecommendClassifyReq;
import com.mchz.nyx.pipeline.model.api.RecommendClassifyResp;
import com.mchz.nyx.pipeline.model.dto.ClassifyTypeDTO;
import com.mchz.nyx.pipeline.model.dto.StandardVO;
import com.mchz.nyx.pipeline.model.param.CheckCustomCodeParam;
import com.mchz.nyx.pipeline.model.vo.ClassifyTypeVO;
import com.mchz.nyx.pipeline.service.RuleDetailService;
import com.mchz.nyx.pipeline.util.Func;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/api")
@AllArgsConstructor
public class ApiController {

    private final RuleDetailService ruleDetailService;

    @PostMapping("/recommendClassify")
    public R<RecommendClassifyResp> recommendClassify(@Valid @RequestBody RecommendClassifyReq req) {
        return R.success(new RecommendClassifyResp(new ArrayList<>(0)));
    }

    @PostMapping("/checkCustomCode")
    public R<Boolean> checkCustomCode(@RequestBody CheckCustomCodeParam param) {
        JavaScriptEngine customCode = new JavaScriptEngine(param.getCustomCode());
        return R.success(customCode.eval(param.getValue()));
    }

    @GetMapping("/v1/standard/{stdId}/classifyType")
    public R<List<ClassifyTypeVO>> getClassifyType(@PathVariable Long stdId) {
        List<ClassifyTypeDTO> list = ruleDetailService.loadClassifyType(stdId);
        return R.success(getItems(list));
    }

    @GetMapping("/v1/standard/{stdId}")
    public R<StandardVO> getStandard(@PathVariable Long stdId) {
        List<ClassifyTypeDTO> list = ruleDetailService.loadClassifyType(stdId);
        return R.success(new StandardVO(ruleDetailService.getIndustryId(stdId, null), getItems(list)));
    }

    @NotNull
    private List<ClassifyTypeVO> getItems(List<ClassifyTypeDTO> list) {
        return list.stream().map(v -> new ClassifyTypeVO(v.getId(), Func.clearClassify(v.getFullName()), v.getSensitiveLevel(), v.getDescription())).collect(Collectors.toList());
    }
}
