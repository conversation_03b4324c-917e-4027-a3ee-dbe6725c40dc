package com.mchz.nyx.pipeline.controller;

import cn.hutool.core.util.StrUtil;
import com.mchz.nyx.pipeline.common.api.R;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.support.PropertiesLoaderUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.Properties;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/11/17 11:25
 */
@Slf4j
@RestController
@RequestMapping("/test")
public class TestController {
    /**
     * 测试
     */
    @GetMapping
    public R<String> test() {
        log.info("test request come in ......");
        return R.message("测试");
    }

    @GetMapping("/version")
    public R<String> version() {
        try {
            Properties properties = PropertiesLoaderUtils.loadAllProperties("git.properties");
            if (properties.isEmpty()) {
                return R.success("dev");
            }
            String version = properties.getProperty("git.build.version");
            String abbrev = properties.getProperty("git.commit.id.abbrev", StrUtil.EMPTY);
            return R.success(version + StrUtil.DOT + abbrev);
        } catch (IOException e) {
            return R.fail("dev");
        }
    }
}
