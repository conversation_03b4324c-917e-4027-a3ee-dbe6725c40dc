package com.mchz.nyx.pipeline.util.cache;

import cn.hutool.core.io.IoUtil;
import com.mchz.nyx.pipeline.config.props.EhCacheProperties;
import com.mchz.nyx.pipeline.config.props.SourceProperties;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.ehcache.Cache;
import org.ehcache.CacheManager;
import org.ehcache.config.ResourcePools;
import org.ehcache.config.builders.CacheConfigurationBuilder;
import org.ehcache.config.builders.CacheManagerBuilder;
import org.ehcache.config.builders.ResourcePoolsBuilder;
import org.ehcache.config.units.MemoryUnit;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor(onConstructor_ = @Autowired)
public class EhCacheManager implements InitializingBean, DisposableBean {
    private final SourceProperties properties;

    private EhCacheProperties ehCacheProperties;
    @Getter
    private CacheManager cacheManager;

    @Override
    public void afterPropertiesSet() {
        ehCacheProperties = properties.getEhcache();
        cacheManager = CacheManagerBuilder.newCacheManagerBuilder()//
            .with(CacheManagerBuilder.persistence(ehCacheProperties.getRootDir()))//
            .build(true);
    }

    public <K, V> Cache<K, V> createCache(String cacheName, Class<K> keyType, Class<V> valueType) {
        ResourcePools resourcePools = ResourcePoolsBuilder.newResourcePoolsBuilder()//
            .heap(ehCacheProperties.getHeapSizeMb(), MemoryUnit.MB)//
            .disk(ehCacheProperties.getDiskSizeGb(), MemoryUnit.GB).build();

        CacheConfigurationBuilder<K, V> configurationBuilder = CacheConfigurationBuilder.newCacheConfigurationBuilder(
            keyType, valueType, resourcePools);
        return cacheManager.createCache(cacheName, configurationBuilder);
    }

    public void removeCache(String cacheName) {
        cacheManager.removeCache(cacheName);
    }

    public <K, V> Cache<K, V> getCache(String cacheName, Class<K> keyType, Class<V> valueType) {
        return cacheManager.getCache(cacheName, keyType, valueType);
    }

    @Override
    public void destroy() {
        IoUtil.close(cacheManager);
    }


}
