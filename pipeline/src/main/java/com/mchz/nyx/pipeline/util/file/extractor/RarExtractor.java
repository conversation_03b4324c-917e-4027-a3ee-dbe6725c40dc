package com.mchz.nyx.pipeline.util.file.extractor;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IORuntimeException;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Filter;
import cn.hutool.extra.compress.extractor.Extractor;
import com.github.junrar.Archive;
import com.github.junrar.exception.RarException;
import com.github.junrar.rarfile.FileHeader;
import lombok.AllArgsConstructor;
import org.apache.commons.compress.archivers.ArchiveEntry;

import java.io.*;
import java.nio.file.Files;
import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/7
 */
public class RarExtractor implements Extractor {
    private final Archive archive;

    public RarExtractor(File file) {
        this(file, null);
    }

    public RarExtractor(File file, String password) {
        try {
            this.archive = new Archive(file, password);
        } catch (IOException | RarException e) {
            throw new IORuntimeException(e);
        }
    }

    public RarExtractor(InputStream in) {
        this(in, null);
    }

    public RarExtractor(InputStream in, String password) {
        try {
            this.archive = new Archive(in, password);
        } catch (IOException | RarException e) {
            throw new IORuntimeException(e);
        }
    }


    /**
     * 释放（解压）到指定目录，结束后自动关闭流，此方法只能调用一次
     *
     * @param targetDir 目标目录
     * @param filter    解压文件过滤器，用于指定需要释放的文件，null表示不过滤。当{@link Filter#accept(Object)}为true时释放。
     */
    @Override
    public void extract(File targetDir, int stripComponents, Filter<ArchiveEntry> filter) {
        try {
            extractInternal(targetDir, stripComponents, filter);
        } catch (IOException| RarException e) {
            throw new IORuntimeException(e);
        } finally {
            close();
        }
    }

    private void extractInternal(File targetDir, int stripComponents, Filter<ArchiveEntry> filter) throws IOException, RarException {
        Assert.isTrue(null != targetDir && (!targetDir.exists() || targetDir.isDirectory()), "target must be dir.");
        FileHeader entry;
        File outItemFile;
        while (null != (entry = archive.nextFileHeader())) {
            if (null != filter && !filter.accept(new FileArchiveEntry(entry))) {
                continue;
            }
            String entryName = stripName(entry.getFileName(), stripComponents);
            if (entryName == null) {
                // 剥离文件夹层级
                continue;
            }
            outItemFile = FileUtil.file(targetDir, entryName);
            if (entry.isDirectory()) {
                // 创建对应目录
                //noinspection ResultOfMethodCallIgnored
                outItemFile.mkdirs();
            }
            archive.extractFile(entry, Files.newOutputStream(outItemFile.toPath()));
        }
    }

    @Override
    public void close() {
        IoUtil.close(archive);
    }

    @AllArgsConstructor
    private static class FileArchiveEntry implements ArchiveEntry {
        private final FileHeader header;

        @Override
        public Date getLastModifiedDate() {
            return new Date(header.getLastModifiedTime().toMillis());
        }

        @Override
        public String getName() {
            return header.getFileName();
        }

        @Override
        public long getSize() {
            return header.getDataSize();
        }

        @Override
        public boolean isDirectory() {
            return header.isDirectory();
        }
    }
}
