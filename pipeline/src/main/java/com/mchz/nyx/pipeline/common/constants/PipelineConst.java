package com.mchz.nyx.pipeline.common.constants;

/**
 * <p>
 * 通用常量池
 * </p>
 *
 * <AUTHOR>
 * @date 2020/10/21 10:12
 */
public interface PipelineConst {

    /**
     * 内置数据源标识
     */
    String EMBED = "embed";
    /**
     * 内置表独立指定schema
     */
    String EMBED_SCHEMA = "${schema}";

    int DEFAULT_POOL_SIZE = Math.min(Runtime.getRuntime().availableProcessors(), 8);

    int PARALLEL_THREAD_SIZE = Math.max(DEFAULT_POOL_SIZE * 60 / 100, 1);

    String THIRD_PARTY = "sendExecutor";

    String STORE_THREAD = "STORE_THREAD";

    int JOB_LOG_BUFFER_SIZE = 1 << 10;

    int SCOPE_TABLE_SIZE = 2000;

    Long SCHEMA_PID = 0L;

    /**
     * 最大存储样本条数
     */
    int MAX_SAMPLE_DATA_SIZE = 100;

    int TABLE_COLUMN_NUM = 200;
    /**
     * 特殊空样本标识
     */
    String SAMPLE_EMPTY = "[]";
    /**
     * 默认，最大类型长度，主要为varchar、特定text
     */
    long MAX_COLUMN_LENGTH = 1000L;

    /**
     * 默认，候选个数
     */
    int CANDIDATE_COUNT = 3;

    int ERROR_MSG_MAX_LEN = 1000;

    float DEFAULT_TABLE_CLASSIFY_BASELINE = 0.7f;

    long REDIRECT = 100000;

    int CUSTOM_PRIORITY = 1000;

    int MAX_TERM_LEN = 1000;

    int MAX_META_NAME_LEN = 255;

    String DEFAULT_SCHEMA = "default";

    String DEFAULT_COLUMN_PREFIX = "field_";
}
