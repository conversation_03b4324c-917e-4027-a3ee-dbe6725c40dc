package com.mchz.nyx.pipeline.common;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.core.injector.AbstractMethod;
import com.baomidou.mybatisplus.core.injector.DefaultSqlInjector;
import com.baomidou.mybatisplus.core.mapper.Mapper;
import com.baomidou.mybatisplus.core.metadata.TableInfo;
import com.baomidou.mybatisplus.extension.injector.methods.AlwaysUpdateSomeColumnById;
import com.baomidou.mybatisplus.extension.injector.methods.InsertBatchSomeColumn;
import com.github.yulichang.method.*;
import com.github.yulichang.method.mp.*;
import com.github.yulichang.toolkit.MPJTableMapperHelper;
import com.github.yulichang.toolkit.ReflectionKit;
import com.mchz.nyx.pipeline.util.Func;
import org.apache.ibatis.builder.MapperBuilderAssistant;
import org.apache.ibatis.session.Configuration;

import java.util.List;
import java.util.Set;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @see com.github.yulichang.injector.MPJSqlInjector
 */
public class CustomInjector extends DefaultSqlInjector {

    @Override
    @SuppressWarnings("deprecation")
    public List<AbstractMethod> getMethodList(Class<?> mapperClass, TableInfo tableInfo) {
        return null;
    }

    @Override
    public List<AbstractMethod> getMethodList(Configuration configuration, Class<?> mapperClass, TableInfo tableInfo) {
        List<AbstractMethod> list = super.getMethodList(configuration, mapperClass, tableInfo);
        return methodFilter(list, needMore(mapperClass));
    }

    private boolean needMore(Class<?> mapperClass) {
        return BaseMapper.class.isAssignableFrom(mapperClass);
    }

    private List<AbstractMethod> methodFilter(List<AbstractMethod> list, boolean more) {
        Set<String> methodList = Func.newHashSet(
            "Delete",
            "Update",
            "SelectCount",
            "SelectMaps",
            "SelectObjs",
            "SelectList");
        list.removeIf(i -> methodList.contains(i.getClass().getSimpleName()));
        addWrapperMethod(list);
        addJoinMethod(list);
        if (more) {
            addMoreMethod(list);
        }
        return list;
    }

    private void addWrapperMethod(List<AbstractMethod> list) {
        list.add(new Delete());
        list.add(new Update());
        list.add(new SelectCount());
        list.add(new SelectMaps());
        list.add(new SelectObjs());
        list.add(new SelectList());
    }

    private void addJoinMethod(List<AbstractMethod> list) {
        list.add(new DeleteJoin(SqlMethod.DELETE_JOIN.getMethod()));
        list.add(new UpdateJoin(SqlMethod.UPDATE_JOIN.getMethod()));
        list.add(new UpdateJoinAndNull(SqlMethod.UPDATE_JOIN_AND_NULL.getMethod()));
        list.add(new SelectJoinCount(SqlMethod.SELECT_JOIN_COUNT.getMethod()));
        list.add(new SelectJoinOne(SqlMethod.SELECT_JOIN_ONE.getMethod()));
        list.add(new SelectJoinList(SqlMethod.SELECT_JOIN_LIST.getMethod()));
        list.add(new SelectJoinPage(SqlMethod.SELECT_JOIN_PAGE.getMethod()));
    }

    private void addMoreMethod(List<AbstractMethod> list) {
        //更新时自动填充的字段，不用插入值
        list.add(new InsertBatchSomeColumn("insertList", i -> i.getFieldFill() != FieldFill.UPDATE));
        list.add(new AlwaysUpdateSomeColumnById("alwaysUpdateById", i -> i.getFieldFill() != FieldFill.INSERT));
    }

    @Override
    public void inspectInject(MapperBuilderAssistant builderAssistant, Class<?> mapperClass) {
        Class<?> modelClass = ReflectionKit.getSuperClassGenericType(mapperClass, Mapper.class, 0);
        super.inspectInject(builderAssistant, mapperClass);
        MPJTableMapperHelper.init(modelClass, mapperClass);
    }
}
