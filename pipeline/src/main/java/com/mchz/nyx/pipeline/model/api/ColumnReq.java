package com.mchz.nyx.pipeline.model.api;

import com.mchz.nyx.common.enums.DataTypeGroup;
import com.mchz.nyx.dark.model.meta.ColumnType;
import com.mchz.nyx.dark.model.meta.NyxMetaColumn;
import lombok.Data;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2022/7/15 11:03
 */
@Data
public class ColumnReq implements NyxMetaColumn {
    private String name;
    private String comment;
    private String type;
    private List<Object> data;

    private ColumnType columnType;

    @Override
    public String getColumnName() {
        return name;
    }

    @Override
    public ColumnType getColumnType() {
        if (null == columnType) {
            columnType = new ColumnType(type, DataTypeGroup.CHARACTER);
        }
        return columnType;
    }

    @Override
    public String getColumnComment() {
        return comment;
    }
}
