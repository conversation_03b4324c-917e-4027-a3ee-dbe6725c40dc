package com.mchz.nyx.pipeline.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.mchz.nyx.pipeline.common.enums.SampleStatus;
import lombok.Data;

import java.util.Date;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/1/13
 */
@Data
@TableName("sample_column")
public class SampleColumn {
    @TableId(type = IdType.INPUT)
    private Long oid;

    private Long sourceId;

    private Long tableId;

    private SampleStatus status;

    private Date updatedAt;

    private String sampleData;
}
