package com.mchz.nyx.pipeline.service.impl;

import cn.hutool.core.util.ObjUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mchz.base.metis.RangeService;
import com.mchz.nyx.dark.common.enums.LoadType;
import com.mchz.nyx.dark.factory.RangeFunction;
import com.mchz.nyx.dark.model.dto.StandardLoadContext;
import com.mchz.nyx.pipeline.entity.CStdDictData;
import com.mchz.nyx.pipeline.entity.CSysRuleDetail;
import com.mchz.nyx.pipeline.entity.RefDataItem;
import com.mchz.nyx.pipeline.mapper.CStdDictDataMapper;
import com.mchz.nyx.pipeline.mapper.CSysRuleDetailMapper;
import com.mchz.nyx.pipeline.mapper.RefDataItemMapper;
import com.mchz.nyx.pipeline.util.Func;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class RangeFunctionImpl implements RangeFunction {
    private final RefDataItemMapper refDataItemMapper;

    private final CStdDictDataMapper stdDictDataMapper;
    private final CSysRuleDetailMapper sysRuleDetailMapper;

    @Setter
    private RangeService rangeService;

    @Override
    public Set<String> getValueRange(StandardLoadContext context, LoadType loadType, long dictId) {
        if (null == loadType) {
            return Collections.emptySet();
        }
        Set<String> res = new HashSet<>();
        switch (loadType) {
            case INNER:
                refDataItemMapper.selectList(Wrappers.lambdaQuery(RefDataItem.class).eq(RefDataItem::getRdsId, dictId).select(RefDataItem::getCode), rc -> res.add(rc.getResultObject().getCode()));
                break;
            case INNER_VALUE:
                refDataItemMapper.selectList(Wrappers.lambdaQuery(RefDataItem.class).eq(RefDataItem::getRdsId, dictId).select(RefDataItem::getName), rc -> res.add(rc.getResultObject().getName()));
                break;
            case CUSTOM:
                stdDictDataMapper.selectList(Wrappers.lambdaQuery(CStdDictData.class).select(CStdDictData::getContent).eq(CStdDictData::getDictId, dictId), v -> res.add(v.getResultObject().getContent()));
                break;
            case EMBED:
                Set<String> value = rangeService.getValueRange(dictId, context.getTenant());
                return ObjUtil.defaultIfNull(value, Collections.emptySet());
            case KEYWORD:
                CSysRuleDetail cSysRuleDetail = sysRuleDetailMapper.selectOne(Wrappers.lambdaQuery(CSysRuleDetail.class).select(CSysRuleDetail::getEnumContent).eq(CSysRuleDetail::getId, dictId));
                if (null != cSysRuleDetail) {
                    res.addAll(Func.parseEnums(cSysRuleDetail.getEnumContent()));
                }
                break;
            default:
        }
        return res;
    }

    @Override
    public Map<Long, Set<String>> listValueRange(StandardLoadContext context, LoadType loadType, Collection<Long> dictIds) {
        if (null == loadType) {
            return Collections.emptyMap();
        }
        Map<Long, Set<String>> res = new HashMap<>();
        switch (loadType) {
            case INNER:
                refDataItemMapper.selectList(Wrappers.lambdaQuery(RefDataItem.class).select(RefDataItem::getRdsId, RefDataItem::getCode).in(RefDataItem::getRdsId, dictIds), rc -> res.computeIfAbsent(rc.getResultObject().getRdsId(), k -> new HashSet<>()).add(rc.getResultObject().getCode()));
                break;
            case INNER_VALUE:
                refDataItemMapper.selectList(Wrappers.lambdaQuery(RefDataItem.class).select(RefDataItem::getRdsId, RefDataItem::getName).in(RefDataItem::getRdsId, dictIds), rc -> res.computeIfAbsent(rc.getResultObject().getRdsId(), k -> new HashSet<>()).add(rc.getResultObject().getCode()));
                break;
            case CUSTOM:
                stdDictDataMapper.selectList(Wrappers.lambdaQuery(CStdDictData.class).select(CStdDictData::getDictId, CStdDictData::getContent).in(CStdDictData::getDictId, dictIds), v -> res.computeIfAbsent(v.getResultObject().getDictId(), k -> new HashSet<>()).add(v.getResultObject().getContent()));
                break;
            case EMBED:
                Map<Long, Set<String>> map = rangeService.listValueRange(dictIds, context.getTenant());
                return ObjUtil.defaultIfNull(map, Collections.emptyMap());
            case KEYWORD:
                List<CSysRuleDetail> cSysRuleDetail = sysRuleDetailMapper.selectList(Wrappers.lambdaQuery(CSysRuleDetail.class).select(CSysRuleDetail::getId, CSysRuleDetail::getEnumContent).in(CSysRuleDetail::getId, dictIds));
                if (null != cSysRuleDetail) {
                    cSysRuleDetail.forEach(v -> res.put(v.getId(), new HashSet<>(Func.parseEnums(v.getEnumContent()))));
                }
                break;
            default:
        }
        return res;
    }
}
