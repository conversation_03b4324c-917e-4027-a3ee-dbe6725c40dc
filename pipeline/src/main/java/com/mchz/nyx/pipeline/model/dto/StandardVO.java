package com.mchz.nyx.pipeline.model.dto;

import com.mchz.nyx.pipeline.model.vo.ClassifyTypeVO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/8/19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class StandardVO {
    private Integer industryId;
    private List<ClassifyTypeVO> items;
}
