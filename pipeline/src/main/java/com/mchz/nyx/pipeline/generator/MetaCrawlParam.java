package com.mchz.nyx.pipeline.generator;

import com.mchz.nyx.meta.common.enums.MetaObjType;
import com.mchz.nyx.meta.support.CharsetConvert;
import com.mchz.nyx.meta.support.DataSourceConfig;
import com.mchz.nyx.meta.support.MetaCrawlConfig;
import com.mchz.nyx.meta.support.SchemaTables;
import com.mchz.nyx.pipeline.job.JobLogManager;
import com.mchz.nyx.pipeline.util.DbUtil;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.function.Consumer;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/2
 */
@Data
@Builder
public class MetaCrawlParam implements MetaCrawlConfig {
    private DataSourceConfig source;

    private List<SchemaTables> schemaTables;

    private String lastSchema;

    private List<MetaObjType> crawl;

    private JobLogManager jobLogManager;

    private Consumer<String> callback;

    @Override
    public CharsetConvert getCharsetConvert(){
        return DbUtil.getCharsetConvert(source);
    }

    /**
     * 兼容2.3.x 均采集数据量
     */
    @Override
    public boolean count() {
        return true;
    }
}
