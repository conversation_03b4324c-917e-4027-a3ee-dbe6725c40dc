package com.mchz.nyx.pipeline.service.impl;

import com.mchz.mcdatasource.core.DataBaseType;

import java.util.Objects;

public enum SupportAllTablesAndViewsDatabaseType {
    ORACLE(DataBaseType.ORACLE),
    MYSQL(DataBaseType.MYSQL),
    MYSQL_5(DataBaseType.MYSQL_5),
    RDS_MYSQL(DataBaseType.RDS_MYSQL),
    MSSQL(DataBaseType.MSSQL),
    DB2(DataBaseType.DB2),
    PGSQL(DataBaseType.PGSQL),
    GBASE8A(DataBaseType.GBASE8A),
    GBASE8S87(DataBaseType.GBASE8S87),
    DM(DataBaseType.DM),
    TERADATA(DataBaseType.TERADATA),
    ;
    private final DataBaseType type;

    SupportAllTablesAndViewsDatabaseType(DataBaseType type) {
        this.type = type;
    }

    public static boolean isSupport(DataBaseType type) {
        Objects.requireNonNull(type);
        for (SupportAllTablesAndViewsDatabaseType value : values()) {
            if (Objects.equals(value.type, type)) {
                return true;
            }
        }
        return false;
    }

    public static boolean isSupport(String pluginId) {
        Objects.requireNonNull(pluginId);
        for (SupportAllTablesAndViewsDatabaseType value : values()) {
            if (Objects.equals(value.type.pluginId, pluginId)) {
                return true;
            }
        }
        return false;
    }
}
