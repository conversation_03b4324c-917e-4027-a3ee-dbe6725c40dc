package com.mchz.nyx.pipeline.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mchz.nyx.pipeline.common.enums.ArchiverEnum;
import com.mchz.nyx.pipeline.common.enums.DatabaseType;
import com.mchz.nyx.pipeline.config.props.SourceDataProperties;
import com.mchz.nyx.pipeline.entity.FileUploadDetail;
import com.mchz.nyx.pipeline.exception.ServiceException;
import com.mchz.nyx.pipeline.generator.FileHandlerGenerator;
import com.mchz.nyx.pipeline.generator.file.FileHandler;
import com.mchz.nyx.pipeline.mapper.FileUploadDetailMapper;
import com.mchz.nyx.pipeline.model.api.RemoteAddressParam;
import com.mchz.nyx.pipeline.service.FileManagerService;
import com.mchz.nyx.pipeline.util.file.fs.CompressionBundle;
import com.mchz.nyx.pipeline.util.file.fs.FilesBundle;
import com.mchz.nyx.pipeline.util.file.fs.SourceFile;
import com.mchz.nyx.pipeline.util.file.fs.SourceFileBundle;
import com.mchz.nyx.pipeline.util.filter.StrFilter;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/11
 */
@Service
@AllArgsConstructor
public class FileManagerServiceImpl implements FileManagerService {
    private final FileUploadDetailMapper fileUploadDetailMapper;
    private final FileHandlerGenerator fileHandlerGenerator;
    private final SourceDataProperties properties;

    @Override
    public List<FileUploadDetail> listFileDetail(Long sourceId) {
        LambdaQueryWrapper<FileUploadDetail> q = Wrappers.lambdaQuery(FileUploadDetail.class).eq(FileUploadDetail::getConfigId, sourceId);
        return fileUploadDetailMapper.selectList(q);
    }

    @Override
    public FilesBundle<SourceFile> loadFile(List<FileUploadDetail> fileDetails, RemoteAddressParam remote, Map<String, StrFilter> filterMap, DatabaseType fileType) {
        if (CollUtil.isEmpty(fileDetails)) {
            throw new ServiceException("文件列表为空");
        }
        FileHandler handler = fileHandlerGenerator.generate(remote);
        File baseDir = new File(properties.getFile().getPath());
        if (ArchiverEnum.compressSuffix(fileDetails.get(0).getFormat())) {
            return new CompressionBundle(fileDetails, null == filterMap ? Collections.emptyMap() : filterMap, handler, baseDir, fileType);
        }
        return new SourceFileBundle(fileDetails, handler, baseDir);
    }
}
