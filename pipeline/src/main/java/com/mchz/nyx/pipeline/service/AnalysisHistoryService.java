package com.mchz.nyx.pipeline.service;

import com.mchz.nyx.dark.model.TargetInfo;

import java.util.List;
import java.util.function.BiConsumer;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/12
 */
public interface AnalysisHistoryService {
    List<Long> loadPublishedPlanIds(Long stdId);

    void loadAllHistoryResult(List<Long> planIds, BiConsumer<String, String> tcCall, BiConsumer<String, String> ccCall);

    List<TargetInfo> loadHistoryResultWithCC(List<Long> planIds, String name, String comment);

    List<TargetInfo> loadHistoryResultWithTC(List<Long> planIds, String table, String column);
}
