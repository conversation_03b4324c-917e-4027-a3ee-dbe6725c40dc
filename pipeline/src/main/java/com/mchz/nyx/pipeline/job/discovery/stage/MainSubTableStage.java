package com.mchz.nyx.pipeline.job.discovery.stage;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.NumberUtil;
import com.mchz.nyx.meta.entity.DbMetaObject;
import com.mchz.nyx.pipeline.common.enums.AnalysisColumnStatus;
import com.mchz.nyx.pipeline.common.enums.AnalysisTableStatus;
import com.mchz.nyx.pipeline.common.enums.RepeatColumnType;
import com.mchz.nyx.pipeline.common.enums.RepeatTableType;
import com.mchz.nyx.pipeline.entity.AnalysisColumn;
import com.mchz.nyx.pipeline.entity.AnalysisTable;
import com.mchz.nyx.pipeline.job.discovery.param.DiscoveryContext;
import com.mchz.nyx.pipeline.model.dto.SchemaScopeDTO;
import com.mchz.nyx.pipeline.model.dto.TableMSInfo;
import com.mchz.nyx.pipeline.service.AnalysisService;
import com.mchz.nyx.pipeline.service.MetadataService;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.roaringbitmap.RoaringBitmap;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/1
 */
@AllArgsConstructor
public class MainSubTableStage {
    private static final int BATCH_SIZE = 500;

    protected final MetadataService metadataService;
    protected final AnalysisService analysisService;

    public void execute(DiscoveryContext context, List<SchemaScopeDTO> stScope, boolean deleteSchema) {
        if (deleteSchema) {
            Set<Long> schemas = analysisService.listJobSchemas(context.getPlanId());
            stScope.forEach(v -> schemas.remove(v.getSchema().getOid()));
            analysisService.deleteJobWithSchemaId(context.getPlanId(), schemas);
        }
        for (SchemaScopeDTO schema : stScope) {
            context.heartbeat(schema.getSchema().getOid());
            context.interrupted();
            List<DbMetaObject> list = null != schema.getTables() ? schema.getTables() : metadataService.listMetaTableObj(context.getSourceId(), schema.getSchema().getOid());
            schemaScopeMerge(context, schema.getSchema(), list);
        }
    }

    private void schemaScopeMerge(DiscoveryContext context, DbMetaObject schema, List<DbMetaObject> tables) {
        List<AnalysisTable> list = analysisService.listJobTable(context.getPlanId(), schema.getOid());
        if (CollUtil.isEmpty(list)) {
            if (context.isMainSub()) {
                List<TableMSInfo> infos = buildTableMSInfoList(context, tables);
                processMainSub(context, schema, infos);
            } else {
                processInitData(context, schema, tables);
            }
            return;
        }
        Map<Long, AnalysisTable> map = list.stream().collect(Collectors.toMap(AnalysisTable::getTableId, Function.identity()));
        if (context.isMainSub()) {
            Map<Boolean, List<DbMetaObject>> part = tables.stream().collect(Collectors.partitioningBy(v -> map.containsKey(v.getOid())));
            List<DbMetaObject> init = part.get(Boolean.FALSE);
            if (CollUtil.isNotEmpty(init)) {
                List<TableMSInfo> infos = buildTableMSInfoList(context, init);
                processMainSub(context, schema, infos);
            }
            List<DbMetaObject> incr = part.get(Boolean.TRUE);
            if (CollUtil.isNotEmpty(incr)) {
                processIncremental(context, schema, incr, map);
            }
        } else {
            processIncremental(context, schema, tables, map);
        }
    }

    private void processInitData(DiscoveryContext context, DbMetaObject schema, List<DbMetaObject> metaList) {
        List<AnalysisTable> tables = new ArrayList<>();
        List<AnalysisColumn> columns = new ArrayList<>();
        metadataService.listMetaColumnObj(context.getSourceId(), metaList, (t, c) -> {
            tables.add(buildTable(context, schema, t, c.size()));
            c.forEach(v -> columns.add(buildColumn(context, schema, t, v)));
            batchProcessing(tables, columns, null);
        });
        analysisService.saveAnalysisTableColumn(tables, columns, null);
    }

    private void processIncremental(DiscoveryContext context, DbMetaObject schema, List<DbMetaObject> metaList, Map<Long, AnalysisTable> map) {
        List<AnalysisTable> updateMaster = new ArrayList<>();
        Map<Long, List<AnalysisTable>> mainSubMap = new HashMap<>();
        Map<Long, List<Long>> msMap = new HashMap<>();

        UpdatePayload up = new UpdatePayload();

        for (DbMetaObject table : metaList) {
            AnalysisTable at = map.remove(table.getOid());
            if (null == at) {
                List<DbMetaObject> columns = metadataService.listMetaColumnObj(context.getSourceId(), table.getOid());
                up.insert(buildTable(context, schema, table, columns.size()));
                columns.forEach(v -> up.insert(buildColumn(context, schema, table, v)));
                batchProcessing(context, up);
                continue;
            }
            if (Objects.equals(at.getVersion(), table.getRevision())) {
                if (RepeatTableType.SUB.equal(at.getRepeatType())) {
                    msMap.computeIfAbsent(at.getRepeatTableId(), k -> new ArrayList<>()).add(at.getTableId());
                } else if (RepeatTableType.MAIN.equal(at.getRepeatType())) {
                    msMap.computeIfAbsent(at.getTableId(), k -> new ArrayList<>());
                }
                continue;
            }
            at.setTableName(table.getName());
            at.setVersion(table.getRevision());
            if (RepeatTableType.SUB.equal(at.getRepeatType())) {
                msMap.computeIfAbsent(at.getRepeatTableId(), k -> new ArrayList<>()).add(at.getTableId());
                mainSubMap.computeIfAbsent(at.getRepeatTableId(), k -> new ArrayList<>()).add(at);
            } else if (RepeatTableType.MAIN.equal(at.getRepeatType())) {
                msMap.computeIfAbsent(at.getTableId(), k -> new ArrayList<>());
                updateMaster.add(at);
            } else {
                processTableDiff(context, up, schema, at, null);
            }
        }
        updateMaster.forEach(v -> processMasterTableDiff(context, up, schema, v, mainSubMap, msMap));
        msMap.entrySet().stream().filter(v -> CollUtil.isEmpty(v.getValue())).forEach(v -> up.normal(v.getKey()));
        for (Map.Entry<Long, AnalysisTable> entry : map.entrySet()) {
            if (!RepeatTableType.MAIN.equal(entry.getValue().getRepeatType())) {
                continue;
            }
            List<AnalysisTable> tables = mainSubMap.remove(entry.getKey());
            tables.forEach(v -> processTableDiff(context, up, schema, v, null));
            up.normal(tables.stream().map(AnalysisTable::getTableId).collect(Collectors.toList()));
        }
        mainSubMap.forEach((k, v) -> processSubTableDiff(context, up, schema, k, v));

        analysisService.updateToNormalTable(context.getPlanId(), up.getReTableIds());
        analysisService.saveAnalysisTableColumnIncremental(context.getPlanId(), up.getInsertTable(), up.getUpdate(), map.values().stream().map(AnalysisTable::getId).collect(Collectors.toList()), map.keySet(), up.getInsertColumn(), up.getDelete(), context.isIgnoreCandidate());
    }

    private void processMasterTableDiff(DiscoveryContext context, UpdatePayload ctx, DbMetaObject schema, AnalysisTable table, Map<Long, List<AnalysisTable>> mainSubMap, Map<Long, List<Long>> msMap) {
        List<Long> subTableIds = msMap.remove(table.getTableId());
        if (subTableIds.isEmpty()) {
            processTableDiff(context, ctx, schema, table, null);
            ctx.normal(table.getTableId());
            return;
        }
        List<AnalysisColumn> c2 = analysisService.listJobColumnAll(context.getPlanId(), table.getTableId());
        List<DbMetaObject> c1 = metadataService.listMetaColumnObj(context.getSourceId(), table.getTableId());
        Map<Long, AnalysisColumn> map = c2.stream().collect(Collectors.toMap(AnalysisColumn::getColumnId, Function.identity()));
        c1.stream().filter(v -> null == map.remove(v.getOid())).map(v -> buildMasterColumn(context, schema, table, v)).forEach(v -> {
            analysisService.updateSubColumn(context.getPlanId(), v.getColumnId(), subTableIds, v.getColumnName());
            ctx.insert(v);
        });
        analysisService.unlinkSubTableColumn(context.getPlanId(), map.keySet());
        map.values().forEach(v -> ctx.delete(v.getId()));
        batchProcessing(context, ctx);

        List<AnalysisTable> remove = mainSubMap.remove(table.getTableId());
        if (CollUtil.isEmpty(remove)) {
            return;
        }
        Map<String, Long> mColMap = c1.stream().collect(Collectors.toMap(DbMetaObject::getName, DbMetaObject::getOid));
        for (AnalysisTable st : remove) {
            processTableDiff(context, ctx, schema, st, mColMap);
        }
    }

    private void processSubTableDiff(DiscoveryContext context, UpdatePayload up, DbMetaObject schema, Long mTId, List<AnalysisTable> tables) {
        List<DbMetaObject> columns = metadataService.listMetaColumnObj(context.getSourceId(), mTId);
        Map<String, Long> mColMap = columns.stream().collect(Collectors.toMap(DbMetaObject::getName, DbMetaObject::getOid));
        for (AnalysisTable st : tables) {
            processTableDiff(context, up, schema, st, mColMap);
        }
    }

    private void processTableDiff(DiscoveryContext context, UpdatePayload ctx, DbMetaObject schema, AnalysisTable table, Map<String, Long> mColMap) {
        //TODO 修改表格确认状态
        List<DbMetaObject> c1 = metadataService.listMetaColumnObj(context.getSourceId(), table.getTableId());
        List<AnalysisColumn> c2 = analysisService.listJobColumnAll(context.getPlanId(), table.getTableId());
        Map<Long, AnalysisColumn> map = c2.stream().collect(Collectors.toMap(AnalysisColumn::getColumnId, Function.identity()));
        boolean[] verify = new boolean[]{true};
        if (null == mColMap) {
            c1.stream().filter(v -> isInsert(v, map, verify)).map(v -> buildColumn(context, schema, table, v)).forEach(ctx::insert);
        } else {
            c1.stream().filter(v -> isInsert(v, map, verify)).map(v -> buildSubColumn(context, schema, table, v, mColMap.get(v.getName()))).forEach(ctx::insert);
        }
        map.values().forEach(v -> ctx.delete(v.getId()));
        AnalysisTable u = new AnalysisTable();
        u.setId(table.getId());
        u.setVersion(table.getVersion());
        u.setStatus(verify[0] ? AnalysisTableStatus.S01.getCode() : AnalysisTableStatus.S00.getCode());
        ctx.update(u);
        batchProcessing(context, ctx);
    }

    private boolean isInsert(DbMetaObject obj, Map<Long, AnalysisColumn> map, boolean[] verify) {
        AnalysisColumn column = map.remove(obj.getOid());
        if (null == column) {
            if (verify[0]) {
                verify[0] = false;
            }
            return true;
        }
        if (verify[0] && !AnalysisColumnStatus.S1.getCode().equals(column.getStatus())) {
            verify[0] = false;
        }
        return false;
    }

    /**
     * 主副表计算
     */
    private List<TableMSInfo> buildTableMSInfoList(DiscoveryContext context, List<DbMetaObject> tables) {
        AtomicInteger i = new AtomicInteger();
        Map<String, Integer> column2Index = new HashMap<>(tables.size() << 4);
        List<TableMSInfo> res = new ArrayList<>(tables.size());
        metadataService.listMetaColumnObj(context.getSourceId(), tables, (t, c) -> {
            TableMSInfo info = new TableMSInfo(t);
            c.forEach(v -> info.addColumnIndex(column2Index.computeIfAbsent(v.getName(), k -> i.incrementAndGet())));
            res.add(info);
        });
        return res;
    }

    /**
     * 主副表计算
     */
    private void processMainSub(DiscoveryContext context, DbMetaObject schema, List<TableMSInfo> list) {
        List<AnalysisTable> tables = new ArrayList<>();
        List<AnalysisColumn> columns = new ArrayList<>();
        List<AnalysisTable> updateTables = new ArrayList<>();
        Deque<TableMSInfo> infos = list.stream().filter(v -> {
            if (0 == v.getSize()) {
                tables.add(buildTable(context, schema, v.getTable(), 0));
                return false;
            }
            return true;
        }).sorted().collect(Collectors.toCollection(LinkedList::new));
        for (TableMSInfo mTable = infos.poll(); null != mTable; mTable = infos.poll()) {
            DbMetaObject mObj = mTable.getTable();
            List<AnalysisTable> sub = getSubTables(context, schema, mTable, infos, mObj);
            List<DbMetaObject> column = metadataService.listMetaColumnObj(context.getSourceId(), mObj.getOid());
            AnalysisTable table = buildTable(context, schema, mObj, column.size());
            tables.add(table);
            if (sub.isEmpty()) {
                table.setRepeatType(RepeatTableType.NORMAL.getCode());
                column.forEach(v -> columns.add(buildColumn(context, schema, mObj, v)));
            } else {
                table.setRepeatType(RepeatTableType.MAIN.getCode());
                Map<String, Long> name2Id = new HashMap<>(columns.size());
                column.forEach(c -> {
                    name2Id.put(c.getName(), c.getOid());
                    columns.add(buildMasterColumn(context, schema, mObj, c));
                });
                List<Pair<AnalysisTable, Integer>> subTables = sub.stream().map(v -> Pair.of(v, v.getVersion())).peek(v -> v.getKey().setVersion(0)).collect(Collectors.toList());
                tables.addAll(sub);
                metadataService.listMetaColumnObj(context.getSourceId(), subTables, v -> v.getKey().getTableId(), (p, c) -> {
                    AnalysisTable t = p.getKey();
                    if (null == t.getId()) {
                        t.setVersion(p.getValue());
                    } else {
                        AnalysisTable tmp = new AnalysisTable();
                        tmp.setId(t.getId());
                        tmp.setVersion(p.getValue());
                        updateTables.add(tmp);
                    }
                    c.forEach(v -> columns.add(buildSubColumn(context, schema, t, v, name2Id.get(v.getName()))));
                    batchProcessing(tables, columns, updateTables);
                });
            }
            batchProcessing(tables, columns, updateTables);
        }
        analysisService.saveAnalysisTableColumn(tables, columns, updateTables);
    }

    private List<AnalysisTable> getSubTables(DiscoveryContext context, DbMetaObject schema, TableMSInfo mTable, Deque<TableMSInfo> infos, DbMetaObject mObj) {
        int mainSimilarity = context.getMainSimilarity();
        int subSimilarity = context.getSubSimilarity();
        List<AnalysisTable> sub = new ArrayList<>();
        Queue<TableMSInfo> queue = new LinkedList<>();
        for (TableMSInfo l = mTable; null != l; l = queue.poll()) {
            Iterator<TableMSInfo> iterator = infos.iterator();
            int maxSiz = l.getSize() * 100 / mainSimilarity;
            int minSize = (l.getSize() * mainSimilarity + 99) / 100;
            while (iterator.hasNext()) {
                TableMSInfo r = iterator.next();
                if (minSize > r.getSize()) {
                    continue;
                }
                if (maxSiz < r.getSize()) {
                    break;
                }
                RoaringBitmap and = RoaringBitmap.and(l.getBitmap(), r.getBitmap());
                int cardinality = and.getCardinality();
                int tmp = cardinality * 100;
                boolean isMainSub = l.getSize() > r.getSize() ? tmp >= l.getSize() * mainSimilarity && tmp >= r.getSize() * subSimilarity : tmp >= l.getSize() * subSimilarity && tmp >= r.getSize() * mainSimilarity;
                if (isMainSub) {
                    queue.add(r);
                    iterator.remove();
                    AnalysisTable table = buildTable(context, schema, r.getTable(), r.getSize());
                    table.setRepeatType(RepeatTableType.SUB.getCode());
                    table.setRepeatTableId(mObj.getOid());
                    table.setRepeatRate(NumberUtil.toBigDecimal(cardinality * 100.0d / r.getSize()));
                    sub.add(table);
                }
            }
        }
        return sub;
    }

    private void batchProcessing(List<AnalysisTable> tables, List<AnalysisColumn> columns, List<AnalysisTable> updateTables) {
        if (columns.size() < BATCH_SIZE) {
            return;
        }
        analysisService.saveAnalysisTableColumn(tables, columns, updateTables);
        tables.clear();
        columns.clear();
    }

    private void batchProcessing(DiscoveryContext context, UpdatePayload up) {
        if (up.checkSize(BATCH_SIZE)) {
            return;
        }
        analysisService.updateToNormalTable(context.getPlanId(), up.getReTableIds());
        analysisService.saveAnalysisTableColumnIncremental(context.getPlanId(), up.getInsertTable(), up.getUpdate(), null, null, up.getInsertColumn(), up.getDelete(), context.isIgnoreCandidate());
        up.clear();
    }

    private AnalysisTable buildTable(DiscoveryContext context, DbMetaObject schema, DbMetaObject table, int size) {
        context.interrupted();
        AnalysisTable at = new AnalysisTable();
        at.setSourceId(context.getSourceId());
        at.setPlanId(context.getPlanId());
        at.setJobId(context.getJobId());
        at.setSchemaId(schema.getOid());
        at.setSchemaName(schema.getName());
        at.setTableId(table.getOid());
        at.setTableName(table.getName());
        at.setVersion(table.getRevision());
        at.setStatus(size > 0 ? AnalysisTableStatus.S00.getCode() : AnalysisTableStatus.S01.getCode());
        at.setRepeatType(RepeatTableType.NORMAL.getCode());
        return at;
    }

    private AnalysisColumn buildSubColumn(DiscoveryContext context, DbMetaObject schema, AnalysisTable table, DbMetaObject column, Long mid) {
        AnalysisColumn ac = buildColumn(context, schema.getOid(), schema.getName(), table.getTableId(), table.getTableName(), column.getOid(), column.getName());
        if (null != mid) {
            ac.setRepeatType(RepeatColumnType.SUB.getCode());
            ac.setRepeatColumnId(mid);
        }
        return ac;
    }

    private AnalysisColumn buildMasterColumn(DiscoveryContext context, DbMetaObject schema, DbMetaObject table, DbMetaObject column) {
        AnalysisColumn ac = buildColumn(context, schema, table, column);
        ac.setRepeatType(RepeatColumnType.MAIN.getCode());
        return ac;
    }

    private AnalysisColumn buildMasterColumn(DiscoveryContext context, DbMetaObject schema, AnalysisTable table, DbMetaObject column) {
        AnalysisColumn ac = buildColumn(context, schema, table, column);
        ac.setRepeatType(RepeatColumnType.MAIN.getCode());
        return ac;
    }

    private AnalysisColumn buildColumn(DiscoveryContext context, DbMetaObject schema, AnalysisTable table, DbMetaObject column) {
        return buildColumn(context, schema.getOid(), schema.getName(), table.getTableId(), table.getTableName(), column.getOid(), column.getName());
    }

    private AnalysisColumn buildColumn(DiscoveryContext context, DbMetaObject schema, DbMetaObject table, DbMetaObject column) {
        return buildColumn(context, schema.getOid(), schema.getName(), table.getOid(), table.getName(), column.getOid(), column.getName());
    }

    private AnalysisColumn buildColumn(DiscoveryContext context, Long sid, String sName, Long tid, String tName, Long cId, String cName) {
        AnalysisColumn ac = new AnalysisColumn();
        ac.setPlanId(context.getPlanId());
        ac.setJobId(context.getJobId());
        ac.setSourceId(context.getSourceId());
        ac.setSchemaId(sid);
        ac.setSchemaName(sName);
        ac.setTableId(tid);
        ac.setTableName(tName);
        ac.setColumnId(cId);
        ac.setColumnName(cName);
        ac.setFlag(0);
        ac.setStatus(AnalysisColumnStatus.S5.getCode());
        ac.setRepeatType(RepeatColumnType.NORMAL.getCode());
        return ac;
    }

    @Getter
    private static class UpdatePayload {
        private final List<AnalysisTable> insertTable;
        private final List<AnalysisTable> update;
        private final List<AnalysisColumn> insertColumn;
        private final List<Long> delete;
        private final List<Long> reTableIds;

        public UpdatePayload() {
            this.insertTable = new ArrayList<>();
            this.update = new ArrayList<>();
            this.insertColumn = new ArrayList<>();
            this.delete = new ArrayList<>();
            this.reTableIds = new ArrayList<>();
        }

        public boolean checkSize(int size) {
            if (insertTable.size() > size) {
                return false;
            }
            if (update.size() > size) {
                return false;
            }
            if (delete.size() > size) {
                return false;
            }
            if (reTableIds.size() > size) {
                return false;
            }
            return insertColumn.size() <= (size << 1);
        }

        public void insert(AnalysisTable table) {
            insertTable.add(table);
        }

        public void insert(AnalysisColumn column) {
            insertColumn.add(column);
        }

        public void update(AnalysisTable table) {
            update.add(table);
        }

        public void delete(Long id) {
            delete.add(id);
        }

        public void normal(Long tableId) {
            reTableIds.add(tableId);
        }

        public void normal(List<Long> tableId) {
            reTableIds.addAll(tableId);
        }

        public void clear() {
            insertTable.clear();
            update.clear();
            insertColumn.clear();
            delete.clear();
            reTableIds.clear();
        }
    }

}
