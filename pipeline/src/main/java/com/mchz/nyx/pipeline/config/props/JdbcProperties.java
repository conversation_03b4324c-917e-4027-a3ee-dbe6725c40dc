package com.mchz.nyx.pipeline.config.props;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <p>
 * jdbc 配置
 * </p>
 *
 * <AUTHOR>
 * @date 2019-03-26 11:19
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "sourcedata.jdbc")
public class JdbcProperties {
    /**
     * jdbc url
     */
    private String url;
    /**
     * 用户名
     */
    private String username;
    /**
     * 密码
     */
    private String password;
    /**
     * 驱动名
     */
    private String driverClassName;

    private int maxPoolSize = 10;

    private int connectionTimeout = 30000;
}
