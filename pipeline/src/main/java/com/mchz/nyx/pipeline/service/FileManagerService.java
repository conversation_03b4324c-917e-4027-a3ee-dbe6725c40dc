package com.mchz.nyx.pipeline.service;

import com.mchz.nyx.pipeline.common.enums.DatabaseType;
import com.mchz.nyx.pipeline.entity.FileUploadDetail;
import com.mchz.nyx.pipeline.model.api.RemoteAddressParam;
import com.mchz.nyx.pipeline.util.file.fs.FilesBundle;
import com.mchz.nyx.pipeline.util.file.fs.SourceFile;
import com.mchz.nyx.pipeline.util.filter.StrFilter;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/11
 */
public interface FileManagerService {
    /**
     * 获取文件源列表信息
     */
    List<FileUploadDetail> listFileDetail(Long sourceId);

    default FilesBundle<SourceFile> loadFile(List<FileUploadDetail> fileDetails, RemoteAddressParam remote) {
        return loadFile(fileDetails, remote, null, null);
    }

    /**
     * 加载文件集合
     */
    FilesBundle<SourceFile> loadFile(List<FileUploadDetail> fileDetails, RemoteAddressParam remote, Map<String, StrFilter> filterMap, DatabaseType fileType);
}
