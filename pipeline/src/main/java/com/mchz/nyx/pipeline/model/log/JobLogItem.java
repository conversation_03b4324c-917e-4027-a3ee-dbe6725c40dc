package com.mchz.nyx.pipeline.model.log;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.boot.logging.LogLevel;

import java.math.BigDecimal;

/**
 * <p>
 * 日志内容
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/12/22 14:25
 */
@Data
@NoArgsConstructor
public class JobLogItem {
    /**
     * job id
     */
    private Long jobId;

    private String tenantId;

    /**
     * 开始时间
     */
    private Long startTime;

    /**
     * 日志生成时间
     */
    private Long time;

    /**
     * 进度
     */
    private BigDecimal percent;

    /**
     * 日志级别
     */
    private LogLevel level;

    /**
     * 内容
     */
    private String message;

    /**
     * 详情
     */
    private String remark;
}
