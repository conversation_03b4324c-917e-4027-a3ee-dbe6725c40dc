package com.mchz.nyx.pipeline.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/22
 */
@Data
@TableName("plan_release")
public class PlanRelease implements Serializable {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * planId
     */
    private Long planId;

    /**
     * 标准Id
     */
    private Long stdId;

    /**
     * 数据源Id
     */
    private Long sourceId;

    /**
     * 发布时间
     */
    private Long releaseTime;

    /**
     * 包含表数量
     */
    private Long tableCount;

    /**
     * 包含字段数量
     */
    private Long columnCount;
}
