package com.mchz.nyx.pipeline.job.discovery.stage;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.mchz.nyx.meta.support.DataSourceConfig;
import com.mchz.nyx.pipeline.common.enums.ArchiverEnum;
import com.mchz.nyx.pipeline.entity.AnalysisFileResult;
import com.mchz.nyx.pipeline.entity.AnalysisTable;
import com.mchz.nyx.pipeline.entity.FileUploadDetail;
import com.mchz.nyx.pipeline.generator.ClassifyRecommender;
import com.mchz.nyx.pipeline.generator.DarkContext;
import com.mchz.nyx.pipeline.job.discovery.param.DiscoveryJobState;
import com.mchz.nyx.pipeline.model.dto.ClassifyFileDTO;
import com.mchz.nyx.pipeline.model.param.McFileSourceParam;
import com.mchz.nyx.pipeline.service.AnalysisService;
import com.mchz.nyx.pipeline.service.FileManagerService;
import com.mchz.nyx.pipeline.util.DbUtil;
import com.mchz.nyx.pipeline.util.file.fs.FilesBundle;
import com.mchz.nyx.pipeline.util.file.fs.SourceFile;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/21
 */
@Slf4j
@AllArgsConstructor
public class FileClassificationStage {
    private final AnalysisService analysisService;
    private final FileManagerService fileManagerService;

    public void execute(DarkContext context, ClassifyRecommender recommender, DataSourceConfig source, List<Long> schemaIds) {
        DiscoveryJobState state = context.getState();

        Stream<Long> stream = schemaIds.stream();
        if (null != state && null != state.getSId()) {
            Long schemaId = schemaIds.get(0);
            if (state.getSId().equals(schemaId)) {
                processTable(context, recommender, source, schemaId, state.getTId());
                stream = stream.skip(1);
            }
        }
        stream.forEach(v -> processTable(context, recommender, source, v, null));

    }

    private void processTable(DarkContext context, ClassifyRecommender classifier, DataSourceConfig source, Long schema, Long tableId) {
        List<AnalysisTable> tables = analysisService.listJobFile(context.getPlanId(), schema, tableId, context.isIncrement());
        Map<String, AnalysisTable> map = tables.stream().collect(Collectors.toMap(AnalysisTable::getTableName, Function.identity()));
        List<FileUploadDetail> fileDetails = fileManagerService.listFileDetail(context.getSourceId());
        if (CollUtil.isEmpty(fileDetails)) {
            log.warn("【非结构化文件】源文件列表为空");
            return;
        }
        List<FileUploadDetail> tmp;
        if (ArchiverEnum.compressSuffix(fileDetails.get(0).getFormat())) {
            tmp = fileDetails;
        } else {
            tmp = fileDetails.stream().filter(v -> map.containsKey(v.getName())).collect(Collectors.toList());
        }
        if (CollUtil.isEmpty(tmp)) {
            log.warn("【非结构化文件】本次作业范围为空");
            return;
        }
        context.tables(tables.size());
        McFileSourceParam fileSource = DbUtil.fileSource(source);
        FilesBundle<SourceFile> sourceFiles = fileManagerService.loadFile(tmp, fileSource.getRemote(), null, fileSource.getFileType());
        for (SourceFile sourceFile : sourceFiles) {
            AnalysisTable table = map.get(sourceFile.getMeta().getName());
            if (null == table) {
                log.debug("【非结构化文件】忽略[{}]{}", sourceFile.getMeta().getName(), sourceFile.getFile().getPath());
                continue;
            }
            context.heartbeat(schema, table.getTableId());
            context.info("正在分析 {}", table.getTableName());
            context.table(table.getTableName());
            List<ClassifyFileDTO> classifications = classifier.recommendFileClassify(context, "jf" + table.getId(), sourceFile.getMeta().getRelPath());
            if (CollUtil.isNotEmpty(classifications)) {
                List<AnalysisFileResult> fileResults = new ArrayList<>();
                StringBuilder sb = new StringBuilder(",");
                Integer level = null;
                for (ClassifyFileDTO dto : classifications) {
                    fileResults.add(createFileResult(context, table, dto));
                    sb.append(dto.getTypeId()).append(",");
                    if (null == level || null != dto.getLevel() && dto.getLevel() > level) {
                        level = dto.getLevel();
                    }
                }
                AnalysisTable temp = new AnalysisTable();
                temp.setId(table.getId());
                temp.setJobId(context.getJobId());
                temp.setClassifyId(sb.toString());
                temp.setLevel(level);
                analysisService.saveFileClassify(temp, fileResults);
            }
            context.complete(table.getTableName());
        }
    }

    private AnalysisFileResult createFileResult(DarkContext context, AnalysisTable table, ClassifyFileDTO cf) {
        AnalysisFileResult result = new AnalysisFileResult();
        result.setPlanId(context.getPlanId());
        result.setJobId(context.getJobId());
        result.setResultId(table.getId());
        result.setClassifyId(cf.getTypeId());
        result.setLevel(cf.getLevel());
        result.setAccording(cf.getAccording());
        result.setContent(cf.getContent());
        result.setVersion(1);
        result.setRef(JSONUtil.toJsonStr(cf.getRef()));
        return result;
    }
}
