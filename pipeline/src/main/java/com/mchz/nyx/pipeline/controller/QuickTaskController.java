package com.mchz.nyx.pipeline.controller;

import com.mchz.nyx.pipeline.common.api.R;
import com.mchz.nyx.pipeline.model.api.DataMatchReq;
import com.mchz.nyx.pipeline.service.QuickTaskService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/9/29
 */
@Slf4j
@RestController
@RequestMapping("/quick")
@AllArgsConstructor
public class QuickTaskController {
    private final QuickTaskService service;

    @PostMapping("/reload")
    public R<Void> reload() {
        service.reload();
        return R.success();
    }

    @PostMapping("/match")
    public R<Object> match(@RequestBody DataMatchReq req) {
        return R.success(service.match(req).getColumns());
    }

    @PostMapping("/sim")
    public R<List<String>> match(@RequestBody Map<String, List<String>> data) {
        return R.success(service.similarity(data));
    }
}
