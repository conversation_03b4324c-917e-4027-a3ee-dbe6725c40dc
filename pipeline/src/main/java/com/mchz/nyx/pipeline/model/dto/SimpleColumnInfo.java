package com.mchz.nyx.pipeline.model.dto;

import com.mchz.nyx.common.enums.DataTypeGroup;
import com.mchz.nyx.dark.model.meta.ColumnType;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/1/3
 */
@Data
public class SimpleColumnInfo implements ColumnInfo {
    /**
     * 列id
     */
    private Long oid;

    private Long tableId;

    /**
     * 列名字
     */
    private String name;

    /**
     * 原始类型
     */
    private String dataType;

    /**
     * 类型分组
     */
    private String typeGroup;

    /**
     * 列类型
     */
    private ColumnType columnType;

    /**
     * 列长度
     */
    private Long length;


    @Override
    public Long getCid() {
        return oid;
    }

    @Override
    public String getColumnName() {
        return name;
    }

    @Override
    public ColumnType getColumnType() {
        if (null == columnType) {
            columnType = new ColumnType(dataType, DataTypeGroup.getType(typeGroup));
        }
        return columnType;
    }
}
