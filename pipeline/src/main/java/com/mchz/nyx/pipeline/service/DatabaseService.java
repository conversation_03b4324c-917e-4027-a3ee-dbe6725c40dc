package com.mchz.nyx.pipeline.service;

import com.mchz.datasource.cli.RsResult;
import com.mchz.nyx.pipeline.model.api.DataSourceLimitParam;
import com.mchz.nyx.pipeline.model.api.DataSourceParam;
import com.mchz.nyx.pipeline.model.api.TableDataSetParam;
import com.mchz.nyx.pipeline.model.dto.MetaColumnPlusDTO;
import com.mchz.nyx.pipeline.model.dto.MetaTableDTO;
import com.mchz.nyx.pipeline.model.other.ColumnData;
import com.mchz.nyx.pipeline.model.other.MetaTableExpand;
import com.mchz.nyx.pipeline.model.other.TriConsumer;
import com.mchz.nyx.pipeline.model.vo.ResultSetVO;
import com.mchz.nyx.pipeline.model.vo.TableVO;
import com.mchz.nyx.pipeline.job.JobLogManager;
import com.mchz.nyx.pipeline.util.cache.ListCache;

import java.util.List;

/**
 * <p>
 * 元数据采集
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/10/20 15:42
 */
public interface DatabaseService {

    /**
     * 优先级 越小优先级越高
     *
     * @return 优先级
     */
    int priority();

    /**
     * 元数据采集
     *
     * @param source   数据源信息
     * @param consumer 采集结果回调
     * @return true 支持
     */
    boolean crawlMetadata(DataSourceLimitParam source, JobLogManager jobLog,
                          TriConsumer<MetaTableDTO, List<MetaColumnPlusDTO>, MetaTableExpand> consumer) throws Exception;

    boolean crawlViewMetadata(DataSourceLimitParam source, JobLogManager jobLog,
        TriConsumer<MetaTableDTO, List<MetaColumnPlusDTO>, MetaTableExpand> consumer) throws Exception;

    default RsResult openQuery(DataSourceParam param, String sql) throws Exception {
        return null;
    }

    /**
     * 采集数据源schema列表
     *
     * @param config 数据源配置信息
     * @return schema列表
     */
    List<String> crawlSchema(DataSourceParam config);

    /**
     * 采集数据源table列表
     *
     * @param config 数据源配置信息
     * @return table列表
     */
    List<TableVO> getTables(DataSourceParam config);

    /**
     * 测试连接
     *
     * @param config 配置
     * @return true 支持测试连接（非测试连接结果）
     */
    boolean testConnection(DataSourceParam config);

    /**
     * 获取表格数据 缓存连接
     *
     * @param param 参数
     * @param close 主动关闭连接
     * @return 内容
     */
    ResultSetVO getTableDataSet(TableDataSetParam param, boolean close) throws Exception;

    /**
     * 获取表格数据，放入缓存
     *
     * @param param 参数
     * @param resultDataCache 结果集缓存
     * @param close 主动关闭连接
     * @throws Exception 异常
     */
    ClassLoader getTableDataSet2Cache(TableDataSetParam param,
                               ListCache<ColumnData> resultDataCache,
                               boolean close) throws Exception;

    /**
     * 关闭连接
     *
     * @param key 主键
     */
    default void closeConnection(Long key) {
    }
}
