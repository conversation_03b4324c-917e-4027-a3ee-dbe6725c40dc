package com.mchz.nyx.pipeline.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.mchz.base.metis.StandardService;
import com.mchz.base.metis.model.SimpleRuleDetail;
import com.mchz.base.metis.model.StandardDetail;
import com.mchz.nyx.dark.common.enums.LoadType;
import com.mchz.nyx.dark.common.enums.MatchType;
import com.mchz.nyx.dark.common.enums.RuleLoad;
import com.mchz.nyx.dark.exception.RuleException;
import com.mchz.nyx.dark.model.TargetInfo;
import com.mchz.nyx.dark.model.definition.*;
import com.mchz.nyx.dark.model.dto.StandardLoadContext;
import com.mchz.nyx.dark.model.dto.StdDetailDTO;
import com.mchz.nyx.dark.model.dto.TargetWithTagGroup;
import com.mchz.nyx.dark.model.dto.TermVendorDTO;
import com.mchz.nyx.dark.model.rule.RuleIdInfo;
import com.mchz.nyx.pipeline.common.constants.PipelineConst;
import com.mchz.nyx.pipeline.model.dto.ClassifyTypeDTO;
import com.mchz.nyx.pipeline.model.dto.StandardDetailDTO;
import com.mchz.nyx.pipeline.service.InnerRuleService;
import com.mchz.nyx.pipeline.service.RuleDetailService;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/29
 */
@Profile("embed")
@Slf4j
@Service
@RequiredArgsConstructor
public class RuleDetailServiceEmbedImpl implements RuleDetailService {

    private final InnerRuleService innerRuleService;

    @Setter
    private StandardService standardService;

    @Override
    public StandardDetailDTO loadRuleDetail(StandardLoadContext context, Long stdId, Integer industryId, boolean loadRule, boolean loadBase, Double hit) {
        StdDetailDTO res;
        if (null != standardService) {
            res = loadCustomRule(context, stdId, loadRule, hit);
        } else {
            Map<Long, TargetInfo> terms = innerRuleService.loadTerms(stdId);
            if (terms.isEmpty()) {
                return null;
            }
            if (loadRule) {
                res = innerRuleService.loadInnerRule(terms.keySet(), true, hit);
            } else {
                res = new StdDetailDTO();
            }
            res.setTargets(terms);
        }
        return new StandardDetailDTO(res);
    }

    @Override
    public List<ClassifyTypeDTO> loadClassifyType(Long stdId) {
        return Collections.emptyList();
    }

    private StdDetailDTO loadCustomRule(StandardLoadContext context, Long stdId, boolean loadRule, Double hit) {
        StandardDetail standardDTO = standardService.loadStandardDetail(stdId, context.getTenant());
        StdDetailDTO res = new StdDetailDTO();
        if (null == standardDTO || CollUtil.isEmpty(standardDTO.getRule())) {
            return res;
        }
        List<SimpleRuleDetail> rule = standardDTO.getRule();
        if (loadRule) {
            Map<String, TargetInfo> map = innerRuleService.loadTerms(stdId).entrySet().stream()
                .collect(Collectors.toMap(v -> v.getKey().toString(), Map.Entry::getValue));
            Map<Long, Long> mapping = loadCustomRuleDetail(res, hit, rule, map);
            mergeInnerRule(res, hit, mapping);
        } else {
            Map<Long, TargetInfo> terms = rule.stream().filter(v -> null != v.getId() && null != v.getName())
                .map(v -> new TargetInfo(v.getId(), v.getName(), false))
                .collect(Collectors.toMap(TargetInfo::getId, Function.identity()));
            res.setTargets(terms);
        }
        return res;
    }

    private Map<Long, Long> loadCustomRuleDetail(StdDetailDTO res, Double hit, List<SimpleRuleDetail> rule, Map<String, TargetInfo> map) {
        Map<Long, TargetInfo> terms = new HashMap<>(rule.size());
        List<TargetWithTagGroup> tagGroups = new ArrayList<>(rule.size());
        Map<String, RuleDetail> regexRule = new HashMap<>();
        Map<Long, RuleDetail> dictRule = new HashMap<>();
        Map<Long, Long> mapping = new HashMap<>();
        long i = PipelineConst.REDIRECT;
        for (SimpleRuleDetail item : rule) {
            if (null == item.getId()) {
                continue;
            }
            TargetInfo info = new TargetInfo(item.getId(), item.getName(), false);
            if (StrUtil.isNotEmpty(item.getInner())) {
                TargetInfo tmp = map.get(item.getInner());
                if (null != tmp) {
                    Long old = mapping.put(tmp.getId(), item.getId());
                    if (null != old) {
                        log.warn("【loadRule】重复绑定内置规则 {}({}):{}", item.getId(), item.getName(), old);
                    }
                    if (null == info.getName()) {
                        info.setName(tmp.getName());
                    }
                }
            }
            long id;
            if (null != item.getDictId()) {
                id = ++i;
                dictRule.computeIfAbsent(item.getDictId(), dictId -> buildRuleDetail(id, dictId, null, hit));
            } else if (null != item.getRegex()) {
                id = ++i;
                regexRule.computeIfAbsent(item.getRegex(), regex -> buildRuleDetail(id, null, regex, hit));
            } else {
                if (StrUtil.isEmpty(info.getName())) {
                    continue;
                }
                id = 0;
            }
            terms.put(info.getId(), info);

            if (id > 0) {
                TargetWithTagGroup group = new TargetWithTagGroup();
                group.setId(info.getId());
                group.setTags(Collections.singletonList(id));
                tagGroups.add(group);
            }
        }

        List<RuleDetail> ruleDetails = new ArrayList<>(regexRule.size() + dictRule.size() + mapping.size());
        ruleDetails.addAll(regexRule.values());
        ruleDetails.addAll(dictRule.values());

        res.setTargets(terms);
        res.setTagGroups(tagGroups);
        res.setRuleDetails(ruleDetails);
        return mapping;
    }

    private void mergeInnerRule(StdDetailDTO res, Double hit, Map<Long, Long> mapping) {
        List<TargetWithTagGroup> groups = res.getTagGroups();
        StdDetailDTO tmp = innerRuleService.loadInnerRule(mapping.keySet(), true, hit);
        tmp.getTagGroups().forEach(v -> {
            Long id = mapping.get(v.getId());
            if (null == id) {
                return;
            }
            v.setId(id);
            groups.add(v);
        });
        if (null != tmp.getVendor()) {
            res.setVendor(tmp.getVendor().stream().map(v -> {
                Long id = mapping.get(v.getId());
                if (null == id) {
                    return null;
                }
                return new TermVendorDTO(id, v.getTable(), v.getColumn());
            }).filter(Objects::nonNull).collect(Collectors.toList()));
        }
        res.getRuleDetails().addAll(tmp.getRuleDetails());
    }

    private RuleDetail buildRuleDetail(long id, Long dictId, String regex, Double hit) {
        StrSegmentRule strRule = new StrSegmentRule();
        if (null != dictId) {
            DictDetail dictDetail = new DictDetail();
            dictDetail.setDictId(dictId);
            dictDetail.setLoadType(LoadType.EMBED);
            dictDetail.setMatchType(MatchType.EQUAL);
            strRule.setDict(Collections.singletonList(dictDetail));
        } else if (null != regex) {
            RegexDetail regexDetail = new RegexDetail();
            regexDetail.setValue(regex);
            strRule.setRegex(Collections.singletonList(regexDetail));
        } else {
            throw new RuleException("规则参数为空");
        }

        SingleRuleDetail single = new SingleRuleDetail();
        single.setPriority(PipelineConst.CUSTOM_PRIORITY);
        single.setData(strRule);
        single.setHit(hit);

        RuleDetail detail = new RuleDetail();
        detail.setInfo(new RuleIdInfo(id));
        detail.setLoad(RuleLoad.TAG_SINGLE);
        detail.setRule(single);
        return detail;
    }
}
