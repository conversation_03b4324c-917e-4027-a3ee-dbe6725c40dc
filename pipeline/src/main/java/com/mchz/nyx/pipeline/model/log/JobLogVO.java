package com.mchz.nyx.pipeline.model.log;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <p>
 * 作业日志表
 * </p>
 *
 * <AUTHOR>
 * @date 2020/11/5 14:07
 */
@Data
@NoArgsConstructor
public class JobLogVO {
    /**
     * 源数据发现作业id
     */
    private Long jobId;

    /**
     * 开始时间
     */
    private Long startTime;

    /**
     * 系统当前时间
     */
    private Long nowTime;

    /**
     * 百分比
     */
    private BigDecimal percent;

    /**
     * 集群-多租户 租户Id
     */
    private String tenantId;

}
