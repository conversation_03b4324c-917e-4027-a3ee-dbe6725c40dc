package com.mchz.nyx.pipeline.model.dto;

import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NonNull;

import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/1/21
 */
@Data
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class IdsQueryDTO {
    private Long start;
    private Long end;
    private List<Long> ids;

    public static IdsQueryDTO of(List<Long> ids) {
        return new IdsQueryDTO(null, null, ids);
    }

    public static IdsQueryDTO ofOrderly(List<Long> ids) {
        if (null != ids && ids.size() > 1) {
            Long s = ids.get(0);
            Long e = ids.get(ids.size() - 1);
            if (e - s == ids.size() - 1) {
                return new IdsQueryDTO(s, e, null);
            }
        }
        return new IdsQueryDTO(null, null, ids);
    }

    public static <T> IdsQueryDTO ofOrderly(@NonNull List<T> list, @NonNull Function<T, Long> getId) {
        if (list.size() > 1) {
            Long s = getId.apply(list.get(0));
            Long e = getId.apply(list.get(list.size() - 1));
            if (e - s == list.size() - 1) {
                return new IdsQueryDTO(s, e, null);
            }
        }
        return new IdsQueryDTO(null, null, list.stream().map(getId).collect(Collectors.toList()));
    }

    public static IdsQueryDTO of(Long start, Long end) {
        return new IdsQueryDTO(start, end, null);
    }
}
