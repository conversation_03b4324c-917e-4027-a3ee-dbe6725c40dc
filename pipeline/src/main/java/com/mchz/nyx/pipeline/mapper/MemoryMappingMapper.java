package com.mchz.nyx.pipeline.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.mchz.nyx.pipeline.common.BaseEmbedMapper;
import com.mchz.nyx.pipeline.common.constants.PipelineConst;
import com.mchz.nyx.pipeline.entity.MemoryMapping;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/7/9
 */
@DS(PipelineConst.EMBED)
public interface MemoryMappingMapper extends BaseEmbedMapper<MemoryMapping> {
}
