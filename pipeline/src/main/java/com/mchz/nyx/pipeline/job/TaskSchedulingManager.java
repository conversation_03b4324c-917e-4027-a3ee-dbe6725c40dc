package com.mchz.nyx.pipeline.job;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.thread.ExecutorBuilder;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.ttl.threadpool.TtlExecutors;
import com.mchz.nyx.common.exception.InterruptedJobException;
import com.mchz.nyx.pipeline.common.api.CommonResultCode;
import com.mchz.nyx.pipeline.common.api.Status;
import com.mchz.nyx.pipeline.common.enums.JobType;
import com.mchz.nyx.pipeline.config.props.SourceProperties;
import com.mchz.nyx.pipeline.exception.ApiException;
import com.mchz.nyx.pipeline.exception.ServiceException;
import com.mchz.nyx.pipeline.model.log.ProcessingTable;
import com.mchz.nyx.pipeline.service.JobStatusService;
import com.mchz.nyx.pipeline.util.Func;
import com.mchz.starter.tenant.util.TenantUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <p>
 * 任务调度中心
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/12/22 14:06
 */
@Slf4j
@Component
public class TaskSchedulingManager {
    private final SourceProperties properties;
    private final JobStatusService jobStatusService;
    private final Map<JobType, Job> jobFactory;

    private int size;
    private ExecutorService poolExecutor;
    private Map<String, RunningTask> taskMap;

    public TaskSchedulingManager(SourceProperties properties, JobStatusService jobStatusService, List<Job> jobs) {
        this.properties = properties;
        this.jobStatusService = jobStatusService;
        this.jobFactory = jobs.stream().collect(Collectors.toMap(Job::getJobType, Function.identity(), ((o1, o2) -> o1), (Supplier<Map<JobType, Job>>) () -> new EnumMap<>(JobType.class)));
    }

    @PostConstruct
    public void init() {
        size = Math.max(properties.getSys().getDiscoveryTaskSize(), 1);
        ThreadFactory factory = ThreadUtil.newNamedThreadFactory("dark-task-", null, false,
            (t, e) -> log.error("【作业执行异常】线程{}({})发现未被捕获的异常:{}", t.getName(), t.getId(), e.getMessage()));
        ThreadPoolExecutor executor = ExecutorBuilder.create().setCorePoolSize(Math.min(3, size)).setThreadFactory(factory).setMaxPoolSize(size).useSynchronousQueue().build();
        poolExecutor = TtlExecutors.getTtlExecutorService(executor);
        taskMap = MapUtil.newConcurrentHashMap();
        jobFactory.forEach((k, v) -> log.info("{}: {}", k, v.getClass().getSimpleName()));
    }

    @PreDestroy
    public void destroy() {
        log.info("Shutting down task manager executor");
        poolExecutor.shutdown();
        try {
            int timeout = 10;
            if (!poolExecutor.awaitTermination(timeout, TimeUnit.SECONDS)) {
                poolExecutor.shutdownNow();
            }
        } catch (InterruptedException e) {
            poolExecutor.shutdownNow();
        }
    }

    /**
     * 提交任务
     */
    public synchronized void submit(JobStartConfig jobConfig) {
        if (taskMap.size() >= size) {
            log.warn("【任务管理】队列已满{}/{}", taskMap.size(), size);
            throw new ApiException(Status.QUEUE_BUSY);
        }
        if (null == jobConfig.getSource().getSchemaSet() && StrUtil.isEmpty(jobConfig.getSource().getTables())) {
            log.info("【任务管理】全库");
        }
        Long jobId = jobConfig.getJobId();
        String taskId = buildTaskId(jobId);
        if (taskMap.containsKey(taskId)) {
            log.warn("【任务管理】忽略{}", taskId);
            return;
        }
        if (taskMap.values().stream().anyMatch(v -> v.isSamePlan(jobConfig))) {
            throw new ApiException(Status.HAS_PENDING_TASK, jobConfig.getPlanId());
        }
        try {
            String id = String.format("J%d(P%d-S%d%s)", jobId, jobConfig.getPlanId(), jobConfig.getSource().getId(), null == jobConfig.getStdId() ? "" : "-s" + jobConfig.getStdId());
            JobType jobType = jobConfig.getJobType();
            Job job = jobFactory.get(jobType);
            if (null == job) {
                throw new ApiException(jobType + "无对应执行器");
            }
            JobLogManager jobLog = new JobLogManager(id, jobId, jobConfig.getTenantId(), job.getStageProportion(jobConfig));
            Future<?> future = poolExecutor.submit(() -> executeTask(job, jobConfig, jobLog));
            taskMap.put(taskId, new RunningTask(future, jobConfig.getPlanId(), jobLog));
        } catch (RejectedExecutionException e) {
            throw new ServiceException(CommonResultCode.REQ_REJECT);
        }
    }


    /**
     * 终止任务
     *
     * @param jobId 任务id
     */
    public void terminated(Long jobId) {
        String key = buildTaskId(jobId);
        RunningTask task = taskMap.get(key);
        if (null == task) {
            return;
        }
        log.info("【任务管理】发送中断 {}", key);
        task.getJobLog().interrupt();
        Future<?> future = task.getFuture();
        if (future.isDone() || future.isCancelled()) {
            try {
                future.get(1, TimeUnit.MINUTES);
            } catch (InterruptedException e) {
                log.info("【任务管理】{} 已经中断", key);
            } catch (ExecutionException e) {
                log.info("【任务管理】{} 已经中断,等待结果异常:{}", key, Func.getMessage(e));
            } catch (TimeoutException e) {
                log.info("【任务管理】{} 已经中断,等待结果超时", key);
                return;
            }
            taskMap.remove(key);
            return;
        }
        future.cancel(true);
        log.info("【任务管理】接收中断 {} ", key);
    }

    public ProcessingTable getProcessingTable(Long jobId) {
        RunningTask task = taskMap.get(buildTaskId(jobId));
        if (null == task) {
            return null;
        }
        JobLogManager jobLog = task.getJobLog();
        return jobLog.getProcessingTable();
    }

    private void executeTask(Job job, JobStartConfig jobConfig, JobLogManager jobLog) {
        Func.pushJobLog(jobLog);
        String id = jobLog.getId();
        JobType jobType = jobConfig.getJobType();
        log.info("【作业执行】{} {}", id, jobConfig.getSource().getConfigName());
        boolean success = false;
        Throwable error = null;
        try {
            jobLog.start(jobStatusService.jobStart(jobType, jobLog));
            job.execute(jobLog, jobConfig);
            success = true;
        } catch (InterruptedException | InterruptedJobException e) {
            log.error("【作业执行】{} 被中断,{}", id, Func.getMessageWithStack(e));
        } catch (Throwable e) {
            if (jobLog.isInterrupted()) {
                log.error("【作业执行】{} 被中断", id, e);
            } else {
                log.error("【作业执行】{} 执行异常", id, e);
                jobLog.error("作业执行异常", e);
                error = e;
            }
        } finally {
            try {
                jobStatusService.jobFinish(jobType, jobLog, success, error);
            } catch (Exception e) {
                log.error("【作业执行】结束异常,{}", Func.getMessageWithStack(e));
            }
            Func.popJobLog();
            taskMap.remove(buildTaskId(jobLog.getJobId()));
            log.info(jobLog.getStopWatch().prettyPrint());
        }
    }

    private String buildTaskId(Long jobId) {
        return String.format("%s%s", TenantUtil.tenant(), jobId);
    }

    @Data
    private static class RunningTask {
        private final Future<?> future;
        private final Long planId;
        private final JobLogManager jobLog;

        public boolean isSamePlan(JobStartConfig config) {
            return planId.equals(config.getPlanId()) && StrUtil.equals(jobLog.getTenantId(), config.getTenantId());
        }
    }
}
