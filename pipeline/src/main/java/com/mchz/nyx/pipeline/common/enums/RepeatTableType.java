package com.mchz.nyx.pipeline.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 * 重复表类型：0=非重复表；1=重复主表；2=重复副表；
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/1
 */
@Getter
@AllArgsConstructor
public enum RepeatTableType {
    NORMAL(0),
    MAIN(1),
    SUB(2),
    ;
    private final Integer code;

    public boolean equal(Integer code) {
        return this.code.equals(code);
    }
}
