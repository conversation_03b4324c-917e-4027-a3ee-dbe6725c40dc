package com.mchz.nyx.pipeline.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;

import java.util.*;
import java.util.function.Function;
import java.util.function.Supplier;

/**
 * 集合查找器
 */
public final class CollectionSearcher<T, R> {
    public static final CollectionSearcher EMPTY = new CollectionSearcher();

    private final Collection<R> collection;
    private final Map<T, R> map;

    private CollectionSearcher() {
        this.collection = new ArrayList<>();
        this.map = new HashMap<>();
    }

    private CollectionSearcher(Collection<R> collection, Map<T, R> map) {
        this.collection = collection;
        this.map = map;
    }

    public R get(T t) {
        return map.get(t);
    }

    public R getNonNull(T t) {
        return Objects.requireNonNull(get(t), JSONUtil.toJsonStr(t));
    }

    public Collection<R> getCollection() {
        return collection;
    }

    public static <T, R> CollectionSearcher<T, R> newInstance(Supplier<Collection<R>> supplier, Function<? super R, T> function) {
        Collection<R> collection = supplier.get();
        if (CollUtil.isNotEmpty(collection)) {
            return newInstance(collection, function);
        }
        return new CollectionSearcher<>();
    }

    public static <T, R> CollectionSearcher<T, R> newInstance(Collection<R> collection, Function<? super R, T> function) {
        HashMap<T, R> map = new HashMap<>();
        if (CollUtil.isNotEmpty(collection)) {
            for (R r : collection) {
                map.put(function.apply(r), r);
            }
            return new CollectionSearcher<>(collection, map);
        }
        return new CollectionSearcher<>();
    }

    public static <T, R> CollectionSearcher<T, R> newInstance(Collection<R> collection,
                                                              Function<? super R, T> keyFunction, Function<? super R, R> valueFunction) {
        HashMap<T, R> map = new HashMap<>();
        if (CollUtil.isNotEmpty(collection)) {
            for (R r : collection) {
                map.put(keyFunction.apply(r), valueFunction.apply(r));
            }
            return new CollectionSearcher<>(collection, map);
        }
        return new CollectionSearcher<>();
    }
}
