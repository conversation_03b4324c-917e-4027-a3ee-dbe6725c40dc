package com.mchz.nyx.pipeline.util.file.reader;

import cn.hutool.core.text.csv.CsvData;
import cn.hutool.core.text.csv.CsvReadConfig;
import cn.hutool.core.text.csv.CsvReader;
import cn.hutool.core.text.csv.CsvRow;

import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.Charset;
import java.util.List;
import java.util.stream.Collectors;

public class CsvDatabaseFileReader extends AbstractDatabaseFileReader {
    private final Charset charset;
    private final char fieldSeparator;
    private CsvData csvData;

    public CsvDatabaseFileReader(InputStream inputStream, boolean hasHead, Charset charset, char fieldSeparator) {
        super(inputStream, hasHead);
        this.charset = charset;
        this.fieldSeparator = fieldSeparator;
        init();
    }

    private void init() {
        CsvReadConfig config = new CsvReadConfig();
        config.setFieldSeparator(fieldSeparator);
        config.setContainsHeader(this.hasHead);
        config.setSkipEmptyRows(false);
        config.setErrorOnDifferentFieldCount(true);
        InputStreamReader streamReader = new InputStreamReader(this.inputStream, this.charset);
        CsvReader csvReader = new CsvReader(streamReader, config);
        this.csvData = csvReader.read();
    }

    @Override
    public List<String> readHead() {
        return this.csvData.getHeader();
    }

    @Override
    public List<List<String>> readData(int num) {
        return this.csvData.getRows().stream().map(CsvRow::getRawList).limit(num).collect(Collectors.toList());
    }

    @Override
    public int count() {
        return this.csvData.getRowCount();
    }
}
