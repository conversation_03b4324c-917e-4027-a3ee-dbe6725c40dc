package com.mchz.nyx.pipeline.generator.file;

import cn.hutool.core.io.FileUtil;

import java.io.File;
import java.io.InputStream;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/4/29
 */
public interface FileHandler extends AutoCloseable {

    void loadFile(String source, File target);

    default File getFile(String source, String target) {
        File file = new File(target);
        loadFile(source, file);
        return file;
    }

    default File getFile(String source) {
        File file = FileUtil.createTempFile();
        loadFile(source, file);
        return file;
    }


    default InputStream getInputStream(String path) {
        return FileUtil.getInputStream(getFile(path));
    }
}
