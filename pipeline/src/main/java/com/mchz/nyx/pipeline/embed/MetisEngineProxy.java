package com.mchz.nyx.pipeline.embed;

import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.mchz.base.metis.MetisEngine;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;

import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/8/16
 */
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class MetisEngineProxy implements InvocationHandler {
    private final ClassLoader cl;
    private final MetisEngine metisEngine;

    public static MetisEngine engine() {
        MetisEngine bean = SpringUtil.getBean(MetisEngine.class);
        return (MetisEngine) Proxy.newProxyInstance(MetisEngine.class.getClassLoader(), new Class[]{MetisEngine.class}, new MetisEngineProxy(SpringUtil.class.getClassLoader(), bean));
    }

    @Override
    public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {
        ClassLoader loader = Thread.currentThread().getContextClassLoader();
        try {
            Thread.currentThread().setContextClassLoader(cl);
            return method.invoke(metisEngine, args);
        } catch (Throwable e) {
            throw ExceptionUtil.unwrap(e);
        } finally {
            Thread.currentThread().setContextClassLoader(loader);
        }
    }
}
