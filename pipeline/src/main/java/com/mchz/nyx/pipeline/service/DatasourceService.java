package com.mchz.nyx.pipeline.service;

import com.mchz.nyx.pipeline.model.api.DataSourceParam;
import com.mchz.nyx.pipeline.model.dto.TestConnInfo;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/12/26
 */
public interface DatasourceService {
    /**
     * 测试连接
     *
     * @param config 配置
     */
    TestConnInfo testConnection(DataSourceParam config);

    List<String> loadSchemas(DataSourceParam config);
}
