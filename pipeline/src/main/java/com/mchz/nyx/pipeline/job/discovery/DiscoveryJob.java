package com.mchz.nyx.pipeline.job.discovery;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.mchz.nyx.common.exception.NyxException;
import com.mchz.nyx.common.util.NyxStopWatch;
import com.mchz.nyx.dark.model.meta.AdditionalInfo;
import com.mchz.nyx.meta.NyxInstance;
import com.mchz.nyx.meta.entity.DbMetaObject;
import com.mchz.nyx.meta.model.meta.CatalogSchema;
import com.mchz.nyx.meta.support.DataSourceConfig;
import com.mchz.nyx.meta.support.SchemaTables;
import com.mchz.nyx.pipeline.common.constants.PipelineConst;
import com.mchz.nyx.pipeline.common.enums.DatabaseType;
import com.mchz.nyx.pipeline.common.enums.JobType;
import com.mchz.nyx.pipeline.config.props.SourceProperties;
import com.mchz.nyx.pipeline.generator.*;
import com.mchz.nyx.pipeline.job.Job;
import com.mchz.nyx.pipeline.job.JobLogManager;
import com.mchz.nyx.pipeline.job.JobStartConfig;
import com.mchz.nyx.pipeline.job.discovery.param.DiscoveryContext;
import com.mchz.nyx.pipeline.job.discovery.param.DiscoveryJobState;
import com.mchz.nyx.pipeline.job.discovery.stage.ClassificationStage;
import com.mchz.nyx.pipeline.job.discovery.stage.FileClassificationStage;
import com.mchz.nyx.pipeline.job.discovery.stage.MainSubTableStage;
import com.mchz.nyx.pipeline.model.api.DataSourceLimitParam;
import com.mchz.nyx.pipeline.model.dto.SchemaScopeDTO;
import com.mchz.nyx.pipeline.model.vo.TableVO;
import com.mchz.nyx.pipeline.service.AnalysisService;
import com.mchz.nyx.pipeline.service.FileManagerService;
import com.mchz.nyx.pipeline.service.MetaExtendService;
import com.mchz.nyx.pipeline.service.MetadataService;
import com.mchz.nyx.pipeline.util.DbUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Component
@RequiredArgsConstructor
public class DiscoveryJob implements Job {
    private final SourceProperties properties;
    private final MetaInstanceGenerator metaInstanceGenerator;
    private final ActuatorGenerator actuatorGenerator;
    private final AnalysisService analysisService;
    private final MetaExtendService metaExtendService;
    private final MetadataService metadataService;
    private final FileManagerService fileManagerService;

    @Override
    public JobType getJobType() {
        return JobType.DISCOVERY;
    }

    @Override
    public int[] getStageProportion(JobStartConfig jobConfig) {
        if (Boolean.TRUE.equals(jobConfig.getIgnoreMeta())) {
            return new int[]{1, 0, 9, 80};
        }
        return new int[]{0, 45, 5, 45};
    }

    @Override
    public void execute(JobLogManager jobLog, JobStartConfig jobConfig) {
        DataSourceLimitParam sourceParam = jobConfig.getSource();
        List<SchemaTables> scope = jobScope(sourceParam);
        DataSourceConfig source = DbUtil.newDatasourceParam(sourceParam);
        NyxStopWatch watch = jobLog.getStopWatch();

        if (jobLog.setStep(1) && !Boolean.TRUE.equals(jobConfig.getIgnoreMeta())) {
            String schema = jobLog.getHeartbeatDetails(String.class);
            log.info("【分类分级】采集元数据{}", StrUtil.nullToEmpty(schema));
            List<SchemaTables> schemaTables = scopeStage1(source.getSourceId(), scope, schema);
            NyxInstance instance = metaInstanceGenerator.generate(source, jobLog, schemaTables, null, jobLog::heartbeat);
            jobLog.info(null == schema ? "开始采集元数据信息" : "继续采集元数据信息");
            long[] key = watch.startChild("stage1");
            instance.run();
            watch.stopChild(key);
            jobLog.info("元数据采集完成");
        }

        List<SchemaScopeDTO> stScope;
        if (jobLog.setStep(2)) {
            jobLog.info("初始化与增量分析");
            DiscoveryJobState state = ObjUtil.defaultIfNull(jobLog.getHeartbeatDetails(DiscoveryJobState.class), DiscoveryJobState::of);
            log.info("【分类分级】主副表与增量处理 {}", ObjUtil.defaultIfNull(state.getSId(), StrUtil.EMPTY));
            stScope = metadataService.getSchemaTableScope(source.getSourceId(), scope, false);
            DiscoveryContext context = buildDisContext(source, jobConfig, jobLog);
            MainSubTableStage stage2 = new MainSubTableStage(metadataService, analysisService);
            long[] key = watch.startChild("stage2");
            stage2.execute(context, scopeStage2(stScope, state.getSId()), null == state.getSId());
            watch.stopChild(key);
        } else {
            stScope = metadataService.getSchemaTableScope(source.getSourceId(), scope, true);
        }

        if (jobLog.setStep(3)) {
            jobLog.info("创建分析引擎");
            log.info("【分类分级】创建分析引擎");
            long[] key;
            DarkContext darkContext = buildDarkContext(source, jobConfig, jobLog);
            List<Long> schemaIds = stScope.stream().map(SchemaScopeDTO::getSchema).map(DbMetaObject::getOid).collect(Collectors.toList());
            DatabaseType fileType = DbUtil.fileType(source);
            if (fileType.isUnstructured()) {
                try (ClassifyRecommender recommender = actuatorGenerator.generateClassifyRecommender(jobConfig)) {
                    if (recommender.nonClient()) {
                        throw new NyxException("作业配置异常");
                    }
                    FileClassificationStage stage3 = new FileClassificationStage(analysisService, fileManagerService);
                    key = watch.startChild("stage3 udf");
                    stage3.execute(darkContext, recommender, source, schemaIds);
                }
            } else {
                key = watch.startChild("actuator");
                try (DarkClassifyActuator actuator = actuatorGenerator.generate(jobConfig)) {
                    watch.stopChild(key);
                    ClassificationStage stage3 = new ClassificationStage(metadataService, analysisService, metaExtendService, fileManagerService);
                    key = watch.startChild("stage3");
                    stage3.execute(darkContext, actuator, source, schemaIds);
                }
            }
            watch.stopChild(key);
        }

        jobLog.setStep(4);
        jobLog.info("采样分析阶段完成");
    }

    private DiscoveryContext buildDisContext(DataSourceConfig source, JobStartConfig param, JobLogManager jobLog) {
        return DiscoveryContext.builder()
            .sourceId(source.getSourceId())
            .planId(param.getPlanId())
            .jobId(param.getJobId())
            .mainSub(Boolean.TRUE.equals(param.getFormMerge()))
            .mainSimilarity(ObjUtil.defaultIfNull(param.getMainSimilarity(), 70))
            .subSimilarity(ObjUtil.defaultIfNull(param.getSubSimilarity(), 70))
            .ignoreCandidate(Boolean.TRUE.equals(param.getIgnoreCandidate()))
            .jobLogManager(jobLog)
            .build();
    }

    private DarkContext buildDarkContext(DataSourceConfig source, JobStartConfig param, JobLogManager jobLog) {
        DarkContext context = new DarkContext(param.getPlanId(), param.getJobId(), param.getStdId(), ObjUtil.defaultIfNull(param.getLastJobId(), 0L), source.getSourceId());
        AdditionalInfo info = new AdditionalInfo();
        info.setDbType(source.getType());
        context.setSampleLine(ObjUtil.defaultIfNull(param.getSampleLine(), 50))
            .setSampleRate(ObjUtil.defaultIfNull(param.getSampleRate(), 100))
            .setSampleSize(context.getSampleLine() * context.getSampleRate() / 100)
            .setIgnoreData(Boolean.TRUE.equals(param.getIgnoreData()))
            .setSaveSample(!context.isIgnoreData() && !Boolean.TRUE.equals(param.getIgnoreSample()))
            .setIgnoreCandidate(Boolean.TRUE.equals(param.getIgnoreCandidate()))
            .setTableStatus(!Boolean.FALSE.equals(param.getSaveTableStatus()))
            .setUseLLM(Boolean.TRUE.equals(param.getLlm()) && StrUtil.isNotEmpty(param.getLlmUrl()))
            .setIncrement(!Boolean.TRUE.equals(param.getFull()))
            .setOverlaySample(properties.getSys().isOverlaySampling())
            .setColumnNum(ObjUtil.defaultIfNull(param.getTableColumnNum(), properties.getSys().getTableColumnNum()))
            .setMaxColumnLength(properties.getSys().getMaxColumnLength())
            .setDefaultClassifyId(param.getDefaultClassifyId())
            .setDefaultLevel(param.getDefaultLevel())
            .setAddInfo(info)
            .setJobLogManager(jobLog)
            .setStopWatch(jobLog.getStopWatch());
        return context;
    }

    private List<SchemaTables> jobScope(DataSourceLimitParam source) {
        if (StrUtil.isEmpty(source.getTables())) {
            if (null == source.getSchemaSet()) {
                return Collections.emptyList();
            }
            return source.getSchemaSet().stream().map(v -> SchemaTables.of(CatalogSchema.of(v))).collect(Collectors.toList());
        }
        List<TableVO> list = JSONUtil.toList(source.getTables(), TableVO.class);
        return list.stream().collect(Collectors.groupingBy(TableVO::getSchema, Collectors.mapping(TableVO::getTableName, Collectors.toSet())))
            .entrySet()
            .stream().map(v -> SchemaTables.of(CatalogSchema.of(v.getKey()), new ArrayList<>(v.getValue()))).collect(Collectors.toList());
    }

    private List<SchemaTables> scopeStage1(Long sourceId, List<SchemaTables> list, String schema) {
        Stream<SchemaTables> stream = list.stream();
        if (CollUtil.isEmpty(list)) {
            List<SchemaScopeDTO> scope = metadataService.getSchemaTableScope(sourceId, null, true);
            stream = scope.stream().map(SchemaScopeDTO::getSchema).map(v -> SchemaTables.of(CatalogSchema.of(v.getName())));
        } else {
            stream = stream.map(v -> null != v.getTables() && v.getTables().size() > PipelineConst.SCOPE_TABLE_SIZE ? SchemaTables.of(v.getSchema()) : v);
        }
        if (null != schema) {
            stream = stream.filter(v -> v.getSchema().getName().compareTo(schema) >= 0);
        }
        return stream.sorted(Comparator.comparing(o -> o.getSchema().getName())).collect(Collectors.toList());
    }

    private List<SchemaScopeDTO> scopeStage2(List<SchemaScopeDTO> list, Long schemaId) {
        Stream<SchemaScopeDTO> stream = list.stream();
        if (null != schemaId) {
            stream = stream.filter(v -> v.getSchema().getOid() >= schemaId);
        }
        return stream.sorted(Comparator.comparing(o -> o.getSchema().getOid())).collect(Collectors.toList());
    }
}
