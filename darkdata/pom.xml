<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.mchz.nyx</groupId>
    <artifactId>nyx</artifactId>
    <version>1.0.0-SNAPSHOT</version>
  </parent>

  <artifactId>darkdata</artifactId>

  <dependencies>
    <dependency>
      <groupId>com.mchz.nyx</groupId>
      <artifactId>common</artifactId>
    </dependency>

    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-api</artifactId>
      <scope>test</scope>
    </dependency>

    <dependency>
      <groupId>org.mvel</groupId>
      <artifactId>mvel2</artifactId>
      <version>${mvel2.version}</version>
    </dependency>

    <dependency>
      <groupId>com.gliwka.hyperscan</groupId>
      <artifactId>hyperscan</artifactId>
      <version>${hyperscan.version}</version>
    </dependency>

    <dependency>
      <groupId>com.stepstone.search.hnswlib.jna</groupId>
      <artifactId>hnswlib-jna</artifactId>
      <version>${hnswlib.version}</version>
    </dependency>

    <dependency>
      <groupId>com.microsoft.onnxruntime</groupId>
      <artifactId>onnxruntime</artifactId>
      <version>1.14.0</version>
      <optional>true</optional>
    </dependency>

    <dependency>
      <groupId>org.roaringbitmap</groupId>
      <artifactId>RoaringBitmap</artifactId>
      <version>0.9.39</version>
    </dependency>

    <dependency>
      <groupId>com.hankcs</groupId>
      <artifactId>hanlp</artifactId>
      <version>${hanlp.version}</version>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
    </dependency>
  </dependencies>
</project>
