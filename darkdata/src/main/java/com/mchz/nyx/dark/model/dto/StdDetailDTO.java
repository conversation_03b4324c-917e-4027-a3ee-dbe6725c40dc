package com.mchz.nyx.dark.model.dto;

import com.mchz.nyx.dark.model.ClassifyInfo;
import com.mchz.nyx.dark.model.TargetInfo;
import com.mchz.nyx.dark.model.definition.RuleDetail;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 完整的规则集合
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2023/4/7 14:55
 */
@Data
public class StdDetailDTO {
    private Map<Long, TargetInfo> targets;
    private List<TargetWithTagGroup> tagGroups;
    private List<RuleDetail> ruleDetails;
    private List<TermVendorDTO> vendor;
    private List<? extends ClassifyInfo> classify;
}
