package com.mchz.nyx.dark.common.enums;

import cn.hutool.core.util.CharUtil;
import com.mchz.nyx.common.util.CommUtil;
import lombok.AllArgsConstructor;

import java.util.function.Predicate;

/**
 * <AUTHOR>
 */
@AllArgsConstructor
public enum CharType {
    /**
     * 0x30~0x39
     */
    NUM(0b1, Character::isDigit),
    /**
     * 0x61~0x7a
     */
    LOW_ENG(0b10, CharUtil::isLetterLower),
    /**
     * 0x41~0x5a
     */
    UPP_ENG(0b100, CharUtil::isLetterUpper),
    CHS(0b10000, CommUtil::isChineseChar),
    OTHER(0b100000, null),
    ENG(0b110, null);

    private final int value;
    private final Predicate<Character> range;

    public boolean isType(int v) {
        return (v & value) > 0;
    }

    public boolean isType(CharType type) {
        return null != type && (equals(type) || isType(type.value));
    }

    public boolean isOnlyType(int v) {
        return (v & ~value) == 0;
    }

    public static CharType of(char c) {
        CharType[] values = values();
        for (int i = 0; i < OTHER.ordinal(); i++) {
            CharType type = values[i];
            if (type.range.test(c)) {
                return type;
            }
        }
        return OTHER;
    }

    public static int ofValue(char c) {
        return of(c).value;
    }
}
