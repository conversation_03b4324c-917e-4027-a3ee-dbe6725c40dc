package com.mchz.nyx.dark.model.rule;

import com.mchz.nyx.dark.model.EngineContext;
import com.mchz.nyx.dark.model.meta.AdditionalInfo;
import com.mchz.nyx.dark.model.meta.MetaColumnData;
import com.mchz.nyx.dark.model.meta.NyxMetaColumn;
import com.mchz.nyx.dark.model.meta.NyxMetaTable;
import lombok.RequiredArgsConstructor;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/14
 */
@RequiredArgsConstructor
public class TablePayloadPoxy implements TablePayload {
    private final EngineContext context;
    private List<NyxMetaColumn> list;

    @Override
    public AdditionalInfo getInfo() {
        return context.getInfo();
    }

    @Override
    public NyxMetaTable getTable() {
        return context.getTable();
    }

    @Override
    public List<NyxMetaColumn> getColumns() {
        if (null == list) {
            list = context.getColumns().stream().map(MetaColumnData::getMeta).collect(Collectors.toList());
        }
        return list;
    }
}
