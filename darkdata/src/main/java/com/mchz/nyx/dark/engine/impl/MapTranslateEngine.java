package com.mchz.nyx.dark.engine.impl;

import cn.hutool.cache.Cache;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.mchz.nyx.common.util.CommUtil;
import com.mchz.nyx.common.util.WeakLFUCache;
import com.mchz.nyx.dark.common.constants.DarkConst;
import com.mchz.nyx.dark.common.enums.CharType;
import com.mchz.nyx.dark.engine.TranslateEngine;
import com.mchz.nyx.dark.engine.algorithm.MinNumSplit;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2022/9/9 11:49
 */
public class MapTranslateEngine implements TranslateEngine {
    private final MinNumSplit match;
    private final Cache<String, String> cache;

    public MapTranslateEngine(MinNumSplit match) {
        this.match = match;
        this.cache = new WeakLFUCache<>(DarkConst.TRANS_CACHE_CAPACITY, DarkConst.CACHE_TIMEOUT);
    }

    @Override
    public String translate(String name) {
        return cache.get(name, () -> execute(name));
    }

    protected String execute(String name) {
        List<String> list = split(name);
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        StringBuilder sb = new StringBuilder();
        for (String v : list) {
            if (NumberUtil.isInteger(v)) {
                if (0 == sb.length()) {
                    return null;
                }
                sb.append(v);
                continue;
            }
            String res = match.get(v);
            if (null != res) {
                sb.append(res);
            } else {
                List<String> all = match.split(v);
                if (null == all) {
                    if (CommUtil.isChineseChar(v.charAt(0))) {
                        sb.append(v);
                        continue;
                    }
                    return null;
                }
                all.forEach(sb::append);
            }
        }
        return sb.toString();
    }

    private List<String> split(CharSequence str) {
        if (StrUtil.isEmpty(str)) {
            return null;
        }
        int length = str.length();
        StringBuilder sb = new StringBuilder();
        List<String> list = new ArrayList<>(8);
        CharType before = CharType.OTHER, now;
        for (int i = 0; i < length; i++) {
            char cp = str.charAt(i);
            now = CharType.of(cp);
            if (CharType.OTHER.equals(now)) {
                before = CharType.OTHER;
                continue;
            }
            if (CharType.UPP_ENG.equals(now)) {
                int next = i + 1;
                if (next < str.length() && Character.isLowerCase(str.charAt(next))) {
                    if (CharType.LOW_ENG.equals(before)) {
                        before = CharType.UPP_ENG;
                    }
                    now = CharType.LOW_ENG;
                }
                cp = Character.toLowerCase(cp);
            }
            if (before != now) {
                if (sb.length() > 0) {
                    list.add(sb.toString());
                    sb.delete(0, sb.length());
                }
                before = now;
            }
            sb.append(cp);
        }
        if (sb.length() > 0) {
            list.add(sb.toString());
        }
        return list;
    }

    @Override
    public void close() {
    }
}
