package com.mchz.nyx.dark.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.TypeReference;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.mchz.nyx.dark.common.enums.ParamColumn;
import com.mchz.nyx.dark.common.enums.ParamTable;
import com.mchz.nyx.dark.common.enums.ReturnType;
import com.mchz.nyx.dark.common.enums.RuleLoad;
import com.mchz.nyx.dark.model.definition.RuleDetail;
import com.mchz.nyx.dark.model.definition.SingleRuleDetail;
import com.mchz.nyx.dark.model.definition.StrSegmentRule;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.util.EnumMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/9/25
 */
@Slf4j
@UtilityClass
public class RuleLoadUtil {

    public RuleDetail loadRule(Integer v, String detail, Double hit) {
        RuleDetail ruleDetail = new RuleDetail();
        if (null == v) {
            v = 1;
        }
        try {
            switch (v) {
                case 1:
                    ruleDetail.setLoad(RuleLoad.SINGLE);
                    processSingleRuleDetail(detail, hit, ruleDetail);
                    break;
                case 5:
                    ruleDetail.setLoad(RuleLoad.SINGLE_STR);
                    processSingleRuleDetail(detail, hit, ruleDetail);
                    break;
                case 7:
                    ruleDetail.setLoad(RuleLoad.TAG_SINGLE);
                    processSingleRuleDetail(detail, hit, ruleDetail);
                    break;
                case 2:
                    ruleDetail.setLoad(RuleLoad.MULTIPLE);
                    processMultipleRuleDetails(detail, hit, ruleDetail);
                    break;
                case 6:
                    ruleDetail.setLoad(RuleLoad.MULTIPLE_STR);
                    processMultipleRuleDetails(detail, hit, ruleDetail);
                    break;
                case 8:
                    ruleDetail.setLoad(RuleLoad.TAG_MULTIPLE);
                    processMultipleRuleDetails(detail, hit, ruleDetail);
                    break;
                case 3:
                    ruleDetail.setLoad(RuleLoad.PARAM_C);
                    JSONObject columns = JSONUtil.parseObj(detail);
                    Map<ParamColumn, Object> column = new EnumMap<>(ParamColumn.class);
                    for (Map.Entry<String, Object> entry : columns.entrySet()) {
                        ParamColumn paramColumn = ParamColumn.valueOf(entry.getKey());
                        column.put(paramColumn, transform(paramColumn.getType(), entry.getValue()));
                    }
                    ruleDetail.setParamColumnRule(column);
                    break;
                case 4:
                    ruleDetail.setLoad(RuleLoad.PARAM_T);
                    JSONObject tables = JSONUtil.parseObj(detail);
                    Map<ParamTable, Object> table = new EnumMap<>(ParamTable.class);
                    for (Map.Entry<String, Object> entry : tables.entrySet()) {
                        ParamTable paramColumn = ParamTable.valueOf(entry.getKey());
                        table.put(paramColumn, transform(paramColumn.getType(), entry.getValue()));
                    }
                    ruleDetail.setParamTableRule(table);
                    break;
                default:
            }
        } catch (Exception e) {
            log.debug("规则加载失败【{}】:{}", detail, e.getMessage());
        }
        return ruleDetail;
    }

    private void processSingleRuleDetail(String detail, Double hit, RuleDetail ruleDetail) {
        SingleRuleDetail rule = JSONUtil.toBean(detail, SingleRuleDetail.class);
        if (null == rule.getHit()) {
            rule.setHit(hit);
        }
        ruleDetail.setRule(rule);
    }

    private void processMultipleRuleDetails(String detail, Double hit, RuleDetail ruleDetail) {
        List<SingleRuleDetail> rules = JSONUtil.toBean(detail, new TypeReference<List<SingleRuleDetail>>() {}, true);
        if (null != hit) {
            rules.forEach(r -> {
                if (null == r.getHit()) {
                    r.setHit(hit);
                }
            });
        }
        ruleDetail.setRules(rules);
    }

    private Object transform(ReturnType type, Object value) {
        switch (type) {
            case INTEGER:
            case ENUM:
                return value.toString();
            case STRING:
                return BeanUtil.toBean(value, StrSegmentRule.class);
            case BOOL:
                if (value instanceof Boolean) {
                    return value;
                }
                return BooleanUtil.toBoolean(value.toString());
            case ENUM_ARRAY:
                if (value instanceof JSONArray) {
                    return ((JSONArray) value).toList(String.class);
                }
                if (value instanceof String) {
                    return value;
                }
                return null;
            default:
                return null;
        }
    }
}
