package com.mchz.nyx.dark.engine.impl;

import com.mchz.nyx.common.exception.InterruptedJobException;
import com.mchz.nyx.dark.engine.RulesEngine;
import com.mchz.nyx.dark.engine.rule.Rule;
import com.mchz.nyx.dark.model.rule.Facts;
import com.mchz.nyx.dark.model.rule.Rules;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2021/12/24 16:00
 */
@Slf4j
public final class McRulesEngine implements RulesEngine {

    @Override
    public void fire(Rules rules, Facts facts) {
        if (rules.isEmpty()) {
            log.debug("No rules registered! Nothing to apply");
            return;
        }
        for (Rule rule : rules) {
            final String name = rule.getName();
            if (shouldBeBreak(rule, facts)) {
                log.debug("Skip all subsequent rules");
                break;
            }
            boolean evaluationResult = false;
            try {
                evaluationResult = rule.evaluate(facts);
            } catch (InterruptedJobException exception) {
                throw exception;
            } catch (RuntimeException exception) {
                log.error("Rule '{}' evaluated with error", name, exception);
            }
            if (evaluationResult) {
                log.debug("Rule '{}' triggered", name);
                try {
                    rule.execute(facts);
                    log.debug("Rule '{}' performed successfully", name);
                } catch (InterruptedJobException exception) {
                    throw exception;
                } catch (Exception exception) {
                    log.error("Rule '{}' performed with error", name, exception);
                }
            } else {
                log.debug("Rule '{}' has been evaluated to false, it has not been executed", name);
            }
        }
    }

    private boolean shouldBeBreak(Rule rule, Facts facts) {
        return facts.getResult().size() >= facts.getLimit();
    }
}
