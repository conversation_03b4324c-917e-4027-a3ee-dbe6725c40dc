package com.mchz.nyx.dark.util.nlp.tokenizer;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.CharUtil;
import lombok.AllArgsConstructor;

import java.text.Normalizer;
import java.util.*;
import java.util.stream.Collectors;

@SuppressWarnings("all")
@AllArgsConstructor
public class BasicTokenizer implements Tokenizer {
    private final Set<String> neverSplit;
    private final boolean doLowerCase;
    private final boolean tokenizeChineseChars;
    private final Boolean stripAccents;

    public BasicTokenizer(boolean doLowerCase, Boolean stripAccents) {
        this.doLowerCase = doLowerCase;
        this.neverSplit = new HashSet<>();
        this.tokenizeChineseChars = true;
        this.stripAccents = stripAccents;
    }

    @Override
    public List<String> tokenize(String text) {
        return tokenize(text, null, null);
    }

    public List<String> tokenize(String text, Set<String> token_dict) {
        return tokenize(text, null, token_dict);
    }

    public List<String> tokenize(String text, Set<String> neverSplit, Set<String> tokenDict) {
        if (null == neverSplit || 0 == neverSplit.size()) {
            neverSplit = this.neverSplit;
        } else {
            Set<String> tmp = new HashSet<>(this.neverSplit);
            tmp.addAll(neverSplit);
            neverSplit = tmp;
        }
        Set<Integer> td;
        if (CollUtil.isNotEmpty(tokenDict) && tokenizeChineseChars) {
            td = tokenDict.stream().filter(v -> {
                return 1 == v.length() || 2 == v.length() && 1 == v.codePointCount(0, 2);
            }).map(v -> v.codePointAt(0)).collect(Collectors.toSet());
        } else {
            td = Collections.emptySet();
        }
        // 清洗文本
        // 1. 去除一些特殊字符
        // 2. 将" ", \t, \n, 和 \r统一替换为" "
        // 3. 根据空格分词
        List<String> origTokens = whitespaceTokenize(text, td);
        List<String> outputTokens = new ArrayList<>(origTokens.size());
        for (String token : origTokens) {
            if (neverSplit.contains(token)) {
                outputTokens.add(token);
            }
            if (doLowerCase) {
                token = token.toLowerCase(Locale.ROOT);
                if (!Boolean.FALSE.equals(stripAccents)) {
                    token = runStripAccents(token);
                }
            }
            if (Boolean.TRUE.equals(stripAccents)) {
                token = runStripAccents(token);
            }
            outputTokens.addAll(runSplitOnPunctuation(token));
        }
        return outputTokens;
    }

    private List<String> whitespaceTokenize(String text, Set<Integer> tokenDict) {
        List<String> res = new ArrayList<>();
        StringBuilder sb = new StringBuilder();
        text.codePoints().forEach(c -> {
            if (CharUtil.isBlankChar(c)) {
                if (sb.length() > 0) {
                    res.add(sb.toString());
                    sb.delete(0, sb.length());
                }
                return;
            }
            if (tokenizeChineseChars) {
                boolean flag = isChineseChar(c) || !Character.isAlphabetic(c) && !tokenDict.contains(c);
                if (flag) {
                    if (sb.length() > 0) {
                        res.add(sb.toString());
                        sb.delete(0, sb.length());
                    }
                    res.add(new String(new int[]{c}, 0, 1));
                    return;
                }
            }
            sb.appendCodePoint(c);
        });
        if (sb.length() > 0) {
            res.add(sb.toString());
        }
        return res;
    }

    /**
     * _run_strip_accents
     */
    public String runStripAccents(String text) {
        text = Normalizer.normalize(text, Normalizer.Form.NFD);
        StringBuilder sb = new StringBuilder();
        text.codePoints().forEach(c -> {
            int cat = Character.getType(c);
            if (Character.NON_SPACING_MARK == cat) {
                return;
            }
            sb.appendCodePoint(c);
        });
        return sb.toString();
    }

    /**
     * _run_split_on_punc
     */
    private List<String> runSplitOnPunctuation(String text) {
        if (1 == text.length()) {
            return Collections.singletonList(text);
        }
        List<String> output = new ArrayList<>(4);
        StringBuilder sb = new StringBuilder();
        text.codePoints().forEach(c -> {
            if (isPunctuation(c)) {
                if (sb.length() > 0) {
                    output.add(sb.toString());
                    sb.delete(0, sb.length());
                }
                output.add(new String(new int[]{c}, 0, 1));
            }
            sb.appendCodePoint(c);
        });
        if (sb.length() > 0) {
            output.add(sb.toString());
        }
        return output;
    }

    /**
     * This defines a "chinese character" as anything in the CJK Unicode block:
     * https://en.wikipedia.org/wiki/CJK_Unified_Ideographs_(Unicode_block)
     * Note that the CJK Unicode block is NOT all Japanese and Korean characters,
     * despite its name. The modern Korean Hangul alphabet is a different block,
     * as is Japanese Hiragana and Katakana. Those alphabets are used to write
     * space-separated words, so they are not treated specially and handled
     * like the all of the other languages.
     *
     * @param cp codepoint
     * @return true is chinese char
     */
    private boolean isChineseChar(int cp) {
        // Checks whether CP is the codepoint of a CJK character.
        return (cp >= 0x4E00 && cp <= 0x9FFF) || (cp >= 0x3400 && cp <= 0x4DBF)
            || (cp >= 0x20000 && cp <= 0x2A6DF) || (cp >= 0x2A700 && cp <= 0x2B73F)
            || (cp >= 0x2B740 && cp <= 0x2B81F) || (cp >= 0x2B820 && cp <= 0x2CEAF)
            || (cp >= 0xF900 && cp <= 0xFAFF) || (cp >= 0x2F800 && cp <= 0x2FA1F);
    }

    /**
     * Checks whether `chars` is a punctuation character.
     * _is_punctuation
     */
    private boolean isPunctuation(int cp) {

        // We treat all non-letter/number ASCII as punctuation.
        // Characters such as "^", "$", and "`" are not in the Unicode
        // Punctuation class but we treat them as punctuation anyways, for
        // consistency.
        if ((cp >= 33 && cp <= 47)
            || (cp >= 58 && cp <= 64)
            || (cp >= 91 && cp <= 96)
            || (cp >= 123 && cp <= 126)) {
            return true;
        }
        int cat = Character.getType(cp);
        switch (cat) {
            case Character.CONNECTOR_PUNCTUATION:
            case Character.DASH_PUNCTUATION:
            case Character.START_PUNCTUATION:
            case Character.END_PUNCTUATION:
            case Character.INITIAL_QUOTE_PUNCTUATION:
            case Character.FINAL_QUOTE_PUNCTUATION:
            case Character.OTHER_PUNCTUATION:
                return true;
            default:
                return false;
        }
    }

}
