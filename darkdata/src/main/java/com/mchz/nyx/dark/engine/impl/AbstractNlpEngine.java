package com.mchz.nyx.dark.engine.impl;

import cn.hutool.cache.Cache;
import cn.hutool.cache.CacheUtil;
import com.mchz.nyx.dark.common.constants.DarkConst;
import com.mchz.nyx.dark.common.enums.HitType;
import com.mchz.nyx.dark.engine.NlpEngine;
import com.mchz.nyx.dark.model.EngineContext;
import com.mchz.nyx.dark.model.TargetInfo;
import com.mchz.nyx.dark.model.TargetResult;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/1/24
 */
public abstract class AbstractNlpEngine implements NlpEngine {
    private final NlpMappingEngine mappingEngine;
    private final Cache<String, List<TargetResult>> cache;

    public AbstractNlpEngine(Map<String, List<TargetInfo>> data) {
        this.mappingEngine = new NlpMappingEngine(data);
        this.cache = CacheUtil.newLFUCache(DarkConst.JOB_SIMILAR_CACHE_CAPACITY);
    }

    @Override
    public List<TargetResult> similarityAnalysis(EngineContext context, String str, HitType hitType, float baseline) {
        List<TargetResult> infos = mappingEngine.similarityAnalysis(context, str, hitType, baseline);
        if (null != infos) {
            return infos;
        }
        if (str.length() <= 2) {
            return null;
        }
        List<TargetResult> res = cache.get(str, () -> similarity(str, baseline));
        if (null == res) {
            return null;
        }
        return res.stream().map(v -> new TargetResult(hitType, v.getInfo(), v.getScore())).collect(Collectors.toList());
    }

    protected abstract List<TargetResult> similarity(String str, float baseline);

    protected List<TargetInfo> match(String str) {
        return mappingEngine.match(str);
    }

    @Override
    public void close() {
        cache.clear();
    }
}
