package com.mchz.nyx.dark.util;

import cn.hutool.core.text.CharPool;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.CharUtil;
import cn.hutool.core.util.StrUtil;
import com.mchz.nyx.common.util.CommUtil;
import lombok.experimental.UtilityClass;

import java.util.Objects;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/8/9
 */
@UtilityClass
public class NlpUtil {
    private final char[][] PAIRS = {{'(', ')', '）'}, {'（', ')', '）'}, {'"', '"'}};
    private final char[] W_TRIM = {'"', '%', '(', '（', '）', ')', '?'};

    public float squaredCosineSimilarity(float[] vector1, float[] vector2) {
        Objects.requireNonNull(vector1, "向量1不能为null");
        Objects.requireNonNull(vector2, "向量2不能为null");

        int length1 = vector1.length;
        int length2 = vector2.length;
        if (length1 != length2) {
            throw new IllegalArgumentException(String.format("向量坐标数不相等 %s, %s", length1, length2));
        }

        float molecular = 0f;
        float sourceDenominator = 0f;
        float targetDenominator = 0f;
        for (int i = 0; i < length1; i++) {
            molecular += vector1[i] * vector2[i];
            sourceDenominator += vector1[i] * vector1[i];
            targetDenominator += vector2[i] * vector2[i];
        }

        float denominator = sourceDenominator * targetDenominator;

        return molecular * molecular / denominator;
    }

    public String dealComment(String str) {
        if (StrUtil.isBlank(str) || 1 == (str.length() & 1) && str.endsWith("??") || StrUtil.containsAny(str, "锟斤拷", "烫烫烫", "屯屯屯", "锘锘锘", "□", "�", "???")) {
            return null;
        }
        int start = 0, end = str.length();
        while (start < end && test(str.charAt(end - 1))) {
            end--;
        }
        while (start < end) {
            char l = str.charAt(start);
            char[] pair = null;
            for (char[] p : PAIRS) {
                if (p[0] == l) {
                    pair = p;
                    break;
                }
            }
            if (null != pair) {
                if (contains(pair, 1, str.charAt(end - 1))) {
                    start++;
                    do {
                        end--;
                    } while (start < end && test(str.charAt(end - 1)));
                    continue;
                }
            } else if (test(l)) {
                start++;
                continue;
            }
            break;
        }

        return start > 0 || end < str.length() ? str.substring(start, end) : str;
    }

    public String dealCommentCut(String str) {
        if (str.length() < 6) {
            return str;
        }
        char[] chars = str.toCharArray();
        int checkLen = chars.length - 2;
        int i = 2;
        int[] colonArray = new int[2];
        int colonSize = 0;
        for (; i < checkLen; i++) {
            char c = chars[i];
            if ('0' == c) {
                if (chars.length <= i + 2) {
                    return str;
                }
                if ('1' == chars[i + 1] && !Character.isDigit(chars[i + 2])) {
                    i++;
                }
                break;
            }
            if ('1' == c) {
                break;
            }
            if ('(' == c && CharPool.DASHED == chars[i + 1] && Character.isDigit(chars[i + 2])) {
                return str.substring(0, i);
            }
            if (CharPool.DASHED == c && CharPool.DASHED == chars[i + 1]) {
                return str.substring(0, i);
            }
            if (CharPool.COLON == c) {
                colonArray[colonSize++] = i;
                if (colonSize >= colonArray.length) {
                    break;
                }
            }
        }
        if (colonSize == colonArray.length) {
            return getResultStr(str, chars, findStart(chars, colonArray));
        } else if (i < checkLen && checkNext(chars, i)) {
            return getResultStr(str, chars, i);
        }
        return str;
    }

    private String getResultStr(String str, char[] chars, int i) {
        for (int j = i - 1; j > 0; j--) {
            if (CommUtil.isChineseChar(chars[j]) && !CommUtil.isPunctuation(chars[j - 1]) || ')' == chars[j] || '）' == chars[j]) {
                char[] ignore = {' ', '.', ';', '；', '。'};
                for (int k = j; k > 0; k--) {
                    if (ArrayUtil.contains(ignore, chars[k])) {
                        return str;
                    }
                }
                return new String(chars, 0, j + 1);
            }
        }
        return str;
    }

    private int findStart(char[] chars, int[] colonArray) {
        for (int j = 1; j < colonArray.length; j++) {
            if (findTagStart(chars, colonArray[j - 1], colonArray[j]) < 0) {
                return -1;
            }
        }
        return findTagStart(chars, 2, colonArray[0]);
    }

    private int findTagStart(char[] chars, int l, int r) {
        for (int i = r - 1; i > l; i--) {
            char c = chars[i];
            if (!CharUtil.isLetter(c) && CharPool.UNDERLINE != c) {
                return i;
            }
        }
        return -1;
    }

    private boolean checkNext(char[] chars, int index) {
        int startIndex = index + 2;
        char now = chars[index];
        boolean res = containsNextChar(chars, startIndex, (char) (now + 1));
        if (res) {
            return true;
        }
        if ('1' == now && !Character.isDigit(chars[index - 1]) && !Character.isDigit(chars[index + 1])) {
            return containsNextChar(chars, startIndex, '0');
        }
        return false;
    }

    private boolean containsNextChar(char[] chars, int startIndex, char target) {
        for (int j = startIndex; j < chars.length; j++) {
            if (chars[j] == target) {
                return true;
            }
        }
        return false;
    }

    private boolean test(char c) {
        return CharUtil.isBlankChar(c) || (CommUtil.isPunctuation(c) && !contains(W_TRIM, 0, c));
    }

    private boolean contains(char[] array, int start, char c) {
        for (int i = start; i < array.length; i++) {
            if (c == array[i]) {
                return true;
            }
        }
        return false;
    }
}
