package com.mchz.nyx.dark.engine.impl;

import cn.hutool.core.text.CharPool;
import cn.hutool.core.util.CharUtil;
import com.mchz.nyx.common.util.CommUtil;
import com.mchz.nyx.dark.common.enums.HitType;
import com.mchz.nyx.dark.engine.NlpEngine;
import com.mchz.nyx.dark.engine.algorithm.TrieMatch;
import com.mchz.nyx.dark.model.EngineContext;
import com.mchz.nyx.dark.model.TargetInfo;
import com.mchz.nyx.dark.model.TargetResult;
import lombok.AccessLevel;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/1/24
 */
public class NlpMappingEngine implements NlpEngine {
    @Getter(AccessLevel.PACKAGE)
    private final TrieFormatCutOffMatch<TargetInfo> data;

    public NlpMappingEngine(Map<String, List<TargetInfo>> data) {
        this.data = new TrieFormatCutOffMatch<>(data);
    }

    @Override
    public List<TargetResult> similarityAnalysis(EngineContext context, String str, HitType hitType, float baseline) {
        List<TargetInfo> infos = data.find(str);
        if (infos.isEmpty()) {
            return null;
        }
        return infos.stream().map(v -> new TargetResult(hitType, v, v.getName().length() == str.length() ? 1 : 0.9)).collect(Collectors.toList());
    }

    protected List<TargetInfo> match(String str) {
        return data.match(str);
    }

    private static class TrieFormatCutOffMatch<T> extends TrieMatch<T> {
        public TrieFormatCutOffMatch(Map<String, List<T>> keywords) {
            super(keywords, false, false);
        }

        @Override
        public List<T> find(String text) {
            return match(text.toCharArray(), 0, true);
        }

        @Override
        public List<T> match(String text) {
            return match(text.toCharArray(), 0, false);
        }

        private List<T> match(char[] chars, int start, boolean tryCut) {
            TrieNode<T> now = getRoot();
            int index = 0;
            TrieNode<T> t = null;
            boolean empty = true;
            for (int i = start; i < chars.length; i++) {
                char ch = styleCaseTolerant(chars[i]);
                if (0 == ch) {
                    empty = false;
                    continue;
                }
                if (CharPool.SPACE == ch) {
                    if (empty) {
                        continue;
                    }
                    empty = true;
                } else {
                    empty = false;
                }
                TrieNode<T> temp = now.getChildByName(ch);
                if (null == temp) {
                    break;
                }
                now = temp;
                index = i;
                t = now;
            }
            if (null == t) {
                if (tryCut) {
                    return tryCut(chars);
                }
                return Collections.emptyList();
            }
            int next = index + 1;
            if (next < chars.length) {
                char ch = chars[index];
                char nCh = chars[next];
                if (CharUtil.isLetter(nCh) && CharUtil.isLetter(ch) || CommUtil.isChineseChar(nCh) && CommUtil.isChineseChar(ch)) {
                    return Collections.emptyList();
                }
                int offset;
                if ((CharUtil.UNDERLINE == nCh || CharUtil.DASHED == nCh) && chars.length > (offset = next + 2)) {
                    List<T> match = match(chars, offset, false);
                    if (!match.isEmpty() || CharUtil.DASHED != nCh) {
                        return match;
                    }
                }
            }
            return new ArrayList<>(t.getResults());
        }

        private List<T> tryCut(char[] chars) {
            if (chars.length > 25 || chars.length < 5) {
                return Collections.emptyList();
            }
            int i = 2;
            for (; i < chars.length; i++) {
                char c = chars[i];
                if (CharUtil.UNDERLINE == c || CharUtil.DASHED == c) {
                    break;
                }
            }
            char c = chars[i - 1];
            if (CommUtil.isChineseChar(c) || '）' == c || ')' == c) {
                return match(chars, i + 1, false);
            }
            return Collections.emptyList();
        }

        @Override
        protected TrieNode<T> buildNode(char ch, TrieNode<T> now, boolean set) {
            ch = styleCaseTolerant(ch);
            if (0 == ch || CharPool.SPACE == ch && CharPool.SPACE == now.getName()) {
                return now;
            }
            return super.buildNode(ch, now, set);
        }

        private char styleCaseTolerant(char ch) {
            if (CharUtil.isLetterUpper(ch)) {
                return (char) (ch | 32);
            }
            switch (ch) {
                case ' ':
                case '\\':
                case '？':
                case '?':
                case '(':
                case ')':
                case '（':
                case '）':
                case '"':
                case '#':
                case '*':
                    return 0;
                case '_':
                case '-':
                case ',':
                case '，':
                case '/':
                case '、':
                case '\n':
                case '\r':
                case '\t':
                    return CharPool.SPACE;
                case '“':
                case '”':
                    return CharPool.DOUBLE_QUOTES;
                case '【':
                    return CharPool.BRACKET_START;
                case '】':
                    return CharPool.BRACKET_END;
                case '：':
                    return CharPool.COLON;
                case '。':
                    return CharPool.DOT;
                default:
                    return ch;
            }
        }
    }
}
