package com.mchz.nyx.dark.model.rule;

import com.mchz.nyx.dark.engine.rule.Rule;

import java.util.*;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2023/3/31 15:24
 */
public class Rules implements Iterable<Rule> {

    private final Set<Rule> rules;

    public Rules() {
        this.rules = new TreeSet<>();
    }

    /**
     * Create a new {@link Rules} object.
     *
     * @param rules to register
     */
    public Rules(Set<Rule> rules) {
        this.rules = new TreeSet<>(rules);
    }

    /**
     * Register one or more new rules.
     *
     * @param rules to register, must not be null
     */
    public void register(Rule... rules) {
        Objects.requireNonNull(rules);
        for (Rule rule : rules) {
            Objects.requireNonNull(rule);
            this.rules.add(rule);
        }
    }

    public void register(List<? extends Rule> rules) {
        Objects.requireNonNull(rules);
        for (Rule rule : rules) {
            Objects.requireNonNull(rule);
            this.rules.add(rule);
        }
    }

    /**
     * Unregister one or more rules.
     *
     * @param rules to unregister, must not be null
     */
    public void unregister(Rule... rules) {
        Objects.requireNonNull(rules);
        for (Rule rule : rules) {
            Objects.requireNonNull(rule);
            this.rules.remove(rule);
        }
    }

    /**
     * Unregister a rule by name.
     *
     * @param ruleName name of the rule to unregister, must not be null
     */
    public void unregister(final String ruleName) {
        Objects.requireNonNull(ruleName);
        Rule rule = findRuleByName(ruleName);
        if (rule != null) {
            unregister(rule);
        }
    }

    /**
     * Check if the rule set is empty.
     *
     * @return true if the rule set is empty, false otherwise
     */
    public boolean isEmpty() {
        return rules.isEmpty();
    }

    /**
     * Clear rules.
     */
    public void clear() {
        rules.clear();
    }

    /**
     * Return how many rules are currently registered.
     *
     * @return the number of rules currently registered
     */
    public int size() {
        return rules.size();
    }

    /**
     * Return an iterator on the rules set. It is not intended to remove rules
     * using this iterator.
     *
     * @return an iterator on the rules set
     */
    @Override
    public Iterator<Rule> iterator() {
        return rules.iterator();
    }

    private Rule findRuleByName(String ruleName) {
        return rules.stream()
            .filter(rule -> rule.getName().equalsIgnoreCase(ruleName))
            .findFirst()
            .orElse(null);
    }
}

