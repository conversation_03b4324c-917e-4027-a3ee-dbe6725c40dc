package com.mchz.nyx.dark.model.rule;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/11/26 14:22
 */
@Data
@AllArgsConstructor
public class RuleHitData {
    private RuleIdInfo info;
    private Double hitRate;
    private List<Object> hitData;

    public RuleHitData(RuleIdInfo info) {
        this.info = info;
    }
}
