package com.mchz.nyx.dark.retrieval;


import com.mchz.nyx.dark.common.enums.HitType;
import com.mchz.nyx.dark.model.EngineContext;
import com.mchz.nyx.dark.model.TargetInfo;
import com.mchz.nyx.dark.model.meta.MetaColumnData;
import lombok.AllArgsConstructor;

import java.util.List;
import java.util.Locale;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2023/4/4 17:42
 */
@AllArgsConstructor
public class MappingRecallStage implements RetrievalRecallStage {

    private final Map<String, Map<String, List<TargetInfo>>> map;
    private final Map<String, List<TargetInfo>> columnMap;

    @Override
    public void execute(EngineContext context) {
        if (null == context.getTable().getTableName()) {
            return;
        }
        Map<String, List<TargetInfo>> columns = map.get(context.getTable().getTableName().toUpperCase(Locale.ROOT));
        for (MetaColumnData column : context.getColumns()) {
            String name = column.getMeta().getColumnName().toUpperCase(Locale.ROOT);
            List<TargetInfo> ts = null;
            if (null != columns) {
                ts = columns.get(name);
            }
            if (null == ts) {
                ts = columnMap.get(name);
            }
            column.addAllInfo(HitType.MAPPING, ts);
        }
    }
}
