package com.mchz.nyx.dark.engine.rule;

import com.mchz.nyx.dark.engine.filter.BaseFilterCore;
import com.mchz.nyx.dark.engine.filter.DictPayload;
import com.mchz.nyx.dark.engine.filter.RegexPayload;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2021/12/20 17:26
 */
public class RuleManager implements AutoCloseable {
    private final BaseFilterCore<DictPayload> dictFilter;
    private final BaseFilterCore<RegexPayload> regexFilter;

    public RuleManager(BaseFilterCore<DictPayload> dictFilter, BaseFilterCore<RegexPayload> regexFilter) {
        this.dictFilter = dictFilter;
        this.regexFilter = regexFilter;
    }

    public boolean filter(DictPayload payload, String data) {
        String suffix = payload.getSuffix();
        if (null != suffix) {
            if (data.endsWith(suffix)) {
                data = data.substring(0, data.length() - suffix.length());
            } else {
                return false;
            }
        }
        return dictFilter.filter(payload, data);
    }

    public boolean filter(RegexPayload payload, String data) {
        return regexFilter.filter(payload, data);
    }

    @Override
    public void close() throws Exception {
        dictFilter.close();
        regexFilter.close();
    }

}
