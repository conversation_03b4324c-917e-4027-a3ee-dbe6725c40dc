package com.mchz.nyx.dark.engine.classifier;

import com.mchz.nyx.dark.engine.expression.ValueExpression;
import com.mchz.nyx.dark.engine.rule.RuleManager;
import lombok.AllArgsConstructor;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2023/5/25 13:54
 */
@AllArgsConstructor
public class IntegerClassifier implements BaseClassifier<Integer> {
    private final ValueExpression<Integer> expression;

    @Override
    public boolean classify(Integer payload, RuleManager manager) {
        return expression.compare(payload);
    }
}
