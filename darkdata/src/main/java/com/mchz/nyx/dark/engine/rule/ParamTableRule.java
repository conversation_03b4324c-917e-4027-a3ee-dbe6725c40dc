package com.mchz.nyx.dark.engine.rule;

import cn.hutool.core.util.ObjUtil;
import com.mchz.nyx.dark.common.constants.RuleConst;
import com.mchz.nyx.dark.engine.classifier.BaseClassifier;
import com.mchz.nyx.dark.engine.record.MessageLog;
import com.mchz.nyx.dark.engine.record.RecordLog;
import com.mchz.nyx.dark.model.rule.*;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2023/3/20 15:54
 */
public class ParamTableRule extends BaseRule<TablePayload> {

    private final RuleIdInfo info;
    private final BaseClassifier<TablePayload> classifier;

    public ParamTableRule(RuleParam config, BaseClassifier<TablePayload> classifier) {
        super(config.getName(), ObjUtil.defaultIfNull(config.getPriority(), RuleConst.DEFAULT_MIN_PRIORITY));
        this.info = config.getTarget();
        this.classifier = classifier;
    }

    @Override
    protected boolean when(TablePayload payload) {
        return null != payload.getInfo();
    }

    @Override
    protected RecordLog then(TablePayload payload, RuleResult result, RuleManager manager) {
        if (classifier.classify(payload, manager)) {
            result.add(info);
        }
        return new MessageLog(getName());
    }

    @Override
    protected TablePayload getPayload(Facts facts) {
        return facts.getTablePayload();
    }
}
