package com.mchz.nyx.dark.engine.classifier;

import com.mchz.nyx.dark.engine.rule.RuleManager;
import org.mvel2.MVEL;
import org.mvel2.ParserContext;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2023/4/4 10:25
 */
public class PreTreatClassifier implements BaseClassifier<String> {
    private final Serializable compiledExpression;
    private final Map<String, String> param;
    private final BaseClassifier<String> classifier;

    public PreTreatClassifier(String expression, BaseClassifier<String> classifier) {
        this(MVEL.compileExpression(expression), classifier);
    }

    public PreTreatClassifier(String expression, ParserContext parserContext, BaseClassifier<String> classifier) {
        this(MVEL.compileExpression(expression, parserContext), classifier);
    }

    private PreTreatClassifier(Serializable compiledExpression, BaseClassifier<String> classifier) {
        this.compiledExpression = compiledExpression;
        this.param = new HashMap<>();
        this.classifier = classifier;
    }

    @Override
    public boolean classify(String data, RuleManager manager) {
        param.put("v", data);
        return classifier.classify((String) MVEL.executeExpression(compiledExpression, param), manager);
    }
}
