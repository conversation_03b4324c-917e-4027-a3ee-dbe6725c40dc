package com.mchz.nyx.dark.engine.classifier;

import com.mchz.nyx.dark.engine.rule.RuleManager;
import lombok.Data;

import java.util.function.Function;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2023/3/31 16:06
 */
@Data
public class ParamEnumClassifier<T, P extends Function<T, Object>> implements BaseClassifier<T> {
    private final P param;
    private final Object condition;

    @Override
    public boolean classify(T payload, RuleManager manager) {
        Object value = param.apply(payload);
        if (null == value) {
            return false;
        }
        return condition.equals(value);
    }
}
