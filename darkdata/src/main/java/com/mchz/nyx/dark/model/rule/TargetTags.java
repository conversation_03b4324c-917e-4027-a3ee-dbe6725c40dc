package com.mchz.nyx.dark.model.rule;

import com.mchz.nyx.dark.model.TargetInfo;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Optional;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2023/5/18 13:08
 */
@AllArgsConstructor
public class TargetTags {
    private final TagBitMap t1;
    @Getter
    private final TargetInfo info;
    @Getter
    private final Integer score;

    public Optional<Integer> getIfUnique() {
        return Optional.ofNullable(t1.getIfUnique());
    }

    public boolean contains(TagBitMap sub){
        return sub.contains(t1);
    }
}
