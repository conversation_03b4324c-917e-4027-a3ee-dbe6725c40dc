package com.mchz.nyx.dark.engine.filter;

import cn.hutool.cache.Cache;
import cn.hutool.core.util.ReUtil;
import com.mchz.nyx.common.util.WeakLFUCache;
import org.roaringbitmap.RoaringBitmap;

import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/3
 */
public class RegexCommonFilterCore implements BaseFilterCore<RegexPayload> {
    private final Cache<String, ThreadSafeRoaringBitmap> cache;

    public RegexCommonFilterCore(int size) {
        this.cache = new WeakLFUCache<>(size, -1);
    }

    @Override
    public boolean filter(RegexPayload payload, String data) {
        ThreadSafeRoaringBitmap bitmap = cache.get(data, ThreadSafeRoaringBitmap::new);
        int k = payload.getId() << 1;
        if (bitmap.contains(k)) {
            return bitmap.contains(k + 1);
        }
        bitmap.lockWrite();
        try {
            if (!bitmap.contains(k)) {
                boolean result = ReUtil.contains(payload.getValue(), data);
                bitmap.add(k);
                if (result) {
                    bitmap.add(k + 1);
                }
                return result;
            }
            return bitmap.contains(k + 1);
        } finally {
            bitmap.unlockWrite();
        }
    }

    @Override
    public void close() throws Exception {
    }

    private static class ThreadSafeRoaringBitmap {
        private final RoaringBitmap bitmap = new RoaringBitmap();
        private final ReentrantReadWriteLock lock = new ReentrantReadWriteLock();

        public boolean contains(int value) {
            return bitmap.contains(value);
        }

        public void add(int value) {
            bitmap.add(value);
        }

        public void lockWrite() {
            lock.writeLock().lock();
        }

        public void unlockWrite() {
            lock.writeLock().unlock();
        }
    }
}
