package com.mchz.nyx.dark.engine.filter;

import cn.hutool.core.io.IoUtil;
import com.gliwka.hyperscan.wrapper.Database;
import com.gliwka.hyperscan.wrapper.Match;
import com.gliwka.hyperscan.wrapper.Scanner;
import com.mchz.nyx.common.util.WaitUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.atomic.AtomicReferenceArray;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2021/12/27 17:49
 */
@Slf4j
public class RegexFilterCore extends DefaultFilterCore<String, RegexPayload> {
    private final Database db;
    private final Pool pool;

    public RegexFilterCore(Database db, int cacheSize) {
        super(cacheSize);
        this.db = db;
        this.pool = new Pool(4);
    }

    @Override
    protected String getKey(RegexPayload payload, String data) {
        payload.setValue(null);
        return data;
    }

    @Override
    protected Set<Integer> scanAll(RegexPayload payload, String data) {
        Scanner scanner = pool.get();
        try {
            List<Match> matches = scanner.scan(db, data);
            return matches.stream().map(v -> v.getMatchedExpression().getId()).collect(Collectors.toSet());
        } finally {
            pool.restore(scanner);
        }
    }

    private Scanner buildScanner(Database db) {
        Scanner scanner = new Scanner();
        scanner.allocScratch(db);
        return scanner;
    }

    @Override
    public void close() throws Exception {
        super.close();
        pool.close();
        db.close();
    }

    private class Pool {
        private final AtomicReferenceArray<Scanner> array;
        private final int limit;
        private final List<Scanner> scanners;

        public Pool(int size) {
            this.array = new AtomicReferenceArray<>(size);
            this.limit = size;
            this.scanners = new ArrayList<>(size >> 1);
        }

        public Scanner get() {
            Scanner tmp;
            for (int i = 0; i < scanners.size(); i++) {
                tmp = array.get(i);
                if (null != tmp && array.compareAndSet(i, tmp, null)) {
                    return tmp;
                }
            }
            create();

            int fullTimes = 0;
            while (true) {
                for (int i = 0; i < scanners.size(); i++) {
                    tmp = array.get(i);
                    if (array.compareAndSet(i, tmp, null)) {
                        return tmp;
                    }
                }
                WaitUtil.applyWait(++fullTimes);
            }
        }

        public void restore(Scanner v) {
            for (int i = 0; i < scanners.size(); i++) {
                if (array.compareAndSet(i, null, v)) {
                    return;
                }
            }
            IoUtil.close(v);
        }

        private void create() {
            try {
                if (scanners.size() < limit) {
                    synchronized (scanners) {
                        if (scanners.size() < limit) {
                            Scanner scanner = buildScanner(db);
                            scanners.add(scanner);
                            restore(scanner);
                        }
                    }
                }
            } catch (Exception e) {
                log.warn("【reg-scan】{}", e.getMessage());
            }
        }

        public void close() {
            scanners.forEach(IoUtil::close);
        }
    }
}
