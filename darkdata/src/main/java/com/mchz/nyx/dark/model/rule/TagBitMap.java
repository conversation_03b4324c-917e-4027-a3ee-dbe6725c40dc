package com.mchz.nyx.dark.model.rule;

import lombok.AllArgsConstructor;
import org.roaringbitmap.RoaringBitmap;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2023/5/18 11:44
 */
@AllArgsConstructor
public class TagBitMap {
    private static final int LIMIT = 3;

    private final Set<Integer> ids;
    private final RoaringBitmap bitmap;

    public static TagBitMap of(List<Integer> list) {
        if (list.size() < LIMIT) {
            return new TagBitMap(new HashSet<>(list), null);
        }
        RoaringBitmap bitmap = RoaringBitmap.bitmapOf(list.stream().mapToInt(v -> v).toArray());
        bitmap.runOptimize();
        return new TagBitMap(null, bitmap);
    }

    public static TagBitMap ofTmp(Set<Integer> list) {
        if (list.size() < LIMIT) {
            return new TagBitMap(list, null);
        }
        RoaringBitmap bitmap = RoaringBitmap.bitmapOfUnordered(list.stream().mapToInt(v -> v).toArray());
        return new TagBitMap(null, bitmap);
    }

    public boolean contains(TagBitMap subset) {
        if (null != ids) {
            if (null == subset.ids) {
                return false;
            }
            return ids.containsAll(subset.ids);
        }
        if (null != subset.ids) {
            return subset.ids.stream().allMatch(bitmap::contains);
        }
        return bitmap.contains(subset.bitmap);
    }

    public boolean intersect(TagBitMap subset) {
        if (null != ids) {
            if (null != subset.ids) {
                return ids.stream().anyMatch(subset.ids::contains);
            }
            return ids.stream().anyMatch(subset.bitmap::contains);
        }
        if (null != subset.ids) {
            return subset.ids.stream().anyMatch(bitmap::contains);
        }
        return RoaringBitmap.intersects(bitmap, subset.bitmap);
    }

    public Integer getIfUnique() {
        if (null != ids && 1 == ids.size()) {
            return ids.iterator().next();
        }
        return null;
    }
}
