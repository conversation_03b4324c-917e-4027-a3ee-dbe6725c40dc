package com.mchz.nyx.dark.retrieval;

import com.mchz.nyx.dark.engine.NlpEngine;
import com.mchz.nyx.dark.model.EngineContext;
import com.mchz.nyx.dark.model.meta.MetaColumnData;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
@Slf4j
public class SimilarRetrievalStage implements RetrievalRecallStage {
    private final NlpEngine nlpEngine;
    private final SimilarSingleStep single;
    private final SimilarEntiretyStep entirety;
    private final int type;

    public SimilarRetrievalStage(NlpEngine nlpEngine, SimilarSingleStep single, SimilarEntiretyStep entirety) {
        this.nlpEngine = nlpEngine;
        this.single = single;
        this.entirety = entirety;
        int t = 0;
        if (null != single) {
            t += 1;
        }
        if (null != entirety) {
            t += 2;
        }
        this.type = t;
    }

    @Override
    public void execute(EngineContext context) {
        if (context.getColumns().isEmpty()) {
            return;
        }
        switch (type) {
            case 1:
                context.getColumns().forEach(v -> single.execute(context, nlpEngine, v));
                break;
            case 2:
                entirety.execute(context, nlpEngine, context.getColumns());
                break;
            case 3:
                List<MetaColumnData> list = new ArrayList<>(0);
                for (MetaColumnData column : context.getColumns()) {
                    if (single.execute(context, nlpEngine, column)) {
                        continue;
                    }
                    list.add(column);
                }
                if (!list.isEmpty()) {
                    entirety.execute(context, nlpEngine, list);
                }
                break;
            default:
        }
    }
}
