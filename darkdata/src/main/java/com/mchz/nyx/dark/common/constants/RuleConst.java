package com.mchz.nyx.dark.common.constants;

/**
 * <p>
 * 常量
 * </p>
 *
 * <AUTHOR>
 */
public interface RuleConst {
    /**
     * 默认优先级
     */
    int DEFAULT_MIN_PRIORITY = 10000;

    /**
     * 默认命中率
     */
    double DEFAULT_HIT = 0.5f;
    /**
     * 最大数据长度
     */
    int MAX_DATA_LEN = 500;
    int MAX_DICT_MATCH_LEN = 255;
    /**
     * 预设的文本最大长度
     */
    int MAX_SEQ_LEN = 64;
    /**
     * 相似度baseline
     */
    int DEFAULT_BASELINE = 80;
    /**
     * 默认缓存条数
     */
    int DEFAULT_CACHE_SIZE = 5000;

    int MAX_TERM_LEN = 1000;
}
