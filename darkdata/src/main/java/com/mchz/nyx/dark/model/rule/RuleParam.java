package com.mchz.nyx.dark.model.rule;

import cn.hutool.core.util.StrUtil;
import lombok.Data;

import java.util.Objects;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/11/19 18:58
 */
@Data
public class RuleParam {
    private String name;
    private Integer priority;
    private Double hit;
    private RuleIdInfo target;

    public RuleParam(RuleIdInfo target, Integer priority, Double hit) {
        this.name = target.toString();
        this.priority = priority;
        this.hit = hit;
        this.target = target;
    }

    public RuleParam(RuleIdInfo target, Integer priority, Double hit, int i) {
        this.name = target.toString() + StrUtil.DASHED + i;
        this.priority = priority;
        this.hit = hit;
        this.target = target;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }

        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        RuleParam that = (RuleParam) o;
        return Objects.equals(name, that.name);
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(name);
    }
}
