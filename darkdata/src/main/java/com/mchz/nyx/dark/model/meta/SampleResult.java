package com.mchz.nyx.dark.model.meta;

import com.mchz.nyx.common.util.CommUtil;
import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 结果集
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/10/26 15:05
 */
@Data
@AllArgsConstructor(access = AccessLevel.PRIVATE)
public class SampleResult {
    private final Map<String, List<Object>> sample;
    private final Integer total;

    public static SampleResult empty() {
        return new SampleResult(Collections.emptyMap(), null);
    }

    public static SampleResult of(Map<String, List<Object>> sample) {
        return new SampleResult(sample, null);
    }

    public static SampleResult ofLine(String[] selectColumn, Iterator<Object[]> lineData, int sampleSize) {
        Object[][] data = new Object[selectColumn.length][sampleSize];
        int limit = CommUtil.sampling(data, lineData, sampleSize);
        Map<String, List<Object>> temp = new HashMap<>(data.length);
        if (0 == limit) {
            Arrays.stream(selectColumn).forEach(v -> temp.put(v, Collections.emptyList()));
        } else {
            int i = 0;
            for (String name : selectColumn) {
                temp.put(name, Arrays.stream(data[i++]).limit(limit).collect(Collectors.toList()));
            }
        }
        return new SampleResult(temp, limit);
    }

    public static SampleResult ofLine(List<String> selectColumn, List<? extends List<?>> columnData, int sampleSize) {
        Object[][] data = new Object[selectColumn.size()][sampleSize];
        int limit = CommUtil.sampling(data, new List2Array(columnData.iterator()), sampleSize);
        Map<String, List<Object>> temp = new HashMap<>(data.length);
        if (0 == limit) {
            selectColumn.forEach(v -> temp.put(v, Collections.emptyList()));
        } else {
            int i = 0;
            for (String name : selectColumn) {
                temp.put(name, Arrays.stream(data[i++]).limit(limit).collect(Collectors.toList()));
            }
        }
        return new SampleResult(temp, limit);
    }

    @AllArgsConstructor
    private static class List2Array implements Iterator<Object[]> {
        private final Iterator<? extends List<?>> data;

        @Override
        public boolean hasNext() {
            return data.hasNext();
        }

        @Override
        public Object[] next() {
            List<?> next = data.next();
            return next.toArray();
        }
    }
}
