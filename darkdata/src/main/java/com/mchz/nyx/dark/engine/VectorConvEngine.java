package com.mchz.nyx.dark.engine;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2022/9/6 17:27
 */
public interface VectorConvEngine extends AutoCloseable {

    default float[] getPreVector(String sequence) {
        return getVector(sequence);
    }

    /**
     * 预处理
     *
     * @param sequence 词、句
     * @return 结果
     */
    float[] getVector(String sequence);

    @Override
    void close();
}
