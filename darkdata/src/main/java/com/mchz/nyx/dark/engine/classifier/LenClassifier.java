package com.mchz.nyx.dark.engine.classifier;

import com.mchz.nyx.dark.engine.expression.ValueExpression;
import com.mchz.nyx.dark.engine.rule.RuleManager;
import lombok.AllArgsConstructor;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2022/9/23 16:33
 */
@AllArgsConstructor
public class LenClassifier implements BaseClassifier<String> {
    private final ValueExpression<Integer> lenLimit;
    private final BaseClassifier<String> classifier;

    @Override
    public boolean classify(String data, RuleManager manager) {
        if (!lenLimit.compare(data.length())) {
            return false;
        }
        return classifier.classify(data, manager);
    }
}
