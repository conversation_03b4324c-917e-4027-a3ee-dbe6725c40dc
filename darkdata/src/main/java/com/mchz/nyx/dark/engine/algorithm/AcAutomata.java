package com.mchz.nyx.dark.engine.algorithm;

import cn.hutool.core.util.CharUtil;
import cn.hutool.core.util.StrUtil;
import lombok.Getter;
import lombok.Setter;

import java.util.*;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/11/12 19:06
 */
public class AcAutomata<T> {

    private final TrieNode<T> root;

    public <E extends TrieData<T>> AcAutomata(List<E> keywords) {
        TrieNode<T> root = buildTree(keywords);
        addFailure(root);
        this.root = root;
    }

    protected TrieNode<T> getRoot() {
        return root;
    }

    public List<T> findAll(String text) {
        char[] chars = text.toCharArray();
        List<T> results = new LinkedList<>();
        TrieNode<T> now = root;
        for (char c : chars) {
            while (null != now) {
                TrieNode<T> temp = now.getChildByName(c);
                if (null != temp) {
                    now = temp;
                    results.addAll(now.getResults());
                    break;
                }
                if (now == root) {
                    break;
                }
                now = now.getFailure();
            }
        }
        return results;
    }

    private void addFailure(TrieNode<T> root) {
        Queue<TrieNode<T>> queue = new LinkedList<>();
        queue.add(root);
        while (!queue.isEmpty()) {
            TrieNode<T> node = queue.poll();
            for (TrieNode<T> child : node.getChildren()) {
                TrieNode<T> failure = null;
                for (TrieNode<T> ref = node.getFailure(); ref != null && null == failure; ref = ref.getFailure()) {
                    failure = ref.getChildByName(child.getName());
                }
                if (null == failure) {
                    child.setFailure(root);
                } else {
                    child.setFailure(failure);
                    child.addResults(failure.getResults());
                }
            }
            queue.addAll(node.getChildren());
        }
    }

    private <E extends TrieData<T>> TrieNode<T> buildTree(List<E> list) {
        TrieNode<T> root = new TrieNode<>(CharUtil.SPACE);
        if (null == list) {
            return root;
        }
        for (E item : list) {
            String value = item.getValue();
            if (StrUtil.isEmpty(value)) {
                continue;
            }
            TrieNode<T> now = root;
            for (char ch : value.toCharArray()) {
                TrieNode<T> temp = now.getChildByName(ch);
                if (null == temp) {
                    TrieNode<T> newNode = new TrieNode<>(ch);
                    now.addChild(newNode);
                    now = newNode;
                } else {
                    now = temp;
                }
            }
            now.addResult(item.getResult());
        }
        return root;
    }

    protected static class TrieNode<T> {

        private final char ch;

        private final Map<Character, TrieNode<T>> children;

        @Getter
        @Setter
        private TrieNode<T> failure;

        @Getter
        private final List<T> results;

        public TrieNode(char ch) {
            this.ch = ch;
            this.children = new HashMap<>();
            this.results = new LinkedList<>();
        }

        public char getName() {
            return ch;
        }

        public TrieNode<T> getChildByName(char ch) {
            return children.get(ch);
        }

        public void addChild(TrieNode<T> child) {
            children.put(child.getName(), child);
        }

        public Collection<TrieNode<T>> getChildren() {
            return children.values();
        }

        public void addResult(T data) {
            results.add(data);
        }

        public void addResults(List<T> data) {
            results.addAll(data);
        }

        @Override
        public String toString() {
            return CharUtil.toString(ch);
        }
    }
}
