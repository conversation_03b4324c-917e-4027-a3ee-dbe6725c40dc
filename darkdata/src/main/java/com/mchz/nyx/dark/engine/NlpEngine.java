package com.mchz.nyx.dark.engine;

import com.mchz.nyx.dark.common.enums.HitType;
import com.mchz.nyx.dark.model.EngineContext;
import com.mchz.nyx.dark.model.TargetResult;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/1/20
 */
public interface NlpEngine extends AutoCloseable {
    List<TargetResult> similarityAnalysis(EngineContext context, String str, HitType hitType, float baseline);

    @Override
    default void close() {
    }

    NlpEngine EMPTY = (context, str, hitType, baseline) -> null;
}
