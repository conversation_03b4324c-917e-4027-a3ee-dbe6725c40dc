package com.mchz.nyx.dark.util.nlp.tokenizer;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@SuppressWarnings("all")
public class SimpleTokenizer implements Tokenizer {
    @Override
    public List<String> tokenize(String text) {
        text = text.trim();
        if (text.length() == 0) {
            return new ArrayList<>();
        }
        return Arrays.asList(text.split(" "));
    }
}
