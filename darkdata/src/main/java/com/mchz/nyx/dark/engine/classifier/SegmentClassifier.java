package com.mchz.nyx.dark.engine.classifier;

import cn.hutool.core.util.StrUtil;
import com.mchz.nyx.dark.engine.expression.SegmentExpression;
import com.mchz.nyx.dark.engine.rule.RuleManager;

import java.util.Iterator;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 */
public class SegmentClassifier implements BaseClassifier<String> {

    private final SegmentExpression segmentWay;
    private final BaseClassifier<String>[] classifier;
    private final int[] index;
    private final BaseClassifier<String> rest;
    private final BaseClassifier<String> other;
    private final int expectedNum;
    private final byte type;
    private final byte restType;

    public SegmentClassifier(SegmentExpression segmentWay, BaseClassifier<String>[] classifier, int[] index, BaseClassifier<String> rest, BaseClassifier<String> other, Integer expectedNum, Boolean least) {
        this.segmentWay = segmentWay;
        this.classifier = classifier;
        this.index = null == index || index[index.length - 1] == index.length ? null : index;
        this.rest = null == rest ? other : rest;
        this.other = other;
        this.expectedNum = null == expectedNum ? 0 : expectedNum;
        byte tmp;
        if (null == this.index) {
            if (null == classifier || 0 == classifier.length) {
                tmp = 1;
            } else {
                tmp = 2;
            }
        } else {
            if (null == other) {
                tmp = 3;
            } else {
                tmp = 4;
            }
        }
        this.type = tmp;
        if (null == expectedNum) {
            if (null == this.rest) {
                tmp = 1;
            } else {
                tmp = 2;
            }
        } else {
            if (null == this.rest) {
                tmp = 3;
            } else {
                tmp = 4;
            }
            if (Boolean.TRUE.equals(least)) {
                tmp += 2;
            }
        }
        this.restType = tmp;
    }

    @Override
    public boolean classify(String data, RuleManager manager) {
        Iterator<String> iterator = segmentWay.split(data);
        switch (type) {
            case 1:
                return classify1(iterator, manager);
            case 2:
                return classify2(iterator, manager);
            case 3:
                return classify3(iterator, manager);
            case 4:
                return classify4(iterator, manager);
            default:
                return false;
        }
    }

    private boolean classify1(Iterator<String> iterator, RuleManager manager) {
        return checkRest(iterator, 0, manager);
    }

    private boolean classify2(Iterator<String> iterator, RuleManager manager) {
        for (BaseClassifier<String> classify : classifier) {
            if (!iterator.hasNext()) {
                return false;
            }
            if (checkout(iterator, classify, manager)) {
                return false;
            }
        }
        return checkRest(iterator, classifier.length, manager);
    }

    private boolean classify3(Iterator<String> iterator, RuleManager manager) {
        int[] i = new int[2];
        while (iterator.hasNext() && i[1] < index.length) {
            String str = iterator.next();
            if (++i[0] < index[i[1]]) {
                continue;
            }
            if (StrUtil.isEmpty(str)) {
                return false;
            }
            if (check(i, str, manager)) {
                return false;
            }
        }
        return i[1] == index.length && checkRest(iterator, i[0], manager);
    }

    private boolean classify4(Iterator<String> iterator, RuleManager manager) {
        int[] i = new int[2];
        while (iterator.hasNext() && i[1] < index.length) {
            String str = iterator.next();
            if (StrUtil.isEmpty(str)) {
                return false;
            }
            if (++i[0] < index[i[1]]) {
                if (!other.classify(str, manager)) {
                    return false;
                }
                continue;
            }
            if (check(i, str, manager)) {
                return false;
            }
        }
        return i[1] == index.length && checkRest(iterator, i[0], manager);
    }

    private boolean check(int[] i, String str, RuleManager manager) {
        boolean flag = true;
        while (i[0] == index[i[1]]) {
            if (flag && classifier[i[1]].classify(str, manager)) {
                flag = false;
            }
            if (++i[1] == index.length) {
                break;
            }
        }
        return flag;
    }

    private boolean checkRest(Iterator<String> iterator, int index, RuleManager manager) {
        int i;
        switch (restType) {
            case 1:
                return true;
            case 2:
                while (iterator.hasNext()) {
                    if (checkout(iterator, rest, manager)) {
                        return false;
                    }
                }
                return true;
            case 3:
                i = expectedNum - index;
                while (i-- > 0) {
                    if (!iterator.hasNext()) {
                        return false;
                    }
                    iterator.next();
                }
                return !iterator.hasNext();
            case 4:
                i = expectedNum - index;
                while (i-- > 0) {
                    if (!iterator.hasNext()) {
                        return false;
                    }
                    if (checkout(iterator, rest, manager)) {
                        return false;
                    }
                }
                return !iterator.hasNext();
            case 5:
                i = expectedNum - index;
                while (i-- > 0) {
                    if (!iterator.hasNext()) {
                        return false;
                    }
                    iterator.next();
                }
                return true;
            case 6:
                i = expectedNum - index;
                while (i-- > 0) {
                    if (!iterator.hasNext()) {
                        return false;
                    }
                    if (checkout(iterator, rest, manager)) {
                        return false;
                    }
                }
                return true;
            default:
                return false;
        }
    }

    private boolean checkout(Iterator<String> iterator, BaseClassifier<String> classify, RuleManager manager) {
        String str = iterator.next();
        if (StrUtil.isEmpty(str)) {
            return true;
        }
        return !classify.classify(str, manager);
    }
}
