package com.mchz.nyx.dark.engine.filter;

import com.mchz.nyx.dark.engine.rule.RuleManager;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2021/12/30 13:51
 */
public interface BaseFilter<T extends ExpectAble> {
    /**
     * 过滤
     *
     * @param payload 过滤条件
     * @param data    数据
     * @param manager 规则
     * @return {@code true} 命中
     */
    boolean filter(T payload, String data, RuleManager manager);

    BaseFilter<RegexPayload> REGEX = (payload, data, manager) -> manager.filter(payload, data);
    BaseFilter<DictPayload> DICT = (payload, data, manager) -> manager.filter(payload, data);
}
