package com.mchz.nyx.dark.model.meta;

import com.mchz.nyx.dark.common.enums.HitType;
import com.mchz.nyx.dark.model.TargetInfo;
import com.mchz.nyx.dark.model.TargetResult;
import com.mchz.nyx.dark.model.rule.ColumnPayload;
import com.mchz.nyx.dark.util.NlpUtil;
import lombok.AccessLevel;
import lombok.Data;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/12/1 15:28
 */
@Data
public class MetaColumnData implements ColumnPayload {
    private final NyxMetaColumn meta;

    private final boolean load;
    private final int validNum;
    private final int nullNum;
    private final int nonRepetitiveNum;
    private final List<StrDataInfo> dataStr;
    private final List<IntDataInfo> dataInt;

    private String comment;
    private List<TargetResult> result;
    @Setter(AccessLevel.PRIVATE)
    private String alias;

    public MetaColumnData(NyxMetaColumn meta) {
        this(meta, false, 0, 0, 0, null, null);
    }

    public MetaColumnData(NyxMetaColumn meta, int validNum, int nullNum, int nonRepetitiveNum, List<StrDataInfo> dataStr, List<IntDataInfo> dataInt) {
        this(meta, true, validNum, nullNum, nonRepetitiveNum, dataStr, dataInt);
    }

    public MetaColumnData(NyxMetaColumn meta, boolean load, int validNum, int nullNum, int nonRepetitiveNum, List<StrDataInfo> dataStr, List<IntDataInfo> dataInt) {
        this.meta = meta;
        this.load = load;
        this.validNum = validNum;
        this.nullNum = nullNum;
        this.nonRepetitiveNum = nonRepetitiveNum;
        this.dataStr = dataStr;
        this.dataInt = dataInt;
        this.result = new ArrayList<>(0);
        this.comment = NlpUtil.dealComment(meta.getColumnComment());
    }

    @Override
    public int getTotal() {
        return validNum;
    }

    public void addTarget(TargetResult res) {
        if (null == result) {
            result = new ArrayList<>(1);
        }
        result.add(res);
    }

    public boolean addAllTarget(List<TargetResult> res) {
        if (null != res && !res.isEmpty()) {
            if (null == result) {
                result = new ArrayList<>(res);
            } else {
                result.addAll(res);
            }
            return true;
        }
        return false;
    }

    public void addAllInfo(HitType type, List<TargetInfo> res) {
        if (null != res) {
            if (null == result) {
                result = new ArrayList<>(res.size());
            }
            res.forEach(v -> result.add(new TargetResult(type, v)));
        }
    }

    public void addAlias(String name) {
        addAlias(name, true);
    }

    public void addAlias(String name, boolean isOverride) {
        if (isOverride || null == alias) {
            alias = name;
        }
    }

    public TargetResult getTop() {
        return null == result || result.isEmpty() ? null : result.get(0);
    }

    public boolean nonResult() {
        return null == result || result.isEmpty();
    }

    public boolean isNotMultiple() {
        return null == result || result.isEmpty() || 1 == result.size();
    }
}
