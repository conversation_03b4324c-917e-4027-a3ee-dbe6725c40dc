package com.mchz.nyx.dark.engine.impl;

import com.mchz.nyx.dark.engine.YunCeEngine;
import com.mchz.nyx.dark.model.EmbeddingInfo;
import com.mchz.nyx.dark.model.TargetInfo;
import com.mchz.nyx.dark.model.TargetResult;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/1/20
 */
@Slf4j
public class NlpSemanticEngine extends AbstractNlpEngine {
    private final YunCeEngine client;

    public NlpSemanticEngine(Map<String, List<TargetInfo>> data, YunCeEngine client) {
        super(data);
        this.client = client;
    }

    @Override
    protected List<TargetResult> similarity(String str, float baseline) {
        List<? extends EmbeddingInfo> list = client.askTerm(str);
        Set<String> set = new HashSet<>();
        List<TargetResult> res = list.stream().filter(v -> v.getScore() > baseline && set.add(v.getText())).limit(5).map(v -> {
            List<TargetInfo> temp = match(v.getText());
            if (temp.isEmpty()) {
                log.debug("【语义向量】结果未命中标准:{}({})", v.getText(), v.getId());
                return null;
            }
            TargetInfo info = temp.get(0);
            return new TargetResult(null, info, v.getScore());
        }).filter(Objects::nonNull).collect(Collectors.toList());
        if (res.isEmpty()) {
            return Collections.emptyList();
        }
        return client.askTerm(str, res, v -> v.getInfo().getName());
    }
}
