package com.mchz.nyx.dark.common.enums;

import com.mchz.nyx.dark.model.rule.TablePayload;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.function.Function;

import static com.mchz.nyx.dark.common.enums.ReturnType.ENUM;
import static com.mchz.nyx.dark.common.enums.ReturnType.STRING;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2023/3/22 17:26
 */
@AllArgsConstructor
public enum ParamTable implements Function<TablePayload, Object> {
    /**
     * 参数
     */
    CATALOG_SCHEMA(STRING, v -> v.getTable().getSchema().getCatalogSchema()),
    TABLE_NAME(STRING, v -> v.getTable().getTableName()),
    TABLE_COMMENT(STRING, v -> v.getTable().getTableComment()),
    ADD_DB_TYPE(ENUM, v -> v.getInfo().getDbType());

    @Getter
    private final ReturnType type;
    private final Function<TablePayload, Object> tableFun;

    @Override
    public Object apply(TablePayload payload) {
        if (null == tableFun) {
            return null;
        }
        return tableFun.apply(payload);
    }
}
