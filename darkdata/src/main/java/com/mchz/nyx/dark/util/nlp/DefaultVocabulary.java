package com.mchz.nyx.dark.util.nlp;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public class DefaultVocabulary implements Vocabulary {

    private final Map<String, Integer> vocab;
    private final List<String> indexToToken;
    private final int unk;

    public DefaultVocabulary(List<String> list) {
        Map<String, Integer> tmp = new LinkedHashMap<>(list.size());
        int i = 0;
        for (String s : list) {
            tmp.put(s, i++);
        }
        this.indexToToken = list;
        this.vocab = tmp;
        this.unk = tmp.getOrDefault(UNKNOWN_TOKEN, -1);
    }

    @Override
    public String getToken(long index) {
        if (index < 0 || index >= indexToToken.size()) {
            return UNKNOWN_TOKEN;
        }
        return indexToToken.get((int) index);
    }

    @Override
    public boolean contains(String token) {
        return vocab.contains<PERSON><PERSON>(token);
    }

    @Override
    public long getIndex(String token) {
        return vocab.getOrDefault(token, unk);
    }

    @Override
    public long size() {
        return vocab.size();
    }
}
