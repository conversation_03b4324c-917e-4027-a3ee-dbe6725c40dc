package com.mchz.nyx.dark.factory;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.CharUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.mchz.nyx.dark.DarkActuator;
import com.mchz.nyx.dark.common.constants.RuleConst;
import com.mchz.nyx.dark.common.enums.CharType;
import com.mchz.nyx.dark.common.enums.HitType;
import com.mchz.nyx.dark.model.DefaultEngineContext;
import com.mchz.nyx.dark.model.TargetInfo;
import com.mchz.nyx.dark.model.TargetResult;
import com.mchz.nyx.dark.model.dto.ActuatorParam;
import com.mchz.nyx.dark.model.meta.*;
import com.mchz.nyx.dark.rank.RankStage;
import com.mchz.nyx.dark.retrieval.RetrievalRecallStage;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2021/6/10 10:13
 */
@Slf4j
public abstract class AbstractDarkActuator<T extends NyxMetaTable, C extends NyxMetaColumn, D, R> implements DarkActuator<T, C, R> {

    private final List<RetrievalRecallStage> recallStages;
    private final List<RankStage> rankStages;
    private final List<AutoCloseable> list;
    private final boolean commentOverlay;

    protected AbstractDarkActuator(ActuatorParam stages) {
        this.recallStages = stages.getRecallStages();
        this.rankStages = stages.getRankStages();
        this.list = stages.getCloseableList();
        this.commentOverlay = stages.isCommentOverlay();
    }

    protected boolean isEmptyEngine() {
        return recallStages.isEmpty();
    }

    @Override
    public R execute(AdditionalInfo addInfo, T table, List<C> columns, Function<C, List<Object>> sampleHandler) {
        List<MetaColumnData> columnData = new ArrayList<>(columns.size());
        List<D> res = new ArrayList<>(columns.size());
        columns.forEach(v -> {
            List<Object> data = sampleHandler.apply(v);
            MetaColumnData dto;
            if (null == data) {
                dto = new MetaColumnData(v);
            } else {
                dto = buildMetaColumnData(v, data);
            }
            columnData.add(dto);
            res.add(buildColumnResultObj(v, dto, data));
        });

        DefaultEngineContext context = new DefaultEngineContext(addInfo, table, Collections.unmodifiableList(columnData));
        boolean flag = false;
        for (RetrievalRecallStage stage : recallStages) {
            try {
                stage.execute(context);
                flag = true;
            } catch (Exception e) {
                log.error("【recall】{}({}):{}", context.getTable().getTableName(), stage.getClass().getSimpleName(), ExceptionUtil.stacktraceToOneLineString(e));
            }
        }
        if (flag) {
            for (RankStage stage : rankStages) {
                try {
                    stage.execute(context);
                } catch (Exception e) {
                    log.error("【rank】{}({}):{}", context.getTable().getTableName(), stage.getClass().getSimpleName(), ExceptionUtil.stacktraceToOneLineString(e));
                }
            }
        }
        if (commentOverlay) {
            commentOverlay(context.getColumns());
        }
        return buildResult(table, res);
    }

    protected abstract D buildColumnResultObj(C meta, MetaColumnData columnData, List<Object> data);

    protected abstract R buildResult(T tableMeta, List<D> columns);

    private void commentOverlay(List<MetaColumnData> columns) {
        for (MetaColumnData column : columns) {
            if (column.nonResult()) {
                String temp = dealComment(column.getComment(), column.getAlias());
                if (null != temp) {
                    column.addTarget(new TargetResult(HitType.COMMENT, new TargetInfo(null, temp)));
                }
            }
        }
    }

    private String dealComment(String comment, String alias) {
        if (checkComment(comment)) {
            return comment;
        }
        if (checkComment(alias)) {
            return alias;
        }
        return null;
    }

    private boolean checkComment(String temp) {
        if (StrUtil.isEmpty(temp) || 1 == temp.length() || temp.length() > RuleConst.MAX_TERM_LEN) {
            return false;
        }
        char[] wCon = {'、', '"', '%', '（', '(', '）', ')', '_', '/', '\'', '.'};
        char c;
        boolean z = false, e = false;
        CharType pre = null, now;
        for (int i = 0; i < temp.length(); i++) {
            c = temp.charAt(i);
            now = CharType.of(c);
            switch (now) {
                case CHS:
                    z = true;
                    break;
                case LOW_ENG:
                case UPP_ENG:
                    if (!e && CharType.ENG.isType(pre)) {
                        e = true;
                    }
                    break;
                case OTHER:
                    if (CharType.OTHER.equals(pre)) {
                        return false;
                    }
                    if (ArrayUtil.contains(wCon, c) || i != 0 && i != temp.length() - 1 && CharUtil.DASHED == c && Character.isDigit(temp.charAt(i - 1)) == Character.isDigit(temp.charAt(i + 1))) {
                        break;
                    }
                    return false;
                default:
            }
            pre = now;
        }
        return z || e;
    }

    private MetaColumnData buildMetaColumnData(NyxMetaColumn column, List<Object> values) {
        switch (column.getColumnType().getTypeGroup()) {
            case CHARACTER:
                return buildCharacter(column, values);
            case INTEGER:
                return buildInteger(column, values);
            default:
                return buildOther(column, values);
        }
    }

    private MetaColumnData buildCharacter(NyxMetaColumn meta, List<Object> values) {
        Map<String, int[]> map = new HashMap<>(values.size());
        int i = 0;
        for (Object value : values) {
            String str = StrUtil.utf8Str(value);
            if (StrUtil.isBlank(str)) {
                i++;
                continue;
            }
            if (str.length() > RuleConst.MAX_DATA_LEN) {
                str = StrUtil.subPre(str, RuleConst.MAX_DATA_LEN);
            }
            map.computeIfAbsent(str.trim(), k -> new int[1])[0]++;
        }
        if (map.isEmpty()) {
            return columnEmptyData(meta, values);
        }
        List<StrDataInfo> data = map.entrySet().stream().map(v -> new StrDataInfo(v.getKey(), v.getValue()[0])).collect(Collectors.toList());
        return columnStrData(meta, values, data, i);

    }

    private MetaColumnData buildInteger(NyxMetaColumn meta, List<Object> values) {
        Map<Integer, int[]> map = new HashMap<>(values.size());
        int i = 0;
        for (Object value : values) {
            Integer v = Convert.toInt(value);
            if (null == v) {
                i++;
                continue;
            }
            map.computeIfAbsent(v, k -> new int[1])[0]++;
        }
        if (map.isEmpty()) {
            return columnEmptyData(meta, values);
        }
        List<IntDataInfo> data = map.entrySet().stream().map(v -> new IntDataInfo(v.getKey(), v.getValue()[0])).collect(Collectors.toList());
        return columnIntData(meta, values, data, i);
    }

    private MetaColumnData buildOther(NyxMetaColumn meta, List<Object> values) {
        Set<Object> set = new HashSet<>(values.size());
        int i = 0;
        for (Object value : values) {
            if (ObjUtil.isEmpty(value)) {
                i++;
                continue;
            }
            set.add(value);
        }
        if (set.isEmpty()) {
            return columnEmptyData(meta, values);
        }
        return columnIgnoreData(meta, values, i, set.size());
    }

    private MetaColumnData columnStrData(NyxMetaColumn meta, List<Object> sample, List<StrDataInfo> dataStr, int nullNum) {
        return new MetaColumnData(meta, sample.size() - nullNum, nullNum, dataStr.size(), Collections.unmodifiableList(dataStr), null);
    }

    private MetaColumnData columnIntData(NyxMetaColumn meta, List<Object> sample, List<IntDataInfo> dataInt, int nullNum) {
        return new MetaColumnData(meta, sample.size() - nullNum, nullNum, dataInt.size(), null, Collections.unmodifiableList(dataInt));
    }

    private MetaColumnData columnEmptyData(NyxMetaColumn meta, List<Object> sample) {
        return new MetaColumnData(meta, 0, sample.size(), 0, null, null);
    }

    private MetaColumnData columnIgnoreData(NyxMetaColumn meta, List<Object> sample, int nullNum, int nonRepetitiveNum) {
        return new MetaColumnData(meta, sample.size() - nullNum, nullNum, nonRepetitiveNum, null, null);
    }

    @Override
    public void close() {
        list.forEach(IoUtil::close);
    }
}
