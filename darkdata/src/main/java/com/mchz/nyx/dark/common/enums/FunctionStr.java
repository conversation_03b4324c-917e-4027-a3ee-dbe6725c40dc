package com.mchz.nyx.dark.common.enums;

import com.mchz.nyx.dark.util.RuleFunctionUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

/**
 * <p>
 * 函数
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/11/19 10:48
 */
@Getter
@AllArgsConstructor
public enum FunctionStr {
    /**
     * 身份证
     */
    ID_CARD(RuleFunctionUtil::isValidCard),
    ID_CARD18(RuleFunctionUtil::isValidCard18),
    ID_CARD15(RuleFunctionUtil::isValidCard15),
    ID_TW(RuleFunctionUtil::isValidTWCard),
    ID_HK(RuleFunctionUtil::isValidHKCard),
    /**
     * 主体身份代码
     */
    PID_CA14(RuleFunctionUtil::isValidCa14),
    /**
     * IMO
     */
    IMO(RuleFunctionUtil::isValidImo),
    /**
     * 统一社会信用代码
     */
    USCC(RuleFunctionUtil::isValidUSCC),
    /**
     * 工商注册号
     */
    BUSINESS_LICENSE(RuleFunctionUtil::isValidBusinessLicense),
    /**
     * 组织机构代码
     */
    ORGANIZATION_CODE(RuleFunctionUtil::isValidOrganizationCode),
    /**
     * 车辆识别号码
     */
    VIN(RuleFunctionUtil::isValidVin),
    /**
     * 税务登记证号
     */
    TAX_REGISTRATION(RuleFunctionUtil::isValidTaxRegistration),
    /**
     * 学籍号
     */
    STUDENT_STATUS(RuleFunctionUtil::isValidStudentStatus),
    /**
     * 残疾证号
     */
    DISABLED_CERTIFICATE(RuleFunctionUtil::isValidDisabledCertificate),
    /**
     * 进出口单位代码
     */
    IMPORT_AND_EXPORT_ENTERPRISE(RuleFunctionUtil::isValidImportAndExportEnterprise),
    /**
     * Luhn
     */
    LUHN(RuleFunctionUtil::isValidLuhn),
    /**
     * GB/T 17710,MOD 11,10
     */
    MOD11_10(RuleFunctionUtil::isValidMod1110),
    /**
     * 年
     */
    YEAR(RuleFunctionUtil::isYear),
    /**
     * 年起始
     */
    START_YEAR(RuleFunctionUtil::isStartYear),
    /**
     * yyyyMMdd
     */
    BIRTHDAY(RuleFunctionUtil::isBirthday),
    /**
     * 医师资格证书编号
     */
    DQCN(RuleFunctionUtil::isDoctorQualificationYear),
    /**
     * 时间（日期时间、日期）
     */
    DATE(RuleFunctionUtil::isDate);

    private final Function<String, Boolean> function;

    private static final  Map<String, FunctionStr> CACHE_MAP;

    static {
        FunctionStr[] values = values();
        CACHE_MAP = new HashMap<>(values.length);
        for (FunctionStr type : values) {
            CACHE_MAP.put(type.name(), type);

        }
    }

    public static FunctionStr getType(String name) {
        return CACHE_MAP.get(name);
    }
}
