package com.mchz.nyx.dark.factory;

import ai.onnxruntime.OrtEnvironment;
import ai.onnxruntime.OrtException;
import ai.onnxruntime.OrtSession;
import cn.hutool.cache.Cache;
import cn.hutool.cache.CacheUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.URLUtil;
import com.mchz.nyx.dark.common.constants.DarkConst;
import com.mchz.nyx.dark.common.constants.RuleConst;
import com.mchz.nyx.dark.engine.VectorConvEngine;
import com.mchz.nyx.dark.engine.YunCeEngine;
import com.mchz.nyx.dark.engine.impl.PredictorEngine;
import com.mchz.nyx.dark.model.config.DarkConfig;
import com.mchz.nyx.dark.model.config.EngineConfig;
import com.mchz.nyx.dark.util.nlp.DefaultVocabulary;
import com.mchz.nyx.dark.util.nlp.tokenizer.TransformerTokenizer;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;

import java.lang.ref.WeakReference;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2023/4/14 11:39
 */
@Slf4j
public class DefaultEngineFactory extends AbstractEngineFactory {
    private WeakReference<TransformerTokenizer> tokenizerRef;
    private Cache<String, float[]> vectorCache;

    public DefaultEngineFactory(@NonNull EngineConfig config) {
        super(config);
    }

    @Override
    public RangeFunction createRangeFunction(DarkConfig config) {
        return (context, loadType, dictId) -> Collections.emptySet();
    }

    @Override
    public VectorConvEngine createSimilarityEngine() {
        if (StrUtil.isEmpty(config.getOnnx())) {
            return null;
        }
        OrtEnvironment env = OrtEnvironment.getEnvironment();
        OrtSession session;
        try {
            session = env.createSession(config.getOnnx());
        } catch (OrtException e) {
            log.warn("create session:{}", e.getMessage());
            return null;
        }
        TransformerTokenizer tokenizer = getTokenizer();
        return new PredictorEngine(env, session, tokenizer, getVectorCache());
    }

    @Override
    public YunCeEngine createLLMEngine(DarkConfig config) {
        return null;
    }

    @Override
    protected TransColumnFun translateColumnFun(DarkConfig config) {
        return null;
    }

    private TransformerTokenizer getTokenizer() {
        if (null != tokenizerRef) {
            TransformerTokenizer tokenizer = tokenizerRef.get();
            if (null != tokenizer) {
                return tokenizer;
            }
        }
        boolean io;
        String path;
        if (StrUtil.isEmpty(config.getVocab())) {
            io = true;
            //classpath:vocab.txt
            path = "vocab.txt";
        } else if (config.getVocab().startsWith(URLUtil.CLASSPATH_URL_PREFIX)) {
            io = true;
            path = config.getVocab().substring(URLUtil.CLASSPATH_URL_PREFIX.length());
        } else {
            io = false;
            path = config.getVocab();
        }
        List<String> list;
        if (io) {
            list = new ArrayList<>();
            IoUtil.readLines(ResourceUtil.getStream(path), StandardCharsets.UTF_8, list);
        } else {
            list = FileUtil.readLines(path, StandardCharsets.UTF_8);
        }
        DefaultVocabulary vocabulary = new DefaultVocabulary(list);
        TransformerTokenizer tokenizer = new TransformerTokenizer(vocabulary, ObjUtil.defaultIfNull(config.getMaxSeqLen(), RuleConst.MAX_SEQ_LEN));
        tokenizerRef = new WeakReference<>(tokenizer);
        return tokenizer;
    }

    private Cache<String, float[]> getVectorCache() {
        if (null == vectorCache) {
            vectorCache = CacheUtil.newLFUCache(DarkConst.VECTOR_CACHE_CAPACITY, DarkConst.CACHE_TIMEOUT);
        }
        return vectorCache;

    }

    @Override
    public void close() {
        super.close();
        tokenizerRef = null;
    }
}
