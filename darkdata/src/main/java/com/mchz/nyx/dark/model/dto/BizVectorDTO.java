package com.mchz.nyx.dark.model.dto;

import com.mchz.nyx.dark.model.TargetInfo;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2022/9/7 9:56
 */
@Data
@AllArgsConstructor
public class BizVectorDTO {
    private VectorDTO vectorInfo;
    private List<TargetInfo> bizList;

    public float[] getVector() {
        return vectorInfo.getVector();
    }
}
