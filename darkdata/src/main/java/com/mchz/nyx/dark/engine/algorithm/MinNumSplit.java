package com.mchz.nyx.dark.engine.algorithm;

import lombok.Data;
import lombok.Getter;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2022/9/9 13:46
 */
public class MinNumSplit extends AcAutomata<ValueLenResult> {

    private final Map<String, String> map;

    public static MinNumSplit of(List<? extends Map<String, ?>> keywords) {
        Map<String, String> map = new HashMap<>();
        List<KvData> list = keywords.stream().flatMap(v -> v.entrySet().stream()).map(KvData::new).filter(v -> {
            map.put(v.getValue(), v.getResult().getV());
            return v.getResult().getLen() > 3;
        }).collect(Collectors.toList());
        return new MinNumSplit(list, map);
    }

    public static MinNumSplit of(Map<String, ?> keywords) {
        Map<String, String> map = new HashMap<>();
        List<KvData> list = keywords.entrySet().stream().map(KvData::new).filter(v -> {
            map.put(v.getValue(), v.getResult().getV());
            return v.getResult().getLen() > 3;
        }).collect(Collectors.toList());
        return new MinNumSplit(list, map);
    }

    private MinNumSplit(List<KvData> keywords, Map<String, String> map) {
        super(keywords);
        this.map = map;
    }

    public String get(String text) {
        return map.get(text);
    }

    public List<String> split(String text) {
        char[] chars = text.toCharArray();
        int len = chars.length;
        Node[] nodes = new Node[len + 1];
        TrieNode<ValueLenResult> root = getRoot();
        TrieNode<ValueLenResult> current = root;
        nodes[0] = new Node(0);

        for (int i = 1; i <= len; i++) {
            char c = chars[i - 1];
            TrieNode<ValueLenResult> temp;
            while (null != current) {
                temp = current.getChildByName(c);
                if (null != temp) {
                    current = temp;
                    updateNode(len, current.getResults(), i, nodes);
                    break;
                }
                if (current == root) {
                    break;
                }
                current = current.getFailure();
            }
        }
        return getResult(nodes);
    }


    private List<String> getResult(Node[] nodes) {
        Node temp = nodes[nodes.length - 1];
        if (null == temp) {
            return null;
        }
        List<String> res = new ArrayList<>(temp.getNum());
        for (int idx = nodes.length - 1; 0 == idx; ) {
            temp = nodes[idx];
            if (null == temp || null == temp.getResult()) {
                break;
            }
            res.add(temp.getResult());
            idx = temp.getPreIdx();
        }
        Collections.reverse(res);
        return res;
    }

    private void updateNode(int maxIndex, List<ValueLenResult> results, int nowIndex, Node[] nodes) {
        if (results.isEmpty()) {
            return;
        }
        Node node = new Node(maxIndex);
        for (ValueLenResult result : results) {
            int i = nowIndex - result.getLen();
            Node preNode = nodes[i];
            if (null == preNode) {
                continue;
            }
            if (preNode.getNum() < node.getNum()) {
                node.setNum(preNode.getNum() + 1);
                node.setPreIdx(i);
                node.setResult(result.getV());
            }
        }
        if (null != node.getResult()) {
            nodes[nowIndex] = node;
        }
    }

    @Getter
    private static class KvData implements TrieData<ValueLenResult> {
        private final String value;
        private final ValueLenResult result;

        public KvData(Map.Entry<String, ?> entry) {
            this.value = entry.getKey().toLowerCase(Locale.ROOT);
            this.result = new ValueLenResult(value.length(), entry.getValue().toString());
        }
    }

    @Data
    private static class Node {
        private int num;
        private String result;
        private int preIdx;

        public Node(int num) {
            this.num = num;
        }
    }
}
