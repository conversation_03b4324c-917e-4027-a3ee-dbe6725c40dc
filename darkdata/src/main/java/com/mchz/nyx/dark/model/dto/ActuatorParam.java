package com.mchz.nyx.dark.model.dto;

import com.mchz.nyx.dark.rank.RankStage;
import com.mchz.nyx.dark.retrieval.RetrievalRecallStage;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Collections;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/2/24
 */
@Getter
@AllArgsConstructor
public class ActuatorParam {
    private final List<RetrievalRecallStage> recallStages;
    private final List<RankStage> rankStages;
    private final List<AutoCloseable> closeableList;
    private final boolean commentOverlay;

    public static ActuatorParam empty() {
        return new ActuatorParam(Collections.emptyList(), Collections.emptyList(), Collections.emptyList(), false);
    }
}
