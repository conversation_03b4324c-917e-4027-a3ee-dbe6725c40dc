package com.mchz.nyx.dark.factory;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.mchz.nyx.dark.engine.*;
import com.mchz.nyx.dark.engine.algorithm.MinNumSplit;
import com.mchz.nyx.dark.engine.impl.MapTranslateEngine;
import com.mchz.nyx.dark.engine.impl.MapTranslatePlusEngine;
import com.mchz.nyx.dark.engine.impl.McRulesEngine;
import com.mchz.nyx.dark.model.config.DarkConfig;
import com.mchz.nyx.dark.model.config.EngineConfig;
import com.mchz.nyx.dark.model.dto.StdDetailDTO;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.lang.ref.WeakReference;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2023/4/14 11:39
 */
@Slf4j
public abstract class AbstractEngineFactory implements EngineFactory, AutoCloseable {
    protected final EngineConfig config;
    private final RulesEngine rulesEngine;
    private WeakReference<MinNumSplit> matchRef;

    public AbstractEngineFactory(@NonNull EngineConfig config) {
        this.config = config;
        this.rulesEngine = new McRulesEngine();
    }

    @Override
    public RulesEngine createRulesEngine() {
        return rulesEngine;
    }

    @Override
    public abstract VectorConvEngine createSimilarityEngine();

    @Override
    public abstract YunCeEngine createLLMEngine(DarkConfig config);

    @Override
    public TranslateEngine createTranslateEngine(DarkConfig config) {
        MinNumSplit match = getMatch();
        TransColumnFun engine = translateColumnFun(config);
        if (null == engine) {
            return new MapTranslateEngine(match);
        }
        return new MapTranslatePlusEngine(match, engine);
    }

    @Override
    public HistoryRetrievalEngine createHistoryRetrievalEngine(DarkConfig config, StdDetailDTO rule) {
        return null;
    }

    protected abstract TransColumnFun translateColumnFun(DarkConfig config);

    protected Map<String, ?> loadTransDict() {
        String uri = config.getTransDictPath();
        JSONObject json;
        final String http = "http";
        if (StrUtil.startWith(uri, http, true)) {
            try {
                json = JSONUtil.parseObj(HttpUtil.get(uri));
            } catch (Exception e) {
                return null;
            }
        } else {
            File file = FileUtil.file(uri);
            if (!FileUtil.exist(file)) {
                return null;
            }
            json = JSONUtil.readJSONObject(file, StandardCharsets.UTF_8);
        }
        if (json.isEmpty()) {
            return null;
        }
        return json;
    }

    protected List<Map<String, ?>> loadTransDictList() {
        return null;
    }

    private MinNumSplit getMatch() {
        if (null != matchRef) {
            MinNumSplit match = matchRef.get();
            if (null != match) {
                return match;
            }
        }
        Map<String, ?> dict = loadTransDict();
        List<Map<String, ?>> maps = loadTransDictList();
        MinNumSplit match;
        if (CollUtil.isNotEmpty(maps)) {
            if (MapUtil.isNotEmpty(dict)) {
                maps.add(dict);
            }
            match = MinNumSplit.of(maps);
        } else if (MapUtil.isNotEmpty(dict)) {
            match = MinNumSplit.of(dict);
        } else {
            return null;
        }
        matchRef = new WeakReference<>(match);
        return match;
    }

    @Override
    public void close() {
        matchRef = null;
    }
}
