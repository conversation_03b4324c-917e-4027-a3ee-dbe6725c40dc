package com.mchz.nyx.dark.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.PatternPool;
import cn.hutool.core.text.finder.CharFinder;
import cn.hutool.core.text.finder.PatternFinder;
import cn.hutool.core.text.finder.StrFinder;
import cn.hutool.core.util.CharUtil;
import cn.hutool.core.util.StrUtil;
import com.mchz.nyx.dark.common.enums.CompareSymbolType;
import com.mchz.nyx.dark.common.enums.ConditionSymbolType;
import com.mchz.nyx.dark.engine.expression.*;
import com.mchz.nyx.dark.exception.RuleException;
import com.mchz.nyx.dark.model.definition.SegmentWay;
import lombok.experimental.UtilityClass;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/11/16 16:50
 */
@UtilityClass
public class ExpressionUtil {
    private final char PARENTHESES_START = '(';
    private final char PARENTHESES_END = ')';

    public SubExpression subExpression(String expression) {
        if (StrUtil.isBlank(expression) || !expression.startsWith(StrUtil.BRACKET_START) || !expression.endsWith(StrUtil.BRACKET_END)) {
            throw new RuleException("【表达式】子串 " + expression);
        }
        int i = expression.indexOf(StrUtil.C_COLON);
        if (i < 0) {
            throw new RuleException("【表达式】子串 " + expression);
        }
        String l = expression.substring(1, i);
        String r = expression.substring(i + 1, expression.length() - 1);
        if (r.isEmpty()) {
            if (l.isEmpty()) {
                throw new RuleException("【表达式】子串 " + expression);
            }
            return SubExpression.suffix(parseInt(l));
        } else {
            if (l.isEmpty()) {
                return SubExpression.prefix(parseInt(r));
            }
            return SubExpression.mid(parseInt(l), parseInt(r));
        }
    }

    public SegmentExpression segmentation(SegmentWay way) {
        if (null == way.getType()) {
            return null;
        }
        switch (way.getType()) {
            case 1:
                boolean caseInsensitive = Boolean.TRUE.equals(way.getCaseInsensitive());
                if (1 == way.getSeparator().length()) {
                    return new SegmentExpression.SplitSegment(new CharFinder(way.getSeparator().charAt(0), caseInsensitive), way.getExpectedNum());
                } else {
                    return new SegmentExpression.SplitSegment(new StrFinder(way.getSeparator(), caseInsensitive), way.getExpectedNum());
                }
            case 2:
                return new SegmentExpression.SplitSegment(new PatternFinder(PatternPool.get(way.getSeparator(), Pattern.DOTALL)), way.getExpectedNum());
            case 3:
                return new SegmentExpression.SubSegment(way.getSubStrList().stream().map(ExpressionUtil::subExpression).collect(Collectors.toList()));
            case 4:
                return new SegmentExpression.IntervalSegment(way.getInterval());
            case 5:
                return new SegmentExpression.SplitSegment(new SegmentExpression.MutationFinder(), way.getExpectedNum());
            default:
                return null;
        }
    }

    public Set<Integer> indexes(String expression) {
        final int maxLen = 100;
        if (StrUtil.isEmpty(expression) || expression.length() > maxLen) {
            throw new RuleException("【表达式】下标索引 " + expression);
        }
        List<String> split = StrUtil.split(expression, CharUtil.AMP, true, true);
        Set<Integer> set = new HashSet<>();
        for (String s : split) {
            int index = s.indexOf(StrUtil.C_COMMA);
            if (index > 0) {
                indexRange(s, index, set);
            } else {
                switch (s) {
                    case "A":
                        set.add(0);
                        break;
                    case "R":
                        set.add(-1);
                        break;
                    default:
                        set.add(parseInt(s));
                }
            }
        }
        return set;
    }

    public ValueExpression<Integer> lenLimit(String expression) {
        final int maxLen = 100;
        if (StrUtil.isEmpty(expression) || expression.length() > maxLen) {
            throw new RuleException("【表达式】范围 " + expression);
        }
        List<String> split = StrUtil.split(expression, CharUtil.AMP, true, true);
        List<ValueExpression<Integer>> ret = new ArrayList<>(8);
        Set<Integer> set = new HashSet<>();
        for (String s : split) {
            int index = s.indexOf(StrUtil.C_COMMA);
            if (index > 0) {
                ret.add(lenRange(s, index));
            } else {
                set.add(parseInt(s));
            }
        }
        if (CollUtil.isNotEmpty(set)) {
            ret.add(new InExpression<>(set));
        }
        if (1 == ret.size()) {
            return ret.get(0);
        }
        return new CompoundExpression<>(ret);
    }

    private void indexRange(String expression, int index, Set<Integer> set) {
        int lastIndex = expression.length() - 1;
        if (1 == index || index + 1 == lastIndex) {
            return;
        }

        int l = parseInt(expression.substring(1, index));
        int r = parseInt(expression.substring(index + 1, lastIndex));

        IntStream.range(l, r + 1).forEach(set::add);
    }

    private ValueExpression<Integer> lenRange(String expression, int index) {
        CompareSymbolType symbolType;
        DataExpression<Integer> lE;
        if (1 == index) {
            lE = null;
        } else {
            String l = expression.substring(1, index);
            char c = expression.charAt(0);
            if (c == CharUtil.BRACKET_START) {
                symbolType = CompareSymbolType.GTE;
            } else if (c == PARENTHESES_START) {
                symbolType = CompareSymbolType.GT;
            } else {
                throw new RuleException("【表达式】范围 " + expression);
            }
            lE = new DataExpression<>(symbolType, parseInt(l));
        }
        DataExpression<Integer> rE;
        int lastIndex = expression.length() - 1;
        if (index + 1 == lastIndex) {
            return lE;
        }
        String r = expression.substring(index + 1, lastIndex);
        char c = expression.charAt(lastIndex);
        if (c == CharUtil.BRACKET_END) {
            symbolType = CompareSymbolType.LTE;
        } else if (c == PARENTHESES_END) {
            symbolType = CompareSymbolType.LT;
        } else {
            throw new RuleException("【表达式】范围 " + expression);
        }
        rE = new DataExpression<>(symbolType, parseInt(r));
        if (null == lE) {
            return rE;
        }
        return new RangeExpression<>(ConditionSymbolType.AND, lE, rE);
    }

    private int parseInt(String num) {
        try {
            return Integer.parseInt(num);
        } catch (Exception e) {
            throw new RuleException("【表达式】数值转换 " + num);
        }
    }
}
