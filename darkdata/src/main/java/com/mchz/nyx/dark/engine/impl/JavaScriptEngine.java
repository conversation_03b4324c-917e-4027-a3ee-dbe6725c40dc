package com.mchz.nyx.dark.engine.impl;

import com.mchz.nyx.dark.common.constants.DarkConst;
import com.mchz.nyx.dark.engine.CustomScriptEngine;
import com.mchz.nyx.dark.exception.RuleException;
import lombok.SneakyThrows;

import javax.script.Invocable;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.script.ScriptException;

public class JavaScriptEngine implements CustomScriptEngine {
    private final Invocable env;

    @SneakyThrows
    public JavaScriptEngine(String customCode) {
        ScriptEngineManager scriptEngineManager = new ScriptEngineManager();
        ScriptEngine engine = scriptEngineManager.getEngineByName("JavaScript");
        engine.eval(customCode);
        this.env = (Invocable) engine;
    }

    @Override
    public boolean eval(String data) {
        Object object;
        try {
            object = env.invokeFunction(DarkConst.METHOD, data);
        } catch (ScriptException e) {
            throw new RuleException(String.format("代码语法错误 %s", e.getMessage()), e);
        } catch (NoSuchMethodException e) {
            throw new RuleException(String.format("未找到方法 %s", DarkConst.METHOD));
        }
        if (!(object instanceof Boolean)) {
            throw new RuleException("返回值不是boolean值");
        }
        return (boolean) object;
    }
}
