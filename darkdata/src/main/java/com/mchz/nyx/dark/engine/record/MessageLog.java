package com.mchz.nyx.dark.engine.record;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <p>
 * 日志
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/11/12 11:15
 */

@Getter
@AllArgsConstructor
@NoArgsConstructor
public class MessageLog implements RecordLog {
    private String message;

    public MessageLog(Throwable throwable) {
        StackTraceElement[] stackTraceElement;
        String msg;
        if (throwable instanceof NullPointerException) {
            stackTraceElement = throwable.getStackTrace();
            msg = 0 == stackTraceElement.length ? "NullPointer" : "NullPointer:" + stackTraceElement[0].toString();
        } else {
            stackTraceElement = throwable.getStackTrace();
            msg = 0 == stackTraceElement.length ? throwable.getMessage() : throwable.getMessage() + ":" + stackTraceElement[0].toString();
        }
        this.message = msg;
    }
}
