package com.mchz.nyx.dark.engine.classifier;

import com.mchz.nyx.dark.engine.rule.RuleManager;
import lombok.AllArgsConstructor;

import java.util.function.Function;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/11/22 14:14
 */
@AllArgsConstructor
public class FunctionClassifier implements BaseClassifier<String> {

    private final Function<String, Boolean> function;

    @Override
    public boolean classify(String data, RuleManager manager) {
        return function.apply(data);
    }
}
