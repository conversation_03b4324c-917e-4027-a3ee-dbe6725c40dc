package com.mchz.nyx.dark.util.bloomfilter;

import cn.hutool.bloomfilter.BitSetBloomFilter;
import org.roaringbitmap.RoaringBitmap;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/12
 */
public class CustomBloomFilter {
    private final int m;
    private final int k;
    private final RoaringBitmap bitmap;

    public CustomBloomFilter(long expectedElements, double falsePositiveProbability) {
        double ln2 = Math.log(2);
        this.m = (int) (-expectedElements * Math.log(falsePositiveProbability) / (ln2 * ln2));
        this.k = (int) Math.round((double) m / expectedElements * ln2);
        this.bitmap = new RoaringBitmap();
    }

    public boolean contains(String str) {
        for (int i = 0; i < k; i++) {
            int hash = BitSetBloomFilter.hash(str, i);
            int index = Math.abs(hash % m);
            if (!bitmap.contains(index)) {
                return false;
            }
        }
        return true;
    }


    public void add(String str) {
        for (int i = 0; i < k; i++) {
            int hash = BitSetBloomFilter.hash(str, i);
            int index = Math.abs(hash % m);
            bitmap.add(index);
        }
    }
}
