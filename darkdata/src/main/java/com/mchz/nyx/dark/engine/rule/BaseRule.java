package com.mchz.nyx.dark.engine.rule;

import com.mchz.nyx.common.exception.InterruptedJobException;
import com.mchz.nyx.dark.engine.record.MessageLog;
import com.mchz.nyx.dark.engine.record.RecordLog;
import com.mchz.nyx.dark.engine.record.TaskRecord;
import com.mchz.nyx.dark.model.rule.Facts;
import com.mchz.nyx.dark.model.rule.RuleResult;
import lombok.Data;
import lombok.NonNull;

import java.util.Objects;

/**
 * <p>
 * 规则基类
 * </p >
 *
 * <AUTHOR>
 * @date 2020/6/24 18:10
 */
@Data
public abstract class BaseRule<T> implements Rule {
    /**
     * 规则识别名称
     */
    private final String name;
    /**
     * 优先级，越小越靠前
     */
    private final int priority;

    /**
     * 触发逻辑
     *
     * @param payload 参数
     * @return {@code true} - 触发，{@code false} - 不触发
     */
    protected abstract boolean when(T payload);

    /**
     * 执行逻辑
     *
     * @param payload 参数
     * @param result  结果集
     * @param manager 全局管理
     * @return 日志对象
     */
    protected abstract RecordLog then(T payload, RuleResult result, RuleManager manager);

    @Override
    public boolean evaluate(Facts facts) {
        return when(getPayload(facts));
    }

    @Override
    public void execute(Facts facts) throws Exception {
        TaskRecord record = facts.getRecord();
        record.start();
        try {
            record.stop(then(getPayload(facts), facts.getResult(), facts.getManager()));
        } catch (InterruptedJobException e) {
            throw e;
        } catch (Exception e) {
            record.stop(new MessageLog(e));
        }
    }

    /**
     * 获取载荷
     *
     * @param facts {@link Facts}
     * @return 载荷
     */
    protected abstract T getPayload(Facts facts);

    @Override
    public int compareTo(@NonNull Rule rule) {
        if (getPriority() < rule.getPriority()) {
            return -1;
        } else if (getPriority() > rule.getPriority()) {
            return 1;
        } else {
            return getName().compareTo(rule.getName());
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        BaseRule<?> baseRule = (BaseRule<?>) o;
        return priority == baseRule.priority && Objects.equals(name, baseRule.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(getClass().getSimpleName(), name);
    }
}
