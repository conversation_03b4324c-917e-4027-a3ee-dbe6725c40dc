package com.mchz.nyx.dark.retrieval;

import cn.hutool.core.util.StrUtil;
import com.mchz.nyx.dark.common.enums.HitType;
import com.mchz.nyx.dark.engine.NlpEngine;
import com.mchz.nyx.dark.engine.TranslateEngine;
import com.mchz.nyx.dark.model.EngineContext;
import com.mchz.nyx.dark.model.TargetResult;
import com.mchz.nyx.dark.model.meta.MetaColumnData;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/6
 */
@AllArgsConstructor
public class SimilarNameStep implements SimilarSingleStep {
    private final TranslateEngine translate;
    private final float baseline;

    @Override
    public boolean execute(EngineContext context, NlpEngine nlpEngine, MetaColumnData column) {
        String name = column.getMeta().getColumnName();
        String transName = translate.translate(name);
        if (StrUtil.isNotEmpty(transName)) {
            column.addAlias(transName, false);
            List<TargetResult> res = nlpEngine.similarityAnalysis(context, transName, HitType.NAME_SIMILAR, baseline);
            return column.addAllTarget(res);
        }
        return false;
    }
}
