package com.mchz.nyx.dark.engine.filter;

import com.mchz.nyx.dark.common.enums.LoadType;
import com.mchz.nyx.dark.common.enums.MatchType;
import lombok.Builder;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/11/22 16:57
 */

@Data
@Builder
public class DictPayload implements Identifiable, ExpectAble {
    private int id;
    private long dictId;
    private LoadType loadType;
    private MatchType matchType;

    private String suffix;
    private boolean expect;
}
