package com.mchz.nyx.dark.model.definition;

import com.mchz.nyx.dark.common.enums.ParamColumn;
import com.mchz.nyx.dark.common.enums.ParamTable;
import com.mchz.nyx.dark.common.enums.RuleLoad;
import com.mchz.nyx.dark.model.rule.RuleIdInfo;
import lombok.Data;

import java.util.EnumMap;
import java.util.List;
import java.util.Map;


/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/11/16 13:34
 */
@Data
public class RuleDetail {

    private RuleIdInfo info;

    private RuleLoad load;

    private SingleRuleDetail rule;

    private List<SingleRuleDetail> rules;

    private Map<ParamColumn, Object> paramColumnRule;
    private Map<ParamTable, Object> paramTableRule;

    public void addColumnRule(ParamColumn type, Object rule) {
        if (null == paramColumnRule) {
            paramColumnRule = new EnumMap<>(ParamColumn.class);
            load = RuleLoad.PARAM_C;
        }
        paramColumnRule.put(type, rule);
    }

    public void addTableRule(ParamTable type, Object rule) {
        if (null == paramTableRule) {
            paramTableRule = new EnumMap<>(ParamTable.class);
            load = RuleLoad.PARAM_T;
        }
        paramTableRule.put(type, rule);
    }
}


