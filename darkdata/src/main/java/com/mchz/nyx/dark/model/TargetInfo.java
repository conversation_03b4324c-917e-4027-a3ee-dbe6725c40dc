package com.mchz.nyx.dark.model;

import com.mchz.nyx.dark.common.enums.AddName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.util.Collection;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Stream;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2023/4/4 18:46
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
public class TargetInfo {
    /**
     * 目标id
     */
    private Long id;

    /**
     * 目标名称
     */
    private String name;

    /**
     * 是实体 即name具有明确的指代
     */
    private boolean entity;

    /**
     * 名称
     */
    private Map<AddName, List<String>> addName;

    private Long classifyId;

    private Integer level;

    public TargetInfo(Long id, String name) {
        this(id, name, true);
    }

    public TargetInfo(Long id, String name, boolean entity) {
        this.id = id;
        this.name = name;
        this.entity = entity;
    }

    public TargetInfo(Long id, Long classifyId, Integer level) {
        this.id = id;
        this.classifyId = classifyId;
        this.level = level;
        this.entity = false;
    }

    public TargetInfo(Long id, String name, Long classifyId, Integer level) {
        this.id = id;
        this.name = name;
        this.entity = false;
        this.classifyId = classifyId;
        this.level = level;
    }

    public void put(AddName name, List<String> list) {
        if (null == list) {
            return;
        }
        if (null == addName) {
            addName = new EnumMap<>(AddName.class);
        }
        addName.put(name, list);
    }

    public Stream<String> getAliasStream() {
        return null == addName ? Stream.empty() : addName.values().stream().flatMap(Collection::stream);
    }

    public List<String> get(AddName name) {
        if (null == addName) {
            return null;
        }
        return addName.get(name);
    }
}
