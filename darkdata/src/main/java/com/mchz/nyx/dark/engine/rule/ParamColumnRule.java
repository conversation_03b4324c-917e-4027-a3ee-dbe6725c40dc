package com.mchz.nyx.dark.engine.rule;

import cn.hutool.core.util.ObjUtil;
import com.mchz.nyx.dark.common.constants.RuleConst;
import com.mchz.nyx.dark.engine.classifier.BaseClassifier;
import com.mchz.nyx.dark.engine.record.MessageLog;
import com.mchz.nyx.dark.engine.record.RecordLog;
import com.mchz.nyx.dark.model.rule.ColumnPayload;
import com.mchz.nyx.dark.model.rule.RuleIdInfo;
import com.mchz.nyx.dark.model.rule.RuleParam;
import com.mchz.nyx.dark.model.rule.RuleResult;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2023/3/20 15:54
 */
public class ParamColumnRule extends BaseColumnRule {

    private final RuleIdInfo info;
    private final BaseClassifier<ColumnPayload> classifier;

    public ParamColumnRule(RuleParam config, BaseClassifier<ColumnPayload> classifier) {
        super(config.getName(), ObjUtil.defaultIfNull(config.getPriority(), RuleConst.DEFAULT_MIN_PRIORITY));
        this.info = config.getTarget();
        this.classifier = classifier;
    }

    @Override
    protected boolean when(ColumnPayload payload) {
        return true;
    }

    @Override
    protected RecordLog then(ColumnPayload payload, RuleResult result, RuleManager manager) {
        if (classifier.classify(payload, manager)) {
            result.add(info);
        }
        return new MessageLog(getName());
    }
}
