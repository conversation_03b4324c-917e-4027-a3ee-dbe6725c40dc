package com.mchz.nyx.dark.model.config;

import com.mchz.nyx.dark.common.constants.RuleConst;
import com.mchz.nyx.dark.model.dto.StandardLoadContext;
import lombok.Builder;
import lombok.Getter;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2021/6/4 19:05
 */
@Getter
@Builder
public class DarkConfig {
    @Builder.Default
    private int cacheSize = RuleConst.DEFAULT_CACHE_SIZE;
    private boolean loadValues;
    private boolean fast;

    private boolean autoColName;
    private boolean autoColComment;
    private boolean recallHis;
    private boolean nlp;
    private boolean llm;
    private boolean semantic;
    private String url;
    private String llmUrl;
    private int candidateCount;
    private boolean commentOverlay;
    @Builder.Default
    private int colNameBaseline = RuleConst.DEFAULT_BASELINE;
    @Builder.Default
    private int colCommentBaseline = RuleConst.DEFAULT_BASELINE;
    private Long stdId;
    private Integer industryId;
    private String industry;
    private StandardLoadContext context;
}
