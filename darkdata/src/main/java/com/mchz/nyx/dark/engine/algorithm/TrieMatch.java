package com.mchz.nyx.dark.engine.algorithm;

import cn.hutool.core.util.CharUtil;
import cn.hutool.core.util.StrUtil;
import com.mchz.nyx.common.util.CommUtil;
import lombok.AccessLevel;
import lombok.Getter;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;

/**
 * <p>
 * 前缀树匹配
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/11/12 19:06
 */
@Getter(AccessLevel.PROTECTED)
public class TrieMatch<T> {

    private final boolean reverse;
    private final TrieNode<T> root;

    public <E extends TrieData<T>> TrieMatch(List<E> keywords) {
        this(keywords, false);
    }

    public <E extends TrieData<T>> TrieMatch(List<E> keywords, boolean reverse) {
        this(keywords, reverse, false);
    }

    public <E extends TrieData<T>> TrieMatch(List<E> keywords, boolean reverse, boolean set) {
        this.reverse = reverse;
        this.root = buildTree(keywords, TrieData::getValue, (e, t) -> t.addResult(e.getResult()), set);
    }

    public TrieMatch(Map<String, List<T>> keywords, boolean reverse, boolean set) {
        this.reverse = reverse;
        this.root = buildTree(keywords.entrySet(), Map.Entry::getKey, (e, t) -> t.addResult(e.getValue()), set);
    }

    public List<T> find(String text) {
        List<T> results = new ArrayList<>();
        TrieNode<T> now = root;
        if (reverse) {
            for (int i = text.length() - 1; i >= 0; i--) {
                TrieNode<T> temp = now.getChildByName(text.charAt(i));
                if (null == temp) {
                    break;
                }
                now = temp;
                results.addAll(now.getResults());
            }
        } else {
            char[] chars = text.toCharArray();
            for (char aChar : chars) {
                TrieNode<T> temp = now.getChildByName(aChar);
                if (null == temp) {
                    break;
                }
                now = temp;
                results.addAll(now.getResults());
            }
        }

        return results;
    }

    public List<T> match(String text) {
        return match(text, false);
    }

    public List<T> match(String text, boolean letterUpper) {
        Collection<T> result = null;
        TrieNode<T> now = root;
        if (reverse) {
            int i = text.length() - 1;
            for (; i >= 0; i--) {
                TrieNode<T> temp = now.getChildByName(letterUpper ? CommUtil.letterUpper(text.charAt(i)) : text.charAt(i));
                if (null == temp) {
                    break;
                }
                now = temp;
            }
            if (0 == i) {
                result = now.getResults();
            }
        } else {
            char[] chars = text.toCharArray();
            int i = 0;
            for (char aChar : chars) {
                TrieNode<T> temp = now.getChildByName(letterUpper ? CommUtil.letterUpper(aChar) : aChar);
                if (null == temp) {
                    break;
                }
                now = temp;
                i++;
            }
            if (i == chars.length) {
                result = now.getResults();
            }
        }
        if (null == result) {
            return Collections.emptyList();
        }
        return new ArrayList<>(result);
    }

    private <E> TrieNode<T> buildTree(Collection<E> list, Function<E, String> getValue, BiConsumer<E, TrieNode<T>> addRes, boolean set) {
        TrieNode<T> root = new TrieNode<>(CharUtil.SPACE, set);
        if (null == list) {
            return root;
        }
        for (E item : list) {
            String value = getValue.apply(item);
            if (StrUtil.isEmpty(value)) {
                continue;
            }
            addRes.accept(item, buildTreeNode(value, root, set));
        }
        return root;
    }

    private TrieNode<T> buildTreeNode(String value, TrieNode<T> now, boolean set) {
        char[] chars = value.toCharArray();
        if (reverse) {
            for (int i = chars.length - 1; i >= 0; i--) {
                now = buildNode(chars[i], now, set);
            }
        } else {
            for (char aChar : chars) {
                now = buildNode(aChar, now, set);
            }
        }
        return now;
    }

    protected TrieNode<T> buildNode(char ch, TrieNode<T> now, boolean set) {
        TrieNode<T> temp = now.getChildByName(ch);
        if (null != temp) {
            return temp;
        }
        TrieNode<T> newNode = new TrieNode<>(ch, set);
        now.addChild(newNode);
        return newNode;
    }

    protected static class TrieNode<T> {

        private final char ch;

        private final Map<Character, TrieNode<T>> children;

        @Getter
        private final Collection<T> results;

        public TrieNode(char ch, boolean set) {
            this.ch = ch;
            this.children = new HashMap<>();
            this.results = set ? new HashSet<>() : new ArrayList<>();
        }

        public char getName() {
            return ch;
        }

        public TrieNode<T> getChildByName(char ch) {
            return children.get(ch);
        }

        public void addChild(TrieNode<T> child) {
            children.put(child.getName(), child);
        }

        public void addResult(T data) {
            results.add(data);
        }

        public void addResult(List<T> data) {
            results.addAll(data);
        }

        @Override
        public String toString() {
            return CharUtil.toString(ch);
        }
    }

}
