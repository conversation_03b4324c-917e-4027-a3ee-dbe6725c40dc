package com.mchz.nyx.dark.engine.classifier;

import com.mchz.nyx.dark.engine.rule.RuleManager;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2023/4/28 17:03
 */
public class FixedClassifier implements BaseClassifier<String> {
    private final boolean expect;
    private final String value;
    private final int type;
    private final int start;

    public FixedClassifier(boolean expect, boolean ignoreCase, String value, int type, int start) {
        this.expect = expect;
        this.value = value;
        this.start = start;
        int temp;
        switch (type) {
            case 0:
                if (0 == start) {
                    temp = ignoreCase ? 1 : 0;
                } else {
                    temp = ignoreCase ? 3 : 2;
                }
                break;
            case 1:
                temp = ignoreCase ? 5 : 4;
                break;
            case 2:
                temp = ignoreCase ? 7 : 6;
                break;
            default:
                throw new IllegalArgumentException("type参数异常" + type);

        }
        this.type = temp;
    }

    @Override
    public boolean classify(String payload, RuleManager manager) {
        switch (type) {
            case 0:
                return expect == value.equals(payload);
            case 1:
                return expect == value.equalsIgnoreCase(payload);
            case 2:
                if (payload.length() != start + value.length()) {
                    return !expect;
                }
            case 4:
                return expect == payload.startsWith(value, start);
            case 3:
                if (payload.length() - start != value.length()) {
                    return !expect;
                }
            case 5:
                return expect == payload.regionMatches(true, start, value, 0, value.length());
            case 6:
                return expect == payload.endsWith(value);
            case 7:
                return expect == payload.regionMatches(true, payload.length() - value.length(), value, 0, value.length());
            default:
                return false;
        }
    }
}
