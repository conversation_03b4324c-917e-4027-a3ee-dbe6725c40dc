package com.mchz.nyx.dark.util.nlp.tokenizer;

import cn.hutool.core.util.StrUtil;
import com.mchz.nyx.dark.util.nlp.Vocabulary;

import java.util.ArrayList;
import java.util.List;

@SuppressWarnings("all")
public class WordpieceTokenizer extends SimpleTokenizer {
    private final Vocabulary vocabulary;
    private final String unknown;
    private final int maxInputChars;

    public WordpieceTokenizer(Vocabulary vocab, String unkToken) {
        this(vocab, unkToken, 100);
    }

    public WordpieceTokenizer(Vocabulary vocab, String unkToken, int maxInputCharsPerWord) {
        this.vocabulary = vocab;
        this.unknown = unkToken;
        this.maxInputChars = maxInputCharsPerWord;
    }

    @Override
    public List<String> tokenize(String text) {
        return tokenize(text, true);
    }

    public List<String> tokenize(String text, boolean useUnkToken) {
        // 注意, 这里的text是经过分词的
        // 对于中文来说，text指的是字或者词
        // 对于英文来说，text指的是单词或者短语
        StringBuilder sb = new StringBuilder();
        List<String> subTokens = new ArrayList<>();
        List<String> outputTokens = new ArrayList<>();
        for (String token : text.split(StrUtil.SPACE)) {
            if (token.length() > maxInputChars) {
                outputTokens.add(unknown);
                continue;
            }
            char[] chars = token.toCharArray();
            boolean isBad = false;
            int start = 0;
            subTokens.clear();
            String currentSubString;
            while (start < chars.length) {
                int end = chars.length;
                currentSubString = null;
                while (start < end) {
                    sb.setLength(0);
                    sb.append(token, start, end);
                    if (start > 0) {
                        sb.insert(0, "##");
                    }
                    String subString = sb.toString();
                    if (vocabulary.contains(subString)) {
                        currentSubString = subString;
                        break;
                    }
                    end--;
                }
                if (null == currentSubString) {
                    isBad = true;
                    break;
                }
                subTokens.add(currentSubString);
                if (subTokens.size() > maxInputChars) {
                    throw new IllegalStateException("Too many subTokens for: '" + text + '\'');
                }
                start = end;
            }
            if (isBad) {
                if (useUnkToken) {
                    outputTokens.add(unknown);
                } else {
                    outputTokens.add(token);
                }
            } else {
                outputTokens.addAll(subTokens);
            }
        }
        return outputTokens;
    }


}
