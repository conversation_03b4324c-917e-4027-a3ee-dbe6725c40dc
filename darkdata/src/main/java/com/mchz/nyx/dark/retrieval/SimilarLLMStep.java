package com.mchz.nyx.dark.retrieval;

import cn.hutool.core.lang.Pair;
import com.mchz.nyx.dark.common.enums.HitType;
import com.mchz.nyx.dark.engine.NlpEngine;
import com.mchz.nyx.dark.engine.YunCeEngine;
import com.mchz.nyx.dark.model.EngineContext;
import com.mchz.nyx.dark.model.meta.MetaColumnData;
import lombok.AllArgsConstructor;

import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/6
 */
@AllArgsConstructor
public class SimilarLLMStep implements SimilarEntiretyStep {
    private final YunCeEngine client;
    private final float baseline;

    @Override
    public void execute(EngineContext context, NlpEngine nlpEngine, List<MetaColumnData> list) {
        List<Pair<MetaColumnData, String>> metaColumnDataStringPair = client.askLLM(context, list, c -> c.getMeta().getColumnName());
        metaColumnDataStringPair.stream()
            .peek(v -> v.getKey().addAlias(v.getValue()))
            .map(v -> CompletableFuture.supplyAsync(() -> nlpEngine.similarityAnalysis(context, v.getValue(), HitType.LLM, baseline))
                .thenAccept(r -> v.getKey().addAllTarget(r)))
            .forEach(CompletableFuture::join);
    }
}
