package com.mchz.nyx.dark.common.enums;

/**
 * <p>
 * 字典加载类型
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/12/3 11:10
 */
public enum LoadType {
    /**
     * 内置
     */
    INNER,
    INNER_VALUE,
    /**
     * 外部配置
     */
    CUSTOM,
    /**
     * 内嵌
     */
    EMBED,
    /**
     * 关键词
     */
    KEYWORD;

    public static LoadType getType(Integer code) {
        LoadType[] values = values();
        if (null == code || code < 0 || code >= values.length) {
            return INNER;
        }
        return values[code];
    }

    public static LoadType getType(String name) {
        if (null == name) {
            return INNER;
        }
        return valueOf(name);
    }
}
