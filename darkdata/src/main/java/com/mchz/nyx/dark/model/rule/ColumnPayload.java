package com.mchz.nyx.dark.model.rule;

import com.mchz.nyx.common.enums.DataTypeGroup;
import com.mchz.nyx.dark.model.meta.IntDataInfo;
import com.mchz.nyx.dark.model.meta.NyxMetaColumn;
import com.mchz.nyx.dark.model.meta.StrDataInfo;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/14
 */
public interface ColumnPayload {

    NyxMetaColumn getMeta();

    default String getName(){
        return getMeta().getColumnName();
    }

    default DataTypeGroup getType() {
        return getMeta().getColumnType().getTypeGroup();
    }

    default String getComment(){
        return getMeta().getColumnComment();
    }

    boolean isLoad();

    int getTotal();

    int getNullNum();

    int getNonRepetitiveNum();

    List<StrDataInfo> getDataStr();

    List<IntDataInfo> getDataInt();
}
