package com.mchz.nyx.dark.engine.record;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/11/16 14:27
 */
@AllArgsConstructor
public class MultipleResultLog implements RecordLog {

    private final List<RecordLog> list;

    public MultipleResultLog(RecordLog... logs) {
        this.list = Arrays.asList(logs);
    }

    @Override
    public String getMessage() {
        return list.stream().map(RecordLog::getMessage).collect(Collectors.joining(StrUtil.COMMA));
    }
}
