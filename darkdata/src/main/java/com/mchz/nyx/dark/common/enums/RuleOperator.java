package com.mchz.nyx.dark.common.enums;

import lombok.AllArgsConstructor;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2021/12/21 10:37
 */
@AllArgsConstructor
public enum RuleOperator {
    /**
     * 与
     */
    AND(1),
    /**
     * 或
     */
    OR(2),
    /**
     * 不期望任一为真即为假,期望任一为真即为真
     */
    EXPECT(0);

    private final int code;

    public static RuleOperator getType(Integer code) {
        if (null == code) {
            return EXPECT;
        }
        for (RuleOperator value : values()) {
            if (code == value.code) {
                return value;
            }
        }
        return EXPECT;
    }
}
