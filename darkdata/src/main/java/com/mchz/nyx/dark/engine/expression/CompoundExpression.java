package com.mchz.nyx.dark.engine.expression;

import cn.hutool.core.collection.CollUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 多范围
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/11/17 9:34
 */
public class CompoundExpression<T extends Number & Comparable<T>> implements ValueExpression<T> {

    private final List<ValueExpression<T>> list;

    public CompoundExpression() {
        this.list = new ArrayList<>();
    }

    public CompoundExpression(List<ValueExpression<T>> list) {
        this.list = list;
    }

    public void add(ValueExpression<T> expression) {
        if (null != expression) {
            list.add(expression);
        }
    }


    public void addAll(List<ValueExpression<T>> expression) {
        if (CollUtil.isEmpty(expression)) {
            return;
        }
        expression.removeIf(Objects::isNull);
        list.addAll(expression);
    }

    @Override
    public boolean compare(T value) {
        if (list.isEmpty()) {
            return false;
        }
        for (ValueExpression<T> expression : list) {
            if (expression.compare(value)) {
                return true;
            }
        }
        return false;
    }
}
