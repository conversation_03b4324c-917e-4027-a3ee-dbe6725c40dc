package com.mchz.nyx.dark.util;

import cn.hutool.core.date.DateException;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.date.format.FastDateFormat;
import cn.hutool.core.lang.PatternPool;
import cn.hutool.core.util.*;
import com.mchz.nyx.common.util.CommUtil;
import lombok.experimental.UtilityClass;

import java.time.LocalDate;
import java.time.Year;
import java.util.*;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/11/11 18:55
 */
@UtilityClass
public class RuleFunctionUtil {
    private final char PARENTHESES_START = '(';
    private final char PARENTHESES_START_FULL = '（';
    private final char PARENTHESES_END = ')';
    private final char PARENTHESES_END_FULL = '）';
    private final char ROMAN_NUMERALS_10 = 'X';

    private final int[] POWER_11_2 = {1, 2, 4, 8, 5, 10, 9, 7, 3, 6, 1, 2, 4, 8, 5, 10, 9, 7, 3, 6, 1, 2, 4, 8};
    private final int CHINA_ID_LENGTH15 = 15;
    private final int CHINA_ID_LENGTH18 = 18;
    private final int CHINA_ID_LENGTH10 = 10;

    private final int LEN_BUSINESS_LICENSE = 15;

    private final int LEN_CA14 = 24;

    private final int LEN_IMO = 7;

    private final int[] POWER_USCC = {1, 3, 9, 27, 19, 26, 16, 17, 20, 29, 25, 13, 8, 24, 10, 30, 28};
    private final int LEN_USCC = POWER_USCC.length + 1;

    private final int[] POWER_ORGANIZATION_CODE = {3, 7, 9, 10, 5, 8, 4, 2};
    private final int INDEX_ORGANIZATION_CHECK = POWER_ORGANIZATION_CODE.length;
    private final int LEN_ORGANIZATION_CODE = INDEX_ORGANIZATION_CHECK + 1;

    private final int[] POWER_VIN = {8, 7, 6, 5, 4, 3, 2, 10, 0, 9, 8, 7, 6, 5, 4, 3, 2};
    private final int LEN_VIN = POWER_VIN.length;

    private final Set<Integer> TYPE_CODE = new HashSet<>(Arrays.asList(1, 2, 5, 8, 9));

    private final int[] CITY_CODES = {0, 5, 3, 7, 6, 4, 5, 1, 3, 1};
    private final Map<Character, Integer> TW_FIRST_CODE = new HashMap<>();
    private int thisYear;
    private final String[] WTB;

    public boolean isValidCard(String data) {
        switch (data.length()) {
            case CHINA_ID_LENGTH18:
                return isValidCard18(data, true);
            case CHINA_ID_LENGTH15:
                return isValidCard15(data);
            default:
                return false;
        }
    }

    public boolean isValidCard18(String data) {
        return isValidCard18(data, true);
    }

    public boolean isValidCard18(String idCard, boolean ignoreCase) {
        if (CHINA_ID_LENGTH18 != idCard.length()) {
            return false;
        }
        int[] value = toIntMod11(idCard, ignoreCase);
        if (null == value) {
            return false;
        }

        if (isNotValidCityCode(value[0], value[1])) {
            return false;
        }

        final int birthdayStartIndex = 6;
        if (!isValidBirthday(value, birthdayStartIndex)) {
            return false;
        }

        return isValidMod112(value);
    }

    public boolean isValidCard15(String idCard) {
        if (CHINA_ID_LENGTH15 != idCard.length()) {
            return false;
        }
        int[] value = toIntArray(idCard);
        if (null == value) {
            return false;
        }
        if (isNotValidCityCode(value[0], value[1])) {
            return false;
        }
        int i = 6;
        int[] birthday = {19, 0, 0};
        for (int j = 0; j < birthday.length; j++) {
            birthday[j] *= 10;
            birthday[j] += value[i++];
            birthday[j] *= 10;
            birthday[j] += value[i++];
        }
        return isValidBirthday(birthday[0], birthday[1], birthday[2]);
    }

    @SuppressWarnings("all")
    public boolean isValidTWCard(String idCard) {
        if (CHINA_ID_LENGTH10 != idCard.length()) {
            return false;
        }
        final Integer iStart = TW_FIRST_CODE.get(idCard.charAt(0));
        if (null == iStart) {
            return false;
        }
        switch (idCard.charAt(1)) {
            case '1':
            case '2':
                break;
            default:
                return false;
        }
        int[] value = toIntArray(idCard, 1, 9);
        if (null == value) {
            return false;
        }
        int sum = iStart / 10 + (iStart % 10) * 9;
        for (int i = 0, j = value.length - 1; i < value.length; i++, j--) {
            sum += value[i] * j;
        }
        sum += value[value.length - 1];
        return 0 == sum % 10;
    }

    /**
     * https://bajiu.cn/sfz/?rid=40
     */
    @SuppressWarnings("all")
    public boolean isValidHKCard(String idCard) {
        if (CHINA_ID_LENGTH10 != idCard.length()) {
            return false;
        }
        char c3 = idCard.charAt(7);
        char c4 = idCard.charAt(9);
        boolean check = (c3 == PARENTHESES_START && c4 == PARENTHESES_END) || (c3 == PARENTHESES_START_FULL && c4 == PARENTHESES_END_FULL);
        if (!check) {
            return false;
        }
        char c1 = idCard.charAt(0);
        if (!CharUtil.isLetterUpper(c1)) {
            return false;
        }
        int[] value = toIntArray(idCard, 1, 6);
        if (null == value) {
            return false;
        }
        char c2 = idCard.charAt(8);
        int c;
        if ('A' == c2) {
            c = 10;
        } else {
            c = toInt(c2);
            if (c < 0) {
                return false;
            }
        }
        int f = 8;
        int sum = (c1 - 'A' + 1) * f;
        for (int i : value) {
            sum += i * --f;
        }
        sum += c;
        return 0 == sum % 11;
    }


    /**
     * 主体身份代码(CA14)
     * 用于标识企业、分支机构、个体工商户等市场主体身份的唯一代码
     */
    public boolean isValidCa14(String data) {
        if (LEN_CA14 != data.length()) {
            return false;
        }
        int i = 9;
        final char c0 = '0';
        if (c0 != data.charAt(i)) {
            return false;
        }
        int[] value = toIntMod11(data, true);
        if (null == value) {
            return false;
        }
        if (isNotValidCityCode(value[0], value[1])) {
            return false;
        }
        if (!TYPE_CODE.contains(value[++i])) {
            return false;
        }
        if (!isValidBirthday(value, ++i)) {
            return false;
        }
        return isValidMod112(value);
    }

    /**
     * IMO
     */
    public boolean isValidImo(String data) {
        if (LEN_IMO != data.length()) {
            return false;
        }
        int[] value = toIntArray(data);
        if (null == value) {
            return false;
        }

        int sum = 0;
        for (int i = 0, j = LEN_IMO; i < LEN_IMO - 1; i++, j--) {
            sum += value[i] * j;
        }
        return sum % 10 == value[LEN_IMO - 1];
    }

    /**
     * <a href="https://zh.wikisource.org/zh-hans/GB_32100-2015_%E6%B3%95%E4%BA%BA%E5%92%8C%E5%85%B6%E4%BB%96%E7%BB%84%E7%BB%87%E7%BB%9F%E4%B8%80%E7%A4%BE%E4%BC%9A%E4%BF%A1%E7%94%A8%E4%BB%A3%E7%A0%81%E7%BC%96%E7%A0%81%E8%A7%84%E5%88%99">统一社会信用代码</a>
     */
    @SuppressWarnings("all")
    public boolean isValidUSCC(String data) {
        if (LEN_USCC != data.length()) {
            return false;
        }
        char[] chars = data.toCharArray();

        //统一社会信用代码中登记管理部门代码
        char c1 = chars[0];
        //统一社会信用代码中机构类别代码
        int v2 = toInt(chars[1]);
        if (v2 < 1) {
            return false;
        }

        int v1 = toInt(c1);
        final int[] types = {0, 3, 1, 5, 1, 3, 2, 2, 1, 3};
        final int other = 9;
        if (v1 > 0) {
            if (v2 > types[v1]) {
                if (other != v2 || other == v1) {
                    return false;
                }
            }
        } else {
            switch (c1) {
                case 'A':
                    if (v2 > 1 && other != v2) {
                        return false;
                    }
                    break;
                case 'N':
                    final int limit = 3;
                    if (v2 > limit && other != v2) {
                        return false;
                    }
                    break;
                case 'Y':
                    if (v2 > 1) {
                        return false;
                    }
                    break;
                default:
                    return false;
            }
        }

        //主体标识码（组织机构代码）9位
        final int start = 8;
        if (!isValidOrganizationCode(chars, start)) {
            return false;
        }

        int[] value = new int[LEN_USCC];
        for (int i = 0; i < LEN_USCC; i++) {
            int v = toIntUSCC(chars[i]);
            if (v < 0) {
                return false;
            }
            value[i] = v;
        }
        int sum = 0;
        for (int i = 0; i < LEN_USCC - 1; i++) {
            sum += value[i] * POWER_USCC[i];
        }
        return value[LEN_USCC - 1] == 31 - sum % 31;
    }

    /**
     * <a href="https://wenku.baidu.com/view/19873704cc1755270722087c.html">工商注册号</a>
     */
    public boolean isValidBusinessLicense(String data) {
        if (LEN_BUSINESS_LICENSE != data.length()) {
            return false;
        }
        return isValidMod1110(data);
    }

    /**
     * <a href="https://www.findlaw.cn/189900/article_54514.html">组织机构代码编码规则</a>
     * <a href="https://baike.baidu.com/item/%E7%BB%84%E7%BB%87%E6%9C%BA%E6%9E%84%E4%BB%A3%E7%A0%81/4311310">组织机构代码</a>
     */
    public boolean isValidOrganizationCode(String data) {
        final int separatorNum = 1;
        if (data.length() > LEN_ORGANIZATION_CODE + separatorNum || data.length() < LEN_ORGANIZATION_CODE) {
            return false;
        }
        char[] chars = data.toCharArray();
        return isValidOrganizationCode(true, chars, 0);
    }

    /**
     * 车辆识别号码
     */
    public boolean isValidVin(String data) {
        if (LEN_VIN != data.length()) {
            return false;
        }
        char[] chars = data.toCharArray();
        int[] value = new int[LEN_VIN];
        for (int i = 0; i < LEN_VIN; i++) {
            int v = toVin(chars[i]);
            if (v < 0) {
                return false;
            }
            value[i] = v;
        }

        //第九位为校验位
        final int checkCodeIndex = 8;
        int checkCode;
        char c = chars[checkCodeIndex];
        if (ROMAN_NUMERALS_10 == c) {
            checkCode = 10;
        } else {
            checkCode = toInt(c);
        }
        if (checkCode < 0) {
            return false;
        }

        int sum = 0;
        for (int i = 0; i < LEN_VIN; i++) {
            sum += value[i] * POWER_VIN[i];
        }
        return checkCode == sum % 11;
    }

    /**
     * 税务登记证号
     */
    public boolean isValidTaxRegistration(String data) {
        switch (data.length()) {
            case 15:
                return isValidTaxRegOrg(data);
            case 20:
                return isValidTaxRegPu(data);
            default:
                return false;
        }
    }

    /**
     * Luhn
     */
    public boolean isValidLuhn(String data) {
        int[] value = toIntArray(data);
        return isValidLuhn(value);
    }

    /**
     * <a href="https://baike.baidu.com/reference/2301611/1a32hEBxxI9GqqMWabSuhu5Syzv620wVi-hiw12-JyEhD0IeKS5DifE356bKg-ajjqkvc_eE_RWWHvChV9aGKiEoMLflxDZbRvHKZPxAUWa20MNYJUT4GLbd3hxDIFHvo_KalBz1KGk">学籍号</a>
     */
    public boolean isValidStudentStatus(String data) {
        switch (data.length()) {
            case 16:
            case 19:
                final char[] start = new char[]{'G', 'L', 'J'};
                if (notIn(data.charAt(0), start)) {
                    return false;
                }
                return isValidCard(StrUtil.subSuf(data, 1));
            default:
                return false;
        }
    }

    /**
     * <a href="https://zhidao.baidu.com/question/*********.html">残疾证号</a>
     */
    public boolean isValidDisabledCertificate(String data) {
        switch (data.length()) {
            case 17:
            case 20:
                int last = toInt(data.charAt(data.length() - 1));
                final int start = 1, end1 = 4, end2 = 7;
                if (last < start || last > end1) {
                    return false;
                }
                int last2 = toInt(data.charAt(data.length() - 2));
                if (last2 < start || last2 > end2) {
                    return false;
                }
                return isValidCard(StrUtil.subPre(data, -2));
            default:
                return false;
        }
    }

    /**
     * <a href="https://bbs.fobshanghai.com/archiver/tid-2023766.html?btwaf=53875127">进出口单位代码</a>
     */
    public boolean isValidImportAndExportEnterprise(String data) {
        final int limit = 13;
        if (limit != data.length()) {
            return false;
        }
        char[] chars = data.toCharArray();
        if (isNotValidCityCode(toInt(chars[0]), toInt(chars[1]))) {
            return false;
        }
        return isValidOrganizationCode(chars, 4);
    }

    public boolean isValidMod1110(String data) {
        int[] value = toIntArray(data);
        if (null == value) {
            return false;
        }
        int p = 0;
        for (int a : value) {
            p = ((p << 1) % 11 + a) % 10;
            if (0 == p) {
                p = 10;
            }
        }
        return 1 == p;
    }

    public boolean isDate(String data) {
        if (data.length() < 6) {
            return false;
        }
        try {
            DateTime dt = parse(data);
            if (null == dt) {
                return false;
            }
            switch (data.length()) {
                case 6:
                case 7:
                case 8:
                    return isValidYear(dt.year(), 1900);
                case 17:
                    return isValidYear(dt.year(), 1970);
                default:
                    return true;
            }
        } catch (Exception e) {
            return false;
        }
    }

    public boolean isYear(String data) {
        final int len = 4;
        if (len != data.length()) {
            return false;
        }
        return isValidThisYear(toYear(data), 1949);
    }

    public boolean isStartYear(String data) {
        return isValidThisYear(toYear(data), 1900);
    }

    public boolean isBirthday(String data) {
        final int len = 8;
        if (len != data.length()) {
            return false;
        }
        int[] value = toIntArray(data);
        if (null == value) {
            return false;
        }
        return isValidBirthday(value, 0);
    }

    public boolean isDoctorQualificationYear(String data) {
        return isValidThisYear(toYear(data), 1998);
    }

    /**
     * 每年执行
     */
    public void updateYear() {
        thisYear = LocalDate.now().getYear();
    }

    //private ################################################################################################################

    /**
     * GB/T 17710-1999
     */
    public boolean isValidMod112(int[] data) {
        int sum = 0;
        for (int i = 0; i < data.length; i++) {
            sum += data[i] * POWER_11_2[data.length - i - 1];
        }
        return 1 == (sum % 11);
    }

    private boolean isValidOrganizationCode(char[] chars, int start) {
        return isValidOrganizationCode(false, chars, start);
    }

    /**
     * 默认已经检测过长度
     */
    private boolean isValidOrganizationCode(boolean checkDashed, char[] chars, int start) {
        int reIdx = start + INDEX_ORGANIZATION_CHECK;
        char check = chars[reIdx];
        if (checkDashed && CharUtil.DASHED == check) {
            check = chars[reIdx + 1];
        }
        int cc;
        if (ROMAN_NUMERALS_10 == check) {
            cc = 10;
        } else {
            cc = toInt(check);
            if (cc < 0) {
                return false;
            }
        }
        int[] value = new int[INDEX_ORGANIZATION_CHECK];
        int v;
        for (int i = 0; i < INDEX_ORGANIZATION_CHECK; i++) {
            v = toIntOrganizationCode(chars[i + start]);
            if (v < 0) {
                return false;
            }
            value[i] = v;
        }
        int sum = 0;
        for (int i = 0; i < INDEX_ORGANIZATION_CHECK; i++) {
            sum += value[i] * POWER_ORGANIZATION_CODE[i];
        }
        int mod = 11;
        int checkCode = mod - sum % mod;
        if (checkCode == mod) {
            checkCode = 0;
        }
        return checkCode == cc;
    }

    private boolean isValidTaxRegOrg(String data) {
        int start = 6;
        char[] chars = data.toCharArray();
        for (int i = 0; i < start; i++) {
            if (toInt(chars[i]) < 0) {
                return false;
            }
        }
        return isValidOrganizationCode(chars, start);
    }

    private boolean isValidTaxRegPu(String data) {
        int start = 15, end = 18;
        char[] chars = data.toCharArray();
        boolean flag = true;
        int i = start;
        for (; i < end; i++) {
            int v = toInt(chars[i]);
            if (v < 0) {
                return false;
            }
            if (v != 0) {
                flag = false;
            }
        }
        for (; i < data.length(); i++) {
            if (toInt(chars[i]) < 0) {
                return false;
            }
        }
        if (flag) {
            return isValidCard15(StrUtil.subPre(data, start));
        }
        return isValidCard18(StrUtil.subPre(data, end));
    }

    /**
     * Luhn算法
     */
    private boolean isValidLuhn(int[] digits) {
        if (null == digits) {
            return false;
        }
        int sum = 0;
        int length = digits.length;
        for (int i = 0; i < length; i++) {

            // get digits in reverse order
            int digit = digits[length - i - 1];

            // every 2nd number multiply with 2
            if ((i & 1) == 1) {
                digit <<= 1;
            }
            sum += digit > 9 ? digit - 9 : digit;
        }
        return sum % 10 == 0;
    }

    private int toYear(String data) {
        final int len = 4;
        if (len > data.length()) {
            return -1;
        }
        int[] year = toIntArray(data, 0, len);
        if (null == year) {
            return -1;
        }
        int y = 0;
        for (int i = 0; i < len; i++) {
            y *= 10;
            y += year[i];
        }
        return y;
    }

    private boolean isValidYear(int year, int startYear) {
        return year >= startYear && year <= 2099;
    }

    private boolean isValidThisYear(int year, int startYear) {
        return year >= startYear && year <= thisYear;
    }

    public boolean isValidBirthday(int[] b, int start) {
        final int l1 = 4 + start, l2 = 6 + start, len = 8 + start;
        if (len > b.length) {
            return false;
        }
        int i = start, y = 0, m = 0, d = 0;
        for (; i < l1; i++) {
            y *= 10;
            y += b[i];
        }
        final int s = 1900;
        if (!isValidThisYear(y, s)) {
            return false;
        }
        for (; i < l2; i++) {
            m *= 10;
            m += b[i];
        }
        for (; i < len; i++) {
            d *= 10;
            d += b[i];
        }
        return isValidBirthday(y, m, d);
    }

    @SuppressWarnings("all")
    private boolean isValidBirthday(int year, int month, int day) {
        if (month < 1 || month > 12) {
            return false;
        }
        if (day < 1 || day > 31) {
            return false;
        }
        if (day == 31 && (month == 4 || month == 6 || month == 9 || month == 11)) {
            return false;
        }
        if (month == 2) {
            return day < 29 || (day == 29 && Year.isLeap(year));
        }
        return true;
    }

    @SuppressWarnings("all")
    private boolean isNotValidCityCode(int n1, int n2) {
        if (n1 < 1 || n1 > 9) {
            return true;
        }
        int s = n1 == 5 ? 0 : 1;
        return s > n2 || n2 > CITY_CODES[n1];
    }

    //util ################################################################################################################

    private int[] toIntArray(String numStr) {
        int[] value = new int[numStr.length()];
        for (int i = 0; i < numStr.length(); i++) {
            int v = toInt(numStr.charAt(i));
            if (v < 0) {
                return null;
            }
            value[i] = v;
        }
        return value;
    }

    private int[] toIntArray(String numStr, int start, int length) {
        int[] value = new int[length];
        int end = start + length;
        for (int i = start, j = 0; i < end; i++, j++) {
            int v = toInt(numStr.charAt(i));
            if (v < 0) {
                return null;
            }
            value[j] = v;
        }
        return value;
    }

    private int toInt(char c) {
        if (CharUtil.isNumber(c)) {
            return c - '0';
        }
        return -1;
    }

    private int[] toIntMod11(String data, boolean ignoreCase) {
        int[] value = new int[data.length()];
        int last = data.length() - 1;
        for (int i = 0; i < last; i++) {
            int v = toInt(data.charAt(i));
            if (v < 0) {
                return null;
            }
            value[i] = v;
        }
        char lastC = data.charAt(last);
        int i = toInt(lastC);
        if (i < 0) {
            if (ROMAN_NUMERALS_10 != (ignoreCase ? CommUtil.letterUpper(lastC) : lastC)) {
                return null;
            }
            i = 10;
        }
        value[last] = i;
        return value;
    }

    @SuppressWarnings("all")
    private int toIntUSCC(char c) {
        switch (c) {
            case '0':
                return 0;
            case '1':
                return 1;
            case '2':
                return 2;
            case '3':
                return 3;
            case '4':
                return 4;
            case '5':
                return 5;
            case '6':
                return 6;
            case '7':
                return 7;
            case '8':
                return 8;
            case '9':
                return 9;
            case 'A':
                return 10;
            case 'B':
                return 11;
            case 'C':
                return 12;
            case 'D':
                return 13;
            case 'E':
                return 14;
            case 'F':
                return 15;
            case 'G':
                return 16;
            case 'H':
                return 17;
            case 'J':
                return 18;
            case 'K':
                return 19;
            case 'L':
                return 20;
            case 'M':
                return 21;
            case 'N':
                return 22;
            case 'P':
                return 23;
            case 'Q':
                return 24;
            case 'R':
                return 25;
            case 'T':
                return 26;
            case 'U':
                return 27;
            case 'W':
                return 28;
            case 'X':
                return 29;
            case 'Y':
                return 30;
            default:
                return -1;
        }
    }

    private int toIntOrganizationCode(char c) {
        if (CharUtil.isNumber(c)) {
            return c - '0';
        }
        if (CharUtil.isLetterUpper(c)) {
            return c - 'A' + 10;
        }
        return -1;
    }

    private int toVin(char c) {
        if (CharUtil.isNumber(c)) {
            return c - '0';
        }
        if (CharUtil.isLetterUpper(c)) {
            final char[] exclude = {'I', 'O', 'Q'};
            if (notIn(c, exclude)) {
                int i = (c - 'A') % 9 + 1;
                return c >= 'S' ? i + 1 : i;
            }
        }
        return -1;
    }

    private boolean notIn(char c, char[] chars) {
        for (char aChar : chars) {
            if (c == aChar) {
                return false;
            }
        }
        return true;
    }

    private DateTime parse(String data) {
        // 去掉两边空格并去掉中文日期中的“日”和“秒”，以规范长度
        String dateStr = StrUtil.removeAll(data.trim(), '日', '秒');
        int length = dateStr.length();

        if (NumberUtil.isNumber(dateStr)) {
            if (!CharUtil.isNumber(dateStr.charAt(0))) {
                return null;
            }
            // 纯数字形式
            switch (length) {
                case 14: //DatePattern.PURE_DATETIME_PATTERN.length()
                    return DateUtil.parse(dateStr, DatePattern.PURE_DATETIME_FORMAT);
                case 17: //DatePattern.PURE_DATETIME_MS_PATTERN.length()
                    return DateUtil.parse(dateStr, DatePattern.PURE_DATETIME_MS_FORMAT);
                case 8: //DatePattern.PURE_DATE_PATTERN.length()
                    return DateUtil.parse(dateStr, DatePattern.PURE_DATE_FORMAT);
                case 6: //DatePattern.SIMPLE_MONTH_PATTERN.length()
                    return DateUtil.parse(dateStr, DatePattern.SIMPLE_MONTH_FORMAT);
                default:
            }
        } else if (ReUtil.isMatch(PatternPool.TIME, dateStr)) {
            // HH:mm:ss 或者 HH:mm 时间格式匹配单独解析
            return DateUtil.parseTimeToday(dateStr);
        } else if (StrUtil.containsAnyIgnoreCase(dateStr, WTB)) {
            // JDK的Date对象toString默认格式，类似于：
            // Tue Jun 4 16:25:15 +0800 2019
            // Thu May 16 17:57:18 GMT+08:00 2019
            // Wed Aug 01 00:00:00 CST 2012
            return DateUtil.parseRFC2822(dateStr);
        } else if (StrUtil.contains(dateStr, 'T')) {
            // UTC时间
            return DateUtil.parseUTC(dateStr);
        } else if (length == DatePattern.NORM_MONTH_PATTERN.length()) {
            char c = dateStr.charAt(4);
            if (CharUtil.DASHED == c) {
                return DateUtil.parse(dateStr, DatePattern.NORM_MONTH_FORMAT);
            } else if (CharUtil.SLASH == c) {
                return DateUtil.parse(dateStr, FastDateFormat.getInstance("yyyy/MM"));
            } else if (CharUtil.DOT == c) {
                return DateUtil.parse(dateStr, FastDateFormat.getInstance("yyyy.MM"));
            }
        }

        //标准日期格式（包括单个数字的日期时间）
        dateStr = normalize(dateStr);
        if (ReUtil.isMatch(DatePattern.REGEX_NORM, dateStr)) {
            final int colonCount = StrUtil.count(dateStr, CharUtil.COLON);
            switch (colonCount) {
                case 0:
                    // yyyy-MM-dd
                    return DateUtil.parse(dateStr, DatePattern.NORM_DATE_FORMAT);
                case 1:
                    // yyyy-MM-dd HH:mm
                    return DateUtil.parse(dateStr, DatePattern.NORM_DATETIME_MINUTE_FORMAT);
                case 2:
                    final int indexOfDot = StrUtil.indexOf(dateStr, CharUtil.DOT);
                    if (indexOfDot > 0) {
                        final int length1 = dateStr.length();
                        // yyyy-MM-dd HH:mm:ss.SSS 或者 yyyy-MM-dd HH:mm:ss.SSSSSS
                        if (length1 - indexOfDot > 4) {
                            // 类似yyyy-MM-dd HH:mm:ss.SSSSSS，采取截断操作
                            dateStr = StrUtil.subPre(dateStr, indexOfDot + 4);
                        }
                        return DateUtil.parse(dateStr, DatePattern.NORM_DATETIME_MS_FORMAT);
                    }
                    // yyyy-MM-dd HH:mm:ss
                    return DateUtil.parse(dateStr, DatePattern.NORM_DATETIME_FORMAT);
            }
        }

        // 没有更多匹配的时间格式
        throw new DateException("No format fit for date String [{}] !", dateStr);
    }

    private String normalize(CharSequence dateStr) {
        if (StrUtil.isBlank(dateStr)) {
            return StrUtil.str(dateStr);
        }

        // 日期时间分开处理
        final List<String> dateAndTime = StrUtil.splitTrim(dateStr, ' ');
        final int size = dateAndTime.size();
        if (size < 1 || size > 2) {
            // 非可被标准处理的格式
            return StrUtil.str(dateStr);
        }

        final StringBuilder builder = StrUtil.builder();

        // 日期部分（"\"、"/"、"."、"年"、"月"都替换为"-"）
        String datePart = dateAndTime.get(0).replaceAll("[/.年月]", "-");
        datePart = StrUtil.removeSuffix(datePart, "日");
        builder.append(datePart);

        // 时间部分
        if (size == 2) {
            builder.append(' ');
            String timePart = dateAndTime.get(1).replaceAll("[时分秒]", ":");
            timePart = StrUtil.removeSuffix(timePart, ":");
            //将ISO8601中的逗号替换为.
            timePart = timePart.replace(',', '.');
            builder.append(timePart);
        }

        return builder.toString();
    }

    static {
        SystemPropsUtil.set(SystemPropsUtil.HUTOOL_DATE_LENIENT, Boolean.FALSE.toString());
        updateYear();

        TW_FIRST_CODE.put('A', 10);
        TW_FIRST_CODE.put('B', 11);
        TW_FIRST_CODE.put('C', 12);
        TW_FIRST_CODE.put('D', 13);
        TW_FIRST_CODE.put('E', 14);
        TW_FIRST_CODE.put('F', 15);
        TW_FIRST_CODE.put('G', 16);
        TW_FIRST_CODE.put('H', 17);
        TW_FIRST_CODE.put('J', 18);
        TW_FIRST_CODE.put('K', 19);
        TW_FIRST_CODE.put('L', 20);
        TW_FIRST_CODE.put('M', 21);
        TW_FIRST_CODE.put('N', 22);
        TW_FIRST_CODE.put('P', 23);
        TW_FIRST_CODE.put('Q', 24);
        TW_FIRST_CODE.put('R', 25);
        TW_FIRST_CODE.put('S', 26);
        TW_FIRST_CODE.put('T', 27);
        TW_FIRST_CODE.put('U', 28);
        TW_FIRST_CODE.put('V', 29);
        TW_FIRST_CODE.put('X', 30);
        TW_FIRST_CODE.put('Y', 31);
        TW_FIRST_CODE.put('W', 32);
        TW_FIRST_CODE.put('Z', 33);
        TW_FIRST_CODE.put('I', 34);
        TW_FIRST_CODE.put('O', 35);

        WTB = (String[]) ReflectUtil.getStaticFieldValue(ReflectUtil.getField(DateUtil.class, "wtb"));
    }
}

