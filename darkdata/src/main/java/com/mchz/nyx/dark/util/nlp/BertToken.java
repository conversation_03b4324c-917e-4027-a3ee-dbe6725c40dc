package com.mchz.nyx.dark.util.nlp;

import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@SuppressWarnings("all")
@AllArgsConstructor
public class BertToken {

    private final long[] input_ids;
    private final long[] attention_mask;
    private final long[] token_type_ids;
    private final int sequence_length;

    public BertToken(long[] input_ids, long[] attention_mask) {
        this(input_ids, attention_mask, null, 0);
    }

}
