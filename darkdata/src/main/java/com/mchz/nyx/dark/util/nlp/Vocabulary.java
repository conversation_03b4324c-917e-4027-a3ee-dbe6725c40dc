package com.mchz.nyx.dark.util.nlp;

import java.util.List;

/**
 * {@code Vocabulary} is a collection of tokens. The primary purpose of a vocabulary is the map a
 * token to an index.
 *
 * <AUTHOR>
 */
public interface Vocabulary {
    String UNKNOWN_TOKEN = "[UNK]";

    /**
     * Returns the token corresponding to the given index.
     *
     * @param index the index
     * @return the token corresponding to the given index
     */
    String getToken(long index);

    /**
     * Check if the vocabulary contains a token.
     *
     * @param token String token to be checked
     * @return whether this vocabulary contains the token
     */
    boolean contains(String token);

    /**
     * Returns the index of the given token.
     *
     * @param token the token
     * @return the index of the given token.
     */
    long getIndex(String token);

    /**
     * size
     *
     * @return the size of the {@link Vocabulary}
     */
    long size();

    /**
     * token转为id
     *
     * @param tokens token
     * @return ids
     */
    default long[] convertTokens2Ids(List<String> tokens) {
        long[] output = new long[tokens.size()];

        for (int i = 0; i < tokens.size(); i++) {
            output[i] = getIndex(tokens.get(i));
        }
        return output;
    }
}
