package com.mchz.nyx.dark.model;

import com.mchz.nyx.dark.common.enums.HitType;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/11/26 14:22
 */
@Data
@AllArgsConstructor
public class TargetResult {
    private HitType type;
    private TargetInfo info;
    private Integer degree;
    private Double score;
    private List<Object> hitData;

    public TargetResult(HitType type, TargetInfo info) {
        this.type = type;
        this.info = info;
    }

    public TargetResult(HitType type, TargetInfo info, float score) {
        this.type = type;
        this.info = info;
        this.score = (double) score;
    }

    public TargetResult(HitType type, TargetInfo info, Double score) {
        this.type = type;
        this.info = info;
        this.score = score;
    }

    public void updateTargetInfo(Long classifyId, Integer level) {
        info = new TargetInfo(info.getId(), info.getName(), classifyId, level);
    }
}
