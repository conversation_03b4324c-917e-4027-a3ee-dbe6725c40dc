package com.mchz.nyx.dark.model.meta;

import com.mchz.nyx.common.enums.DataTypeGroup;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.SQLType;

/**
 * <p>
 * 列类型
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/10/21 17:03
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ColumnType {
    private String name;
    private DataTypeGroup typeGroup;

    public ColumnType(String name, SQLType sqlType) {
        this.name = name;
        this.typeGroup = DataTypeGroup.valueOf(sqlType);
    }

    public DataTypeGroup getTypeGroup() {
        return null == typeGroup ? DataTypeGroup.UNKNOWN : typeGroup;
    }
}
