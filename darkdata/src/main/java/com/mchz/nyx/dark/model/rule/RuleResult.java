package com.mchz.nyx.dark.model.rule;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/11/12 10:10
 */
@Data
public class RuleResult {

    /**
     * 带命中率的结果
     */
    private final List<RuleHitData> hitResult;

    public RuleResult() {
        this.hitResult = new ArrayList<>();
    }

    public RuleResult(RuleResult res) {
        this.hitResult = res.isEmpty() ? new ArrayList<>(8) : new ArrayList<>(res.hitResult);
    }

    public boolean isEmpty() {
        return hitResult.isEmpty();
    }

    public int size() {
        return hitResult.size();
    }

    public void add(RuleIdInfo info, double hitRate, List<Object> res) {
        hitResult.add(new RuleHitData(info, hitRate, res));
    }

    public void add(RuleIdInfo info, double hitRate) {
        hitResult.add(new RuleHitData(info, hitRate, null));
    }

    public void add(RuleIdInfo info) {
        hitResult.add(new RuleHitData(info));
    }

    public void add(RuleHitData dto) {
        hitResult.add(dto);
    }
}
