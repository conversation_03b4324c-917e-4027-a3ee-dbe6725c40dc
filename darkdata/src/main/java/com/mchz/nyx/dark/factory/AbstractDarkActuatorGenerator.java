package com.mchz.nyx.dark.factory;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.lang.PatternPool;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.gliwka.hyperscan.wrapper.CompileErrorException;
import com.gliwka.hyperscan.wrapper.Database;
import com.gliwka.hyperscan.wrapper.Expression;
import com.gliwka.hyperscan.wrapper.ExpressionFlag;
import com.mchz.nyx.common.enums.DataTypeGroup;
import com.mchz.nyx.dark.DarkActuator;
import com.mchz.nyx.dark.DarkActuatorGenerator;
import com.mchz.nyx.dark.common.enums.*;
import com.mchz.nyx.dark.engine.*;
import com.mchz.nyx.dark.engine.classifier.*;
import com.mchz.nyx.dark.engine.expression.SegmentExpression;
import com.mchz.nyx.dark.engine.expression.ValueExpression;
import com.mchz.nyx.dark.engine.filter.*;
import com.mchz.nyx.dark.engine.impl.JavaScriptEngine;
import com.mchz.nyx.dark.engine.impl.NlpMappingEngine;
import com.mchz.nyx.dark.engine.impl.NlpSemanticEngine;
import com.mchz.nyx.dark.engine.impl.NlpSimpleEngine;
import com.mchz.nyx.dark.engine.rule.*;
import com.mchz.nyx.dark.exception.RuleException;
import com.mchz.nyx.dark.model.TargetInfo;
import com.mchz.nyx.dark.model.config.DarkConfig;
import com.mchz.nyx.dark.model.definition.*;
import com.mchz.nyx.dark.model.dto.*;
import com.mchz.nyx.dark.model.rule.*;
import com.mchz.nyx.dark.rank.InnerRankStage;
import com.mchz.nyx.dark.rank.RankStage;
import com.mchz.nyx.dark.rank.RerankStage;
import com.mchz.nyx.dark.retrieval.*;
import com.mchz.nyx.dark.util.ExpressionUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.function.Function;
import java.util.function.IntFunction;
import java.util.regex.Matcher;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/6/27
 */
@Slf4j
@AllArgsConstructor
public abstract class AbstractDarkActuatorGenerator<C, R extends DarkActuator<?, ?, ?>> implements DarkActuatorGenerator<C, R> {

    protected ActuatorParam generatorStage(StdDetailDTO rule, DarkConfig config, EngineFactory factory) {
        if (null == rule || CollUtil.isEmpty(rule.getTargets())) {
            return ActuatorParam.empty();
        }
        List<RetrievalRecallStage> recallStages = new ArrayList<>(4);
        List<AutoCloseable> list = new ArrayList<>(3);
        if (config.isRecallHis()) {
            addNotNull(recallStages, buildHistoryRetrieval(config, factory, rule));
        }
        addNotNull(recallStages, buildRuleRetrieval(config, factory, rule, list));
        addNotNull(recallStages, buildMappingRecall(rule));
        YunCeEngine client = config.isLlm() || config.isSemantic() ? factory.createLLMEngine(config) : null;
        addNotNull(recallStages, buildSimilarRetrieval(config, factory, rule, list, client));
        List<RankStage> rankStages = new ArrayList<>(2);
        rankStages.add(new InnerRankStage(config.getCandidateCount()));
        if (config.isSemantic() && null != client) {
            rankStages.add(new RerankStage(client));
        }
        return new ActuatorParam(recallStages, rankStages, list, config.isCommentOverlay());
    }

    //private #####################################################################################################################

    private void addNotNull(List<RetrievalRecallStage> res, RetrievalRecallStage function) {
        if (null == function) {
            return;
        }
        res.add(function);
    }

    private RetrievalRecallStage buildHistoryRetrieval(DarkConfig config, EngineFactory factory, StdDetailDTO rule) {
        HistoryRetrievalEngine engine = factory.createHistoryRetrievalEngine(config, rule);
        if (null == engine) {
            return null;
        }
        return new HistoryRecallStage(engine);
    }

    private RetrievalRecallStage buildRuleRetrieval(DarkConfig config, EngineFactory factory, StdDetailDTO rule, List<AutoCloseable> list) {
        if (CollUtil.isEmpty(rule.getRuleDetails()) || CollUtil.isEmpty(rule.getTagGroups())) {
            return null;
        }
        RuleGlobalHandler handler = new RuleGlobalHandler(config.isLoadValues(), config.isFast());
        Rules[] rules = new Rules[]{new Rules(), new Rules(), new Rules()};
        Map<Long, RuleIdInfo> map = new HashMap<>(rule.getRuleDetails().size());
        int i = 0;
        for (RuleDetail detail : rule.getRuleDetails()) {
            RuleIdInfo info = detail.getInfo();
            RuleLoad load = detail.getLoad();
            Rule r = null;
            List<Rule> rs = null;
            try {
                switch (load) {
                    case SINGLE:
                    case TAG_SINGLE:
                        r = createSingleRule(info, detail.getRule(), handler, false);
                        break;
                    case SINGLE_STR:
                        r = createSingleRule(info, detail.getRule(), handler, true);
                        break;
                    case MULTIPLE:
                    case TAG_MULTIPLE:
                        rs = createMultipleRule(info, detail.getRules(), handler, false);
                        break;
                    case MULTIPLE_STR:
                        rs = createMultipleRule(info, detail.getRules(), handler, true);
                        break;
                    case PARAM_C:
                        r = createParamColumnRule(info, detail.getParamColumnRule(), handler);
                        break;
                    case PARAM_T:
                        r = createParamTableRule(info, detail.getParamTableRule(), handler);
                        break;
                    default:
                        continue;
                }
            } catch (RuleException e) {
                log.error("【规则加载】规则描述异常,{}:{}", info, e.getMessage());
                continue;
            } catch (Exception e) {
                log.error("【规则加载】规则载入异常,{}", info, e);
                continue;
            }
            if (null != r) {
                rules[load.getType()].register(r);
            } else if (null != rs) {
                rules[load.getType()].register(rs);
            } else {
                log.error("【规则加载】规则内容异常{}", detail.getInfo());
                continue;
            }
            RuleIdInfo old = map.put(info.getId(), info);
            if (null == old) {
                info.setReId(i++);
            } else {
                info.setReId(old.getReId());
            }
        }
        List<TargetTags> targetTags = buildTargetTags(rule, map);
        if (targetTags.isEmpty()) {
            return null;
        }
        RuleManager manager = handler.buildManager(config, factory.createRangeFunction(config));
        list.add(manager);
        return new RuleRetrievalStage(factory.createRulesEngine(), rules[0], rules[1], rules[2], manager, targetTags);
    }

    private RetrievalRecallStage buildMappingRecall(StdDetailDTO rule) {
        Map<Long, ? extends TargetInfo> targets = rule.getTargets();
        Map<String, List<TargetInfo>> column = targets.values().stream()
            .flatMap(v -> v.getAliasStream().map(n -> Pair.of(n, v)))
            .collect(Collectors.groupingBy(Pair::getKey, Collectors.mapping(Pair::getValue, Collectors.toList())));
        Map<String, Map<String, List<TargetInfo>>> map;

        if (CollUtil.isEmpty(rule.getVendor())) {
            if (column.isEmpty()) {
                return null;
            }
            map = Collections.emptyMap();
        } else {
            map = rule.getVendor().stream()
                .map(v -> new CustomMappingDTO(targets.get(v.getId()), StrUtil.nullToDefault(v.getTable(), StrUtil.EMPTY), v.getColumn()))
                .filter(v -> null != v.getInfo())
                .collect(Collectors.groupingBy(CustomMappingDTO::getTable, Collectors.groupingBy(CustomMappingDTO::getColumn, Collectors.mapping(CustomMappingDTO::getInfo, Collectors.toList()))));
            Map<String, List<TargetInfo>> emptyTable = map.remove(StrUtil.EMPTY);
            if (CollUtil.isNotEmpty(emptyTable)) {
                column.putAll(emptyTable);
            }
        }
        return new MappingRecallStage(map, column);
    }

    private RetrievalRecallStage buildSimilarRetrieval(DarkConfig config, EngineFactory factory, StdDetailDTO rule, List<AutoCloseable> list, YunCeEngine client) {
        //TODO Map<String, TargetInfo>  规则无重复内容 或者需要合并重复
        Map<String, List<TargetInfo>> map = rule.getTargets().values().stream().filter(TargetInfo::isEntity).collect(Collectors.groupingBy(TargetInfo::getName));
        SimilarSingleStep single = buildSimilarSingle(config, factory, list);
        SimilarEntiretyStep entirety = buildSimilarEntirety(config, client);

        if (CollUtil.isEmpty(map)) {
            return new SimilarRetrievalStage(NlpEngine.EMPTY, single, entirety);
        }

        NlpEngine nlpEngine;
        VectorConvEngine vectorConvEngine;
        List<BizVectorDTO> data = new ArrayList<>(map.size());
        if (config.isSemantic() && null != client) {
            nlpEngine = new NlpSemanticEngine(map, client);
        } else if (config.isNlp() && null != (vectorConvEngine = factory.createSimilarityEngine())) {
            for (Map.Entry<String, List<TargetInfo>> entry : map.entrySet()) {
                float[] v = vectorConvEngine.getPreVector(entry.getKey());
                if (null == v) {
                    continue;
                }
                data.add(new BizVectorDTO(new VectorDTO(v), entry.getValue()));
            }
            nlpEngine = new NlpSimpleEngine(map, data, vectorConvEngine);
        } else {
            nlpEngine = new NlpMappingEngine(map);
        }
        list.add(nlpEngine);
        return new SimilarRetrievalStage(nlpEngine, single, entirety);
    }

    private SimilarSingleStep buildSimilarSingle(DarkConfig config, EngineFactory factory, List<AutoCloseable> closeableList) {
        List<SimilarSingleStep> list = new ArrayList<>(2);
        if (config.isAutoColComment()) {
            list.add(new SimilarCommentStep(config.getColCommentBaseline() / 100f));
        }
        if (config.isAutoColName()) {
            TranslateEngine translateEngine = factory.createTranslateEngine(config);
            if (null != translateEngine) {
                closeableList.add(translateEngine);
                list.add(new SimilarNameStep(translateEngine, config.getColNameBaseline() / 100f));
            }
        }
        if (list.isEmpty()) {
            return null;
        }
        if (1 == list.size()) {
            return list.get(0);
        }
        return new SimilarGroupStep(list);
    }

    private SimilarEntiretyStep buildSimilarEntirety(DarkConfig config, YunCeEngine client) {
        if (null == client || !config.isLlm()) {
            return null;
        }
        return new SimilarLLMStep(client, config.getColNameBaseline() / 100f);
    }


    private List<TargetTags> buildTargetTags(StdDetailDTO rule, Map<Long, RuleIdInfo> map) {
        Map<Long, ? extends TargetInfo> targets = rule.getTargets();
        return rule.getTagGroups().stream().map(v -> {
            TargetInfo targetInfo = targets.get(v.getId());
            if (null == targetInfo) {
                return null;
            }
            List<Integer> reIds = new ArrayList<>(v.getTags().size());
            for (Long tag : v.getTags()) {
                RuleIdInfo info = map.get(tag);
                if (null == info) {
                    return null;
                }
                reIds.add(info.getReId());
            }
            if (CollUtil.isEmpty(v.getNonTags())) {
                return new TargetTags(TagBitMap.of(reIds), targetInfo, v.getScore());
            }
            List<Integer> reNonIds = new ArrayList<>(v.getNonTags().size());
            for (Long tag : v.getNonTags()) {
                RuleIdInfo info = map.get(tag);
                if (null == info) {
                    return null;
                }
                reNonIds.add(info.getReId());
            }
            TagBitMap bm1 = TagBitMap.of(reIds);
            TagBitMap bm2 = TagBitMap.of(reNonIds);
            if (bm1.intersect(bm2)) {
                return null;
            }
            return new TargetNonTags(bm1, bm2, targetInfo, v.getScore());
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    // rule start##############################################################################################################################################

    private Rule createSingleRule(RuleIdInfo info, SingleRuleDetail ruleDetail, RuleGlobalHandler handler, boolean isStr) {
        if (null == ruleDetail) {
            return null;
        }
        RuleParam param = new RuleParam(info, ruleDetail.getPriority(), ruleDetail.getHit());
        return isStr ? createStrMultiRule(param, ruleDetail, handler) : createSingleRule(param, ruleDetail, handler);
    }

    private List<Rule> createMultipleRule(RuleIdInfo info, List<SingleRuleDetail> ruleDetail, RuleGlobalHandler handler, boolean isStr) {
        int i = 0;
        List<Rule> res = new ArrayList<>(ruleDetail.size());
        for (SingleRuleDetail detail : ruleDetail) {
            RuleParam param = new RuleParam(info, detail.getPriority(), detail.getHit(), ++i);
            Rule rule = isStr ? createStrMultiRule(param, detail, handler) : createSingleRule(param, detail, handler);
            if (null == rule) {
                continue;
            }
            res.add(rule);
        }
        return res;
    }

    private Rule createSingleRule(RuleParam param, SingleRuleDetail ruleDetail, RuleGlobalHandler handler) {
        BaseClassifier<String> classifier;
        if (null != ruleDetail.getData()) {
            classifier = createSegmentClassifier(ruleDetail.getData(), handler);
        } else if (null != ruleDetail.getDataGroup()) {
            classifier = createGroupClassifier(ruleDetail.getDataGroup(), handler);
        } else if (null != ruleDetail.getDataInt()) {
            BaseClassifier<Integer> intClassifier = createIntClassifier(ruleDetail.getDataInt());
            if (null == intClassifier) {
                return null;
            }
            return new DataIntRule(param, intClassifier);
        } else {
            return null;
        }
        if (null == classifier) {
            return null;
        }
        if (null != param.getHit() && param.getHit() <= 0) {
            return new DataStrOneRule(param, classifier);
        }
        if (handler.isFast()) {
            return new DataStrFastRule(param, classifier);
        }
        return new DataStrRule(param, classifier);
    }

    private Rule createStrMultiRule(RuleParam param, SingleRuleDetail ruleDetail, RuleGlobalHandler handler) {
        if (null == ruleDetail.getData() || null == ruleDetail.getDataGroup()) {
            return createSingleRule(param, ruleDetail, handler);
        }
        BaseClassifier<String> classifier = createSegmentClassifier(ruleDetail.getData(), handler);
        if (null == classifier) {
            throw new RuleException("规则解析为null");
        }
        List<BaseClassifier<String>> rules = ruleDetail.getDataGroup().stream().map(v -> createSegmentClassifier(v, handler)).filter(Objects::nonNull).collect(Collectors.toList());
        if (ruleDetail.getDataGroup().size() != rules.size()) {
            throw new RuleException("复合规则解析失败");
        }
        return new DataStrMultiRule(param, classifier, rules);
    }

    private Rule createParamColumnRule(RuleIdInfo info, Map<ParamColumn, Object> ruleDetail, RuleGlobalHandler handler) {
        List<BaseClassifier<ColumnPayload>> list = ruleDetail.entrySet().stream().map(v -> createParamClassifier(v.getKey().getType(), v.getKey(), v.getValue(), handler)).filter(Objects::nonNull).collect(Collectors.toList());
        if (list.isEmpty()) {
            return null;
        }
        RuleParam param = new RuleParam(info, null, null);
        if (1 == list.size()) {
            return new ParamColumnRule(param, list.get(0));
        }
        return new ParamColumnRule(param, new GroupAndClassifier<>(list));
    }

    private Rule createParamTableRule(RuleIdInfo info, Map<ParamTable, Object> ruleDetail, RuleGlobalHandler handler) {
        List<BaseClassifier<TablePayload>> list = ruleDetail.entrySet().stream().map(v -> createParamClassifier(v.getKey().getType(), v.getKey(), v.getValue(), handler)).filter(Objects::nonNull).collect(Collectors.toList());
        if (list.isEmpty()) {
            return null;
        }
        RuleParam param = new RuleParam(info, null, null);
        if (1 == list.size()) {
            return new ParamTableRule(param, list.get(0));
        }
        return new ParamTableRule(param, new GroupAndClassifier<>(list));
    }

    // classifier start##############################################################################################################################################

    private <T, P extends Function<T, Object>> BaseClassifier<T> createParamClassifier(ReturnType type, P param, Object value, RuleGlobalHandler handler) {
        switch (type) {
            case BOOL:
                if (value instanceof Boolean) {
                    return new ParamBoolClassifier<>(param, (Boolean) value);
                }
                return null;
            case INTEGER:
                if (value instanceof String) {
                    ValueExpression<Integer> expression = ExpressionUtil.lenLimit((String) value);
                    return new ParamIntClassifier<>(param, expression);
                }
                return null;
            case STRING:
                if (value instanceof StrSegmentRule) {
                    BaseClassifier<String> classifier = createSegmentClassifier((StrSegmentRule) value, handler);
                    return null == classifier ? null : new ParamStrClassifier<>(param, classifier);
                }
                if (value instanceof StrRule) {
                    BaseClassifier<String> classifier = createStrClassifier((StrRule) value, handler);
                    return null == classifier ? null : new ParamStrClassifier<>(param, classifier);
                }
                return null;
            case ENUM_ARRAY:
                if (value instanceof List) {
                    if (ParamColumn.COLUMN_TYPE_GROUP.equals(param)) {
                        Set<DataTypeGroup> set = ((List<?>) value).stream().map(Object::toString).map(DataTypeGroup::getType).collect(Collectors.toSet());
                        return new ParamSetClassifier<>(param, set);
                    }
                    return new ParamSetClassifier<>(param, new HashSet<>((List<?>) value));
                }
            case ENUM:
                if (value instanceof String) {
                    if (ParamColumn.COLUMN_TYPE_GROUP.equals(param)) {
                        return new ParamEnumClassifier<>(param, DataTypeGroup.valueOf(value.toString()));
                    }
                    return new ParamEnumClassifier<>(param, value);
                }
                return null;
            default:
                return null;
        }
    }

    private BaseClassifier<String> createGroupClassifier(List<StrSegmentRule> rule, RuleGlobalHandler globalHandler) {
        List<BaseClassifier<String>> rules = rule.stream().map(v -> createSegmentClassifier(v, globalHandler)).filter(Objects::nonNull).collect(Collectors.toList());
        if (rules.isEmpty()) {
            return null;
        }
        if (1 == rules.size()) {
            return rules.get(0);
        }
        return new GroupOrClassifier<>(rules);
    }

    private BaseClassifier<String> createSegmentClassifier(StrSegmentRule rule, RuleGlobalHandler globalHandler) {
        if (null == rule) {
            return null;
        }
        BaseClassifier<String> base = createStrClassifier(rule, globalHandler);
        SegmentWay segWay = rule.getSegWay();
        if (null == segWay) {
            return base;
        }
        boolean flag = null != rule.getSegRules().get(0).getIndex();
        if (rule.getSegRules().stream().anyMatch(v -> flag == (null == v.getIndex()))) {
            throw new RuleException("段规则关联信息不完整");
        }
        List<Pair<Integer, BaseClassifier<String>>> pairs = getSegRule(rule, globalHandler, flag);
        if (pairs.isEmpty()) {
            throw new RuleException("未找到有效段规则");
        }
        BaseClassifier<String> other;
        BaseClassifier<String> rest;
        int[] index;
        if (flag) {
            Iterator<Pair<Integer, BaseClassifier<String>>> iterator = pairs.iterator();
            Pair<Integer, BaseClassifier<String>> now = iterator.next();
            if (-1 == now.getKey()) {
                rest = now.getValue();
                iterator.remove();
                now = iterator.hasNext() ? iterator.next() : null;
            } else {
                rest = null;
            }
            if (null != now && 0 == now.getKey()) {
                other = now.getValue();
                iterator.remove();
                now = iterator.hasNext() ? iterator.next() : null;
            } else {
                other = null;
            }
            if (null != now) {
                if (now.getKey() < 1) {
                    throw new RuleException("段规则指向异常");
                }
                index = pairs.stream().mapToInt(Pair::getKey).toArray();
            } else {
                index = null;
            }
        } else {
            other = null;
            rest = null;
            index = null;
        }
        SegmentExpression segmentation = ExpressionUtil.segmentation(segWay);
        BaseClassifier<String>[] classifiers = pairs.stream().map(Pair::getValue).toArray((IntFunction<BaseClassifier<String>[]>) BaseClassifier[]::new);
        SegmentClassifier res = new SegmentClassifier(segmentation, classifiers, index, rest, other, segWay.getExpectedNum(), segWay.getLeast());
        if (null == base) {
            return applyInheritedValues(rule, res);
        }
        return new GroupAndClassifier<>(base, res);
    }

    private List<Pair<Integer, BaseClassifier<String>>> getSegRule(StrSegmentRule rule, RuleGlobalHandler globalHandler, boolean flag) {
        Stream<Pair<Integer, BaseClassifier<String>>> stream = rule.getSegRules().stream().flatMap(v -> {
            BaseClassifier<String> classifier = createStrClassifier(v, globalHandler);
            if (null == classifier) {
                throw new RuleException("段规则缺失 " + v.getIndex());
            }
            if (flag) {
                Set<Integer> indexes = ExpressionUtil.indexes(v.getIndex());
                return indexes.stream().map(i -> Pair.of(i, classifier));
            }
            return Stream.of(Pair.of((Integer) null, classifier));
        }).filter(Objects::nonNull);
        if (flag) {
            stream = stream.sorted(Comparator.comparingInt(Pair::getKey));
        }
        return stream.collect(Collectors.toList());
    }

    private BaseClassifier<String> createStrClassifier(StrRule rule, RuleGlobalHandler globalHandler) {
        if (null == rule) {
            return null;
        }
        List<BaseClassifier<String>> checks = new ArrayList<>(4);
        BaseClassifier<String> functionCheck = createFunClassifier(rule.getFun());
        if (null != functionCheck) {
            checks.add(functionCheck);
        }
        BaseClassifier<String> fixedCheck = createFixedClassifier(rule.getFixed(), rule.getFixedOp());
        if (null != fixedCheck) {
            checks.add(fixedCheck);
        }
        BaseClassifier<String> dictCheck = createDictClassifier(rule.getDict(), rule.getDictOp(), globalHandler);
        if (null != dictCheck) {
            checks.add(dictCheck);
        }
        BaseClassifier<String> regexCheck = createRegexClassifier(rule.getRegex(), rule.getRegexOp(), globalHandler);
        if (null != regexCheck) {
            checks.add(regexCheck);
        }
        BaseClassifier<String> codeCheck = createCustomCodeClassifier(rule.getCFun());
        if (null != codeCheck) {
            checks.add(codeCheck);
        }
        if (checks.isEmpty()) {
            return applyInheritedValues(rule, null);
        }
        BaseClassifier<String> res;
        if (1 == checks.size()) {
            res = checks.get(0);
        } else {
            res = new GroupAndClassifier<>(checks);
        }
        return applyInheritedValues(rule, res);
    }

    private BaseClassifier<String> applyInheritedValues(StrRule rule, BaseClassifier<String> res) {
        if (null != rule.getLenLimit()) {
            ValueExpression<Integer> limit = ExpressionUtil.lenLimit(rule.getLenLimit());
            if (null == res) {
                res = new LenOnlyClassifier(limit);
            } else {
                res = new LenClassifier(limit, res);
            }
        }
        if (null == res) {
            return null;
        }
        if (null != rule.getIgnore()) {
            res = new IgnoreClassifier(rule.getIgnore(), res);
        } else if (null != rule.getPreTreat()) {
            res = new PreTreatClassifier(rule.getPreTreat(), res);
        }
        if (Boolean.FALSE.equals(rule.getExpect())) {
            res = new NonClassifier<>(res);
        }
        return res;
    }

    private BaseClassifier<Integer> createIntClassifier(IntRule rule) {
        return null == rule.getRange() ? null : new IntegerClassifier(ExpressionUtil.lenLimit(rule.getRange()));
    }

    private BaseClassifier<String> createRegexClassifier(List<RegexDetail> list, Integer regexOp, RuleGlobalHandler globalHandler) {
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        List<RegexPayload> payloads = list.stream().map(v -> RegexPayload.builder().value(requireNonNull(v.getValue(), "未指定正则规则"))
            .expect(null == v.getExpect() || v.getExpect()).build()).collect(Collectors.toList());
        BaseClassifier<String> classifier;
        if (1 == payloads.size()) {
            classifier = new SingleClassifier<>(payloads.get(0), BaseFilter.REGEX);
        } else {
            switch (RuleOperator.getType(regexOp)) {
                case OR:
                    classifier = new ConditionOrClassifier<>(payloads, BaseFilter.REGEX);
                    break;
                case AND:
                    classifier = new ConditionAndClassifier<>(payloads, BaseFilter.REGEX);
                    break;
                case EXPECT:
                    classifier = new ConditionClassifier<>(payloads, BaseFilter.REGEX);
                    break;
                default:
                    return null;
            }
        }
        globalHandler.addRegex(payloads);
        return classifier;
    }

    private BaseClassifier<String> createDictClassifier(List<DictDetail> list, Integer dictOp, RuleGlobalHandler globalHandler) {
        if (CollUtil.isEmpty(list)) {
            return null;
        }

        List<DictPayload> payloads = list.stream().map(v -> DictPayload.builder()
            .dictId(requireNonNull(v.getDictId(), "未指定字典ID"))
            .loadType(ObjUtil.defaultIfNull(v.getLoadType(), LoadType.INNER))
            .matchType(ObjUtil.defaultIfNull(v.getMatchType(), MatchType.EQUAL))
            .suffix(v.getSuffix())
            .expect(null == v.getExpect() || v.getExpect())
            .build()).collect(Collectors.toList());
        BaseClassifier<String> classifier;
        if (1 == payloads.size()) {
            classifier = new SingleClassifier<>(payloads.get(0), BaseFilter.DICT);
        } else {
            switch (RuleOperator.getType(dictOp)) {
                case OR:
                    classifier = new ConditionOrClassifier<>(payloads, BaseFilter.DICT);
                    break;
                case AND:
                    classifier = new ConditionAndClassifier<>(payloads, BaseFilter.DICT);
                    break;
                case EXPECT:
                    classifier = new ConditionClassifier<>(payloads, BaseFilter.DICT);
                    break;
                default:
                    return null;
            }
        }
        globalHandler.addRange(payloads);
        return classifier;
    }

    private BaseClassifier<String> createFixedClassifier(List<FixedDetail> list, Integer fixedOp) {
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        List<BaseClassifier<String>> classifiers = list.stream().map(v ->
            new FixedClassifier(null == v.getExpect() || v.getExpect(), Boolean.TRUE.equals(v.getIgnore()), v.getValue(),
                ObjUtil.defaultIfNull(v.getType(), 0), ObjUtil.defaultIfNull(v.getStart(), 0))).collect(Collectors.toList());
        if (1 == classifiers.size()) {
            return classifiers.get(0);
        } else {
            switch (RuleOperator.getType(fixedOp)) {
                case OR:
                    return new GroupOrClassifier<>(classifiers);
                case AND:
                case EXPECT:
                    return new GroupAndClassifier<>(classifiers);
                default:
                    return null;
            }
        }
    }

    private FunctionClassifier createFunClassifier(FunctionStr function) {
        if (null == function) {
            return null;
        }
        return new FunctionClassifier(function.getFunction());
    }

    private CustomCodeClassifier createCustomCodeClassifier(String customCode) {
        if (StrUtil.isBlank(customCode)) {
            return null;
        }
        return new CustomCodeClassifier(new JavaScriptEngine(customCode));
    }

    // rule end##############################################################################################################################################

    private <T> T requireNonNull(T obj, String message) {
        if (obj == null) {
            throw new RuleException(message);
        }
        return obj;
    }

    private static class RuleGlobalHandler {
        private final Map<LoadType, Map<Long, Integer>> range;
        private final List<DictPayload> dict;
        private final Map<String, Integer> regex;

        private final int[] no;
        private final boolean load;
        @Getter
        private final boolean fast;

        public RuleGlobalHandler(boolean loadValues, boolean fast) {
            this.range = new EnumMap<>(LoadType.class);
            this.dict = new LinkedList<>();
            this.regex = new HashMap<>();
            this.no = new int[2];
            this.load = loadValues;
            this.fast = fast;
        }

        public RuleManager buildManager(DarkConfig config, RangeFunction rangeFunction) {
            int cacheSize = config.getCacheSize();
            DictFilterCore dictFilterCore = buildDictCore(config.getContext(), rangeFunction, cacheSize);
            BaseFilterCore<RegexPayload> regexFilterCore;
            try {
                regexFilterCore = buildRegexCore(cacheSize);
            } catch (Throwable e) {
                log.warn("【规则加载】构建正则引擎核心失败,{}", ExceptionUtil.getMessage(e));
                regexFilterCore = new RegexCommonFilterCore(cacheSize);
            }
            return new RuleManager(dictFilterCore, regexFilterCore);
        }

        public void addRange(List<DictPayload> list) {
            list.forEach(v -> {
                Map<Long, Integer> map = range.computeIfAbsent(v.getLoadType(), k -> new HashMap<>(16));
                v.setId(map.computeIfAbsent(v.getDictId(), k -> no[0]++));
                if (load || !MatchType.EQUAL.equals(v.getMatchType())) {
                    dict.add(v);
                }
            });
        }

        public void addRegex(List<RegexPayload> list) {
            list.forEach(v -> v.setId(regex.computeIfAbsent(v.getValue(), k -> no[1]++)));
        }

        private DictFilterCore buildDictCore(StandardLoadContext context, RangeFunction valueRange, int cacheSize) {
            Map<MatchType, List<DictPayload>> map = dict.stream().collect(Collectors.groupingBy(DictPayload::getMatchType, () -> new EnumMap<>(MatchType.class), Collectors.toList()));
            Map<MatchType, List<DictData>> initData = new EnumMap<>(MatchType.class);
            for (Map.Entry<MatchType, List<DictPayload>> entry : map.entrySet()) {
                List<DictData> list = loadDictData(context, valueRange, entry.getValue());
                if (CollUtil.isEmpty(list)) {
                    continue;
                }
                initData.put(entry.getKey(), list);
            }
            return new DictFilterCore(initData, context, valueRange, cacheSize);
        }

        private RegexFilterCore buildRegexCore(int cacheSize) {
            final EnumSet<ExpressionFlag> flag = EnumSet.of(ExpressionFlag.SINGLEMATCH, ExpressionFlag.UTF8);
            List<Expression> list = regex.entrySet().stream().map(kv -> new Expression(pcre(kv.getKey()), flag, kv.getValue())).collect(Collectors.toList());
            if (CollUtil.isEmpty(list)) {
                return null;
            }
            try {
                Database db = Database.compile(list);
                return new RegexFilterCore(db, cacheSize);
            } catch (CompileErrorException e) {
                throw new RuleException(String.format("regex unable to compile [%s]:%s", e.getFailedExpression().getExpression(), e.getMessage()));
            }
        }

        private List<DictData> loadDictData(StandardLoadContext context, RangeFunction valueRange, Collection<DictPayload> details) {
            Map<LoadType, Set<Long>> map = details.stream().collect(Collectors.groupingBy(DictPayload::getLoadType, Collectors.mapping(DictPayload::getDictId, Collectors.toSet())));
            List<DictData> ret = new ArrayList<>();
            for (Map.Entry<LoadType, Set<Long>> entry : map.entrySet()) {
                Map<Long, Set<String>> dict = valueRange.listValueRange(context, entry.getKey(), entry.getValue());
                if (CollUtil.isNotEmpty(dict)) {
                    Map<Long, Integer> idMap = range.get(entry.getKey());
                    dict.entrySet().stream().flatMap(v -> {
                        Integer i = idMap.get(v.getKey());
                        return v.getValue().stream().map(d -> new DictData(i, d));
                    }).forEach(ret::add);
                }
            }
            return ret;
        }
    }

    private static String pcre(String regex) {
        Matcher matcher = PatternPool.get("\\\\(u[0-9a-fA-F]{4})").matcher(regex);
        if (!matcher.find()) {
            return regex;
        }
        int i = 0;
        StringBuilder sb = new StringBuilder();
        do {
            int start = matcher.start(0);
            int end = matcher.end(0);
            sb.append(regex, i, start).append("\\x{").append(regex, start + 2, end).append("}");
            i = end;
        } while (matcher.find());
        sb.append(regex, i, regex.length());
        return sb.toString();
    }
}
