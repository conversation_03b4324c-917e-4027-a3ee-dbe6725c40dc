package com.mchz.nyx.dark.model.dto;

import com.mchz.nyx.dark.engine.algorithm.TrieData;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/11/13 17:41
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DictData implements TrieData<Integer> {
    private Integer id;
    private String value;

    @Override
    public Integer getResult() {
        return id;
    }
}
