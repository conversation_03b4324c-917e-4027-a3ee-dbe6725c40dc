package com.mchz.nyx.dark.engine.impl;

import com.mchz.nyx.dark.engine.VectorConvEngine;
import com.mchz.nyx.dark.model.TargetInfo;
import com.mchz.nyx.dark.model.TargetResult;
import com.mchz.nyx.dark.model.dto.BizVectorDTO;
import com.mchz.nyx.dark.util.NlpUtil;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/1/20
 */
public class NlpSimpleEngine extends AbstractNlpEngine {
    private final VectorConvEngine vectorConvEngine;
    private final List<BizVectorDTO> vectors;

    public NlpSimpleEngine(Map<String, List<TargetInfo>> data, List<BizVectorDTO> vectors, VectorConvEngine vectorConvEngine) {
        super(data);
        this.vectors = vectors;
        this.vectorConvEngine = vectorConvEngine;
    }

    @Override
    protected List<TargetResult> similarity(String str, float baseline) {
        float[] vector = vectorConvEngine.getVector(str);
        if (null == vector) {
            return null;
        }
        return vectors.stream().flatMap(v -> {
            float cos = NlpUtil.squaredCosineSimilarity(vector, v.getVector());
            if (cos < baseline) {
                return Stream.empty();
            }
            return v.getBizList().stream().map(b -> new TargetResult(null, b, cos));
        }).sorted((o1, o2) -> o2.getScore().compareTo(o1.getScore())).limit(3).collect(Collectors.toList());
    }

    @Override
    public void close() {
        super.close();
        vectorConvEngine.close();
    }
}
