package com.mchz.nyx.dark.engine.filter;

import cn.hutool.cache.Cache;
import com.mchz.nyx.common.util.WeakLFUCache;

import java.util.Set;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2021/12/28 16:41
 */
abstract class DefaultFilterCore<K, T extends Identifiable> implements BaseFilterCore<T> {
    private final Cache<K, Set<Integer>> cache;

    public DefaultFilterCore(int size) {
        this.cache = new WeakLFUCache<>(size, -1);
    }

    @Override
    public boolean filter(T payload, String data) {
        K key = getKey(payload, data);
        Set<Integer> res = cache.get(key, () -> scanAll(payload, data));
        return res.contains(payload.getId());
    }

    /**
     * 获取key
     *
     * @param payload 载荷
     * @param data    数据
     * @return key
     */
    protected abstract K getKey(T payload, String data);

    /**
     * 执行扫描
     *
     * @param payload 载荷
     * @param data    数据
     * @return 匹配结果集
     */
    protected abstract Set<Integer> scanAll(T payload, String data);

    @Override
    public void close() throws Exception {
    }
}
