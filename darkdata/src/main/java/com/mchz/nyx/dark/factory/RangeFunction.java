package com.mchz.nyx.dark.factory;


import com.mchz.nyx.dark.common.enums.LoadType;
import com.mchz.nyx.dark.model.dto.StandardLoadContext;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/11/12 18:42
 */
public interface RangeFunction {

    /**
     * 获取字典值
     *
     * @param context  上下文
     * @param loadType 字典加载类型
     * @param dictId   字典名
     * @return 字典值
     */
    Set<String> getValueRange(StandardLoadContext context, LoadType loadType, long dictId);

    /**
     * 批量获取字典值
     *
     * @param context  上下文
     * @param loadType 字典加载类型
     * @param dictIds  字典名列表
     * @return 字典值
     */
    default Map<Long, Set<String>> listValueRange(StandardLoadContext context, LoadType loadType, Collection<Long> dictIds) {
        Map<Long, Set<String>> res = new HashMap<>();
        dictIds.forEach(v -> res.put(v, getValueRange(context, loadType, v)));
        return res;
    }
}
