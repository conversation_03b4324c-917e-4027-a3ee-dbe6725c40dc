package com.mchz.nyx.dark.exception;

import com.mchz.nyx.common.exception.NyxException;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/11/12 13:20
 */
public class RuleException extends NyxException {
    private static final long serialVersionUID = 6584144271298677709L;

    public RuleException(String message) {
        super(message);
    }

    public RuleException(String message, Throwable cause) {
        super(message, cause);
    }

    public RuleException(Throwable cause) {
        super("规则异常", cause);
    }
}
