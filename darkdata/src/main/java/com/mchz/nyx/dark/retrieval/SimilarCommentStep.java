package com.mchz.nyx.dark.retrieval;

import cn.hutool.core.util.StrUtil;
import com.mchz.nyx.dark.common.constants.RuleConst;
import com.mchz.nyx.dark.common.enums.HitType;
import com.mchz.nyx.dark.engine.NlpEngine;
import com.mchz.nyx.dark.model.EngineContext;
import com.mchz.nyx.dark.model.meta.MetaColumnData;
import com.mchz.nyx.dark.util.NlpUtil;
import lombok.AllArgsConstructor;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/6
 */
@AllArgsConstructor
public class SimilarCommentStep implements SimilarSingleStep {
    private final float baseline;

    @Override
    public boolean execute(EngineContext context, NlpEngine nlpEngine, MetaColumnData column) {
        String comment = column.getComment();
        if (StrUtil.isNotBlank(comment)) {
            boolean res = column.addAllTarget(nlpEngine.similarityAnalysis(context, comment, HitType.COMMENT_SIMILAR, baseline));
            if (res || comment.length() > RuleConst.MAX_TERM_LEN) {
                return res;
            }
            String tmp = NlpUtil.dealCommentCut(comment);
            if (tmp.length() == comment.length()) {
                return false;
            }
            column.addAlias(tmp);
            return column.addAllTarget(nlpEngine.similarityAnalysis(context, tmp, HitType.COMMENT_SIMILAR, baseline));
        }
        return false;
    }
}
