package com.mchz.nyx.dark.engine.classifier;

import com.mchz.nyx.dark.engine.CustomScriptEngine;
import com.mchz.nyx.dark.engine.rule.RuleManager;
import lombok.AllArgsConstructor;

import java.util.function.Function;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/11/22 14:14
 */
@AllArgsConstructor
public class CustomCodeClassifier implements BaseClassifier<String> {

    private final CustomScriptEngine engine;

    @Override
    public boolean classify(String data, RuleManager manager) {
        return engine.eval(data);
    }
}
