package com.mchz.nyx.dark.engine.impl;

import com.mchz.nyx.dark.factory.TransColumnFun;
import com.mchz.nyx.dark.engine.algorithm.MinNumSplit;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/18
 */
public class MapTranslatePlusEngine extends MapTranslateEngine {
    private final TransColumnFun engine;

    public MapTranslatePlusEngine(MinNumSplit match, TransColumnFun engine) {
        super(match);
        this.engine = engine;
    }

    @Override
    protected String execute(String name) {
        String tmp = engine.toChineseName(name);
        if (null != tmp) {
            return tmp;
        }
        return super.execute(name);
    }
}
