package com.mchz.nyx.dark.engine.record;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/11/12 11:13
 */
@Data
@AllArgsConstructor
public class SingleResultLog implements RecordLog {

    private String name;
    private int hit;
    private int total;

    @Override
    public String getMessage() {
        return String.format("%s %.2f%%(%d/%d)", name, (double) hit * 100 / total, hit, total);
    }
}
