package com.mchz.nyx.dark.engine.rule;

import cn.hutool.core.util.ObjUtil;
import com.mchz.nyx.common.enums.DataTypeGroup;
import com.mchz.nyx.dark.common.constants.DarkConst;
import com.mchz.nyx.dark.common.constants.RuleConst;
import com.mchz.nyx.dark.engine.classifier.BaseClassifier;
import com.mchz.nyx.dark.engine.record.RecordLog;
import com.mchz.nyx.dark.engine.record.SingleResultLog;
import com.mchz.nyx.dark.model.meta.StrDataInfo;
import com.mchz.nyx.dark.model.rule.ColumnPayload;
import com.mchz.nyx.dark.model.rule.RuleParam;
import com.mchz.nyx.dark.model.rule.RuleIdInfo;
import com.mchz.nyx.dark.model.rule.RuleResult;

import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 规则
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/11/12 11:23
 */
public class DataStrRule extends BaseColumnRule {

    final RuleIdInfo res;

    final double hit;
    final BaseClassifier<String> classifier;

    public DataStrRule(RuleParam param, BaseClassifier<String> classifier) {
        super(param.getName(), ObjUtil.defaultIfNull(param.getPriority(), RuleConst.DEFAULT_MIN_PRIORITY));
        this.hit = ObjUtil.defaultIfNull(param.getHit(), RuleConst.DEFAULT_HIT);
        this.res = param.getTarget();
        this.classifier = classifier;
    }

    @Override
    protected boolean when(ColumnPayload payload) {
        if (0 == payload.getTotal()) {
            return false;
        }

        return DataTypeGroup.CHARACTER.equals(payload.getType());
    }

    @Override
    protected RecordLog then(ColumnPayload payload, RuleResult result, RuleManager manager) {
        List<StrDataInfo> list = payload.getDataStr();

        int count = 0;
        List<Object> hitData = new ArrayList<>(DarkConst.MAX_HIT_DATA_SIZE);
        for (StrDataInfo data : list) {
            if (classifier.classify(data.getValue(), manager)) {
                count += data.getSize();
                if (hitData.size() < DarkConst.MAX_HIT_DATA_SIZE) {
                    hitData.add(data.getValue());
                }
            }
        }
        int total = payload.getTotal();
        double hitRate = (double) count / total;
        if (hitRate >= hit) {
            result.add(res, hitRate, hitData);
        }
        return new SingleResultLog(getName(), count, total);
    }
}
