package com.mchz.nyx.dark.model.definition;

import com.mchz.nyx.dark.common.enums.LoadType;
import com.mchz.nyx.dark.common.enums.MatchType;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/11/22 16:57
 */

@Data
public class DictDetail {
    private Long dictId;
    private LoadType loadType;
    private MatchType matchType;

    private String suffix;
    private Boolean expect;
}
