package com.mchz.nyx.dark.engine.rule;


import com.mchz.nyx.dark.model.rule.ColumnPayload;
import com.mchz.nyx.dark.model.rule.Facts;

/**
 * <p>
 * 规则基类
 * </p >
 *
 * <AUTHOR>
 * @date 2020/6/24 18:10
 */
public abstract class BaseColumnRule extends BaseRule<ColumnPayload> {

    public BaseColumnRule(String name, int priority) {
        super(name, priority);
    }

    @Override
    protected ColumnPayload getPayload(Facts facts) {
        return facts.getPayload();
    }
}
