package com.mchz.nyx.dark.model;

import cn.hutool.core.map.MapUtil;
import com.mchz.nyx.dark.model.meta.AdditionalInfo;
import com.mchz.nyx.dark.model.meta.MetaColumnData;
import com.mchz.nyx.dark.model.meta.NyxMetaTable;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/12/1 15:26
 */
@Data
public class DefaultEngineContext implements EngineContext {
    private final AdditionalInfo info;
    private final NyxMetaTable table;
    private final List<MetaColumnData> columns;

    private Map<String, Object> data;

    @Override
    public <T> DefaultEngineContext put(String key, T obj) {
        Map<String, Object> data = getData();
        if (null == obj) {
            data.remove(key);
        } else {
            data.put(key, obj);
        }
        return this;
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T> T get(String key, Class<T> clazz, T defaultValue) {
        Map<String, Object> data = getData();
        if (null == clazz) {
            return (T) data.getOrDefault(key, defaultValue);
        }
        return MapUtil.get(data, key, clazz, defaultValue);
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T> T remove(String key) {
        return (T) getData().remove(key);
    }

    private Map<String, Object> getData() {
        if (null == data) {
            data = new HashMap<>();
        }
        return data;
    }
}
