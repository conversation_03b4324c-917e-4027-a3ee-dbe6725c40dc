package com.mchz.nyx.dark.engine.classifier;

import com.mchz.nyx.dark.engine.filter.BaseFilter;
import com.mchz.nyx.dark.engine.filter.ExpectAble;
import com.mchz.nyx.dark.engine.rule.RuleManager;
import lombok.AllArgsConstructor;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2022/9/16 17:04
 */
@AllArgsConstructor
public class SingleClassifier<T extends ExpectAble> implements BaseClassifier<String> {
    private final T payload;
    private final BaseFilter<T> filter;

    @Override
    public boolean classify(String data, RuleManager manager) {
        return payload.isExpect() == filter.filter(payload, data, manager);
    }
}
