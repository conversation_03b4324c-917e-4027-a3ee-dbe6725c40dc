package com.mchz.nyx.dark.util.nlp.tokenizer;

import cn.hutool.core.util.StrUtil;
import com.mchz.nyx.dark.util.nlp.BertToken;
import com.mchz.nyx.dark.util.nlp.Vocabulary;

import java.util.*;

@SuppressWarnings("all")
public class TransformerTokenizer implements Tokenizer {
    private final Vocabulary vocab;
    private final int maxSeqLen;
    private final WordpieceTokenizer wordpieceTokenizer;
    private final BasicTokenizer basicTokenizer;
    private final String spaceToken;
    private final Set<String> tokenDict;

    public TransformerTokenizer(Vocabulary vocab, int maxSeqLen) {
        this(vocab, maxSeqLen, null, null, true, false);
    }

    public TransformerTokenizer(Vocabulary vocab, int maxSeqLen, String spaceToken, Set<String> tokenDict, boolean doLowerCase, boolean stripAccents) {
        this.vocab = vocab;
        this.maxSeqLen = maxSeqLen;
        this.spaceToken = spaceToken;
        this.tokenDict = null == tokenDict ? Collections.emptySet() : tokenDict;

        this.basicTokenizer = new BasicTokenizer(doLowerCase, stripAccents);
        this.wordpieceTokenizer = new WordpieceTokenizer(vocab, Vocabulary.UNKNOWN_TOKEN);
    }

    @Override
    public List<String> tokenize(String text) {
        return tokenize(text, true, true, false, true);
    }

    /**
     * def tokenize(
     * self,
     * text,
     * do_basic_tokenize=True,
     * use_token_dict_in_basic_tokenizer=True,
     * do_keep_space_space_token=False,
     * use_unk_token=True,
     * ):
     */
    public List<String> tokenize(String text, boolean doBasicTokenize, boolean useTokenDictInBasicTokenizer, boolean doKeepSpaceSpaceToken, boolean useUnkToken) {

        if (doKeepSpaceSpaceToken) {
            StringBuilder sb = new StringBuilder();
            for (char cp : text.toCharArray()) {
                if (cp != ' ') {
                    sb.append(cp);
                } else {
                    sb.append(spaceToken);
                }
            }
            text = sb.toString();
        }

        // text_pieces = self.tokens_trie.split(text)
        List<String> textPieces = new ArrayList<>(Collections.singletonList(text));

        List<String> splitTokens = new ArrayList<>(textPieces.size() << 3);

        for (String textPiece : textPieces) {
            if (StrUtil.isEmpty(textPiece)) {
                continue;
            }
            if (tokenDict.contains(textPiece)) {
                splitTokens.add(textPiece);
            } else if (doBasicTokenize) {

                List<String> tokens = basicTokenizer.tokenize(textPiece, useTokenDictInBasicTokenizer ? tokenDict : null);
                for (String token : tokens) {
                    splitTokens.addAll(wordpieceTokenizer.tokenize(token, useUnkToken));
                }
            } else {
                splitTokens.addAll(wordpieceTokenizer.tokenize(textPiece));
            }
        }
        return splitTokens;
    }

    /**
     * sentence_to_ids
     */
    public BertToken sentence2Ids(String text) {
        if (StrUtil.isEmpty(text)) {
            return null;
        }
        return sentence2Ids(tokenize(text));
    }

    /**
     * sentence_to_ids
     */
    public BertToken sentence2Ids(List<String> sequence) {
        // 对超长序列进行截断
        if (sequence.size() > this.maxSeqLen - 2) {
            sequence = sequence.subList(0, this.maxSeqLen - 2);
        }

        // 分别在首尾拼接特殊符号
        sequence.add(0, "[CLS]");
        sequence.add("[SEP]");

        // ID化
        long[] ids = vocab.convertTokens2Ids(sequence);

        // 根据max_seq_len与seq的长度产生填充序列
        long[] sequenceIds = new long[this.maxSeqLen];
        long[] sequenceMask = new long[this.maxSeqLen];

        System.arraycopy(ids, 0, sequenceIds, 0, ids.length);
        Arrays.fill(sequenceMask, 0, ids.length, 1);

        return new BertToken(sequenceIds, sequenceMask);
    }

}
