package com.mchz.nyx.dark.engine.classifier;

import com.mchz.nyx.dark.engine.rule.RuleManager;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2022/9/16 16:53
 */
@AllArgsConstructor
public class GroupOrClassifier<T> implements BaseClassifier<T> {

    private final List<BaseClassifier<T>> classifiers;

    @Override
    public boolean classify(T payload, RuleManager manager) {
        for (BaseClassifier<T> classifier : classifiers) {
            if (classifier.classify(payload, manager)) {
                return true;
            }
        }
        return false;
    }
}
