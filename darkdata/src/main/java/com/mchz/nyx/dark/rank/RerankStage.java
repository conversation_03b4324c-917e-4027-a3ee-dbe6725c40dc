package com.mchz.nyx.dark.rank;

import com.mchz.nyx.dark.engine.YunCeEngine;
import com.mchz.nyx.dark.model.EngineContext;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/1/17
 */
@Slf4j
@AllArgsConstructor
public class RerankStage implements RankStage {

    private final YunCeEngine client;

    @Override
    public void execute(EngineContext context) {
        client.rerank(context.getColumns());
    }
}
