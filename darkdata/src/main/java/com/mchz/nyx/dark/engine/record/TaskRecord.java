package com.mchz.nyx.dark.engine.record;

import cn.hutool.core.date.StopWatch;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;

import java.text.NumberFormat;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;

/**
 * <p>
 * 任务执行记录
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/11/12 10:06
 */
public class TaskRecord {
    private final List<RecordLog> logs;
    protected final StopWatch stopWatch;

    public TaskRecord(String id) {
        this.logs =  new LinkedList<>();
        this.stopWatch = new StopWatch(id);
    }

    public void start() {
        stopWatch.start();
    }

    public void start(String name) {
        stopWatch.start(name);
    }

    public void stop(RecordLog log) {
        stopWatch.stop();
        logs.add(log);
    }

    public String shortSummary() {
        return StrUtil.format("TaskRecord '{}': running time = {} ms", stopWatch.getId(), stopWatch.getTotalTimeMillis());
    }

    /**
     * 生成所有任务的一个任务花费时间表
     *
     * @return 任务时间表
     */
    public String prettyPrint() {
        StringBuilder sb = new StringBuilder(shortSummary());
        sb.append(FileUtil.getLineSeparator());
        if (0 == stopWatch.getTaskCount()) {
            sb.append("No task info kept");
        } else {
            sb.append("---------------------------------------------").append(FileUtil.getLineSeparator());
            sb.append("Task name      %       Log").append(FileUtil.getLineSeparator());
            sb.append("---------------------------------------------").append(FileUtil.getLineSeparator());

            final NumberFormat nf = NumberFormat.getNumberInstance();
            nf.setMinimumIntegerDigits(9);
            nf.setGroupingUsed(false);

            final NumberFormat pf = NumberFormat.getPercentInstance();
            pf.setMinimumIntegerDigits(3);
            pf.setGroupingUsed(false);
            Iterator<RecordLog> logIterator = logs.iterator();
            for (StopWatch.TaskInfo task : stopWatch.getTaskInfo()) {
                sb.append(task.getTaskName()).append('(');
                sb.append(nf.format(task.getTimeNanos())).append(")   ");
                sb.append(pf.format((double) task.getTimeNanos() / stopWatch.getTotalTimeNanos())).append("  ");
                if (logIterator.hasNext()) {
                    RecordLog next = logIterator.next();
                    sb.append("  ").append(null == next ? StrUtil.EMPTY : next.getMessage());
                }
                sb.append(FileUtil.getLineSeparator());
            }
        }
        return sb.toString();
    }
}
