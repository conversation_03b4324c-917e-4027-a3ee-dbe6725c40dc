package com.mchz.nyx.dark.engine.impl;

import ai.onnxruntime.OnnxTensor;
import ai.onnxruntime.OrtEnvironment;
import ai.onnxruntime.OrtException;
import ai.onnxruntime.OrtSession;
import cn.hutool.cache.Cache;
import cn.hutool.core.lang.func.Func0;
import com.mchz.nyx.common.exception.NyxException;
import com.mchz.nyx.dark.engine.VectorConvEngine;
import com.mchz.nyx.dark.exception.RuleException;
import com.mchz.nyx.dark.util.nlp.BertToken;
import com.mchz.nyx.dark.util.nlp.tokenizer.TransformerTokenizer;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2023/4/17 11:08
 */

public class PredictorEngine implements VectorConvEngine {
    private final OrtEnvironment env;
    private final OrtSession session;
    private final TransformerTokenizer tokenizer;
    private final Cache<String, float[]> vectorCache;

    public PredictorEngine(OrtEnvironment env, OrtSession session, TransformerTokenizer tokenizer, Cache<String, float[]> vectorCache) {
        this.env = env;
        this.session = session;
        this.tokenizer = tokenizer;
        this.vectorCache = vectorCache;
    }

    @Override
    public float[] getVector(String sequence) {
        return vectorCache.get(sequence, (Func0<float[]>) () -> {
            try {
                return getTensor(sequence);
            } catch (OrtException e) {
                return null;
            }
        });
    }

    public float[] getTensor(String text) throws OrtException {
        BertToken bertToken = tokenizer.sentence2Ids(text);
        if (null == bertToken) {
            return null;
        }
        long[][] inputIds = new long[1][];
        inputIds[0] = bertToken.getInput_ids();

        long[][] attentionMask = new long[1][];
        attentionMask[0] = bertToken.getAttention_mask();

        OnnxTensor a = OnnxTensor.createTensor(env, inputIds);
        OnnxTensor b = OnnxTensor.createTensor(env, attentionMask);

        // Input and output collections.
        Map<String, OnnxTensor> inputMap = new HashMap<>(2);
        Set<String> requestedOutputs = new HashSet<>(1);
        // Request all outputs, supply all inputs
        inputMap.put("input_ids", a);
        inputMap.put("attention_mask", b);
        requestedOutputs.add("output_0");
        try (OrtSession.Result r = session.run(inputMap, requestedOutputs)) {
            return unwrap(r);
        }
    }

    private float[] unwrap(OrtSession.Result r) {
        try {
            return ((float[][]) r.get(0).getValue())[0];
        } catch (OrtException e) {
            // 这里的256是由模型的输出决定的
            return new float[256];
        } catch (Exception e) {
            throw new RuleException(e);
        }
    }

    @Override
    public void close() {
        try {
            session.close();
        } catch (OrtException e) {
            throw new NyxException(e);
        }
    }
}
