package com.mchz.nyx.dark.engine.classifier;

import com.mchz.nyx.dark.engine.rule.RuleManager;
import lombok.AllArgsConstructor;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/12/11
 */
@AllArgsConstructor
public class NonClassifier<T> implements BaseClassifier<T> {

    private final BaseClassifier<T> classifier;

    @Override
    public boolean classify(T data, RuleManager manager) {
        return !classifier.classify(data, manager);
    }
}
