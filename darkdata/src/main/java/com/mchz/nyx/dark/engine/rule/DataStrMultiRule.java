package com.mchz.nyx.dark.engine.rule;

import com.mchz.nyx.dark.common.constants.DarkConst;
import com.mchz.nyx.dark.engine.classifier.BaseClassifier;
import com.mchz.nyx.dark.engine.record.RecordLog;
import com.mchz.nyx.dark.engine.record.SingleResultLog;
import com.mchz.nyx.dark.model.meta.StrDataInfo;
import com.mchz.nyx.dark.model.rule.ColumnPayload;
import com.mchz.nyx.dark.model.rule.RuleParam;
import com.mchz.nyx.dark.model.rule.RuleResult;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.IntStream;

/**
 * <p>
 * 规则
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/11/12 11:23
 */
public class DataStrMultiRule extends DataStrRule {

    private final List<BaseClassifier<String>> classifiers;

    public DataStrMultiRule(RuleParam param, BaseClassifier<String> classifier, List<BaseClassifier<String>> list) {
        super(param, classifier);
        this.classifiers = list;
    }

    @Override
    protected RecordLog then(ColumnPayload payload, RuleResult result, RuleManager manager) {
        List<StrDataInfo> list = payload.getDataStr();

        int count = 0;
        boolean[] flag = new boolean[classifiers.size()];
        List<Object> hitData = new ArrayList<>(DarkConst.MAX_HIT_DATA_SIZE);
        for (StrDataInfo data : list) {
            if (classifier.classify(data.getValue(), manager)) {
                count += data.getSize();
                if (hitData.size() < DarkConst.MAX_HIT_DATA_SIZE) {
                    hitData.add(data.getValue());
                }
            }
            int i = 0;
            for (BaseClassifier<String> c : classifiers) {
                if (!flag[i] && c.classify(data.getValue(), manager)) {
                    flag[i] = true;
                }
                i++;
            }
        }
        int total = payload.getTotal();
        double hitRate = (double) count / total;
        if (hitRate >= hit && IntStream.range(0, flag.length).allMatch(i -> flag[i])) {
            result.add(res, hitRate, hitData);
        }
        return new SingleResultLog(getName(), count, total);
    }
}
