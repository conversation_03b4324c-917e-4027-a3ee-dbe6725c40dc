package com.mchz.nyx.dark.engine.expression;

import cn.hutool.core.collection.CollUtil;
import com.mchz.nyx.dark.exception.RuleException;

import java.util.Set;

/**
 * <p>
 * 范围表达式
 * in (1,2,3)
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/11/17 9:34
 */
public class InExpression<T extends Number & Comparable<T>> implements ValueExpression<T> {

    private final Set<T> set;

    public InExpression(Set<T> set) {
        if (CollUtil.isEmpty(set)) {
            throw new RuleException("表达式内容为空");
        }
        this.set = set;
    }

    @Override
    public boolean compare(T value) {
        if (CollUtil.isEmpty(set)) {
            return false;
        }
        return set.contains(value);
    }
}
