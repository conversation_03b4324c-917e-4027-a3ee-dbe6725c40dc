package com.mchz.nyx.dark.retrieval;

import com.mchz.nyx.dark.engine.NlpEngine;
import com.mchz.nyx.dark.model.EngineContext;
import com.mchz.nyx.dark.model.meta.MetaColumnData;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/6
 */
@AllArgsConstructor
public class SimilarGroupStep implements SimilarSingleStep {
    private final List<SimilarSingleStep> singles;

    @Override
    public boolean execute(EngineContext context, NlpEngine nlpEngine, MetaColumnData column) {
        for (SimilarSingleStep single : singles) {
            if (single.execute(context, nlpEngine, column)) {
                return true;
            }
        }
        return false;
    }
}
