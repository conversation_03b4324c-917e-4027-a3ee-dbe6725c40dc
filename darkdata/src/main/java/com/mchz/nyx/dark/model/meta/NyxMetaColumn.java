package com.mchz.nyx.dark.model.meta;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2024/7/12
 */
public interface NyxMetaColumn {
    String getColumnName();

    ColumnType getColumnType();

    String getColumnComment();

    default Long getLength() {
        return null;
    }

    default Integer getScale() {
        return null;
    }

    default Integer getPrecision() {
        return null;
    }

    default Boolean getNullable() {
        return null;
    }

    default Integer getPosition() {
        return null;
    }

    default Boolean getIsPk() {
        return null;
    }

    default Boolean getIsUnique() {
        return null;
    }

    default Boolean getIsIndex() {
        return null;
    }

    default Boolean getIsFk() {
        return null;
    }
}

