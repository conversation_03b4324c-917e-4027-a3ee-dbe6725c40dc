package com.mchz.nyx.dark.engine.expression;

import cn.hutool.core.collection.ComputeIter;
import cn.hutool.core.text.finder.TextFinder;
import cn.hutool.core.text.split.SplitIter;
import com.mchz.nyx.dark.common.enums.CharType;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;

import java.util.Iterator;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2022/9/15 16:50
 */
public interface SegmentExpression {
    /**
     * 分段
     *
     * @param value 待分段
     * @return 分段结果
     */
    Iterator<String> split(String value);

    class SplitSegment implements SegmentExpression {
        private final TextFinder finder;
        private final int limit;

        public SplitSegment(TextFinder finder, Integer limit) {
            this.finder = finder;
            this.limit = null == limit ? 0 : limit + 1;
        }

        @Override
        public Iterator<String> split(String value) {
            return new SplitIter(value, finder, limit, false);
        }
    }

    class SubSegment implements SegmentExpression {
        private final List<SubExpression> list;

        public SubSegment(List<SubExpression> list) {
            this.list = list;
        }

        @Override
        public Iterator<String> split(String value) {
            return new SubSegmentIter(value, list.iterator());
        }

        @AllArgsConstructor
        private static final class SubSegmentIter extends ComputeIter<String> {
            private final String value;
            private final Iterator<SubExpression> list;

            @Override
            protected String computeNext() {
                if (list.hasNext()) {
                    return list.next().sub(value);
                }
                return null;
            }
        }

    }

    @AllArgsConstructor
    class IntervalSegment implements SegmentExpression {
        private final int n;

        @Override
        public Iterator<String> split(String value) {
            return new IntervalSegmentIter(value, n);
        }

        @RequiredArgsConstructor
        private static final class IntervalSegmentIter extends ComputeIter<String> {
            private final String value;
            private final int n;
            private int i;

            @Override
            protected String computeNext() {
                if (i < value.length()) {
                    return value.substring(i, Math.min((i += n), value.length()));
                }
                return null;
            }
        }
    }

    class MutationFinder extends TextFinder {
        private CharType type;

        @Override
        public TextFinder setText(CharSequence text) {
            super.setText(text);
            type = CharType.of(text.charAt(0));
            return this;
        }

        @Override
        public int start(int from) {
            final int limit = getValidEndIndex();
            CharType nextType;
            for (int i = from; i < limit; i++) {
                if (type != (nextType = CharType.of(text.charAt(i)))) {
                    type = nextType;
                    return i;
                }
            }
            return -1;
        }

        @Override
        public int end(int start) {
            return start;
        }
    }

}
