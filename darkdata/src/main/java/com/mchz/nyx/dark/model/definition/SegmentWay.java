package com.mchz.nyx.dark.model.definition;

import lombok.Data;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2022/9/14 17:58
 */
@Data
public class SegmentWay {
    /**
     * 类型
     * 0: 无
     * 1:分隔符（字符）
     * 2.分隔符（正则）
     * 3.位置
     * 4.距离（步长）
     * 5.类型变化
     */
    private Integer type;
    private Integer expectedNum;
    private Boolean least;
    private String separator;
    private Boolean caseInsensitive;
    private Integer interval;
    private List<String> subStrList;
}
