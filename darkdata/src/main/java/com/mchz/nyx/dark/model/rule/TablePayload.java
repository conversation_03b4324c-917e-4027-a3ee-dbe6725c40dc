package com.mchz.nyx.dark.model.rule;

import com.mchz.nyx.dark.model.meta.AdditionalInfo;
import com.mchz.nyx.dark.model.meta.NyxMetaColumn;
import com.mchz.nyx.dark.model.meta.NyxMetaTable;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2023/4/12 15:27
 */
public interface TablePayload {

    AdditionalInfo getInfo();

    NyxMetaTable getTable();

    List<NyxMetaColumn> getColumns();
}
