package com.mchz.nyx.dark.engine.classifier;

import com.mchz.nyx.dark.engine.rule.RuleManager;
import lombok.AllArgsConstructor;

import java.util.Arrays;
import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2023/3/31 16:38
 */
@AllArgsConstructor
public class GroupAndClassifier<T> implements BaseClassifier<T> {

    private final List<BaseClassifier<T>> classifiers;

    public GroupAndClassifier(BaseClassifier<T> c1, BaseClassifier<T> c2) {
        this.classifiers = Arrays.asList(c1, c2);
    }

    @Override
    public boolean classify(T payload, RuleManager manager) {
        for (BaseClassifier<T> classifier : classifiers) {
            if (!classifier.classify(payload, manager)) {
                return false;
            }
        }
        return true;
    }
}
