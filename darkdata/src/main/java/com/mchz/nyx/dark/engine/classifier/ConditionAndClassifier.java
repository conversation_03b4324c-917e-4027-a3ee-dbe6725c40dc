package com.mchz.nyx.dark.engine.classifier;

import com.mchz.nyx.dark.engine.filter.BaseFilter;
import com.mchz.nyx.dark.engine.filter.ExpectAble;
import com.mchz.nyx.dark.engine.rule.RuleManager;
import lombok.AllArgsConstructor;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2021/12/30 13:11
 */
@AllArgsConstructor
public class ConditionAndClassifier<T extends ExpectAble> implements BaseClassifier<String> {

    private final List<T> payloads;
    private final BaseFilter<T> filter;

    @Override
    public boolean classify(String data, RuleManager manager) {
        for (T payload : payloads) {
            if (payload.isExpect() != filter.filter(payload, data, manager)) {
                return false;
            }
        }
        return true;
    }
}
