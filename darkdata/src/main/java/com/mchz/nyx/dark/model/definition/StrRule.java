package com.mchz.nyx.dark.model.definition;

import com.mchz.nyx.dark.common.enums.FunctionStr;
import lombok.Data;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/11/19 9:59
 */
@Data
public class StrRule {
    private String lenLimit;
    private List<RegexDetail> regex;
    private List<DictDetail> dict;
    private List<FixedDetail> fixed;
    private FunctionStr fun;
    private String cFun;

    private Integer regexOp;
    private Integer dictOp;
    private Integer fixedOp;

    private String ignore;
    private String preTreat;

    private Boolean expect;
}
