package com.mchz.nyx.dark.retrieval;

import com.mchz.nyx.dark.common.enums.HitType;
import com.mchz.nyx.dark.engine.RulesEngine;
import com.mchz.nyx.dark.engine.record.TaskRecord;
import com.mchz.nyx.dark.engine.record.TaskRecordIgnore;
import com.mchz.nyx.dark.engine.rule.RuleManager;
import com.mchz.nyx.dark.model.EngineContext;
import com.mchz.nyx.dark.model.TargetResult;
import com.mchz.nyx.dark.model.meta.MetaColumnData;
import com.mchz.nyx.dark.model.rule.*;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2021/12/24 16:00
 */
@Slf4j
@Data
@AllArgsConstructor
public final class RuleRetrievalStage implements RetrievalRecallStage {

    private final RulesEngine engine;

    private final Rules rules;
    private final Rules tagColumnRules;
    private final Rules tagTableRules;
    private final RuleManager manager;

    private final List<TargetTags> tag2Target;

    @Override
    public void execute(EngineContext context) {
        RuleResult res = fireTable(context);
        for (MetaColumnData column : context.getColumns()) {
            RuleResult result = new RuleResult(res);
            TaskRecord record = getTaskRecord(column.getName());
            Facts facts = Facts.builder().payload(column).result(result).record(record).manager(manager).build();
            facts.setLimit(3);
            engine.fire(rules, facts);
            facts.setLimit(64);
            engine.fire(tagColumnRules, facts);
            log.debug("{}", record.prettyPrint());
            if (result.isEmpty()) {
                continue;
            }
            column.addAllTarget(tag2Target(result.getHitResult()));
        }
    }

    private RuleResult fireTable(EngineContext table) {
        RuleResult result = new RuleResult();
        TaskRecord record = getTaskRecord(table.getTable().getTableName());
        Facts tFacts = Facts.builder().tablePayload(new TablePayloadPoxy(table)).result(result).record(record).manager(manager).limit(64).build();
        engine.fire(tagTableRules, tFacts);
        log.debug("{}", record.prettyPrint());
        return result;
    }

    private List<TargetResult> tag2Target(List<RuleHitData> res) {
        Map<Integer, RuleHitData> map = new HashMap<>(res.size());
        res.forEach(v -> map.put(v.getInfo().getReId(), v));

        TagBitMap tagBitMap = TagBitMap.ofTmp(map.keySet());
        return tag2Target.stream().filter(v -> v.contains(tagBitMap))
            .map(v -> {
                Optional<RuleHitData> hit = v.getIfUnique().map(map::get);
                return new TargetResult(HitType.RULE, v.getInfo(), v.getScore(), hit.map(RuleHitData::getHitRate).orElse(null), hit.map(RuleHitData::getHitData).orElse(null));
            }).collect(Collectors.toList());
    }

    private TaskRecord getTaskRecord(String name) {
        return log.isDebugEnabled() ? new TaskRecord(name) : new TaskRecordIgnore(name);
    }
}
