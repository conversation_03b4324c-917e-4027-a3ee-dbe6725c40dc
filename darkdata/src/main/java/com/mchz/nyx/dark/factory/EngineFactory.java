package com.mchz.nyx.dark.factory;

import com.mchz.nyx.dark.engine.*;
import com.mchz.nyx.dark.model.config.DarkConfig;
import com.mchz.nyx.dark.model.dto.StdDetailDTO;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/18
 */
public interface EngineFactory {
    RangeFunction createRangeFunction(DarkConfig config);

    RulesEngine createRulesEngine();

    VectorConvEngine createSimilarityEngine();

    TranslateEngine createTranslateEngine(DarkConfig config);

    YunCeEngine createLLMEngine(DarkConfig config);

    HistoryRetrievalEngine createHistoryRetrievalEngine(DarkConfig config, StdDetailDTO rule);
}
