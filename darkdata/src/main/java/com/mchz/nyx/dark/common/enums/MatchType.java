package com.mchz.nyx.dark.common.enums;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/12/4 16:32
 */
public enum MatchType {
    /**
     * 尾匹配
     */
    END_WITH,
    /**
     * 首匹配
     */
    START_WITH,
    /**
     * 全匹配
     */
    EQUAL,
    /**
     * 模糊
     */
    CONTAINS;

    public static MatchType getType(Integer code) {
        MatchType[] values = values();
        if (null == code || code < 0 || code >= values.length) {
            return EQUAL;
        }
        return values[code];
    }

    public int getCode() {
        return ordinal();
    }
}
