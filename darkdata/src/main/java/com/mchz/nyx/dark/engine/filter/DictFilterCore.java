package com.mchz.nyx.dark.engine.filter;

import cn.hutool.cache.Cache;
import com.mchz.nyx.common.util.SoftCache;
import com.mchz.nyx.dark.common.constants.RuleConst;
import com.mchz.nyx.dark.common.enums.MatchType;
import com.mchz.nyx.dark.engine.algorithm.AcAutomata;
import com.mchz.nyx.dark.engine.algorithm.TrieMatch;
import com.mchz.nyx.dark.factory.RangeFunction;
import com.mchz.nyx.dark.model.dto.DictData;
import com.mchz.nyx.dark.model.dto.StandardLoadContext;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.ToString;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/11/22 13:56
 */
public class DictFilterCore extends DefaultFilterCore<DictFilterCore.MyKey, DictPayload> {
    private final AcAutomata<Integer> automata;
    private final TrieMatch<Integer> startWith;
    private final TrieMatch<Integer> endWith;
    private final Cache<Integer, Set<String>> hash;
    private final StandardLoadContext context;
    private final RangeFunction valueRange;

    public DictFilterCore(Map<MatchType, List<DictData>> initData, StandardLoadContext context, RangeFunction valueRange, int cacheSize) {
        super(cacheSize);
        this.automata = new AcAutomata<>(initData.get(MatchType.CONTAINS));
        this.startWith = new TrieMatch<>(initData.get(MatchType.START_WITH));
        this.endWith = new TrieMatch<>(initData.get(MatchType.END_WITH), true);
        this.hash = new SoftCache<>(-1);
        this.context = context;
        this.valueRange = valueRange;
        List<DictData> list = initData.get(MatchType.EQUAL);
        if (null != list) {
            Map<Integer, Set<String>> data = list.stream().collect(Collectors.groupingBy(DictData::getId, Collectors.mapping(DictData::getValue, Collectors.toSet())));
            data.forEach(hash::put);
        }
    }

    @Override
    public boolean filter(DictPayload payload, String data) {
        if (MatchType.EQUAL.equals(payload.getMatchType())) {
            Set<String> set = hash.get(payload.getId(), () -> valueRange.getValueRange(context, payload.getLoadType(), payload.getDictId()));
            return set.contains(data);
        }
        if (data.length() > RuleConst.MAX_DICT_MATCH_LEN) {
            return false;
        }
        return super.filter(payload, data);
    }

    @Override
    protected MyKey getKey(DictPayload payload, String data) {
        return new MyKey(payload.getMatchType(), data);
    }

    @Override
    protected Set<Integer> scanAll(DictPayload payload, String data) {
        switch (payload.getMatchType()) {
            case START_WITH:
                return new HashSet<>(startWith.find(data));
            case END_WITH:
                return new HashSet<>(endWith.find(data));
            case CONTAINS:
                return new HashSet<>(automata.findAll(data));
            default:
                return Collections.emptySet();
        }
    }

    @Getter
    @ToString
    @AllArgsConstructor
    protected static class MyKey {
        private final MatchType type;
        private final String data;

        @Override
        public boolean equals(Object o) {
            if (this == o) {
                return true;
            }
            if (o instanceof MyKey) {
                MyKey myKey = (MyKey) o;
                return type == myKey.type && Objects.equals(data, myKey.data);
            }
            return false;
        }

        @Override
        public int hashCode() {
            return Objects.hash(type, data);
        }
    }
}
