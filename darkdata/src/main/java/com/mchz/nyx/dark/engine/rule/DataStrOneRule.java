package com.mchz.nyx.dark.engine.rule;

import com.mchz.nyx.dark.engine.classifier.BaseClassifier;
import com.mchz.nyx.dark.engine.record.MessageLog;
import com.mchz.nyx.dark.engine.record.RecordLog;
import com.mchz.nyx.dark.model.meta.StrDataInfo;
import com.mchz.nyx.dark.model.rule.ColumnPayload;
import com.mchz.nyx.dark.model.rule.RuleParam;
import com.mchz.nyx.dark.model.rule.RuleResult;

import java.util.List;

/**
 * <p>
 * 规则
 * </p>
 *
 * <AUTHOR>
 * @since 2025/3/22
 */
public class DataStrOneRule extends DataStrRule {

    public DataStrOneRule(RuleParam param, BaseClassifier<String> classifier) {
        super(param, classifier);
    }

    @Override
    protected RecordLog then(ColumnPayload payload, RuleResult result, RuleManager manager) {
        List<StrDataInfo> list = payload.getDataStr();
        for (StrDataInfo data : list) {
            if (classifier.classify(data.getValue(), manager)) {
                result.add(res);
                break;
            }
        }
        return new MessageLog(getName());
    }
}
