package com.mchz.nyx.dark.engine.expression;

import com.mchz.nyx.dark.common.enums.ConditionSymbolType;
import lombok.AllArgsConstructor;

/**
 * <p>
 * 范围表达式
 * [1,2]
 * </p>
 *
 * <AUTHOR>
 * @date 2020/7/17 9:07
 */
@AllArgsConstructor
public class RangeExpression<T extends Number & Comparable<T>> implements ValueExpression<T> {

    /**
     * 逻辑运算符,{@link ConditionSymbolType}
     */
    private final ConditionSymbolType condition;

    private final DataExpression<T> e1;
    private final DataExpression<T> e2;

    public RangeExpression(DataExpression<T> expression) {
        this.condition = ConditionSymbolType.NULL;
        this.e1 = expression;
        this.e2 = null;
    }

    @Override
    public boolean compare(T value) {
        boolean check = compareItem(e1, value);
        switch (condition) {
            case AND:
                if (check) {
                    return compareItem(e2, value);
                } else {
                    return false;
                }
            case OR:
                if (check) {
                    return true;
                } else {
                    return compareItem(e2, value);
                }
            case NULL:
            default:
                return check;
        }
    }

    private boolean compareItem(DataExpression<T> expression, T value) {
        if (null == expression) {
            return false;
        }
        return expression.compare(value);
    }
}
