package com.mchz.nyx.dark.engine.classifier;

import cn.hutool.core.util.StrUtil;
import com.mchz.nyx.dark.engine.rule.RuleManager;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2023/4/4 10:25
 */
public class IgnoreClassifier implements BaseClassifier<String> {
    private final String ignore;
    private final BaseClassifier<String> classifier;

    public IgnoreClassifier(String ignore, BaseClassifier<String> classifier) {
        this.ignore = ignore;
        this.classifier = classifier;
    }

    @Override
    public boolean classify(String data, RuleManager manager) {
        return classifier.classify(data.replaceAll(ignore, StrUtil.EMPTY), manager);
    }
}
