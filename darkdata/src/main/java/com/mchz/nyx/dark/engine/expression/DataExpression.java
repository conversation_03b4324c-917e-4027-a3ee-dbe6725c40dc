package com.mchz.nyx.dark.engine.expression;

import com.mchz.nyx.dark.common.enums.CompareSymbolType;
import lombok.AllArgsConstructor;

/**
 * <p>
 * 数值表达式
 * </p>
 *
 * <AUTHOR>
 * @date 2020/7/17 9:07
 */
@AllArgsConstructor
public class DataExpression<T extends Number & Comparable<T>> implements ValueExpression<T> {
    /**
     * 比较符号,{@link CompareSymbolType}
     */
    private final CompareSymbolType symbol;
    /**
     * 值
     */
    private final T value;

    @Override
    public boolean compare(T v) {
        int i = v.compareTo(this.value);
        switch (symbol) {
            case GT:
                return i > 0;
            case GTE:
                return i >= 0;
            case EQUAL:
                return i == 0;
            case NOTEQUAL:
                return i != 0;
            case LTE:
                return i <= 0;
            case LT:
                return i < 0;
            default:
                return false;
        }
    }
}
