package com.mchz.nyx.dark.common.constants;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2022/2/10 15:26
 */
public interface DarkConst {

    int MAX_HIT_DATA_SIZE = 10;

    /**
     * 缓存超时时间
     */
    long CACHE_TIMEOUT = 1800000;
    /**
     * 任务中相似度缓存限制
     */
    int JOB_SIMILAR_CACHE_CAPACITY = 1000;
    /**
     * 向量缓存限制
     */
    int VECTOR_CACHE_CAPACITY = 5000;
    /**
     * 映射表缓存限制
     */
    int TRANS_CACHE_CAPACITY = 5000;

    String METHOD = "isMatchSingleValue";
}
