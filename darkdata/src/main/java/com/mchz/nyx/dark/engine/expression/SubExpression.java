package com.mchz.nyx.dark.engine.expression;

import cn.hutool.core.util.StrUtil;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/11/19 10:51
 */
public class SubExpression {
    /**
     * 位置枚举 0-全 1-前 2-后 3-中
     */
    private final byte type;
    /**
     * 位置信息
     */
    private final int[] index;

    private SubExpression(int type, int[] index) {
        this.type = (byte) type;
        this.index = index;
    }

    public static SubExpression prefix(int index) {
        return new SubExpression(1, new int[]{index});
    }

    public static SubExpression suffix(int index) {
        return new SubExpression(2, new int[]{index});
    }

    public static SubExpression mid(int fromIndex, int toIndex) {
        return new SubExpression(3, new int[]{fromIndex, toIndex});
    }

    public String sub(String str) {
        switch (type) {
            case 1:
                return StrUtil.subPre(str, index[0]);
            case 2:
                return StrUtil.sub(str, index[0], null == str ? 0 : str.length());
            case 3:
                return StrUtil.sub(str, index[0], index[1]);
            default:
                return str;
        }
    }
}
