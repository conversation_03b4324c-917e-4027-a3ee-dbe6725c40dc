package com.mchz.nyx.dark.rank;

import cn.hutool.core.lang.Pair;
import com.mchz.nyx.dark.common.enums.HitType;
import com.mchz.nyx.dark.model.EngineContext;
import com.mchz.nyx.dark.model.TargetResult;
import com.mchz.nyx.dark.model.meta.MetaColumnData;
import lombok.AllArgsConstructor;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/1/17
 */
@AllArgsConstructor
public class InnerRankStage implements RankStage {

    private final int limit;

    @Override
    public void execute(EngineContext context) {
        context.getColumns().forEach(c -> {
            if (c.nonResult()) {
                return;
            }
            execute(c);
        });
    }

    private void execute(MetaColumnData column) {
        Map<Long, Pair<Integer, TargetResult>> dedupMap = merge(column);
        List<TargetResult> list = dedupMap.values().stream().sorted((p1, p2) -> {
            int c = p1.getValue().getType().compareTo(p2.getValue().getType());
            if (c != 0) {
                return c;
            }
            if (HitType.RULE.equals(p1.getValue().getType())) {
                Double s1 = p1.getValue().getScore();
                Double s2 = p2.getValue().getScore();
                boolean high1 = s1 == null || s1 > 0.5;
                boolean high2 = s2 == null || s2 > 0.5;
                if (high1 && !high2) {
                    return -1;
                } else if (!high1 && high2) {
                    return 1;
                }
            }
            return Integer.compare(p1.getKey(), p2.getKey());
        }).limit(limit).map(Pair::getValue).collect(Collectors.toList());

        int ti = 0;
        TargetResult res = list.get(ti);
        String columnName = column.getMeta().getColumnName();
        if (list.size() > 1 && HitType.RULE.equals(res.getType()) && null != res.getInfo().getName()) {
            String name = res.getInfo().getName();
            int j = -1;
            for (TargetResult hit : list) {
                String tmp;
                if (++j == 0 || null == (tmp = hit.getInfo().getName())) {
                    continue;
                }
                if (columnName.equals(tmp) || HitType.COMMENT_SIMILAR.equals(hit.getType())
                    && null != hit.getScore() && hit.getScore() > 0.98
                    && (!res.getInfo().isEntity() || tmp.length() > 3)) {
                    ti = j;
                    break;
                }
                if (tmp.endsWith(name)) {
                    name = tmp;
                    ti = j;
                }
            }
            if (ti != 0) {
                Collections.swap(list, 0, ti);
            }
        }
        column.setResult(list);
    }

    private Map<Long, Pair<Integer, TargetResult>> merge(MetaColumnData column) {
        Map<Long, Pair<Integer, TargetResult>> dedupMap = new HashMap<>();
        int index = 0;
        for (TargetResult v : column.getResult()) {
            Long id = v.getInfo().getId();
            Pair<Integer, TargetResult> currentPair = Pair.of(index++, v);
            dedupMap.merge(id, currentPair, (o1, o2) -> o1);
        }
        return dedupMap;
    }
}
