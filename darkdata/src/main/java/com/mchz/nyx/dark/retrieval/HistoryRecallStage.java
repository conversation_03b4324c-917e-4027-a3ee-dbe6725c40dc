package com.mchz.nyx.dark.retrieval;


import com.mchz.nyx.dark.common.enums.HitType;
import com.mchz.nyx.dark.engine.HistoryRetrievalEngine;
import com.mchz.nyx.dark.model.EngineContext;
import com.mchz.nyx.dark.model.meta.MetaColumnData;
import lombok.AllArgsConstructor;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/5/15
 */
@AllArgsConstructor
public class HistoryRecallStage implements RetrievalRecallStage {

    private final HistoryRetrievalEngine engine;

    @Override
    public void execute(EngineContext context) {
        if (null == context.getTable().getTableName()) {
            return;
        }
        for (MetaColumnData column : context.getColumns()) {
            column.addAllInfo(HitType.HISTORY, engine.retrieve(context.getTable(), column.getMeta()));
        }
    }
}
