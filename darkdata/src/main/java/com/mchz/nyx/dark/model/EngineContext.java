package com.mchz.nyx.dark.model;

import com.mchz.nyx.dark.model.meta.AdditionalInfo;
import com.mchz.nyx.dark.model.meta.MetaColumnData;
import com.mchz.nyx.dark.model.meta.NyxMetaTable;

import java.util.List;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2020/12/1 15:26
 */
public interface EngineContext {

    AdditionalInfo getInfo();

    NyxMetaTable getTable();

    List<MetaColumnData> getColumns();

    <T> EngineContext put(String key, T obj);

    default <T> T get(String key) {
        return get(key, null, null);
    }

    @SuppressWarnings("unchecked")
    default <T> T get(String key, T defaultValue) {
        if (null == defaultValue) {
            return get(key);
        }
        return get(key, (Class<T>) defaultValue.getClass(), defaultValue);
    }

    <T> T get(String key, Class<T> clazz, T defaultValue);

    <T> T remove(String key);
}
