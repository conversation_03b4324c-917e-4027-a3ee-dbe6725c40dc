package com.mchz.nyx.dark.engine;

import cn.hutool.core.lang.Pair;
import com.mchz.nyx.dark.model.EmbeddingInfo;
import com.mchz.nyx.dark.model.EngineContext;
import com.mchz.nyx.dark.model.meta.MetaColumnData;

import java.util.List;
import java.util.function.Function;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/2/24
 */
public interface YunCeEngine {
    List<? extends EmbeddingInfo> askTerm(String str);

    <T> List<T> askTerm(String str, List<T> candidate, Function<T, String> getText);

    <T> List<Pair<T, String>> askLLM(EngineContext context, List<T> columns, Function<T, String> columnName);

    void rerank(List<MetaColumnData> column);
}
