package com.mchz.nyx.dark.common.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 * 比较符号类型枚举
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2019/10/28 15:00
 */
@Getter
@AllArgsConstructor
public enum CompareSymbolType {
    /**
     * 大于
     */
    GT(">", "大于", "gt"),
    /**
     * 大于等于
     */
    GTE(">=", "大于等于", "gte"),
    /**
     * 小于等于
     */
    LTE("<=", "小于等于", "lte"),
    /**
     * 小于
     */
    LT("<", "小于", "lt"),
    /**
     * 等于
     */
    EQUAL("=", "等于", "eq"),
    /**
     * 不等于
     */
    NOTEQUAL("!=", "不等于", "noteq"),
    /**
     * 介于
     */
    BETWEEN("between", "介于", "between"),
    /**
     * 范围
     */
    IN("in", "范围", "in"),
    /**
     * 为空
     */
    ISNULL("is null", "为空", "isnull"),
    /**
     * 不为空
     */
    NOTNULL("is not null", "不为空", "notnull"),
    /**
     * 在字典中
     */
    DICT("in", "在字典中", "dict"),
    /**
     * 不在字典中
     */
    NOT_DICT("not in", "不在字典中", "notdict");

    private final String value;

    private final String description;

    private final String name;

    public CompareSymbolType getType(String name) {
        if (null == name) {
            return EQUAL;
        }
        for (CompareSymbolType type : values()) {
            if (name.equals(type.getName())) {

                return type;
            }
        }
        return EQUAL;
    }
}
