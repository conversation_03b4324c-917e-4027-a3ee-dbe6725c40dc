package com.mchz.nyx.dark.model.rule;

import com.mchz.nyx.dark.engine.rule.RuleManager;
import com.mchz.nyx.dark.engine.record.TaskRecord;
import lombok.Builder;
import lombok.Data;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2023/3/31 15:26
 */
@Data
@Builder
public class Facts {
    private int limit;

    private RuleManager manager;

    private TaskRecord record;
    private RuleResult result;

    private ColumnPayload payload;
    private TablePayload tablePayload;
}
