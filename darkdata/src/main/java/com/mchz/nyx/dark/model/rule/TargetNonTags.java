package com.mchz.nyx.dark.model.rule;


import com.mchz.nyx.dark.model.TargetInfo;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2023/5/18 13:08
 */
public class TargetNonTags extends TargetTags {
    private final TagBitMap t2;

    public TargetNonTags(TagBitMap t1, TagBitMap t2, TargetInfo info, Integer score) {
        super(t1, info, score);
        this.t2 = t2;
    }

    @Override
    public boolean contains(TagBitMap sub) {
        if (t2.intersect(sub)) {
            return false;
        }
        return super.contains(sub);
    }
}
