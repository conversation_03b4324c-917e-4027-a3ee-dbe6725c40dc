package com.mchz.nyx.dark.common.enums;

import com.mchz.nyx.dark.model.rule.ColumnPayload;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.function.Function;

import static com.mchz.nyx.dark.common.enums.ReturnType.*;


/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2023/3/22 17:26
 */
@AllArgsConstructor
public enum ParamColumn implements Function<ColumnPayload, Object> {
    /**
     * 参数
     */
    COLUMN_NAME(STRING, v -> v.getMeta().getColumnName()),
    COLUMN_COMMENT(STRING, v -> v.getMeta().getColumnComment()),
    COLUMN_TYPE(STRING, v -> v.getMeta().getColumnType().getName()),
    COLUMN_TYPE_GROUP(ENUM_ARRAY, ColumnPayload::getType),
    COLUMN_POSITION(INTEGER, v -> v.getMeta().getPosition()),
    COLUMN_NULLABLE(BOOL, v -> v.getMeta().getNullable()),
    COLUMN_IS_PK(BOOL, v -> v.getMeta().getIsPk()),
    COLUMN_IS_UNIQUE(BOOL, v -> v.getMeta().getIsUnique()),
    COLUMN_IS_INDEX(BOOL, v -> v.getMeta().getIsIndex()),
    COLUMN_SCALE(INTEGER,v->v.getMeta().getScale()),
    DATA_TOTAL(INTEGER, columnPayload -> columnPayload.isLoad() ? columnPayload.getTotal() : null),
    DATA_NUM_NON_DUPLICATES(INTEGER, ColumnPayload::getNonRepetitiveNum);

    @Getter
    private final ReturnType type;
    private final Function<ColumnPayload, Object> columnFun;

    @Override
    public Object apply(ColumnPayload payload) {
        return columnFun.apply(payload);
    }
}
