package com.mchz.nyx.dark.engine.classifier;

import com.mchz.nyx.dark.engine.expression.ValueExpression;
import com.mchz.nyx.dark.engine.rule.RuleManager;
import lombok.AllArgsConstructor;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @date Created in 2022/9/23 16:33
 */
@AllArgsConstructor
public class LenOnlyClassifier implements BaseClassifier<String> {
    private final ValueExpression<Integer> lenLimit;

    @Override
    public boolean classify(String data, RuleManager manager) {
        return lenLimit.compare(data.length());
    }
}
