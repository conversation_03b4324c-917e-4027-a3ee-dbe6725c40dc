package com.mchz.nyx.dark.common.enums;


import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <p>
 * 逻辑运算符号类型枚举
 * </p>
 *
 * <AUTHOR>
 * @date 2020/11/12 13:54
 */
@Getter
@AllArgsConstructor
public enum ConditionSymbolType {
    /**
     * and
     */
    AND(0, "and", ""),
    /**
     * or
     */
    OR(1, "or", ""),
    /**
     * all
     */
    ALL(4, "all", ""),
    /**
     * any
     */
    ANY(5, "any", ""),
    /**
     * 自定义
     */
    CUSTOM(2, "custom", "自定义"),
    /**
     * 条件
     */
    CONDITION(3, "condition", "条件"),
    /**
     * 无
     */
    NULL(99, "null", "无");

    private final Integer code;

    private final String value;

    private final String description;

    public static ConditionSymbolType getType(int code) {
        for (ConditionSymbolType value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return NULL;
    }
}
