package com.mchz.nyx.dark.engine.algorithm;

import lombok.Data;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * <p>
 *
 * </p>
 *
 * <AUTHOR>
 * @since 2025/2/19
 */
class AcAutomataTest {
    private AcAutomata<String> acAutomata;

    @BeforeEach
    void setUp() {
        List<TestData> keywords = Arrays.asList(
            new TestData("he", "HE"),
            new TestData("she", "SHE"),
            new TestData("his", "HIS"),
            new TestData("hers", "HERS")
        );
        acAutomata = new AcAutomata<>(keywords);
    }

    @Test
    void testFindAll() {
        List<String> results = acAutomata.findAll("ushers");
        assertEquals(3, results.size());
        assertTrue(results.contains("HE"));
        assertTrue(results.contains("SHE"));
        assertTrue(results.contains("HERS"));
    }

    @Test
    void testFindAllNoMatch() {
        List<String> results = acAutomata.findAll("xyz");
        assertTrue(results.isEmpty());
    }

    @Test
    void testFindAllPartialMatch() {
        List<String> results = acAutomata.findAll("she");
        assertEquals(2, results.size());
        assertTrue(results.contains("SHE"));
        assertTrue(results.contains("HE"));
    }

    @Data
    private static class TestData implements TrieData<String> {
        private final String value;
        private final String result;
    }
}
