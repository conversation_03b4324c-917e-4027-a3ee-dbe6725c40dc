variables:
  CI: "false"
  TEMP_DIR: /home/<USER>/temp/$CI_PROJECT_NAME/$CI_PIPELINE_ID
  VERSION: 3.6.0
  EMBED_VERSION: 1.3.0
  PRE: v

stages:
  - build
  - upload
  - clean

build-standalone:
  stage: build
  script:
    - $MAVEN_HOME/bin/mvn versions:set -DnewVersion=$VERSION
    - $MAVEN_HOME/bin/mvn clean -DskipTests=true package -P standalone
    - mkdir -p "$TEMP_DIR"
    - mv pipeline/target/pipeline*.war "$TEMP_DIR"/pipeline-$PRE$VERSION-standalone.war
  only:
    - /^v.+$/
    - dev

build-embed:
  stage: build
  script:
    - $MAVEN_HOME/bin/mvn versions:set -DnewVersion=$VERSION
    #    - $MAVEN_HOME/bin/mvn versions:set-property -DnewVersion=8.0.11_mc -Dproperty=mysql-connector-java.version
    - $MAVEN_HOME/bin/mvn clean -DskipTests=true package -P embed
    - mkdir -p "$TEMP_DIR"/metis/api "$TEMP_DIR"/metis/data "$TEMP_DIR"/metis/engine
    - cp pipeline/target/pipeline*.jar "$TEMP_DIR"/pipeline-$PRE$VERSION-embed.jar
    - mv pipeline/target/pipeline*.jar "$TEMP_DIR"/metis/engine/pipeline.jar
    - mv pipeline/config "$TEMP_DIR"/metis/
    - mv metis/metis-engine/target/lib "$TEMP_DIR"/metis/api/
    - mv metis/metis-engine/target/metis-engine-*.jar "$TEMP_DIR"/metis/api/metis.jar
    - curl -o "$TEMP_DIR"/metis/data/ddac2.mv.db http://file2.mchz.com.cn/datatransfer/data/ddac2.mv.db
    - cd "$TEMP_DIR" && tar -zvcf metis-$EMBED_VERSION.tar.gz metis
  only:
    - web

build-cluster:
  stage: build
  script:
    - $MAVEN_HOME/bin/mvn clean -DskipTests=true package -pl pipeline -P cluster
    - mkdir -p "$TEMP_DIR"
    - mv pipeline/target/pipeline*.jar "$TEMP_DIR"/pipeline-$PRE$VERSION-cluster.jar
  only:
    - cluster

upload:
  stage: upload
  allow_failure: true
  script:
    - scp "$TEMP_DIR"/pipeline*.war <EMAIL>:/datatransfer/nyx
  only:
    - /^v.+$/
    - cluster

upload-dev:
  stage: upload
  allow_failure: true
  script:
    - scp "$TEMP_DIR"/pipeline*.war <EMAIL>:/datatransfer/nyx/pipeline-dev.war
  only:
    - dev

upload-embed:
  stage: upload
  allow_failure: true
  script:
    - scp "$TEMP_DIR"/metis*.tar.gz <EMAIL>:/datatransfer/metis
  only:
    - web

clean:
  stage: clean
  allow_failure: true
  script:
    - rm -rf $TEMP_DIR
  only:
    - /^v.+$/
    - web
    - cluster
    - dev
