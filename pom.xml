<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>com.mchz.nyx</groupId>
  <artifactId>nyx</artifactId>
  <packaging>pom</packaging>
  <modules>
    <module>common</module>
    <module>metadata</module>
    <module>darkdata</module>
    <module>metis</module>
    <module>pipeline</module>
  </modules>
  <version>1.0.0-SNAPSHOT</version>

  <properties>
    <java.version>1.8</java.version>
    <maven.compiler.source>8</maven.compiler.source>
    <maven.compiler.target>8</maven.compiler.target>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <!--maven.build.timestamp保存了maven编译时间戳-->
    <timestamp>${maven.build.timestamp}</timestamp>
    <!--指定时间格式-->
    <maven.build.timestamp.format>yyyyMMddHHmmss</maven.build.timestamp.format>
    <packaging>war</packaging>

    <!--底层框架版本号-->
    <spring-boot.version>2.7.18</spring-boot.version>
    <spring-core.version>5.3.41</spring-core.version>
    <spring-web.version>5.3.39</spring-web.version>
    <tomcat.version>9.0.106</tomcat.version>
    <logback.version>1.2.13</logback.version>
    <spring-cloud.version>2021.0.8</spring-cloud.version>
    <spring-cloud-alibaba.version>2021.0.5.0</spring-cloud-alibaba.version>
    <mchz-bom.version>1.3.2</mchz-bom.version>
    <mapper-spring-boot-starter.version>4.2.1</mapper-spring-boot-starter.version>
    <mapper.version>4.1.5</mapper.version>
    <mybatis-plus.version>3.5.12</mybatis-plus.version>
    <mybatis-plus-join.version>1.5.4</mybatis-plus-join.version>
    <flyway.version>8.5.11</flyway.version>

    <tlog.version>1.5.2</tlog.version>
    <okio.version>3.9.0</okio.version>
    <protobuf-java.version>3.13.0</protobuf-java.version>
    <grpc-all.version>1.11.0</grpc-all.version>

    <mvel2.version>2.5.2.Final</mvel2.version>
    <jna.version>5.14.0</jna.version>
    <hyperscan.version>5.4.11-3.1.0</hyperscan.version>
    <hnswlib.version>1.4.0</hnswlib.version>
    <xgboost.version>2.0.3</xgboost.version>
    <hanlp.version>portable-1.8.2</hanlp.version>
    <ehcache.version>3.10.8</ehcache.version>

    <!--工具类版本号-->
    <fastexcel.version>1.2.0</fastexcel.version>
    <distruptor.version>3.4.4</distruptor.version>
    <jfinal.enjoy.version>5.0.0</jfinal.enjoy.version>
    <xmlgraphics.version>2.6</xmlgraphics.version>
    <pinyin4j.version>2.5.1</pinyin4j.version>
    <!--其他依赖-->
    <jasypt.version>3.0.5</jasypt.version>
    <snakeyaml.version>2.4</snakeyaml.version>
    <commons.net.version>3.10.0</commons.net.version>
    <jsch.version>0.1.55</jsch.version>
    <!--swagger-->
    <knife4j-spring-boot-starter.version>4.3.0</knife4j-spring-boot-starter.version>
    <!--mvn check style-->
    <mvn-compiler-plugin.version>3.8.0</mvn-compiler-plugin.version>
    <mvn-checkstyle-plugin.version>3.0.0</mvn-checkstyle-plugin.version>
    <mvn-pmd-plugin.version>3.11.0</mvn-pmd-plugin.version>
    <mvn-spotbugs-plugin.version>4.0.2</mvn-spotbugs-plugin.version>
    <mvn-surefire-plugin.version>2.22.1</mvn-surefire-plugin.version>
    <checkstyle.tool.version>8.12</checkstyle.tool.version>
    <!--JDBC-->
    <mysql.version>8.0.30</mysql.version>
    <postgresql.version>42.7.4</postgresql.version>
    <!-- 同步修改 com.mchz.nyx.pipeline.config.McDatasourceConfig -->
    <mcdatasource.version>*******-release</mcdatasource.version>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <version>1.18.30</version>
        <scope>provided</scope>
      </dependency>
      <dependency>
        <groupId>org.postgresql</groupId>
        <artifactId>postgresql</artifactId>
        <version>${postgresql.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-dependencies</artifactId>
        <version>${spring-boot.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-dependencies</artifactId>
        <version>${spring-cloud.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.alibaba.cloud</groupId>
        <artifactId>spring-cloud-alibaba-dependencies</artifactId>
        <version>${spring-cloud-alibaba.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>tk.mybatis</groupId>
        <artifactId>mapper-spring-boot-starter</artifactId>
        <version>${mapper-spring-boot-starter.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-core</artifactId>
        <version>${spring-core.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-web</artifactId>
        <version>${spring-web.version}</version>
      </dependency>
      <dependency>
        <groupId>org.yaml</groupId>
        <artifactId>snakeyaml</artifactId>
        <version>${snakeyaml.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.tomcat.embed</groupId>
        <artifactId>tomcat-embed-core</artifactId>
        <version>${tomcat.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.tomcat.embed</groupId>
        <artifactId>tomcat-embed-el</artifactId>
        <version>${tomcat.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.tomcat.embed</groupId>
        <artifactId>tomcat-embed-websocket</artifactId>
        <version>${tomcat.version}</version>
      </dependency>
      <dependency>
        <groupId>com.jfinal</groupId>
        <artifactId>enjoy</artifactId>
        <version>${jfinal.enjoy.version}</version>
      </dependency>
      <dependency>
        <groupId>com.lmax</groupId>
        <artifactId>disruptor</artifactId>
        <version>${distruptor.version}</version>
      </dependency>
      <dependency>
        <groupId>mysql</groupId>
        <artifactId>mysql-connector-java</artifactId>
        <version>${mysql.version}</version>
      </dependency>
      <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-boot-starter</artifactId>
        <version>${mybatis-plus.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.yulichang</groupId>
        <artifactId>mybatis-plus-join-boot-starter</artifactId>
        <version>${mybatis-plus-join.version}</version>
      </dependency>
      <!-- 全链路日志 -->
      <dependency>
        <groupId>com.yomahub</groupId>
        <artifactId>tlog-web-spring-boot-starter</artifactId>
        <version>${tlog.version}</version>
        <exclusions>
          <exclusion>
            <artifactId>fastjson</artifactId>
            <groupId>com.alibaba</groupId>
          </exclusion>
          <exclusion>
            <artifactId>log4j</artifactId>
            <groupId>log4j</groupId>
          </exclusion>
          <exclusion>
            <groupId>cn.hutool</groupId>
            <artifactId>*</artifactId>
          </exclusion>
          <exclusion>
            <artifactId>commons-beanutils</artifactId>
            <groupId>commons-beanutils</groupId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.mchz</groupId>
        <artifactId>datasource-cli</artifactId>
        <version>${mcdatasource.version}</version>
        <exclusions>
          <exclusion>
            <artifactId>log4j-slf4j-impl</artifactId>
            <groupId>org.apache.logging.log4j</groupId>
          </exclusion>
          <exclusion>
            <artifactId>hutool-all</artifactId>
            <groupId>cn.hutool</groupId>
          </exclusion>
          <exclusion>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
          </exclusion>
          <exclusion>
            <groupId>net.java.dev.jna</groupId>
            <artifactId>jna</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>cn.hutool</groupId>
        <artifactId>hutool-all</artifactId>
        <version>5.8.37</version>
      </dependency>
      <dependency>
        <groupId>com.mchz.base</groupId>
        <artifactId>mc-basestarters-dependencies</artifactId>
        <version>${mchz-bom.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.mchz.nyx</groupId>
        <artifactId>common</artifactId>
        <version>1.2.0</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <repositories>
    <repository>
      <id>nexus</id>
      <name>Team Nexus Repository</name>
      <url>http://nexus.mchz.com.cn:8081/nexus/content/groups/public</url>
      <snapshots>
        <enabled>true</enabled>
        <updatePolicy>always</updatePolicy>
      </snapshots>
      <releases>
        <enabled>true</enabled>
        <updatePolicy>never</updatePolicy>
      </releases>
    </repository>
  </repositories>
</project>
